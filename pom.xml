<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.bjgoodwill.hip</groupId>
        <artifactId>hip-parent</artifactId>
        <version>5.0-SNAPSHOT</version>
    </parent>

    <artifactId>hip-base-common-as</artifactId>
    <version>5.0-SNAPSHOT</version>

    <properties>
        <hip_security.version>5.0-SNAPSHOT</hip_security.version>
        <hip_econ_price_api.version>5.0-SNAPSHOT</hip_econ_price_api.version>
        <hip_drug_goods_api.version>5.0-SNAPSHOT</hip_drug_goods_api.version>
        <hip_mate_goods_api.version>5.0-SNAPSHOT</hip_mate_goods_api.version>
        <hip_base_material_api.version>5.0-SNAPSHOT</hip_base_material_api.version>
        <hip_base_pat_index_api.version>5.0-SNAPSHOT</hip_base_pat_index_api.version>
        <hip_term_api.version>5.0-SNAPSHOT</hip_term_api.version>
        <hip_org_api.version>5.0-SNAPSHOT</hip_org_api.version>
        <hip_pat_regist_api.version>5.0-SNAPSHOT</hip_pat_regist_api.version>
        <hip_pat_in_hospital_api.version>5.0-SNAPSHOT</hip_pat_in_hospital_api.version>
        <hip_base_cis_dict_api.version>5.0-SNAPSHOT</hip_base_cis_dict_api.version>
        <hip_cis_cds_api.version>5.0-SNAPSHOT</hip_cis_cds_api.version>
        <hip_file.version>5.0-SNAPSHOT</hip_file.version>
        <hip_cis_ipd_cpoe_api.version>5.0-SNAPSHOT</hip_cis_ipd_cpoe_api.version>
        <hip_econ_ipd_bill_api.version>5.0-SNAPSHOT</hip_econ_ipd_bill_api.version>
        <hip_param_api.version>5.0-SNAPSHOT</hip_param_api.version>
        <hip_business_as_util.version>5.0-SNAPSHOT</hip_business_as_util.version>
        <hip_base_mi_api.version>5.0-SNAPSHOT</hip_base_mi_api.version>
        <hip_drug_stock_api.version>5.0-SNAPSHOT</hip_drug_stock_api.version>
        <hip_base_econ_fixed_api.version>5.0-SNAPSHOT</hip_base_econ_fixed_api.version>
        <hip_base_material_api.version>5.0-SNAPSHOT</hip_base_material_api.version>
        <hip_econ_invoice_api.version>5.0-SNAPSHOT</hip_econ_invoice_api.version>
        <hip_base_cis_diagnose_api.version>5.0-SNAPSHOT</hip_base_cis_diagnose_api.version>
        <hip-cis-apply-api.version>5.0-SNAPSHOT</hip-cis-apply-api.version>
        <hip_cis_cdr_api.version>5.0-SNAPSHOT</hip_cis_cdr_api.version>
        <hip_base_cis_medicineitem_api.version>5.0-SNAPSHOT</hip_base_cis_medicineitem_api.version>
        <hip_econ_opd_bill_api.version>5.0-SNAPSHOT</hip_econ_opd_bill_api.version>
        <hip_mi_opd_api.version>5.0-SNAPSHOT</hip_mi_opd_api.version>
        <hip-cis-opd-cpoe-api.version>5.0-SNAPSHOT</hip-cis-opd-cpoe-api.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-security</artifactId>
            <version>${hip_security.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-base-pat-index-api</artifactId>
            <version>${hip_base_pat_index_api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-term-api</artifactId>
            <version>${hip_term_api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-econ-price-api</artifactId>
            <version>${hip_econ_price_api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-drug-goods-api</artifactId>
            <version>${hip_drug_goods_api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-mate-base-api</artifactId>
            <version>${hip_mate_goods_api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-base-material-api</artifactId>
            <version>${hip_base_material_api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-org-api</artifactId>
            <version>${hip_org_api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-pat-regist-api</artifactId>
            <version>${hip_pat_regist_api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-pat-in-hospital-api</artifactId>
            <version>${hip_pat_in_hospital_api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-file</artifactId>
            <version>${hip_file.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-base-cis-dict-api</artifactId>
            <version>${hip_base_cis_dict_api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-cis-cds-api</artifactId>
            <version>${hip_cis_cds_api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-cis-ipd-cpoe-api</artifactId>
            <version>${hip_cis_ipd_cpoe_api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-econ-ipd-bill-api</artifactId>
            <version>${hip_econ_ipd_bill_api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-param-api</artifactId>
            <version>${hip_param_api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-business-as-util</artifactId>
            <version>${hip_business_as_util.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-drug-stock-api</artifactId>
            <version>${hip_drug_stock_api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-base-mi-api</artifactId>
            <version>${hip_base_mi_api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-base-econ-fixed-api</artifactId>
            <version>${hip_base_econ_fixed_api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-base-material-api</artifactId>
            <version>${hip_base_material_api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-econ-invoice-api</artifactId>
            <version>${hip_econ_invoice_api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-base-cis-diagnose-api</artifactId>
            <version>${hip_base_cis_diagnose_api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-cis-apply-api</artifactId>
            <version>${hip-cis-apply-api.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-seata</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-cis-cdr-api</artifactId>
            <version>${hip_cis_cdr_api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-base-cis-medicineitem-api</artifactId>
            <version>${hip_base_cis_medicineitem_api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-excel</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-econ-opd-bill-api</artifactId>
            <version>${hip_econ_opd_bill_api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-mi-opd-api</artifactId>
            <version>${hip_mi_opd_api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-cis-opd-cpoe-api</artifactId>
            <version>${hip-cis-opd-cpoe-api.version}</version>
        </dependency>
    </dependencies>
    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <!-- 工程编码插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.target}</target>
                    <encoding>${encoding}</encoding>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>