<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.bjgoodwill.hip</groupId>
        <artifactId>hip-parent</artifactId>
        <version>5.0-SNAPSHOT</version>
    </parent>

    <artifactId>hip-drug-wms-as</artifactId>
    <version>5.0-SNAPSHOT</version>
    <name>hip-drug-wms-as</name>

    <properties>
        <hip_drug_stock_api.version>5.0-SNAPSHOT</hip_drug_stock_api.version>
        <hip_drug_goods_api.version>5.0-SNAPSHOT</hip_drug_goods_api.version>
        <hip_drug_inout_api.version>5.0-SNAPSHOT</hip_drug_inout_api.version>
        <hip_drug_pms_api.version>5.0-SNAPSHOT</hip_drug_pms_api.version>
        <hip_drug_balance_api.version>5.0-SNAPSHOT</hip_drug_balance_api.version>
        <hip-drug-price-api>5.0-SNAPSHOT</hip-drug-price-api>
        <hip-drug-ipd-api>5.0-SNAPSHOT</hip-drug-ipd-api>
        <hip_drug_opd_api>5.0-SNAPSHOT</hip_drug_opd_api>
        <hip_term_api.version>5.0-SNAPSHOT</hip_term_api.version>
        <hip_org_api.version>5.0-SNAPSHOT</hip_org_api.version>
        <hip_file.version>5.0-SNAPSHOT</hip_file.version>
        <hip_code_generation_api.version>5.0-SNAPSHOT</hip_code_generation_api.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-excel</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-seata</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-security</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-org-api</artifactId>
            <version>${hip_org_api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-term-api</artifactId>
            <version>${hip_term_api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-drug-stock-api</artifactId>
            <version>${hip_drug_stock_api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-drug-goods-api</artifactId>
            <version>${hip_drug_goods_api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-drug-inout-api</artifactId>
            <version>${hip_drug_inout_api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-drug-pms-api</artifactId>
            <version>${hip_drug_pms_api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-drug-balance-api</artifactId>
            <version>${hip_drug_balance_api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-param-api</artifactId>
            <version>5.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-commons</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-business-as-util</artifactId>
            <version>5.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-drug-price-api</artifactId>
            <version>${hip-drug-price-api}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-drug-ipd-api</artifactId>
            <version>${hip-drug-ipd-api}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-drug-opd-api</artifactId>
            <version>${hip_drug_opd_api}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-code-generation-api</artifactId>
            <version>${hip_code_generation_api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-file</artifactId>
            <version>${hip_file.version}</version>
        </dependency>
    </dependencies>

</project>
