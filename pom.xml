<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.bjgoodwill.hip</groupId>
        <artifactId>hip-parent</artifactId>
        <version>5.0-SNAPSHOT</version>
    </parent>

    <artifactId>hip-opd-ac</artifactId>
    <version>5.0-SNAPSHOT</version>
    <name>hip-opd-ac</name>

    <properties>
        <!--as-->
        <hip_drug_opd_as.version>5.0-SNAPSHOT</hip_drug_opd_as.version>
        <hip_econ_opd_as.version>5.0-SNAPSHOT</hip_econ_opd_as.version>
        <hip_cis_doc_opd_as.version>5.0-SNAPSHOT</hip_cis_doc_opd_as.version>
        <hip_cis_opd_as.version>5.0-SNAPSHOT</hip_cis_opd_as.version>
        <hip_business_as_util.version>5.0-SNAPSHOT</hip_business_as_util.version>
        <!--架构-->
        <hip_security_feign.version>5.0-SNAPSHOT</hip_security_feign.version>
        <hip_org_feign.version>5.0-SNAPSHOT</hip_org_feign.version>
        <hip_term_feign.version>5.0-SNAPSHOT</hip_term_feign.version>
        <hip_param_feign.version>5.0-SNAPSHOT</hip_param_feign.version>
        <hip_code_generation_feign.version>5.0-SNAPSHOT</hip_code_generation_feign.version>
        <hip_fs_feign.version>5.0-SNAPSHOT</hip_fs_feign.version>
        <hip_notice_feign.version>5.0-SNAPSHOT</hip_notice_feign.version>
        <hip_print_config_feign.version>5.0-SNAPSHOT</hip_print_config_feign.version>
        <hip_report_manager_feign.version>5.0-SNAPSHOT</hip_report_manager_feign.version>
        <!--药品-->
        <hip_drug_stock_feign.version>5.0-SNAPSHOT</hip_drug_stock_feign.version>
        <hip_drug_goods_feign.version>5.0-SNAPSHOT</hip_drug_goods_feign.version>
        <hip_drug_inout_feign.version>5.0-SNAPSHOT</hip_drug_inout_feign.version>
        <hip_drug_pms_feign.version>5.0-SNAPSHOT</hip_drug_pms_feign.version>
        <hip-drug-price-feign.version>5.0-SNAPSHOT</hip-drug-price-feign.version>
        <hip_drug_ipd_feign.version>5.0-SNAPSHOT</hip_drug_ipd_feign.version>
        <hip_drug_balance_feign.version>5.0-SNAPSHOT</hip_drug_balance_feign.version>
        <hip_drug_opd_feign.version>5.0-SNAPSHOT</hip_drug_opd_feign.version>
        <!--物资-->
        <hip_mate_base_feign.version>5.0-SNAPSHOT</hip_mate_base_feign.version>
        <hip_base_material_feign.version>5.0-SNAPSHOT</hip_base_material_feign.version>
        <!--经济-->
        <hip_econ_price_feign.version>5.0-SNAPSHOT</hip_econ_price_feign.version>
        <hip_base_econ_fixed_feign.version>5.0-SNAPSHOT</hip_base_econ_fixed_feign.version>
        <hip_econ_invoice_feign.version>5.0-SNAPSHOT</hip_econ_invoice_feign.version>
        <hip_base_econ_statistic_feign.version>5.0-SNAPSHOT</hip_base_econ_statistic_feign.version>
        <hip_econ_ipd_bill_feign.version>5.0-SNAPSHOT</hip_econ_ipd_bill_feign.version>
        <hip_econ_finance_feign.version>5.0-SNAPSHOT</hip_econ_finance_feign.version>
        <hip_econ_opd_bill_feign.version>5.0-SNAPSHOT</hip_econ_opd_bill_feign.version>
        <hip-base-econ-statistic-feign.version>5.0-SNAPSHOT</hip-base-econ-statistic-feign.version>
        <!--患者-->
        <hip_pat_apply_feign.version>5.0-SNAPSHOT</hip_pat_apply_feign.version>
        <hip_pat_in_hospital_feign.version>5.0-SNAPSHOT</hip_pat_in_hospital_feign.version>
        <hip_pat_regist_feign.version>5.0-SNAPSHOT</hip_pat_regist_feign.version>
        <hip_base_pat_index_feign.version>5.0-SNAPSHOT</hip_base_pat_index_feign.version>
        <!--门诊办-->
        <hip_pat_schedule_feign.version>5.0-SNAPSHOT</hip_pat_schedule_feign.version>
        <!--诊疗-->
        <hip_base_cis_dict_feign.version>5.0-SNAPSHOT</hip_base_cis_dict_feign.version>
        <hip_base_cis_diagnose_feign.version>5.0-SNAPSHOT</hip_base_cis_diagnose_feign.version>
        <hip_base_cis_medicineitem_feign.version>5.0-SNAPSHOT</hip_base_cis_medicineitem_feign.version>
        <hip_cis_apply_feign.version>5.0-SNAPSHOT</hip_cis_apply_feign.version>
        <hip_cis_ipd_cpoe_feign.version>5.0-SNAPSHOT</hip_cis_ipd_cpoe_feign.version>
        <hip_cis_ipd_nurse_feign.version>5.0-SNAPSHOT</hip_cis_ipd_nurse_feign.version>
        <hip_cis_rule_feign.version>5.0-SNAPSHOT</hip_cis_rule_feign.version>
        <hip_cis_cds_feign.version>5.0-SNAPSHOT</hip_cis_cds_feign.version>
        <hip_cis_cdr_feign.version>5.0-SNAPSHOT</hip_cis_cdr_feign.version>
        <hip_cis_opd_cpoe_feign.version>5.0-SNAPSHOT</hip_cis_opd_cpoe_feign.version>
        <!--医保-->
        <hip_mi_ipd_feign.version>5.0-SNAPSHOT</hip_mi_ipd_feign.version>
        <hip_mi_opd_feign.version>5.0-SNAPSHOT</hip_mi_opd_feign.version>
        <hip_base_mi_feign.version>5.0-SNAPSHOT</hip_base_mi_feign.version>
        <hip_mi_interface_feign.version>5.0-SNAPSHOT</hip_mi_interface_feign.version>
        <hip_mi_audit_feign.version>5.0-SNAPSHOT</hip_mi_audit_feign.version>
        <hip_mi_dict_feign.version>5.0-SNAPSHOT</hip_mi_dict_feign.version>
        <!--通用-->
        <image.tag>0.0.1</image.tag>
    </properties>

    <dependencies>
        <!--幂等校验-->
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-idempotent</artifactId>
        </dependency>
        <!--HTTP请求-->
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-web</artifactId>
        </dependency>
        <!--as-->
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-drug-opd-as</artifactId>
            <version>${hip_drug_opd_as.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-econ-opd-as</artifactId>
            <version>${hip_econ_opd_as.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-cis-opd-as</artifactId>
            <version>${hip_cis_opd_as.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-cis-doc-opd-as</artifactId>
            <version>${hip_cis_doc_opd_as.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-business-as-util</artifactId>
            <version>${hip_business_as_util.version}</version>
        </dependency>
        <!--架构-->
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-security-feign</artifactId>
            <version>${hip_security_feign.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-org-feign</artifactId>
            <version>${hip_org_feign.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-term-feign</artifactId>
            <version>${hip_term_feign.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-param-feign</artifactId>
            <version>${hip_param_feign.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-code-generation-feign</artifactId>
            <version>${hip_code_generation_feign.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-fs-feign</artifactId>
            <version>${hip_fs_feign.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-notice-feign</artifactId>
            <version>${hip_notice_feign.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-print-config-feign</artifactId>
            <version>${hip_print_config_feign.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-report-manager-feign</artifactId>
            <version>${hip_report_manager_feign.version}</version>
        </dependency>
        <!-- 药品 -->
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-drug-inout-feign</artifactId>
            <version>${hip_drug_inout_feign.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-drug-stock-feign</artifactId>
            <version>${hip_drug_stock_feign.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-drug-goods-feign</artifactId>
            <version>${hip_drug_goods_feign.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-drug-pms-feign</artifactId>
            <version>${hip_drug_pms_feign.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-drug-price-feign</artifactId>
            <version>${hip-drug-price-feign.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-drug-ipd-feign</artifactId>
            <version>${hip_drug_ipd_feign.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-drug-opd-feign</artifactId>
            <version>${hip_drug_opd_feign.version}</version>
        </dependency>
        <!-- 物资 -->
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-mate-base-feign</artifactId>
            <version>${hip_mate_base_feign.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-base-material-feign</artifactId>
            <version>${hip_base_material_feign.version}</version>
        </dependency>
        <!--经济-->
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-econ-price-feign</artifactId>
            <version>${hip_econ_price_feign.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-base-econ-fixed-feign</artifactId>
            <version>${hip_base_econ_fixed_feign.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-econ-invoice-feign</artifactId>
            <version>${hip_econ_invoice_feign.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-base-econ-statistic-feign</artifactId>
            <version>${hip_base_econ_statistic_feign.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-econ-ipd-bill-feign</artifactId>
            <version>${hip_econ_ipd_bill_feign.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-econ-finance-feign</artifactId>
            <version>${hip_econ_finance_feign.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-econ-opd-bill-feign</artifactId>
            <version>${hip_econ_opd_bill_feign.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-base-econ-statistic-feign</artifactId>
            <version>${hip-base-econ-statistic-feign.version}</version>
        </dependency>
        <!--患者-->
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-pat-apply-feign</artifactId>
            <version>${hip_pat_apply_feign.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-pat-in-hospital-feign</artifactId>
            <version>${hip_pat_in_hospital_feign.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-base-pat-index-feign</artifactId>
            <version>${hip_base_pat_index_feign.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-pat-regist-feign</artifactId>
            <version>${hip_pat_regist_feign.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-pat-schedule-feign</artifactId>
            <version>${hip_pat_schedule_feign.version}</version>
        </dependency>
        <!--诊疗-->
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-base-cis-dict-feign</artifactId>
            <version>${hip_base_cis_dict_feign.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-base-cis-diagnose-feign</artifactId>
            <version>${hip_base_cis_diagnose_feign.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-base-cis-medicineitem-feign</artifactId>
            <version>${hip_base_cis_medicineitem_feign.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-cis-apply-feign</artifactId>
            <version>${hip_cis_apply_feign.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-cis-ipd-cpoe-feign</artifactId>
            <version>${hip_cis_ipd_cpoe_feign.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-cis-ipd-nurse-feign</artifactId>
            <version>${hip_cis_ipd_nurse_feign.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-cis-rule-feign</artifactId>
            <version>${hip_cis_rule_feign.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-cis-cds-feign</artifactId>
            <version>${hip_cis_cds_feign.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-cis-cdr-feign</artifactId>
            <version>${hip_cis_cdr_feign.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-cis-opd-cpoe-feign</artifactId>
            <version>${hip_cis_opd_cpoe_feign.version}</version>
        </dependency>
        <!--医保-->
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-base-mi-feign</artifactId>
            <version>${hip_base_mi_feign.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-mi-interface-feign</artifactId>
            <version>${hip_mi_interface_feign.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-mi-ipd-feign</artifactId>
            <version>${hip_mi_ipd_feign.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-mi-opd-feign</artifactId>
            <version>${hip_mi_opd_feign.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-mi-audit-feign</artifactId>
            <version>${hip_mi_audit_feign.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bjgoodwill.hip</groupId>
            <artifactId>hip-mi-dict-feign</artifactId>
            <version>${hip_mi_dict_feign.version}</version>
        </dependency>
        <!--通用-->
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-commons</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <!-- 工程编码插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.target}</target>
                    <encoding>${encoding}</encoding>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>