package com.bjgoodwill.hip.org.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.bjgoodwill.hip.common.bean.EnumTo;
import com.bjgoodwill.hip.business.util.enums.dict.DictCodeEnum;
import com.bjgoodwill.hip.ds.org.api.service.DeptService;
import com.bjgoodwill.hip.ds.org.api.service.WorkGroupService;
import com.bjgoodwill.hip.ds.org.api.to.*;
import com.bjgoodwill.hip.ds.org.api.type.WorkGroupType;
import com.bjgoodwill.hip.ds.term.api.service.DictElementService;
import com.bjgoodwill.hip.ds.term.api.to.DictElementTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@Tag(name = "门诊科室和门诊分诊台管理服务", description = "门诊科室和门诊分诊台服务类")
@SaCheckPermission("org:outDeptTriageControllerManage")
@RequestMapping("/org/outDeptTriage")
public class OutDeptTriageController {
    @Autowired
    private DeptService deptService;
    @Autowired
    private WorkGroupService workGroupService;
    @Autowired
    private DictElementService dictElementService;

    @Operation(summary = "查询所有院区", description = "查询所有院区")
    @ApiResponse(description = "返回分院集合", content = @Content(mediaType = "application/json", schema = @Schema(implementation = HospitalAreaTo.class)))
    @GetMapping("/hospitalArea")
    public List<HospitalAreaTo> getHospitalAreas() {
        return deptService.getAllHospital();
    }


    @Operation(summary = "根据院区编码查询门急诊出诊科室工作组", description = "根据院区编码查询门急诊出诊科室工作组")
    @ApiResponse(description = "返回出诊科室工作组", content = @Content(mediaType = "application/json", schema = @Schema(implementation = WorkGroupOutpatientDeptTo.class)))
    @GetMapping("/outpatientEmergencyDept/{hospitalAreaCode}")
    public Map<String, List> getOutpatientEmergencyDeptByHosArea(@PathVariable("hospitalAreaCode") String hospitalAreaCode) {
        List<WorkGroupEmergencyDeptTo> emergencyDeptToList = workGroupService.getEmergencyDeptByHosArea(hospitalAreaCode);
        List<WorkGroupOutpatientDeptTo> outpatientDeptToList = workGroupService.getOutpatientDeptByHosArea(hospitalAreaCode);
        Map<String, List> map = new HashMap();
        map.put(WorkGroupType.WORK_GROUP_EMERGENCY, emergencyDeptToList);
        map.put(WorkGroupType.WORK_GROUP_OUTPATIENT, outpatientDeptToList);
        return map;
    }

    @Operation(summary = "根据院区编码查询门诊分诊台工作组", description = "根据院区编码查询门诊分诊台工作组")
    @ApiResponse(description = "返回出诊科室工作组", content = @Content(mediaType = "application/json", schema = @Schema(implementation = WorkGroupOutpatientTriageTo.class)))
    @GetMapping("/outpatientTriage/{hospitalAreaCode}")
    public List<WorkGroupOutpatientTriageTo> getOutpatientTriagesByHosArea(@PathVariable("hospitalAreaCode") String hospitalAreaCode) {
        return workGroupService.getOutpatientTriagesByHosArea(hospitalAreaCode);
    }

    @Operation(summary = "根据标识启用工作组", description = "根据标识启用工作组")
    @PutMapping("/{id}/enable")
    public Boolean enableWorkGroup(@PathVariable("id") String id) {
        workGroupService.enableWorkGroup(id);
        return true;
    }

    @Operation(summary = "根据标识禁用工作组", description = "根据标识禁用工作组")
    @PutMapping("/{id}/disable")
    public Boolean disableWorkGroup(@PathVariable("id") String id) {
        workGroupService.disableWorkGroup(id);
        return true;
    }

    @Operation(summary = "新增门诊出诊科室工作组", description = "新增门诊出诊科室工作组")
    @ApiResponse(description = "返回门诊出诊科室工作组", content = @Content(mediaType = "application/json", schema = @Schema(implementation = WorkGroupOutpatientDeptTo.class)))
    @PostMapping("/workGroupOutpatientDept")
    public WorkGroupOutpatientDeptTo saveOutpatientDept(@RequestBody @Valid WorkGroupOutpatientDeptNto workGroupOutpatientDeptNto) {
        return workGroupService.saveOutpatientDept(workGroupOutpatientDeptNto);
    }

    @Operation(summary = "新增急诊出诊科室工作组", description = "新增急诊出诊科室工作组")
    @ApiResponse(description = "返回门诊出诊科室工作组", content = @Content(mediaType = "application/json", schema = @Schema(implementation = WorkGroupEmergencyDeptTo.class)))
    @PostMapping("/workGroupEmergencyDept")
    public WorkGroupEmergencyDeptTo saveEmergencyDept(@RequestBody @Valid WorkGroupEmergencyDeptNto workGroupEmergencyDeptNto) {
        return workGroupService.saveEmergencyDept(workGroupEmergencyDeptNto);
    }

    @Operation(summary = "新增分诊台工作组", description = "新增分诊台工作组")
    @ApiResponse(description = "返回分诊台工作组", content = @Content(mediaType = "application/json", schema = @Schema(implementation = WorkGroupOutpatientTriageTo.class)))
    @PostMapping("/workGroupOutpatientTriage")
    public WorkGroupOutpatientTriageTo saveOutpatientTriage(@RequestBody @Valid WorkGroupOutpatientTriageNto workGroupOutpatientTriageNto) {
        return workGroupService.saveOutpatientTriage(workGroupOutpatientTriageNto);
    }

    @Operation(summary = "根据标识更新门诊出诊科室工作组", description = "根据标识更新门诊出诊科室工作组")
    @ApiResponse(description = "返回门诊出诊科室工作组", content = @Content(mediaType = "application/json", schema = @Schema(implementation = WorkGroupOutpatientDeptTo.class)))
    @PutMapping("/workGroupOutpatientDept/{code}")
    public WorkGroupOutpatientDeptTo updateOutpatientDept(@PathVariable("code") String code, @RequestBody @Valid WorkGroupOutpatientDeptEto workGroupOutpatientDeptEto) {
        return workGroupService.updateOutpatientDept(code, workGroupOutpatientDeptEto);
    }

    @Operation(summary = "根据标识更新急诊出诊科室工作组", description = "根据标识更新急诊出诊科室工作组")
    @ApiResponse(description = "返回急诊出诊科室工作组", content = @Content(mediaType = "application/json", schema = @Schema(implementation = WorkGroupEmergencyDeptTo.class)))
    @PutMapping("/workGroupEmergencyDept/{code}")
    public WorkGroupEmergencyDeptTo updateEmergencyDept(@PathVariable("code") String code, @RequestBody @Valid WorkGroupEmergencyDeptEto workGroupEmergencyDeptEto) {
        return workGroupService.updateEmergencyDept(code, workGroupEmergencyDeptEto);
    }


    @Operation(summary = "根据标识更新分诊台工作组", description = "根据标识更新分诊台工作组")
    @ApiResponse(description = "返回分诊台工作组", content = @Content(mediaType = "application/json", schema = @Schema(implementation = WorkGroupOutpatientTriageTo.class)))
    @PutMapping("/workGroupOutpatientTriage/{code}")
    public WorkGroupOutpatientTriageTo updateOutpatientTriage(@PathVariable("code") String code, @RequestBody @Valid WorkGroupOutpatientTriageEto workGroupOutpatientTriageEto) {
        return workGroupService.updateOutpatientTriage(code, workGroupOutpatientTriageEto);
    }

    @Operation(summary = "查询按类型区分工作组列表下拉框", description = "查询按类型区分工作组列表下拉框")
    @ApiResponse(description = "返回工作组集合", content = @Content(mediaType = "application/json", schema = @Schema(implementation = Map.class)))
    @GetMapping("/workGroup")
    public WorkGroupsDetailUseTo getWorkGroups() {
        return workGroupService.getWorkGroupsDetail();
    }

    @Operation(summary = "获取工作组类型", description = "获取工作组类型")
    @ApiResponse(description = "返回类型集合", content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnumTo.class)))
    @GetMapping("/workGroupType")
    public List<EnumTo<String>> getWorkGroupType() {
        List<EnumTo<String>> enumToList = new ArrayList<>();
        EnumTo<String> emergency = new EnumTo<>();
        emergency.setCode(WorkGroupType.WORK_GROUP_EMERGENCY);
        emergency.setName("急诊科室");
        enumToList.add(emergency);
        EnumTo<String> outPatient = new EnumTo<>();
        outPatient.setCode(WorkGroupType.WORK_GROUP_OUTPATIENT);
        outPatient.setName("门诊科室");
        enumToList.add(outPatient);
        return enumToList;
    }

    @Operation(summary = "获取科室代码列表", description = "获取科室代码列表")
    @ApiResponse(description = "返回类型集合", content = @Content(mediaType = "application/json", schema = @Schema(implementation = DictElementTo.class)))
    @GetMapping("/dictDept")
    public List<DictElementTo> getDictDept() {
        return dictElementService.getCustomDictElement(DictCodeEnum.科室.getCode());
    }

    @Operation(summary = "获取所有行政科室列表", description = "获取所有行政科室列表")
    @ApiResponse(description = "返回类型集合", content = @Content(mediaType = "application/json", schema = @Schema(implementation = DeptTo.class)))
    @GetMapping("/dept/all")
    public List<DeptTo> getAllDept() {
        return deptService.getAllDept();
    }
}
