package com.bjgoodwill.hip.it.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.communication.progress.util.ProgressNotifier;
import com.bjgoodwill.hip.ds.org.api.service.DeptService;
import com.bjgoodwill.hip.ds.org.api.to.*;
import com.bjgoodwill.hip.excel.util.ExcelUtil;
import com.bjgoodwill.hip.it.to.DeptImpTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@RestController
@Tag(name = "行政科室管理服务", description = "行政科室服务类")
@SaCheckPermission("it:deptControllerManage")
@RequestMapping("/it/dept")
public class DeptManageController {
    @Autowired
    private DeptService deptService;
    private static final Logger log = LoggerFactory.getLogger(DeptManageController.class);

    @Operation(summary = "根据查询条件查询行政科室-分页查询", description = "根据查询条件查询本行政科室-分页查询")
    @ApiResponse(description = "返回行政科室集合", content = @Content(mediaType = "application/json", schema = @Schema(implementation = DeptTo.class)))
    @GetMapping(value = "/pages")
    public GridResultSet<DeptTo> getDeptByPage(DeptQto deptQto) {
        return deptService.getDeptByPage(deptQto);
    }

    @Operation(summary = "根据标识启用行政科室", description = "根据标识启用行政科室")
    @ApiResponse(description = "返回行政科室", content = @Content(mediaType = "application/json", schema = @Schema(implementation = DeptTo.class)))
    @PutMapping("/{code}/enable")
    DeptTo enableDept(@PathVariable("code") String code) {
        return deptService.enableDept(code);
    }

    @Operation(summary = "根据标识禁用行政科室", description = "根据标识禁用行政科室")
    @ApiResponse(description = "返回行政科室", content = @Content(mediaType = "application/json", schema = @Schema(implementation = DeptTo.class)))
    @PutMapping("/{code}/disable")
    DeptTo disableDept(@PathVariable("code") String code) {
        return deptService.disableDept(code);
    }

    @Operation(summary = "新增行政科室", description = "新增行政科室")
    @ApiResponse(description = "返回行政科室", content = @Content(mediaType = "application/json", schema = @Schema(implementation = DeptTo.class)))
    @PostMapping("")
    public DeptTo saveDept(@RequestBody @Valid DeptNto deptNto) {
        return deptService.saveDept(deptNto);
    }

    @Operation(summary = "根据标识更新行政科室", description = "根据标识更新行政科室")
    @ApiResponse(description = "返回行政科室", content = @Content(mediaType = "application/json", schema = @Schema(implementation = DeptTo.class)))
    @PutMapping("/{code}")
    public DeptTo updateStaffExternalDoctor(@PathVariable("code") String code, @RequestBody @Valid DeptEto deptEto) {
        return deptService.updateDept(code, deptEto);
    }

    @Operation(summary = "查询所有分院", description = "查询所有分院")
    @ApiResponse(description = "返回分院集合", content = @Content(mediaType = "application/json", schema = @Schema(implementation = HospitalBranchTo.class)))
    @GetMapping("/hospitalBranch")
    public List<HospitalBranchTo> getHospitalBranch() {
        return deptService.getAllHospitalBranch();
    }

    @GetMapping("/impTemplates")
    @Operation(summary = "下载科室的导入模板", description = "下载科室的导入模板")
    @ResponseBody
    public void downloadDeptTemplate(HttpServletResponse response) {
        ExcelUtil.exportImportTemplate(response, "科室", DeptImpTo.class);
    }

    @PostMapping("/import")
    @Operation(summary = "科室导入", description = "科室导入")
    @ResponseBody
    public boolean importDept(MultipartFile file) throws Exception {
        List<DeptImpTo> deptImpTos = ExcelUtil.getImportExcelData(file.getInputStream(), DeptImpTo.class);
        for (DeptImpTo deptImpTo : deptImpTos) {
            try {
                deptService.saveDept(impToToNto(deptImpTo));
            } catch (Exception e) {
                log.error("导入科室 " + deptImpTo.getName() + " 时新增错误！ ", e);
            }
        }
        return true;
    }

    private DeptNto impToToNto(DeptImpTo deptImpTo) {
        DeptNto deptNto = new DeptNto();
        deptNto.setCode(deptImpTo.getCode());
        deptNto.setName(deptImpTo.getName());
        deptNto.setHospitalBranchCode(deptImpTo.getHospitalBranchCode());
        deptNto.setTel(deptImpTo.getTel());
        deptNto.setPlace(deptImpTo.getPlace());
        deptNto.setIntroduction(deptImpTo.getIntroduction());
        return deptNto;
    }

//    @Autowired
//    private ThreadPoolTaskExecutor executor;
//
//    @Operation(summary = "测试进度条1", description = "测试进度条1")
//    @ApiResponse(description = "返回", content = @Content(mediaType = "application/json", schema = @Schema(implementation = String.class)))
//    @PostMapping("testProgress1")
//    public String testProgress1() {
//        // 1. 初始化通知器
////        ProgressNotifier notifier = new ProgressNotifier("task/progress");
//
//        // 2. 准备测试数据
//        List<String> items = List.of("1", "2", "3", "4", "5");
//
//        // 3. 异步处理集合
//        ProgressNotifier.processItemsAsync("task/progress", items, (item, callback) -> {
//            System.out.println("开始处理: " + item);
//            executor.execute(() -> {
//                try {
//                    Thread.sleep(500 + (long) (Math.random() * 1000));
//                    // 随机模拟失败
//                    if (Math.random() < 0.2) {
//                        callback.onError("随机失败");
//                    } else {
//                        System.out.println("完成处理: " + item);
//                        callback.onSuccess();
//                    }
//                } catch (InterruptedException e) {
//                    callback.onError("中断异常");
//                }
//            });
//        });
//        return "success";
//    }

    @Operation(summary = "测试进度条2", description = "测试进度条2")
    @ApiResponse(description = "返回", content = @Content(mediaType = "application/json", schema = @Schema(implementation = String.class)))
    @PostMapping("testProgress2")
    public String testProgress2() {
        // 1. 初始化通知器

        ProgressNotifier.sendProgress("task/progress", 20, "---");

        return "success";
    }

}
