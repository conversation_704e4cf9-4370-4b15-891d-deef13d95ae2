package com.bjgoodwill.hip.it.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.ds.param.api.enums.DevelopModuleEnum;
import com.bjgoodwill.hip.ds.param.api.enums.MaintainableCategoryEnum;
import com.bjgoodwill.hip.ds.param.api.service.ParameterService;
import com.bjgoodwill.hip.ds.param.api.to.ParameterEto;
import com.bjgoodwill.hip.ds.param.api.to.ParameterNto;
import com.bjgoodwill.hip.ds.param.api.to.ParameterQto;
import com.bjgoodwill.hip.ds.param.api.to.ParameterTo;
import com.bjgoodwill.hip.ds.term.api.to.DictTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@Tag(name = "门诊流程管理服务", description = "门诊流程管理服务类")
@SaCheckPermission("it:opdProcedureManage")
@RequestMapping("/it/opdProcedure")
public class OutPatientProcedureController {

    @Autowired
    private ParameterService parameterService;

    @Operation(summary = "根据查询条件查询参数-分页查询", description = "根据查询条件查询参数-分页查询")
    @ApiResponse(description = "返回参数集合", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = ParameterTo.class))))
    @GetMapping(value = "/pages")
    public GridResultSet<ParameterTo<?>> getOpdProcedureByPage(ParameterQto parameterQto) {
        parameterQto.setMaintainableCategory(MaintainableCategoryEnum.DEVELOPER.getCode());
        return parameterService.getParameterByPage(parameterQto);
    }


    @Operation(summary = "新增Json类型参数", description = "新增Json类型参数")
    @ApiResponse(description = "返回参数", content = @Content(mediaType = "application/json", schema = @Schema(implementation = ParameterTo.class)))
    @PostMapping("/Json")
    public ParameterTo<String> saveJsonOpdProcedure(@RequestBody @Valid ParameterNto<String> parameterNto) {
        parameterNto.setMaintainableCategory(MaintainableCategoryEnum.DEVELOPER.getCode());
        parameterNto.setModule(DevelopModuleEnum.OPD_PROCEDURE.getCode());
        return parameterService.saveJsonParameter(parameterNto);
    }


    @Operation(summary = "根据编码更新Json类型参数", description = "根据编码更新Json类型参数")
    @ApiResponse(description = "返回参数", content = @Content(mediaType = "application/json", schema = @Schema(implementation = ParameterTo.class)))
    @PutMapping("/Json/{code}")
    public ParameterTo<String> updateJsonOpdProcedure(@PathVariable("code") String code, @RequestBody @Valid ParameterEto<String> parameterEto) {
        return parameterService.updateJsonParameter(code, parameterEto);
    }

    @Operation(summary = "根据编码更新Json类型参数值", description = "根据编码更新Json类型参数值")
    @ApiResponse(description = "返回参数", content = @Content(mediaType = "application/json", schema = @Schema(implementation = ParameterTo.class)))
    @PutMapping("/Json/value/{code}")
    public ParameterTo<String> updateJsonOpdProcedureValue(@PathVariable("code") String code, @RequestParam("value") String value) {
        return parameterService.updateJsonParameterValue(code, value);
    }

    @Operation(summary = "根据标识启用参数", description = "根据标识启用参数")
    @ApiResponse(description = "返回参数", content = @Content(mediaType = "application/json", schema = @Schema(implementation = ParameterTo.class)))
    @PutMapping("/{code}/enable")
    public ParameterTo<?> enableOpdProcedure(@PathVariable("code") String code) {
        return parameterService.enableParameter(code);
    }

    @Operation(summary = "根据标识禁用参数", description = "根据标识禁用参数")
    @ApiResponse(description = "返回参数", content = @Content(mediaType = "application/json", schema = @Schema(implementation = ParameterTo.class)))
    @PutMapping("/{code}/disable")
    public ParameterTo<?> disableOpdProcedure(@PathVariable("code") String code) {
        return parameterService.disableParameter(code);
    }

    @Operation(summary = "根据标识查询Json类型参数值", description = "根据标识查询Json类型参数值")
    @ApiResponse(description = "返回参数值", content = @Content(mediaType = "application/json", schema = @Schema(implementation = String.class)))
    @GetMapping("/Json/value/{code}")
    public String getJsonOpdProcedureValue(@PathVariable("code") String code) {
        return parameterService.getJsonParameterValue(code);
    }

}
