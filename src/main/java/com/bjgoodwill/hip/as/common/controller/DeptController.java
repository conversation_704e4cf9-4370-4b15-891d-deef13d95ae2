package com.bjgoodwill.hip.as.common.controller;

import com.bjgoodwill.hip.ds.org.api.service.DeptService;
import com.bjgoodwill.hip.ds.org.api.service.WorkGroupService;
import com.bjgoodwill.hip.ds.org.api.to.*;
import com.bjgoodwill.hip.security.util.HIPCurrentOrgUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.collections.CollectionUtils;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Author: z<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/8/20 11:42
 * @PROJECT: hip-common-ac
 */
@Tag(name = "科室服务", description = "科室应用服务类")
@RestController("com.bjgoodwill.hip.as.common.controller.DeptController")
@RequestMapping(value = "/common/dept", produces = "application/json; charset=utf-8")
public class DeptController {

    @Autowired
    private DeptService deptService;

    @Autowired
    private WorkGroupService workGroupService;

    @Operation(summary = "查询所有科室", description = "查询所有科室")
    @ApiResponse(description = "获取所有科室", content = @Content(mediaType = "application/json", schema = @Schema(implementation = DeptTo.class)))
    @PostMapping(value = "/allDept")
    public List<DeptTo> getAllDept() {
        return deptService.getAllDept();
    }

    @Operation(summary = "查询所有已启用科室", description = "查询所有已启用科室")
    @ApiResponse(description = "获取所有已启用科室", content = @Content(mediaType = "application/json", schema = @Schema(implementation = DeptTo.class)))
    @PostMapping(value = "/allDept/enable")
    public List<DeptTo> getAllDeptEnable() {
        List<DeptTo> allDept = deptService.getAllDept();
        if (CollectionUtils.isNotEmpty(allDept)) {
            return allDept.stream().filter(a -> a.getEnable() != null && a.getEnable().equals(true)).toList();
        } else {
            return List.of();
        }
    }

    @Operation(summary = "查询所有启用的住院护理组", description = "查询所有启用的住院护理组")
    @ApiResponse(description = "返回所有启用的住院护理组信息", content = @Content(mediaType = "application/json", schema = @Schema(implementation = WorkGroupInpatientNursingTo.class)))
    @GetMapping(value = "/getEnableWorkGroup")
    public List<WorkGroupInpatientNursingTo> getEnableWorkGroup() {
        return workGroupService.getInpatientNursing(new WorkGroupInpatientNursingQto());
    }

    @Operation(summary = "查询所有行政科室", description = "查询行政科室数据集合")
    @ApiResponse(description = "返回行政科室集合", content = @Content(mediaType = "application/json", schema = @Schema(implementation = WorkGroupInpatientDeptTo.class)))
    @PostMapping("/workGroup")
    public List<WorkGroupInpatientDeptTo> getDeptWorkGroups() {
        return workGroupService.getInpatientDept(new WorkGroupInpatientDeptQto());
    }

    @Operation(summary = "查询行政科室下的工作组", description = "查询行政科室下的工作组列表")
    @ApiResponse(description = "返回行政科室对应的工作组集合", content = @Content(mediaType = "application/json", schema = @Schema(implementation = WorkGroupInpatientNursingTo.class)))
    @GetMapping("/workGroup/{deptCode}")
    public List<WorkGroupInpatientNursingTo> getNurseWorkGroups(@PathVariable("deptCode") String deptCode) {
        WorkGroupInpatientNursingQto workGroupInpatientNursingQto = new WorkGroupInpatientNursingQto();
        workGroupInpatientNursingQto.setDeptCode(deptCode);
        return workGroupService.getInpatientNursing(workGroupInpatientNursingQto);
    }

    @Operation(summary = "根据工作组编码查询工作组", description = "根据工作组编码查询工作组")
    @ApiResponse(description = "返回工作组信息", content = @Content(mediaType = "application/json", schema = @Schema(implementation = WorkGroupTo.class)))
    @GetMapping(value = "/{id}")
    public WorkGroupTo getWorkGroupById(@PathVariable("id") String id) {
        return workGroupService.getWorkGroup(id);
    }

    @Operation(summary = "根据查询条件查询工作组", description = "根据查询条件查询工作组")
    @ApiResponse(description = "返回工作组信息", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = WorkGroupTo.class))))
    @PostMapping(value = "/workGroup/byCondition")
    public List<WorkGroupTo> getWorkGroupsByCondition(@ParameterObject @SpringQueryMap WorkGroupQto workGroupQto) {
        return workGroupService.getWorkGroups(workGroupQto);
    }

    @Operation(summary = "查询所有住院医生工作组", description = "查询所有住院医生工作组")
    @ApiResponse(description = "返回住院工作组信息", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = WorkGroupInpatientDeptTo.class))))
    @PostMapping(value = "/workGroup/ipd/getDoctor")
    public List<WorkGroupInpatientDeptTo> getDoctorWorkGroup() {
        String areaCode = HIPCurrentOrgUtils.getCurrentOrgInfo().getHospitalAreaCode();

        WorkGroupInpatientDeptQto qto = new WorkGroupInpatientDeptQto();
        qto.setHospitalAreaCode(areaCode);
        return workGroupService.getInpatientDept(qto);
    }

    @Operation(summary = "查询所有门诊出诊科室工作组", description = "查询所有门诊出诊科室工作组")
    @ApiResponse(description = "返回门诊工作组信息", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = WorkGroupOutpatientDeptTo.class))))
    @PostMapping(value = "/workGroup/opd/getDoctor")
    public List<WorkGroupOutpatientDeptTo> getOpdDoctorWorkGroup() {
        return workGroupService.getOutpatientDeptByHosArea(HIPCurrentOrgUtils.getCurrentOrgInfo().getHospitalAreaCode());
    }

    @Operation(summary = "查询检查执行科室工作组", description = "")
    @ApiResponse(description = "", content = @Content(mediaType = "application/json", schema = @Schema(implementation = WorkGroupPhysicalExaminationTo.class)))
    @GetMapping(value = "/workgroup/dgimg")
    public List<WorkGroupPhysicalExaminationTo> getWorkGroupPhysicalExaminations() {
        String areaCode = HIPCurrentOrgUtils.getCurrentOrgInfo().getHospitalAreaCode();

        WorkGroupPhysicalExaminationQto qto = new WorkGroupPhysicalExaminationQto();
        qto.setHospitalAreaCode(areaCode);
        return workGroupService.getPhysicalExaminations(qto);
    }

    @Operation(summary = "查询检验执行科室工作组", description = "")
    @ApiResponse(description = "", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = WorkGroupMedicalLaboratoryTo.class))))
    @GetMapping(value = "/workgroup/spcobs")
    public List<WorkGroupMedicalLaboratoryTo> getSpcobsWorkGroup() {
        String areaCode = HIPCurrentOrgUtils.getCurrentOrgInfo().getHospitalAreaCode();

        WorkGroupMedicalLaboratoryQto qto = new WorkGroupMedicalLaboratoryQto();
        qto.setHospitalAreaCode(areaCode);
        return workGroupService.getMedicalLaboratory(qto);
    }

    @Operation(summary = "查询诊疗工作组", description = "")
    @ApiResponse(description = "", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = WorkGroupInpatientDeptTo.class))))
    @GetMapping(value = "/workgroup/diagnosisAndTreatment")
    public List<WorkGroupInpatientDeptTo> getDiagnosisAndTreatmentDept() {
        String areaCode = HIPCurrentOrgUtils.getCurrentOrgInfo().getHospitalAreaCode();

        WorkGroupInpatientDeptQto qto = new WorkGroupInpatientDeptQto();
        qto.setHospitalAreaCode(areaCode);
        return workGroupService.getInpatientDept(qto);
    }

    @Operation(summary = "查询护理工作组", description = "")
    @ApiResponse(description = "", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = WorkGroupInpatientNursingTo.class))))
    @GetMapping(value = "/workgroup/nursing")
    public List<WorkGroupInpatientNursingTo> getNursingWorkGroup() {
        String areaCode = HIPCurrentOrgUtils.getCurrentOrgInfo().getHospitalAreaCode();

        WorkGroupInpatientNursingQto qto = new WorkGroupInpatientNursingQto();
        qto.setHospitalAreaCode(areaCode);
        return workGroupService.getInpatientNursing(qto);
    }

    @Operation(summary = "查询处置工作组", description = "")
    @ApiResponse(description = "", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = WorkGroupDisposalNursingTo.class))))
    @GetMapping(value = "/workgroup/dispose")
    public List<WorkGroupDisposalNursingTo> getDisposeWorkGroup() {
        String areaCode = HIPCurrentOrgUtils.getCurrentOrgInfo().getHospitalAreaCode();

        WorkGroupDisposalNursingQto qto = new WorkGroupDisposalNursingQto();
        qto.setHospitalAreaCode(areaCode);
        return workGroupService.getDisposalNursing(qto);
    }

}
