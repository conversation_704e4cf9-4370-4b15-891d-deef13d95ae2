package com.bjgoodwill.hip.as.common.controller;

import com.bjgoodwill.hip.as.to.DrugGoodsOtherAsTo;
import com.bjgoodwill.hip.common.bean.EnumTo;
import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.common.util.HIPBeanUtil;
import com.bjgoodwill.hip.ds.drug.goods.enmus.*;
import com.bjgoodwill.hip.ds.drug.goods.service.*;
import com.bjgoodwill.hip.ds.drug.goods.to.*;
import com.bjgoodwill.hip.ds.term.api.service.DictElementService;
import com.bjgoodwill.hip.ds.term.api.to.DictElementTo;
import com.bjgoodwill.hip.ds.term.api.to.TreeItem;
import com.bjgoodwill.hip.file.util.HIPFileUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController("com.bjgoodwill.hip.as.common.controller.DrugGoodsController")
@Tag(name = "药品字典通用服务", description = "药品字典通用服务类")
@RequestMapping("/common/drugGoods")
public class DrugGoodsController {

    @Autowired
    private DrugGoodsService drugGoodsService;

    @Autowired
    private DrugGoodsWestService drugGoodsWestService;

    @Autowired
    private DrugGoodsHerbsService drugGoodsHerbsService;

    @Autowired
    private DrugGoodsFlagService drugGoodsFlagService;

    @Autowired
    private DrugGoodsOtherService drugGoodsOtherService;

    @Autowired
    private DictElementService dictElementService;

    @Autowired
    private DrugPriceMarkupService drugPriceMarkupService;

    @Operation(summary = "根据编码、名称、拼音码、助记码等模糊查询药品信息", description = "根据编码、名称、拼音码、助记码等模糊查询药品信息")
    @ApiResponse(description = "返回药品信息", content = @Content(mediaType = "application/json", schema = @Schema(implementation = DrugGoodsTo.class)))
    @GetMapping("/getByName")
    public List<DrugGoodsTo> getDrugGoodsByName(@RequestParam("name") String name) {
        return drugGoodsService.getDrugGoodsByName(name);
    }


    @Operation(summary = "根据编码查询药品信息", description = "根据编码查询药品信息")
    @ApiResponse(description = "返回本药品信息", content = @Content(mediaType = "application/json", schema = @Schema(implementation = DrugGoodsTo.class)))
    @GetMapping("/{code}")
    public DrugGoodsTo getDrugGoods(@PathVariable("code") String drugGoodsCode) {
        return drugGoodsService.getDrugGoodsById(drugGoodsCode);
    }

    @Operation(summary = "根据编码查询西药药品信息", description = "根据编码查询西药药品信息")
    @ApiResponse(description = "返回本药品信息", content = @Content(mediaType = "application/json", schema = @Schema(implementation = DrugGoodsWestTo.class)))
    @GetMapping("/{code}/west")
    public DrugGoodsWestTo getDrugGoodsWest(@PathVariable("code") String drugGoodsCode) {
        return drugGoodsWestService.getDrugGoodsWestById(drugGoodsCode);
    }

    @Operation(summary = "根据编码查询草药药品信息", description = "根据编码查询草药药品信息")
    @ApiResponse(description = "返回本药品信息", content = @Content(mediaType = "application/json", schema = @Schema(implementation = DrugGoodsHerbsTo.class)))
    @GetMapping("/{code}/herbs")
    public DrugGoodsHerbsTo getDrugGoodsHerbs(@PathVariable("code") String drugGoodsCode) {
        return drugGoodsHerbsService.getDrugGoodsHerbsById(drugGoodsCode);
    }

    @Operation(summary = "根据编码查询药品标识信息", description = "根据编码查询药品标识信息")
    @ApiResponse(description = "返回本药品标识信息", content = @Content(mediaType = "application/json", schema = @Schema(implementation = DrugGoodsFlagTo.class)))
    @GetMapping("/{code}/flag")
    public DrugGoodsFlagTo getDrugGoodsFlag(@PathVariable("code") String drugGoodsCode) {
        return drugGoodsFlagService.getDrugGoodsFlagById(drugGoodsCode);
    }

    @Operation(summary = "根据编码查询药品其他信息", description = "根据编码查询药品其他信息")
    @ApiResponse(description = "返回本药品信息", content = @Content(mediaType = "application/json", schema = @Schema(implementation = DrugGoodsOtherTo.class)))
    @GetMapping("/{code}/other")
    public DrugGoodsOtherTo getDrugGoodsOther(@PathVariable("code") String drugGoodsCode) {
        DrugGoodsOtherTo drugGoodsOtherTo = drugGoodsOtherService.getDrugGoodsOtherById(drugGoodsCode);
        if (drugGoodsOtherTo == null) {
            return null;
        }
        DrugGoodsOtherAsTo drugGoodsOtherAsTo = HIPBeanUtil.copy(drugGoodsOtherTo, DrugGoodsOtherAsTo.class);
        drugGoodsOtherAsTo.setDrugImageUrl(HIPFileUtil.getStaticResourceFileUrl(drugGoodsOtherTo.getDrugImage()));
        return drugGoodsOtherAsTo;
    }

    @Operation(summary = "根据查询条件查询药品信息-分页查询", description = "根据查询条件查询药品信息-分页查询")
    @ApiResponse(description = "返回药品信息集合", content = @Content(mediaType = "application/json", schema = @Schema(implementation = DrugGoodsTo.class)))
    @GetMapping(value = "/pages")
    public GridResultSet<DrugGoodsTo> getDrugGoodsByPage(@ParameterObject @SpringQueryMap DrugGoodsQto drugGoodsQto) {
        return drugGoodsService.getDrugGoodsPage(drugGoodsQto);
    }

    @Operation(summary = "获取药品状态数据源", description = "获取药品状态数据源")
    @ApiResponse(description = "返回药品状态集合", content = @Content(mediaType = "application/json", schema = @Schema(implementation = List.class)))
    @GetMapping(value = "/drugStatus")
    public List<EnumTo<String>> getDrugStatus() {
        //获取药品状态
        return DrugStatusEnum.getList();
    }

    @Operation(summary = "获取西药药品类型数据源", description = "获取西药药品类型数据源")
    @ApiResponse(description = "返回西药药品类型集合", content = @Content(mediaType = "application/json", schema = @Schema(implementation = List.class)))
    @GetMapping(value = "/westDrugType")
    public List<EnumTo<String>> getWestDrugType() {
        //获取西药药品类型
        return DrugWestTypeEnum.getList();
    }

    @Operation(summary = "获取药品类型数据源", description = "获取药品类型数据源")
    @ApiResponse(description = "返回药品类型集合", content = @Content(mediaType = "application/json", schema = @Schema(implementation = List.class)))
    @GetMapping(value = "/drugType")
    public List<EnumTo<String>> getDrugType() {
        //获取药品类型
        return DrugTypeEnum.getList();
    }

    @Operation(summary = "获取药品拆分类型数据源", description = "获取药品拆分类型数据源")
    @ApiResponse(description = "返回药品拆分类型集合", content = @Content(mediaType = "application/json", schema = @Schema(implementation = List.class)))
    @GetMapping(value = "/drugSplitPackage")
    public List<EnumTo<String>> getDrugSplitPackage() {
        //获取药品拆分类型
        return DrugSplitPackageEnum.getList();
    }

    @Operation(summary = "获取药理归类字典树型结构", description = "获取药理归类字典树型结构")
    @ApiResponse(description = "返回药理归类字典项型集合", content = @Content(mediaType = "application/json", schema = @Schema(implementation = List.class)))
    @GetMapping(value = "actionType")
    public List<TreeItem<DictElementTo>> getDrugActionType() {
        //获取字典项集合
        return dictElementService.getCustomDictElementTreeByParent("PharmacologyClass");
    }

    @Operation(summary = "获取药理归类字典树型结构（简版）", description = "获取药理归类字典树型结构（简版）")
    @ApiResponse(description = "返回药理归类字典项型集合", content = @Content(mediaType = "application/json", schema = @Schema(implementation = List.class)))
    @GetMapping(value = "simpleActionType")
    public List<TreeItem<DictElementTo>> getSimpleDrugActionType() {
        //获取字典项集合
        return dictElementService.getCustomDictElementTreeByParent("PharmacologyClass");
    }

    @Operation(summary = "根据药品类型集合分页查询药品信息", description = "根据药品类型集合分页查询药品信息")
    @ApiResponse(description = "返回本药品信息", content = @Content(mediaType = "application/json", schema = @Schema(implementation = DrugGoodsTo.class)))
    @GetMapping("/getByDrugTypes")
    public GridResultSet<DrugGoodsTo> getDrugGoodsPageByDrugTypes(@ParameterObject @SpringQueryMap DrugGoodsQto drugGoodsQto) {
        return drugGoodsService.getDrugGoodsPageByDrugTypes(drugGoodsQto);
    }

    @Operation(summary = "获取药品毒理属性数据源", description = "获取药品毒理属性数据源")
    @ApiResponse(description = "返回药品毒理属性集合", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = DictElementTo.class))))
    @GetMapping(value = "/drugToxic")
    public List<DictElementTo> getDrugToxicProperty() {
        return dictElementService.getCustomDictElement("DrugToxiProperty");
    }


    @Operation(summary = "获取药品加价规则数据源", description = "获取药品加价规则数据源")
    @ApiResponse(description = "返回药品医保类型集合", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = DrugPriceMarkupTo.class))))
    @GetMapping(value = "/drugPriceMarkups")
    public List<DrugPriceMarkupTo> getDrugPriceMarkups() {
        return drugPriceMarkupService.getDrugPriceMarkups(new DrugPriceMarkupQto());
    }

    @Operation(summary = "查询主要字段的全部药品字典信息", description = "查询主要字段的全部药品字典信息")
    @ApiResponse(description = "返回药品字典", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = DrugGoodsSimpleTo.class))))
    @GetMapping(value = "/simpleDrugs")
    public List<DrugGoodsSimpleTo> getALLDrugGoodsSimple() {
        return drugGoodsService.getALLDrugGoodsSimple();
    }
}
