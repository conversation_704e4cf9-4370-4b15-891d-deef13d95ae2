package com.bjgoodwill.hip.as.common.controller.temp;

import com.bjgoodwill.hip.as.common.service.temp.CisCdrugOrderTempMaintainService;
import com.bjgoodwill.hip.as.to.temp.*;
import com.bjgoodwill.hip.ds.cis.cds.enmus.TempRangeEnum;
import com.bjgoodwill.hip.ds.econ.price.to.EconServicePriceTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR> liangy<PERSON><PERSON>
 * @description :
 * @createDate : 2025/3/5 11:49
 */
@RestController
@Tag(name = "草药组套维护应用服务", description = "")
@RequestMapping("/common/temp/cdrug/maintain")
public class CisCdruOrderTempIpdMaintainController {

    @Resource
    private CisCdrugOrderTempMaintainService cisCdrugOrderTempMaintainService;

    @Operation(summary = "协定处方固定费用查询")
    @ApiResponse(description = "协定处方固定费用查询", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = EconServicePriceTo.class))))
    @GetMapping(value = "/prescription/price")
    public List<EconServicePriceTo> getCdrugPrescriptionPriceList(@RequestParam("text") String inputText) {
        return cisCdrugOrderTempMaintainService.getCdrugPrescriptionPrice(inputText);
    }

    @Operation(summary = "查询组套树")
    @ApiResponse(description = "组套树列表", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = CisOrderTempAsTo.class))))
    @GetMapping("/tree")
    public List<CisOrderTempAsTo> getCisOrderTempAsToTree() {
        return cisCdrugOrderTempMaintainService.getCdrugTempTree();
    }

    @Operation(summary = "新建组套信息")
    @ApiResponse(description = "新建组套信息", content = @Content(mediaType = "application/json"))
    @PostMapping("/save")
    public void createCisOrderTemp(@RequestBody @Valid CisOrderTempAsNto cisOrderTempAsNto) {
        cisCdrugOrderTempMaintainService.createCdrugCisOrderTemp(cisOrderTempAsNto);
    }

    @Operation(summary = "修改组套信息")
    @ApiResponse(description = "修改组套信息", content = @Content(mediaType = "application/json"))
    @PutMapping("/update")
    public void updateCisOrderTemp(@RequestBody @Valid CisOrderTempAsEto cisOrderTempAsEto) {
        cisCdrugOrderTempMaintainService.updateCdrugCisOrderTemp(cisOrderTempAsEto);
    }

    @Operation(summary = "删除组套信息")
    @ApiResponse(description = "删除组套信息", content = @Content(mediaType = "application/json"))
    @DeleteMapping("/{temp-id}/{temp-range}")
    public void deleteCisOrderTemp(@PathVariable("temp-id") String tempId, @PathVariable("temp-range") TempRangeEnum tempRange) {
        cisCdrugOrderTempMaintainService.deleteCdrugCisOrderTemp(tempId, tempRange);
    }

    @Operation(summary = "存为各类别草药组套信息")
    @ApiResponse(description = "存为各类别草药组套信息", content = @Content(mediaType = "application/json"))
    @PostMapping("/with/details")
    public void createCisOrderTempWithDetails(@RequestBody @Valid CisCdrugOrderTempWithDetailNto cisCdrugOrderTempWithDetailNto) {
        cisCdrugOrderTempMaintainService.createCDrugTempAndDetail(cisCdrugOrderTempWithDetailNto);
    }

    @Operation(summary = "更新草药组套信息，带明细")
    @ApiResponse(description = "更新草药组套信息，带明细", content = @Content(mediaType = "application/json"))
    @PutMapping("/with/details/update")
    public void updateCisOrderTempWithDetails(@RequestBody @Valid CisCdrugOrderTempWithDetailNto cisCdrugOrderTempWithDetailNto) {
        cisCdrugOrderTempMaintainService.updateCDrugTempAndDetail(cisCdrugOrderTempWithDetailNto);
    }

    @Operation(summary = "查询草药组套信息，带明细")
    @ApiResponse(description = "查询草药组套信息，带明细", content = @Content(mediaType = "application/json", schema = @Schema(implementation = CisOrderTempAsTo.class)))
    @GetMapping("/get/temp/detail/{temp-id}")
    public CisOrderTempAsTo getCdrugTempWithDetail(@PathVariable("temp-id") String tempId) {
        return cisCdrugOrderTempMaintainService.getCisOrderTempAsTo(tempId);
    }

    @Operation(summary = "将草药方存为个人组套信息")
    @ApiResponse(description = "将草药方存为个人组套信息", content = @Content(mediaType = "application/json"))
    @PostMapping("/cdrugsave")
    public void saveCdrugTempByOrder(@RequestBody @Valid CisIpdDocOrderAsToTempNto cisIpdDocOrderAsNto) {
        cisCdrugOrderTempMaintainService.saveCdrugCisOrderTemp(cisIpdDocOrderAsNto);
    }

    @Operation(summary = "更新组套信息")
    @ApiResponse(description = "更新组套信息", content = @Content(mediaType = "application/json"))
    @PutMapping("/cdrugupdate")
    public void updateCdrugTempByOrder(@RequestBody @Valid CisIpdDocOrderAsToTempNto cisIpdDocOrderAsNto) {
        cisCdrugOrderTempMaintainService.updateCdrugCisOrderTemp(cisIpdDocOrderAsNto);
    }
}
