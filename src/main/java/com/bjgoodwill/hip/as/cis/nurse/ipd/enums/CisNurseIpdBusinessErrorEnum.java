package com.bjgoodwill.hip.as.cis.nurse.ipd.enums;

import com.bjgoodwill.hip.common.exception.BusinessErrorEnum;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/10/11 9:39
 * @PROJECT: hip-ac
 */
public enum CisNurseIpdBusinessErrorEnum implements BusinessErrorEnum {

    CIS_NURSE_IPD_0001("[%s]获取失败！"),
    CIS_NURSE_IPD_0002("[%s]不能为空！"),
    CIS_NURSE_IPD_0003("[%s]不为0，不能作废！"),
    CIS_NURSE_IPD_0004("[%s]大于0，不能作废！"),
    ;

    private final String message;

    CisNurseIpdBusinessErrorEnum(String message) {
        this.message = message;
    }

    @Override
    public String getCode() {
        return this.name();
    }

    @Override
    public String getMessage() {
        return message;
    }
}
