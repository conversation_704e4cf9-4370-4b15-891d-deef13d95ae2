package com.bjgoodwill.hip.as.cis.nurse.ipd.service.impl;

import com.bjgoodwill.hip.as.cis.nurse.ipd.service.DrugBackSendAsService;
import com.bjgoodwill.hip.as.cis.nurse.ipd.service.DrugReceiptSendAsService;
import com.bjgoodwill.hip.as.cis.nurse.ipd.to.*;
import com.bjgoodwill.hip.business.util.drug.enums.DrugIpdDataStatusEnum;
import com.bjgoodwill.hip.common.bean.LoginInfo;
import com.bjgoodwill.hip.common.util.HIPBeanUtil;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyTo;
import com.bjgoodwill.hip.ds.cis.apply.charge.to.CisApplyChargeTo;
import com.bjgoodwill.hip.ds.cis.apply.drug.service.CisCDrugApplyService;
import com.bjgoodwill.hip.ds.cis.apply.drug.service.CisEDrugApplyService;
import com.bjgoodwill.hip.ds.cis.apply.drug.to.CisCDrugApplyTo;
import com.bjgoodwill.hip.ds.cis.apply.drug.to.CisDrugApplyDetailTo;
import com.bjgoodwill.hip.ds.cis.apply.drug.to.CisEDrugApplyTo;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.service.CisOrderExecPlanService;
import com.bjgoodwill.hip.ds.cis.cpoe.order.service.CisIpdCpoeService;
import com.bjgoodwill.hip.ds.cis.cpoe.order.to.CisIpdOrderTo;
import com.bjgoodwill.hip.ds.drug.ipd.apply.enmus.DrugIpdDataTypeEnum;
import com.bjgoodwill.hip.ds.drug.ipd.apply.enmus.DrugIpdInoutTypeEnum;
import com.bjgoodwill.hip.ds.drug.ipd.apply.service.DrugIpdApplyService;
import com.bjgoodwill.hip.ds.drug.ipd.apply.to.DrugIpdApplyNurseQto;
import com.bjgoodwill.hip.ds.drug.ipd.apply.to.DrugIpdApplyTo;
import com.bjgoodwill.hip.ds.econ.ipd.bill.amount.service.EconIpdAmountService;
import com.bjgoodwill.hip.ds.econ.ipd.bill.amount.to.EconGuaranteLevelQto;
import com.bjgoodwill.hip.ds.econ.ipd.bill.amount.to.EconIpdAmountTo;
import com.bjgoodwill.hip.ds.econ.ipd.bill.service.EconIpdApplyService;
import com.bjgoodwill.hip.ds.econ.ipd.bill.service.EconIpdBillService;
import com.bjgoodwill.hip.ds.econ.ipd.bill.to.EconIpdBillCancelSendDrugApplyTo;
import com.bjgoodwill.hip.ds.param.api.service.ParameterService;
import com.bjgoodwill.hip.ds.param.api.to.ParameterTo;
import com.bjgoodwill.hip.ds.pat.in.hospital.inpatient.service.PatIpdInpatientService;
import com.bjgoodwill.hip.ds.pat.in.hospital.inpatient.to.PatIpdInpatientExtTo;
import com.bjgoodwill.hip.ds.pat.in.hospital.inpatient.to.PatIpdInpatientTo;
import com.bjgoodwill.hip.ds.term.api.service.DictElementService;
import com.bjgoodwill.hip.ds.term.api.to.DictDto;
import com.bjgoodwill.hip.ds.term.api.to.DictElementTo;
import com.bjgoodwill.hip.enums.DictParameterEnum;
import com.bjgoodwill.hip.security.bean.CurrentOrgInfo;
import com.bjgoodwill.hip.security.util.HIPCurrentOrgUtils;
import com.bjgoodwill.hip.security.util.HIPSecurityUtils;
import io.micrometer.common.util.StringUtils;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.seata.spring.annotation.GlobalTransactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/2/26 9:20
 * @ClassName: DrugBackSendAsServiceImpl
 * @Description: 退发药查询应用服务
 */
@Service("com.bjgoodwill.hip.as.cis.nurse.ipd.service.DrugBackSendAsService")
public class DrugBackSendAsServiceImpl implements DrugBackSendAsService {

    @Autowired
    private DrugIpdApplyService drugIpdApplyService;

    @Autowired
    private PatIpdInpatientService patIpdInpatientService;

    @Autowired
    private EconIpdAmountService econIpdAmountService;

    @Autowired
    private ParameterService parameterService;

    @Autowired
    private DrugReceiptSendAsService drugReceiptSendAsService;

    @Autowired
    private DictElementService dictElementService;

    @Resource
    private CisIpdCpoeService cisIpdCpoeService;

    @Autowired
    private CisEDrugApplyService cisEDrugApplyService;

    @Autowired
    private CisOrderExecPlanService cisOrderExecPlanService;

    @Autowired
    private EconIpdBillService econIpdBillService;

    @Autowired
    private CisCDrugApplyService cisCDrugApplyService;
    @Autowired
    private EconIpdApplyService econIpdApplyService;

    @Override
    public List<DrugIpdPatAsTo> getDrugIpdApply(DrugIpdApplyNurseQto drugIpdApplyNurseQto) {
        List<DrugIpdPatAsTo> drugIpdPatAsTos = new ArrayList<>();
        List<DrugIpdApplyTo> drugIpdAppliesForNurse = drugIpdApplyService.getDrugIpdAppliesForNurse(drugIpdApplyNurseQto);

        //根据患者进行分组
        Map<String, List<DrugIpdApplyTo>> visitCodeCollect = drugIpdAppliesForNurse.stream().collect(Collectors.groupingBy(DrugIpdApplyTo::getVisitCode));
        List<String> visitCodeList = visitCodeCollect.keySet().stream().toList();
        //查询所有患者信息
        List<PatIpdInpatientTo> patIpdInpatientTos = patIpdInpatientService.getConsultationsPat(visitCodeList);
        Map<String,PatIpdInpatientTo> patMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(patIpdInpatientTos)) {
            patIpdInpatientTos.forEach(p -> {
                patMap.put(p.getVisitCode(), p);
            });
        }
        //查询所有患者余额信息
        List<EconIpdAmountTo> econIpdAmountTos = econIpdAmountService.getEconIpdAmountByIdList(visitCodeCollect.keySet().stream().toList());
        Map<String,EconIpdAmountTo> econMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(econIpdAmountTos)) {
            econIpdAmountTos.forEach(p -> {
                econMap.put(p.getVisitCode(), p);
            });
        }

        //获取字典信息
        List<String> dictCodes = new ArrayList<>();
        dictCodes.add("NursingLevel");
        dictCodes.add("DiseaseDisplay");
        List<DictDto> nursingLevels = dictElementService.getCustomDictElementByDictCodes(dictCodes);

        //拼装医嘱和患者信息
        visitCodeCollect.forEach((k, v) -> {
            //患者信息
            PatIpdInpatientTo patIpdInpatientTo = patMap.get(k);
            DrugIpdPatAsTo drugIpdPatAsTo = HIPBeanUtil.copy(patIpdInpatientTo, DrugIpdPatAsTo.class);
            drugIpdPatAsTo.setFeeType(patIpdInpatientTo.getFeeType());
            drugIpdPatAsTo.setFeeTypeName(patIpdInpatientTo.getFeeType() == null ? null : "08".equals(patIpdInpatientTo.getFeeType()) ? "自费" : "医保");
            drugIpdPatAsTo.setDeptNurseCode(patIpdInpatientTo.getDeptNurseCode());
            drugIpdPatAsTo.setDeptNurseName(patIpdInpatientTo.getDeptNurseName());
            PatIpdInpatientExtTo patIpdInpatientExtTo = patIpdInpatientTo.getPatIpdInpatientExtTo();
            if (patIpdInpatientExtTo != null) {
                List<DictDto> nursingLevel = nursingLevels.stream().filter(n -> n.getDictCode().equals("NursingLevel")).toList();
                if (nursingLevel != null && nursingLevel.size() > 0) {
                    List<DictElementTo> dictElementTos = nursingLevel.get(0).getDictElementToList().stream().filter(d -> d.getContrastRelation().equals(patIpdInpatientExtTo.getRoutineCare())).toList();
                    if (CollectionUtils.isNotEmpty(dictElementTos)) {
                        drugIpdPatAsTo.setNursingLevel(dictElementTos.get(0).getElementCode());
                        drugIpdPatAsTo.setNursingLevelName(dictElementTos.get(0).getElementName());
                    }
                }
                List<DictDto> diseaseDisplay = nursingLevels.stream().filter(n -> n.getDictCode().equals("DiseaseDisplay")).toList();
                if (diseaseDisplay != null && diseaseDisplay.size() > 0) {
                    List<DictElementTo> dictElementTos = diseaseDisplay.get(0).getDictElementToList().stream().filter(d -> d.getElementCode().equals(patIpdInpatientExtTo.getCriticalCarePatient())).toList();
                    if (CollectionUtils.isNotEmpty(dictElementTos)) {
                        drugIpdPatAsTo.setCondition(dictElementTos.get(0).getElementName());
                    }
                }
            }
            //住院时间
            LocalDateTime inTime = patIpdInpatientTo.getInTime();
            //当前时间
            LocalDateTime currentDate = LocalDateTime.now();
            //相差小时数
            Duration duration = Duration.between(inTime, currentDate);
            long hours = duration.toHours();
            boolean isSameDate = currentDate.toLocalDate().isEqual(inTime.toLocalDate());
            //判断是否大于12小时，如果大于12小时小于24小时并且不跨天为新入院
            drugIpdPatAsTo.setNewPatFlag(hours <= 12 || hours <= 24 && isSameDate);
            //担保金、余额信息
            EconGuaranteLevelQto econGuaranteLevelQto = new EconGuaranteLevelQto();
            econGuaranteLevelQto.setVisitCode(patIpdInpatientTo.getVisitCode());
            econGuaranteLevelQto.setFeeType(patIpdInpatientTo.getFeeType());
            econGuaranteLevelQto.setDeptNurse(patIpdInpatientTo.getDeptNurseCode());
            EconIpdAmountTo econIpdAmountTo = econIpdAmountService.getEconIpdAmountAndGuarante(econGuaranteLevelQto);
            drugIpdPatAsTo.setBalance(econIpdAmountTo != null?econIpdAmountTo.getBalance():null);//余额
            drugIpdPatAsTo.setGuaranteeAmount(econIpdAmountTo != null?econIpdAmountTo.getGuaranteAmount():null);//担保金

            List<DrugIpdApplyResqAsTo> drugIpdApplyResqAsTos = new ArrayList<>();
            Map<String, List<DrugIpdApplyTo>> drugIpdApplyToList = v.stream().collect(Collectors.groupingBy(DrugIpdApplyTo::getOrderId));
            drugIpdApplyToList.forEach((key, value) -> {
                //查询医嘱信息表
                CisIpdOrderTo cisIpdOrderTo = cisIpdCpoeService.getCisIpdOrderById(key);
                //查询申请单信息
                CisEDrugApplyTo cisEDrugApplyTo = cisEDrugApplyService.getCisEDrugApplyById(cisIpdOrderTo.getApplyCode());
                DrugIpdApplyResqAsTo drugIpdApplyResqAsTo = new DrugIpdApplyResqAsTo();
                drugIpdApplyResqAsTo.setOrderType(cisIpdOrderTo.getOrderType());
                drugIpdApplyResqAsTo.setOrderId(cisIpdOrderTo.getId());
                drugIpdApplyResqAsTo.setOrderContent(String.valueOf(cisIpdOrderTo.getOrderContent()));
                drugIpdApplyResqAsTo.setVisitCode(cisIpdOrderTo.getVisitCode());
                drugIpdApplyResqAsTo.setUsageName(cisEDrugApplyTo.getUsageName());
                drugIpdApplyResqAsTo.setFrequencyName(cisEDrugApplyTo.getFrequencyName());
                BigDecimal reduce = value.stream().map(vo -> ObjectUtils.isEmpty(vo.getSalePriceAmount()) ? new BigDecimal(0) : vo.getSalePriceAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
                drugIpdApplyResqAsTo.setTotalAmount(reduce);
                drugIpdApplyResqAsTo.setStorageName(value.get(0).getStorageName());
                drugIpdApplyResqAsTo.setDrugIpdApplyTos(value);
                List<CisApplyChargeTo> cisApplyChargeTos = cisIpdOrderTo.getCisApplyChargeTos();
                drugIpdApplyResqAsTo.setCisApplyChargeTos(cisApplyChargeTos);
                BigDecimal price = value.stream().map(vo -> ObjectUtils.isEmpty(vo.getSalePrice()) ? new BigDecimal(0) : vo.getSalePrice()).reduce(BigDecimal.ZERO, BigDecimal::add);
                drugIpdApplyResqAsTo.setPrice(price);
                drugIpdApplyResqAsTo.setApprovalStaffName(value.get(0).getApprovalStaffName());
                drugIpdApplyResqAsTo.setApprovalDate(value.get(0).getApprovalDate());
                drugIpdApplyResqAsTos.add(drugIpdApplyResqAsTo);

            });
            drugIpdPatAsTo.setDrugIpdApplyResqAsTos(drugIpdApplyResqAsTos);
            drugIpdPatAsTos.add(drugIpdPatAsTo);
        });

        return drugIpdPatAsTos;
    }

    @Override
    public List<DrugIpdPatAsTo> getDrugIpdApplyHerbs(DrugIpdApplyNurseQto drugIpdApplyNurseQto) {
        List<DrugIpdPatAsTo> drugIpdPatAsTos = new ArrayList<>();
        List<DrugIpdApplyTo> drugIpdAppliesForNurse = drugIpdApplyService.getDrugIpdAppliesForNurse(drugIpdApplyNurseQto);

        //根据患者进行分组
        Map<String, List<DrugIpdApplyTo>> visitCodeCollect = drugIpdAppliesForNurse.stream().collect(Collectors.groupingBy(DrugIpdApplyTo::getVisitCode));
        List<String> visitCodeList = visitCodeCollect.keySet().stream().toList();
        //查询所有患者信息
        List<PatIpdInpatientTo> patIpdInpatientTos = patIpdInpatientService.getConsultationsPat(visitCodeList);
        Map<String,PatIpdInpatientTo> patMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(patIpdInpatientTos)) {
            patIpdInpatientTos.forEach(p -> {
                patMap.put(p.getVisitCode(), p);
            });
        }
        //查询所有患者余额信息
        List<EconIpdAmountTo> econIpdAmountTos = econIpdAmountService.getEconIpdAmountByIdList(visitCodeCollect.keySet().stream().toList());
        Map<String,EconIpdAmountTo> econMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(econIpdAmountTos)) {
            econIpdAmountTos.forEach(p -> {
                econMap.put(p.getVisitCode(), p);
            });
        }

        //获取字典信息
        List<String> dictCodes = new ArrayList<>();
        dictCodes.add("NursingLevel");
        dictCodes.add("DiseaseDisplay");
        List<DictDto> nursingLevels = dictElementService.getCustomDictElementByDictCodes(dictCodes);

        //拼装医嘱和患者信息
        visitCodeCollect.forEach((k, v) -> {
            //患者信息
            PatIpdInpatientTo patIpdInpatientTo = patMap.get(k);
            DrugIpdPatAsTo drugIpdPatAsTo = HIPBeanUtil.copy(patIpdInpatientTo, DrugIpdPatAsTo.class);
            drugIpdPatAsTo.setFeeType(patIpdInpatientTo.getFeeType());
            drugIpdPatAsTo.setFeeTypeName(patIpdInpatientTo.getFeeType() == null ? null : "08".equals(patIpdInpatientTo.getFeeType()) ? "自费" : "医保");
            drugIpdPatAsTo.setDeptNurseCode(patIpdInpatientTo.getDeptNurseCode());
            drugIpdPatAsTo.setDeptNurseName(patIpdInpatientTo.getDeptNurseName());
            PatIpdInpatientExtTo patIpdInpatientExtTo = patIpdInpatientTo.getPatIpdInpatientExtTo();
            if (patIpdInpatientExtTo != null) {
                List<DictDto> nursingLevel = nursingLevels.stream().filter(n -> n.getDictCode().equals("NursingLevel")).toList();
                if (nursingLevel != null && nursingLevel.size() > 0) {
                    List<DictElementTo> dictElementTos = nursingLevel.get(0).getDictElementToList().stream().filter(d -> d.getContrastRelation().equals(patIpdInpatientExtTo.getRoutineCare())).toList();
                    if (CollectionUtils.isNotEmpty(dictElementTos)) {
                        drugIpdPatAsTo.setNursingLevel(dictElementTos.get(0).getElementCode());
                        drugIpdPatAsTo.setNursingLevelName(dictElementTos.get(0).getElementName());
                    }
                }
                List<DictDto> diseaseDisplay = nursingLevels.stream().filter(n -> n.getDictCode().equals("DiseaseDisplay")).toList();
                if (diseaseDisplay != null && diseaseDisplay.size() > 0) {
                    List<DictElementTo> dictElementTos = diseaseDisplay.get(0).getDictElementToList().stream().filter(d -> d.getElementCode().equals(patIpdInpatientExtTo.getCriticalCarePatient())).toList();
                    if (CollectionUtils.isNotEmpty(dictElementTos)) {
                        drugIpdPatAsTo.setCondition(dictElementTos.get(0).getElementName());
                    }
                }
            }
            //住院时间
            LocalDateTime inTime = patIpdInpatientTo.getInTime();
            //当前时间
            LocalDateTime currentDate = LocalDateTime.now();
            //相差小时数
            Duration duration = Duration.between(inTime, currentDate);
            long hours = duration.toHours();
            boolean isSameDate = currentDate.toLocalDate().isEqual(inTime.toLocalDate());
            //判断是否大于12小时，如果大于12小时小于24小时并且不跨天为新入院
            drugIpdPatAsTo.setNewPatFlag(hours <= 12 || hours <= 24 && isSameDate);
            //担保金、余额信息
            EconGuaranteLevelQto econGuaranteLevelQto = new EconGuaranteLevelQto();
            econGuaranteLevelQto.setVisitCode(patIpdInpatientTo.getVisitCode());
            econGuaranteLevelQto.setFeeType(patIpdInpatientTo.getFeeType());
            econGuaranteLevelQto.setDeptNurse(patIpdInpatientTo.getDeptNurseCode());
            EconIpdAmountTo econIpdAmountTo = econIpdAmountService.getEconIpdAmountAndGuarante(econGuaranteLevelQto);
            drugIpdPatAsTo.setBalance(econIpdAmountTo != null?econIpdAmountTo.getBalance():null);//余额
            drugIpdPatAsTo.setGuaranteeAmount(econIpdAmountTo != null?econIpdAmountTo.getGuaranteAmount():null);//担保金

            //同医嘱合并
            List<DrugIpdApplyTo> drugIpdApplyTos = v.stream().collect(Collectors.toMap(DrugIpdApplyTo::getOrderId, y -> y, (s, a) -> s)).values().stream().toList();
            if (CollectionUtils.isNotEmpty(drugIpdApplyTos)) {
                List<DrugIpdApplyNurseHerbsAsTo> drugIpdApplyNurseHerbsAsTos = new ArrayList<>();
                for (DrugIpdApplyTo drugIpdApplyTo : drugIpdApplyTos) {
                    List<CisBaseApplyTo> cisApplyByOrderId = cisCDrugApplyService.findCisApplyByOrderId(drugIpdApplyTo.getOrderId());
                    CisIpdOrderTo cisIpdOrderTo = cisIpdCpoeService.getCisIpdOrderById(drugIpdApplyTo.getOrderId());
                    if (CollectionUtils.isNotEmpty(cisApplyByOrderId)) {
                        DrugIpdApplyNurseHerbsAsTo drugIpdApplyNurseHerbsAsTo = HIPBeanUtil.copy(drugIpdApplyTo, DrugIpdApplyNurseHerbsAsTo.class);
                        CisCDrugApplyTo cisCDrugApplyTo = (CisCDrugApplyTo)cisApplyByOrderId.get(0);
                        List<CisDrugApplyDetailTo> cisDrugApplyDetails = cisCDrugApplyTo.getCisDrugApplyDetails();
                        CisDrugApplyDetailTo cisDrugApplyDetailTo = cisDrugApplyDetails.get(0);
                        //合并后的医嘱查询未合并时的三条中草药医嘱
                        List<DrugIpdApplyTo> list = v.stream().filter(o -> o.getOrderId().equals(drugIpdApplyTo.getOrderId())).toList();
                        BigDecimal sumChangeAmount = new BigDecimal(0);
                        List<String> ids = new ArrayList<>();
                        for (DrugIpdApplyTo ipdApplyTo : list) {
                            sumChangeAmount = sumChangeAmount.add(ipdApplyTo.getSalePriceAmount());
                            ids.add(ipdApplyTo.getId());
                        }
                        drugIpdApplyNurseHerbsAsTo.setDrugApplyIds(ids);
                        drugIpdApplyNurseHerbsAsTo.setCdrugTotalAmount(sumChangeAmount);
                        drugIpdApplyNurseHerbsAsTo.setSbadmWay(cisDrugApplyDetailTo.getSbadmWay() != null ? cisDrugApplyDetailTo.getSbadmWay().getCode() : null);
                        drugIpdApplyNurseHerbsAsTo.setOrderContent(cisIpdOrderTo.getOrderContent());
                        drugIpdApplyNurseHerbsAsTos.add(drugIpdApplyNurseHerbsAsTo);
                    }

                }
                drugIpdPatAsTo.setDrugIpdApplyNurseHerbsAsTos(drugIpdApplyNurseHerbsAsTos);
                drugIpdPatAsTos.add(drugIpdPatAsTo);
            }
        });

        return drugIpdPatAsTos;
    }

    @Override
    @GlobalTransactional
    public void cancelReceiveDrugIpdApply(List<DrugIpdCancelReceiveApplyAsTo> drugIpdCancelReceiveApplyAsTos, LoginInfo loginInfo) {

        List<String> ids = drugIpdCancelReceiveApplyAsTos.stream().map(DrugIpdCancelReceiveApplyAsTo::getId).toList();
        //护士取消领药申请单
        drugIpdApplyService.batchCancelApply(ids);

        //判断是否是整取药
        List<String> execPlanIds = drugIpdCancelReceiveApplyAsTos.stream().filter(a -> !a.getDataType().equals(DrugIpdDataTypeEnum.整取.getCode())).map(DrugIpdCancelReceiveApplyAsTo::getExecPlanId).toList();
        if (CollectionUtils.isNotEmpty(execPlanIds)) {
            //修改医嘱拆分记录表的发药状态(整取药不需要更新 拆分记录状态)
            cisOrderExecPlanService.updateCisOrderExecPlanIsSend(execPlanIds, false);
        }

        //调用经济取消计费（根据药品计费点参数判断是否调用经济计费）
        ParameterTo<String> drugFeeParameter = parameterService.getStringParameter(DictParameterEnum.EconDrugFeeMode.getCode());
        if (drugFeeParameter != null && "Send".equals(drugFeeParameter.getValue())) {
            ids.forEach(id -> {
                EconIpdBillCancelSendDrugApplyTo cancelSendDrugApplyTo = new EconIpdBillCancelSendDrugApplyTo();
                cancelSendDrugApplyTo.setDrugApplyId(id);
                cancelSendDrugApplyTo.setCreateOrg(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
                cancelSendDrugApplyTo.setCurWorkGroupCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
                cancelSendDrugApplyTo.setCurWorkGroupName(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupName());
                cancelSendDrugApplyTo.setBranchHospitalCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getHospitalAreaCode());
                cancelSendDrugApplyTo.setBranchHospitalName(HIPCurrentOrgUtils.getCurrentOrgInfo().getHospitalAreaName());
                cancelSendDrugApplyTo.setCreateOrgName(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupName());
                econIpdBillService.cancelSendDrugApply(cancelSendDrugApplyTo);
            });
        }
    }

    @Override
    @GlobalTransactional
    public void batchCancelRefundApply(List<String> ids) {
        //调用药品取消退药申请
        drugIpdApplyService.batchCancelRefundApply(ids);
        //调用经济取消退药申请作废费用申请单
        econIpdApplyService.cancelBackDrug(ids);

    }

    @Override
    public List<DrugIpdApplyNurseTotalsAsTo> getDrugIpdApplyTotals(DrugIpdApplyNurseQto drugIpdApplyNurseQto) {

        List<DrugIpdApplyNurseTotalsAsTo> drugIpdApplyNurseTotalsAsTos = new ArrayList<>();
        CurrentOrgInfo currentOrgInfo = HIPCurrentOrgUtils.getCurrentOrgInfo();
        drugIpdApplyNurseQto.setReceiveOrg(currentOrgInfo.getWorkGroupCode());
        if (drugIpdApplyNurseQto.getInoutType().equals(DrugIpdInoutTypeEnum.发药)) {
            //查询领药单已发药数据
            drugIpdApplyNurseQto.setStatus(DrugIpdDataStatusEnum.已发);
            List<DrugIpdApplyTo> alreadyDrugIpdApplies = drugIpdApplyService.getDrugIpdAppliesForNurse(drugIpdApplyNurseQto);

            //查询领药单未发药数据
            drugIpdApplyNurseQto.setStatus(DrugIpdDataStatusEnum.未发);
            List<DrugIpdApplyTo> NotAlreadydrugIpdApplies = drugIpdApplyService.getDrugIpdAppliesForNurse(drugIpdApplyNurseQto);

            List<DrugIpdApplyTo> drugIpdApplyTos = new ArrayList<>();
            drugIpdApplyTos.addAll(alreadyDrugIpdApplies);
            drugIpdApplyTos.addAll(NotAlreadydrugIpdApplies);
            if (CollectionUtils.isNotEmpty(drugIpdApplyTos)) {
                drugIpdApplyNurseTotalsAsTos = this.drugApplyMerge(drugIpdApplyTos);
            }

        }else if (drugIpdApplyNurseQto.getInoutType().equals(DrugIpdInoutTypeEnum.退药)){
            //查询领药单已退药数据
            drugIpdApplyNurseQto.setStatus(DrugIpdDataStatusEnum.已退);
            List<DrugIpdApplyTo> alreadyDrugIpdApplies = drugIpdApplyService.getDrugIpdAppliesForNurse(drugIpdApplyNurseQto);

            //查询领药单未退药数据
            drugIpdApplyNurseQto.setStatus(DrugIpdDataStatusEnum.未退);
            List<DrugIpdApplyTo> NotAlreadydrugIpdApplies = drugIpdApplyService.getDrugIpdAppliesForNurse(drugIpdApplyNurseQto);

            List<DrugIpdApplyTo> drugIpdApplyTos = new ArrayList<>();
            drugIpdApplyTos.addAll(alreadyDrugIpdApplies);
            drugIpdApplyTos.addAll(NotAlreadydrugIpdApplies);
            if (CollectionUtils.isNotEmpty(drugIpdApplyTos)) {
                drugIpdApplyNurseTotalsAsTos = this.drugApplyMerge(drugIpdApplyTos);
            }
        }
        return drugIpdApplyNurseTotalsAsTos;
    }

    public List<DrugIpdApplyNurseTotalsAsTo> drugApplyMerge(List<DrugIpdApplyTo> drugIpdApplyTos) {

        //同医嘱合并
        List<DrugIpdApplyTo> list = drugIpdApplyTos.stream().collect(Collectors.toMap(DrugIpdApplyTo::getOrderId, y -> y, (s, a) -> s)).values().stream().toList();
        //多条件进行分组（kay = 领药单发送时间+领药单发送人+药房编码）
        LinkedHashMap<String, List<DrugIpdApplyTo>> addMap = new LinkedHashMap<>();
        for (DrugIpdApplyTo drugIpdApplyTo : list) {
            String applyKey = drugIpdApplyTo.getCreatedDate()+","+drugIpdApplyTo.getCreatedStaff()+","+drugIpdApplyTo.getStorageCode();
            if (addMap.containsKey(applyKey)) {
                addMap.get(applyKey).add(drugIpdApplyTo);
            }else {
                List<DrugIpdApplyTo> drugIpdApplyToList = new ArrayList<>();
                drugIpdApplyToList.add(drugIpdApplyTo);
                addMap.put(applyKey, drugIpdApplyToList);
            }
        }

        List<DrugIpdApplyNurseTotalsAsTo> drugIpdApplyNurseTotalsAsTos = new ArrayList<>();
        addMap.forEach((key, value) -> {
            DrugIpdApplyNurseTotalsAsTo drugIpdApplyNurseTotalsAsTo = new DrugIpdApplyNurseTotalsAsTo();
            drugIpdApplyNurseTotalsAsTo.setCreatedDate(value.get(0).getCreatedDate());
            drugIpdApplyNurseTotalsAsTo.setCreatedStaff(value.get(0).getCreatedStaff());
            drugIpdApplyNurseTotalsAsTo.setCreatedStaffName(value.get(0).getCreatedStaffName());
            drugIpdApplyNurseTotalsAsTo.setStorageName(value.get(0).getStorageName());
            drugIpdApplyNurseTotalsAsTo.setStorageCode(value.get(0).getStorageCode());

            List<DrugIpdApplyNurseHerbsAsTo> drugIpdApplyNurseHerbsAsTos = new ArrayList<>();
            for (DrugIpdApplyTo drugIpdApplyTo : value) {
                if ("02,03".contains(drugIpdApplyTo.getDrugType())) {
                    List<CisBaseApplyTo> cisApplyByOrderId = cisCDrugApplyService.findCisApplyByOrderId(drugIpdApplyTo.getOrderId());
                    CisIpdOrderTo cisIpdOrderTo = cisIpdCpoeService.getCisIpdOrderById(drugIpdApplyTo.getOrderId());
                    if (CollectionUtils.isNotEmpty(cisApplyByOrderId)) {
                        DrugIpdApplyNurseHerbsAsTo drugIpdApplyNurseHerbsAsTo = HIPBeanUtil.copy(drugIpdApplyTo, DrugIpdApplyNurseHerbsAsTo.class);
                        CisCDrugApplyTo cisCDrugApplyTo = (CisCDrugApplyTo)cisApplyByOrderId.get(0);
                        List<CisDrugApplyDetailTo> cisDrugApplyDetails = cisCDrugApplyTo.getCisDrugApplyDetails();
                        CisDrugApplyDetailTo cisDrugApplyDetailTo = cisDrugApplyDetails.get(0);
                        //合并后的医嘱查询未合并时的三条中草药医嘱
                        List<DrugIpdApplyTo> drugIpdApplyList = drugIpdApplyTos.stream().filter(o -> o.getOrderId().equals(drugIpdApplyTo.getOrderId())).toList();
                        BigDecimal sumChangeAmount = new BigDecimal(0);
                        List<String> ids = new ArrayList<>();
                        for (DrugIpdApplyTo ipdApplyTo : drugIpdApplyList) {
                            sumChangeAmount = sumChangeAmount.add(ipdApplyTo.getSalePriceAmount());
                            ids.add(ipdApplyTo.getId());
                        }
                        drugIpdApplyNurseHerbsAsTo.setDrugApplyIds(ids);
                        drugIpdApplyNurseHerbsAsTo.setCdrugTotalAmount(sumChangeAmount);
                        drugIpdApplyNurseHerbsAsTo.setSbadmWay(cisDrugApplyDetailTo.getSbadmWay() != null ? cisDrugApplyDetailTo.getSbadmWay().getCode() : null);
                        drugIpdApplyNurseHerbsAsTo.setOrderContent(cisIpdOrderTo.getOrderContent());
                        drugIpdApplyNurseHerbsAsTos.add(drugIpdApplyNurseHerbsAsTo);
                    }
                }else if ("01".equals(drugIpdApplyTo.getDrugType())) {
                    List<DrugIpdApplyTo> drugIpdApplyList = drugIpdApplyTos.stream().filter(o -> o.getOrderId().equals(drugIpdApplyTo.getOrderId())).toList();
                    DrugIpdApplyNurseHerbsAsTo drugIpdApplyNurseHerbsAsTo = HIPBeanUtil.copy(drugIpdApplyTo, DrugIpdApplyNurseHerbsAsTo.class);
                    BigDecimal currentYearTax = new BigDecimal(drugIpdApplyList.size());
                    drugIpdApplyNurseHerbsAsTo.setCdrugTotalAmount(currentYearTax.multiply(drugIpdApplyTo.getSalePrice()));
                    drugIpdApplyNurseHerbsAsTos.add(drugIpdApplyNurseHerbsAsTo);
                }
            }
            drugIpdApplyNurseTotalsAsTo.setDrugIpdApplyTos(drugIpdApplyNurseHerbsAsTos);
            drugIpdApplyNurseTotalsAsTos.add(drugIpdApplyNurseTotalsAsTo);
        });

        return drugIpdApplyNurseTotalsAsTos;
    }

}
