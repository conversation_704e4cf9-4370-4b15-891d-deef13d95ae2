package com.bjgoodwill.hip.as.cis.opd.visitday.to;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;

/**
 * Description:
 *
 * <AUTHOR>
 * &#064;date 2025/3/25 下午5:18
 */
@Schema(description = "出诊排班模板出诊资源新增科室实体")
public class PatVisitDayResourceDeptNto {

    @Schema(description = "出诊科室编码")
    @NotNull(message = "出诊科室编码不能为空！")
    private String deptCode;
    @Schema(description = "出诊科室拼音码")
    private String deptInputPy;
    @Schema(description = "出诊科室五笔码")
    private String deptWbCode;
    @Schema(description = "出诊科室名称")
    @NotNull(message = "出诊科室名称不能为空！")
    private String deptName;
    @Schema(description = "行政科室编码")
    @NotNull(message = "行政科室编码不能为空！")
    private String admdvsCode;
    @Schema(description = "行政科室拼音码")
    private String admdvsInputPy;
    @Schema(description = "行政科室五笔码")
    private String admdvsWbCode;
    @Schema(description = "行政科室名称")
    @NotNull(message = "行政科室名称不能为空！")
    private String admdvsName;
    @Schema(description = "院区编码")
    @NotNull(message = "院区编码不能为空！")
    private String hospitalAreaCode;
    @Schema(description = "院区名称")
    @NotNull(message = "院区名称不能为空！")
    private String hospitalAreaName;

    public String getDeptCode() {
        return deptCode;
    }

    public void setDeptCode(String deptCode) {
        this.deptCode = deptCode;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getAdmdvsCode() {
        return admdvsCode;
    }

    public void setAdmdvsCode(String admdvsCode) {
        this.admdvsCode = admdvsCode;
    }

    public String getAdmdvsName() {
        return admdvsName;
    }

    public void setAdmdvsName(String admdvsName) {
        this.admdvsName = admdvsName;
    }

    public String getHospitalAreaCode() {
        return hospitalAreaCode;
    }

    public void setHospitalAreaCode(String hospitalAreaCode) {
        this.hospitalAreaCode = hospitalAreaCode;
    }

    public String getHospitalAreaName() {
        return hospitalAreaName;
    }

    public void setHospitalAreaName(String hospitalAreaName) {
        this.hospitalAreaName = hospitalAreaName;
    }

    public String getDeptInputPy() {
        return deptInputPy;
    }

    public void setDeptInputPy(String deptInputPy) {
        this.deptInputPy = deptInputPy;
    }

    public String getDeptWbCode() {
        return deptWbCode;
    }

    public void setDeptWbCode(String deptWbCode) {
        this.deptWbCode = deptWbCode;
    }

    public String getAdmdvsInputPy() {
        return admdvsInputPy;
    }

    public void setAdmdvsInputPy(String admdvsInputPy) {
        this.admdvsInputPy = admdvsInputPy;
    }

    public String getAdmdvsWbCode() {
        return admdvsWbCode;
    }

    public void setAdmdvsWbCode(String admdvsWbCode) {
        this.admdvsWbCode = admdvsWbCode;
    }
}
