package com.bjgoodwill.hip.as.cis.doc.opd.service.impl;

import cn.hutool.extra.pinyin.PinyinUtil;
import com.bjgoodwill.hip.as.cis.doc.opd.enums.CisDocOpdBusinessErrorEnum;
import com.bjgoodwill.hip.as.cis.doc.opd.service.OpdDocPatientListAsService;
import com.bjgoodwill.hip.as.cis.doc.opd.to.docpatientlist.OpdDocPatientListAsQto;
import com.bjgoodwill.hip.as.cis.doc.opd.to.docpatientlist.OpdDocPatientListAsTo;
import com.bjgoodwill.hip.business.util.pat.age.PatAgeUtil;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.common.util.HIPBeanUtil;
import com.bjgoodwill.hip.ds.param.api.service.ParameterService;
import com.bjgoodwill.hip.ds.param.api.to.ParameterTo;
import com.bjgoodwill.hip.ds.pat.index.service.PatIndexService;
import com.bjgoodwill.hip.ds.pat.index.to.PatCardQto;
import com.bjgoodwill.hip.ds.pat.index.to.PatIndexTo;
import com.bjgoodwill.hip.ds.pat.regist.regist.enmus.PatRegistStasEnum;
import com.bjgoodwill.hip.ds.pat.regist.regist.service.PatRegistService;
import com.bjgoodwill.hip.ds.pat.regist.regist.to.PatRegistDocStationQto;
import com.bjgoodwill.hip.ds.pat.regist.regist.to.PatRegistTo;
import com.bjgoodwill.hip.ds.pat.regist.regist.to.PatRegistToVisitEto;
import com.bjgoodwill.hip.enums.DictParameterEnum;
import com.bjgoodwill.hip.security.util.HIPCurrentOrgUtils;
import com.bjgoodwill.hip.security.util.HIPSecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * Description:
 *
 * <AUTHOR>
 * &#064;date 2025/5/14 15:24
 */
@Service("com.bjgoodwill.hip.as.cis.doc.opd.service.OpdDocPatientListAsService")
public class OpdDocPatientListAsServiceImpl implements OpdDocPatientListAsService {

    @Autowired
    private PatRegistService patRegistService;

    @Autowired
    private PatIndexService patIndexService;

    @Autowired
    private ParameterService parameterService;

    /**
     * 查询挂号数据源（待诊）
     *
     * @param qto
     * @return
     */
    @Override
    public List<OpdDocPatientListAsTo> getPendingListFromRegist(OpdDocPatientListAsQto qto) {

        //是否仅看个人号
        boolean isPersonal = qto.isPersonal();
        //查询挂号数据
        List<PatRegistTo> patList = new ArrayList<>();
        List<OpdDocPatientListAsTo> returnList = new ArrayList<>();
        //获取有效时间范围
        ParameterTo<Integer> parameterTo = parameterService.getIntParameter(DictParameterEnum.PatRegistValidTime.getCode());
        int validTime = parameterTo.getValue();
        LocalDateTime currentTime = LocalDateTime.now();
        //当前时间前validTime小时的时间段
        LocalDateTime beginTime = currentTime.minusHours(validTime);
        //有条件查询或者刷新过滤
        if (qto.getSearchText() != null && qto.getSearchText().trim().length() > 0) {
            PatRegistDocStationQto patRegistQto = new PatRegistDocStationQto();
            //身份证
            if ("01".equals(qto.getSearchType())) {
                //卡类型字典IdentifierName，身份证类型1
                PatCardQto cardQto = new PatCardQto();
                cardQto.setType("1");
                cardQto.setCode(qto.getSearchText());
                List<PatIndexTo> patIndexTos = patIndexService.getPatIndexListByCard(cardQto);
                if (patIndexTos != null && patIndexTos.size() > 0) {
                    //一般情况下，一个身份证号对应一个病人
                    for (PatIndexTo patIndexTo : patIndexTos) {
                        patRegistQto.setPatCode(patIndexTo.getCode());
                        patRegistQto.setSetlBeginDate(beginTime);
                        patRegistQto.setSetlEndDate(currentTime);
                        patRegistQto.setRegistStas(PatRegistStasEnum.已挂号);
                        //使用身份证查询，都直接精确查询了，就不再关心是否只查个人号了，查全部了
                        patRegistQto.setSelfFalg(false);
                        patRegistQto.setAppVisitDocCode(HIPSecurityUtils.getLoginInfo().getStaffId());
                        patRegistQto.setAppVisitDeptCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
                        patRegistQto.setHospitalAreaCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getHospitalAreaCode());
                        patList.addAll(patRegistService.getOfDocStation(patRegistQto));
                    }
                    //patList转换成returnList
                    for (PatRegistTo patRegistTo : patList) {
                        OpdDocPatientListAsTo opdDocPatientListAsTo = HIPBeanUtil.copy(patRegistTo, OpdDocPatientListAsTo.class);
                        if (opdDocPatientListAsTo.getBirthDate() != null) {
                            opdDocPatientListAsTo.setAge(PatAgeUtil.getPatAge(opdDocPatientListAsTo.getBirthDate(), LocalDateTime.now()));
                        }
                        returnList.add(opdDocPatientListAsTo);
                    }
                }
                return returnList;
            }
            //就诊卡卡号
            else if ("02".equals(qto.getSearchType())) {
                //卡类型字典IdentifierName，就诊卡类型MED_ID
                PatCardQto cardQto = new PatCardQto();
                cardQto.setType("MED_ID");
                cardQto.setCode(qto.getSearchText());
                List<PatIndexTo> patIndexTos = patIndexService.getPatIndexListByCard(cardQto);
                if (patIndexTos != null && patIndexTos.size() > 0) {
                    //一般情况下，一个身份证号对应一个病人
                    for (PatIndexTo patIndexTo : patIndexTos) {
                        patRegistQto.setPatCode(patIndexTo.getCode());
                        patRegistQto.setSetlBeginDate(beginTime);
                        patRegistQto.setSetlEndDate(currentTime);
                        patRegistQto.setRegistStas(PatRegistStasEnum.已挂号);
                        //使用身份证查询，都直接精确查询了，就不再关心是否只查个人号了，查全部了
                        patRegistQto.setSelfFalg(false);
                        patRegistQto.setAppVisitDocCode(HIPSecurityUtils.getLoginInfo().getStaffId());
                        patRegistQto.setAppVisitDeptCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
                        patRegistQto.setHospitalAreaCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getHospitalAreaCode());
                        patList.addAll(patRegistService.getOfDocStation(patRegistQto));
                    }
                    //patList转换成returnList
                    for (PatRegistTo patRegistTo : patList) {
                        OpdDocPatientListAsTo opdDocPatientListAsTo = HIPBeanUtil.copy(patRegistTo, OpdDocPatientListAsTo.class);
                        if (opdDocPatientListAsTo.getBirthDate() != null) {
                            opdDocPatientListAsTo.setAge(PatAgeUtil.getPatAge(opdDocPatientListAsTo.getBirthDate(), LocalDateTime.now()));
                        }
                        returnList.add(opdDocPatientListAsTo);
                    }
                }
                return returnList;
            } else if ("03".equals(qto.getSearchType())) {
                //姓名/首字母拼音码
                //先查询出来，再转换出拼音码，再筛数据
                patRegistQto.setSetlBeginDate(beginTime);
                patRegistQto.setSetlEndDate(currentTime);
                patRegistQto.setRegistStas(PatRegistStasEnum.已挂号);
                //使用姓名/拼音码查询，就不再关心是否只查个人号了，查全部了
                patRegistQto.setSelfFalg(false);
                patRegistQto.setAppVisitDocCode(HIPSecurityUtils.getLoginInfo().getStaffId());
                patRegistQto.setAppVisitDeptCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
                patRegistQto.setHospitalAreaCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getHospitalAreaCode());
                patList.addAll(patRegistService.getOfDocStation(patRegistQto));
                for (PatRegistTo patRegistTo : patList) {
                    String name = patRegistTo.getName();
                    String Pinyin = PinyinUtil.getFirstLetter(name, "").toUpperCase();
                    if (name.contains(qto.getSearchText()) || Pinyin.contains(qto.getSearchText())) {
                        OpdDocPatientListAsTo opdDocPatientListAsTo = HIPBeanUtil.copy(patRegistTo, OpdDocPatientListAsTo.class);
                        if (opdDocPatientListAsTo.getBirthDate() != null) {
                            opdDocPatientListAsTo.setAge(PatAgeUtil.getPatAge(opdDocPatientListAsTo.getBirthDate(), LocalDateTime.now()));
                        }
                        returnList.add(opdDocPatientListAsTo);
                    }
                }
                return returnList;
            } else {
                //未匹配到查询框查询类型
                BusinessAssert.isFalse(true, CisDocOpdBusinessErrorEnum.BUS_CIS_DOC_OPD_0003, "未匹配到查询框查询类型!");
                return returnList;
            }
        } else {
            PatRegistDocStationQto patRegistQto = new PatRegistDocStationQto();
            //没有条件查询或者刷新过滤
            patRegistQto.setSetlBeginDate(beginTime);
            patRegistQto.setSetlEndDate(currentTime);
            patRegistQto.setRegistStas(PatRegistStasEnum.已挂号);
            if (isPersonal) {
                patRegistQto.setSelfFalg(true);
                patRegistQto.setAppVisitDocCode(HIPSecurityUtils.getLoginInfo().getStaffId());
            } else {
                patRegistQto.setSelfFalg(false);
                patRegistQto.setAppVisitDocCode(HIPSecurityUtils.getLoginInfo().getStaffId());
                patRegistQto.setAppVisitDeptCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
            }
            patRegistQto.setHospitalAreaCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getHospitalAreaCode());
            List<PatRegistTo> patRegistToList = patRegistService.getOfDocStation(patRegistQto);
            for (PatRegistTo patRegistTo : patRegistToList) {
                OpdDocPatientListAsTo opdDocPatientListAsTo = HIPBeanUtil.copy(patRegistTo, OpdDocPatientListAsTo.class);
                if (opdDocPatientListAsTo.getBirthDate() != null) {
                    opdDocPatientListAsTo.setAge(PatAgeUtil.getPatAge(opdDocPatientListAsTo.getBirthDate(), LocalDateTime.now()));
                }
                returnList.add(opdDocPatientListAsTo);
            }
        }
        return returnList;
    }

    /**
     * 查询挂号数据源（已诊）
     *
     * @param qto
     * @return
     */
    @Override
    public List<OpdDocPatientListAsTo> getDiagnosedListFromRegist(OpdDocPatientListAsQto qto) {
        //查询挂号数据
        List<PatRegistTo> patList = new ArrayList<>();
        List<OpdDocPatientListAsTo> returnList = new ArrayList<>();
        // 时间格式化器
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm");
        //获取有效时间范围
        ParameterTo<Integer> parameterTo = parameterService.getIntParameter(DictParameterEnum.PatRegistValidTime.getCode());
        int validTime = parameterTo.getValue();
        LocalDateTime currentTime = LocalDateTime.now();
        //当前时间前validTime小时的时间段
        LocalDateTime beginTime = currentTime.minusHours(validTime);
        //有条件查询或者刷新过滤
        if (qto.getSearchText() != null && qto.getSearchText().trim().length() > 0) {
            PatRegistDocStationQto patRegistQto = new PatRegistDocStationQto();
            //身份证
            if ("01".equals(qto.getSearchType())) {
                //卡类型字典IdentifierName，身份证类型1
                PatCardQto cardQto = new PatCardQto();
                cardQto.setType("1");
                cardQto.setCode(qto.getSearchText());
                List<PatIndexTo> patIndexTos = patIndexService.getPatIndexListByCard(cardQto);
                if (patIndexTos != null && patIndexTos.size() > 0) {
                    //一般情况下，一个身份证号对应一个病人
                    for (PatIndexTo patIndexTo : patIndexTos) {
                        patRegistQto.setPatCode(patIndexTo.getCode());
                        patRegistQto.setVisitBeginDate(beginTime);
                        patRegistQto.setVisitEndDate(currentTime);
                        patRegistQto.setRegistStas(PatRegistStasEnum.已接诊);
                        //已诊查全部
                        patRegistQto.setSelfFalg(false);
                        patRegistQto.setVisitDocCode(HIPSecurityUtils.getLoginInfo().getStaffId());
                        patRegistQto.setVisitDeptCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
                        patRegistQto.setHospitalAreaCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getHospitalAreaCode());
                        patList.addAll(patRegistService.getOfDocStation(patRegistQto));
                    }
                    //patList转换成returnList
                    for (PatRegistTo patRegistTo : patList) {
                        OpdDocPatientListAsTo opdDocPatientListAsTo = HIPBeanUtil.copy(patRegistTo, OpdDocPatientListAsTo.class);
                        if (opdDocPatientListAsTo.getBirthDate() != null) {
                            opdDocPatientListAsTo.setAge(PatAgeUtil.getPatAge(opdDocPatientListAsTo.getBirthDate(), LocalDateTime.now()));
                        }
                        // 拆分 visitDateTime 为日期和时间部分
                        if (patRegistTo.getVisitDateTime() != null) {
                            // 直接获取日期部分
                            LocalDate visitDate = patRegistTo.getVisitDateTime().toLocalDate();
                            opdDocPatientListAsTo.setDate(visitDate);

                            // 提取时间部分 (HH:mm)
                            String visitTime = patRegistTo.getVisitDateTime().format(timeFormatter);
                            opdDocPatientListAsTo.setTime(visitTime);
                        }
                        returnList.add(opdDocPatientListAsTo);
                    }
                }
                return returnList;
            }
            //就诊卡卡号
            else if ("02".equals(qto.getSearchType())) {
                //卡类型字典IdentifierName，就诊卡类型MED_ID
                PatCardQto cardQto = new PatCardQto();
                cardQto.setType("MED_ID");
                cardQto.setCode(qto.getSearchText());
                List<PatIndexTo> patIndexTos = patIndexService.getPatIndexListByCard(cardQto);
                if (patIndexTos != null && patIndexTos.size() > 0) {
                    //一般情况下，一个身份证号对应一个病人
                    for (PatIndexTo patIndexTo : patIndexTos) {
                        patRegistQto.setPatCode(patIndexTo.getCode());
                        patRegistQto.setVisitBeginDate(beginTime);
                        patRegistQto.setVisitEndDate(currentTime);
                        patRegistQto.setRegistStas(PatRegistStasEnum.已接诊);
                        //已诊查全部
                        patRegistQto.setSelfFalg(false);
                        patRegistQto.setVisitDocCode(HIPSecurityUtils.getLoginInfo().getStaffId());
                        patRegistQto.setVisitDeptCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
                        patRegistQto.setHospitalAreaCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getHospitalAreaCode());
                        patList.addAll(patRegistService.getOfDocStation(patRegistQto));
                    }
                    //patList转换成returnList
                    for (PatRegistTo patRegistTo : patList) {
                        OpdDocPatientListAsTo opdDocPatientListAsTo = HIPBeanUtil.copy(patRegistTo, OpdDocPatientListAsTo.class);
                        if (opdDocPatientListAsTo.getBirthDate() != null) {
                            opdDocPatientListAsTo.setAge(PatAgeUtil.getPatAge(opdDocPatientListAsTo.getBirthDate(), LocalDateTime.now()));
                        }
                        // 拆分 visitDateTime 为日期和时间部分
                        if (patRegistTo.getVisitDateTime() != null) {
                            // 直接获取日期部分
                            LocalDate visitDate = patRegistTo.getVisitDateTime().toLocalDate();
                            opdDocPatientListAsTo.setDate(visitDate);

                            // 提取时间部分 (HH:mm)
                            String visitTime = patRegistTo.getVisitDateTime().format(timeFormatter);
                            opdDocPatientListAsTo.setTime(visitTime);
                        }
                        returnList.add(opdDocPatientListAsTo);
                    }
                }
                return returnList;
            } else if ("03".equals(qto.getSearchType())) {
                //姓名/首字母拼音码
                //先查询出来，再转换出拼音码，再筛数据
                patRegistQto.setVisitBeginDate(beginTime);
                patRegistQto.setVisitEndDate(currentTime);
                patRegistQto.setRegistStas(PatRegistStasEnum.已接诊);
                //已诊查全部
                patRegistQto.setSelfFalg(false);
                patRegistQto.setVisitDocCode(HIPSecurityUtils.getLoginInfo().getStaffId());
                patRegistQto.setVisitDeptCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
                patRegistQto.setHospitalAreaCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getHospitalAreaCode());
                patList.addAll(patRegistService.getOfDocStation(patRegistQto));
                for (PatRegistTo patRegistTo : patList) {
                    String name = patRegistTo.getName();
                    String Pinyin = PinyinUtil.getFirstLetter(name, "").toUpperCase();
                    if (name.contains(qto.getSearchText()) || Pinyin.contains(qto.getSearchText())) {
                        OpdDocPatientListAsTo opdDocPatientListAsTo = HIPBeanUtil.copy(patRegistTo, OpdDocPatientListAsTo.class);
                        if (opdDocPatientListAsTo.getBirthDate() != null) {
                            opdDocPatientListAsTo.setAge(PatAgeUtil.getPatAge(opdDocPatientListAsTo.getBirthDate(), LocalDateTime.now()));
                        }
                        // 拆分 visitDateTime 为日期和时间部分
                        if (patRegistTo.getVisitDateTime() != null) {
                            // 直接获取日期部分
                            LocalDate visitDate = patRegistTo.getVisitDateTime().toLocalDate();
                            opdDocPatientListAsTo.setDate(visitDate);

                            // 提取时间部分 (HH:mm)
                            String visitTime = patRegistTo.getVisitDateTime().format(timeFormatter);
                            opdDocPatientListAsTo.setTime(visitTime);
                        }
                        returnList.add(opdDocPatientListAsTo);
                    }
                }
                return returnList;
            } else {
                //未匹配到查询框查询类型
                BusinessAssert.isFalse(true, CisDocOpdBusinessErrorEnum.BUS_CIS_DOC_OPD_0002, "未匹配到查询框查询类型!");
                return returnList;
            }
        } else {
            PatRegistDocStationQto patRegistQto = new PatRegistDocStationQto();
            //没有条件查询或者刷新过滤
            patRegistQto.setVisitBeginDate(beginTime);
            patRegistQto.setVisitEndDate(currentTime);
            patRegistQto.setRegistStas(PatRegistStasEnum.已接诊);
            patRegistQto.setSelfFalg(false);
            patRegistQto.setVisitDocCode(HIPSecurityUtils.getLoginInfo().getStaffId());
            patRegistQto.setVisitDeptCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
            patRegistQto.setHospitalAreaCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getHospitalAreaCode());
            List<PatRegistTo> patRegistToList = patRegistService.getOfDocStation(patRegistQto);
            for (PatRegistTo patRegistTo : patRegistToList) {
                OpdDocPatientListAsTo opdDocPatientListAsTo = HIPBeanUtil.copy(patRegistTo, OpdDocPatientListAsTo.class);
                if (opdDocPatientListAsTo.getBirthDate() != null) {
                    opdDocPatientListAsTo.setAge(PatAgeUtil.getPatAge(opdDocPatientListAsTo.getBirthDate(), LocalDateTime.now()));
                }
                // 拆分 visitDateTime 为日期和时间部分
                if (patRegistTo.getVisitDateTime() != null) {
                    // 直接获取日期部分
                    LocalDate visitDate = patRegistTo.getVisitDateTime().toLocalDate();
                    opdDocPatientListAsTo.setDate(visitDate);

                    // 提取时间部分 (HH:mm)
                    String visitTime = patRegistTo.getVisitDateTime().format(timeFormatter);
                    opdDocPatientListAsTo.setTime(visitTime);
                }
                returnList.add(opdDocPatientListAsTo);
            }
        }
        return returnList;
    }


    /**
     * 查询分诊数据源（待诊）
     *
     * @param qto
     * @return
     */
    @Override
    public List<OpdDocPatientListAsTo> getPendingListFromTriage(OpdDocPatientListAsQto qto) {
        //TODO 等待分诊评审
        List<OpdDocPatientListAsTo> returnList = new ArrayList<>();
        return returnList;
    }

    /**
     * 查询分诊数据源（已诊）
     *
     * @param qto
     * @return
     */
    @Override
    public List<OpdDocPatientListAsTo> getDiagnosedListFromTriage(OpdDocPatientListAsQto qto) {
        //TODO 等待分诊评审
        List<OpdDocPatientListAsTo> returnList = new ArrayList<>();
        return returnList;
    }

    /**
     * 接诊
     *
     * @param visitCode
     * @return
     */
    @Override
    public void toVisit(String visitCode) {
        //根据visitCode查询挂号信息
        PatRegistTo patRegistTo = patRegistService.getPatRegistByVisitCode(visitCode);
        //2025/06/04如果已接诊，则不处理，也不提示（诊疗多次下诊断，与诊疗沟通确定）
        if (PatRegistStasEnum.已接诊.equals(patRegistTo.getRegistStas())) {
            return;
        }
        PatRegistToVisitEto patRegistToVisitEto = new PatRegistToVisitEto();
        patRegistToVisitEto.setVisitDeptCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
        patRegistToVisitEto.setVisitDeptName(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupName());
        patRegistToVisitEto.setVersion(patRegistTo.getVersion());
        patRegistService.toVisit(patRegistTo.getId(), patRegistToVisitEto);
    }
}
