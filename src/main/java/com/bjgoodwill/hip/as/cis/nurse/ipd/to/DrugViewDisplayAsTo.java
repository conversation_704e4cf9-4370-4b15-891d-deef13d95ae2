package com.bjgoodwill.hip.as.cis.nurse.ipd.to;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2025/1/9 16:36
 * @ClassName: DrugViewDisplayAsTo
 * @Description: 领药单发送-汇总药品显示
 */
@Schema(description = "领药单发送-汇总药品显示")
public class DrugViewDisplayAsTo {

    @Schema(description = "药品名称")
    private String priceItemName;

    @Schema(description = "规格")
    private String packageSpec;

    @Schema(description = "领药数量")
    private Double num;

    @Schema(description = "领药数量-单位")
    private String unit;

    @Schema(description = "单价")
    private BigDecimal price;

    @Schema(description = "单价-单位")
    private String packageUnit;

    @Schema(description = "总金额")
    public BigDecimal totalAmount;

    @Schema(description = "单价-数量")
    private Double packageNum;

    public Double getPackageNum() {
        return packageNum;
    }

    public void setPackageNum(Double packageNum) {
        this.packageNum = packageNum;
    }

    public String getPriceItemName() {
        return priceItemName;
    }

    public void setPriceItemName(String priceItemName) {
        this.priceItemName = priceItemName;
    }

    public String getPackageSpec() {
        return packageSpec;
    }

    public void setPackageSpec(String packageSpec) {
        this.packageSpec = packageSpec;
    }

    public Double getNum() {
        return num;
    }

    public void setNum(Double num) {
        this.num = num;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getPackageUnit() {
        return packageUnit;
    }

    public void setPackageUnit(String packageUnit) {
        this.packageUnit = packageUnit;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }
}
