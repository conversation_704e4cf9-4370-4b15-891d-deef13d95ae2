package com.bjgoodwill.hip.as.cis.doc.ipd.service.impl;

import com.bjgoodwill.hip.as.cis.doc.ipd.enums.CisDocIpdBusinessErrorEnum;
import com.bjgoodwill.hip.as.cis.doc.ipd.service.OperationOrderIssuedService;
import com.bjgoodwill.hip.as.cis.doc.ipd.service.assembler.OperationApplyIssuedAsAssembler;
import com.bjgoodwill.hip.as.cis.doc.ipd.service.assembler.OrderIssuedAsAssembler;
import com.bjgoodwill.hip.as.cis.doc.ipd.to.*;
import com.bjgoodwill.hip.as.cis.doc.ipd.to.diagnosis.IpdDiagnosisAsTo;
import com.bjgoodwill.hip.as.cis.doc.ipd.to.operation.*;
import com.bjgoodwill.hip.as.cis.doc.ipd.util.IpdOrderExecuteOrgUtil;
import com.bjgoodwill.hip.as.cis.doc.ipd.util.verify.submit.SubmitOrderVerifyUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.*;
import com.bjgoodwill.hip.common.bean.LoginInfo;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.common.util.HIPBeanUtil;
import com.bjgoodwill.hip.common.util.HIPIDUtil;
import com.bjgoodwill.hip.ds.cis.apply.charge.enmus.CisChargeTypeEnum;
import com.bjgoodwill.hip.ds.cis.apply.charge.to.CisApplyChargeNto;
import com.bjgoodwill.hip.ds.cis.apply.detail.service.ApplyWithDetialService;
import com.bjgoodwill.hip.ds.cis.apply.diag.to.ApplyDiagnosisNto;
import com.bjgoodwill.hip.ds.cis.apply.operation.service.CisOperationApplyService;
import com.bjgoodwill.hip.ds.cis.apply.operation.to.*;
import com.bjgoodwill.hip.ds.cis.cds.order.service.CisOrderCommonService;
import com.bjgoodwill.hip.ds.cis.cds.order.to.CisOrderCommonNto;
import com.bjgoodwill.hip.ds.cis.cpoe.diagnose.service.CisIpdDiagnoseService;
import com.bjgoodwill.hip.ds.cis.cpoe.diagnose.to.CisIpdDiagnoseTo;
import com.bjgoodwill.hip.ds.cis.cpoe.order.service.CisIpdCpoeService;
import com.bjgoodwill.hip.ds.cis.cpoe.order.to.*;
import com.bjgoodwill.hip.ds.cis.medicineitem.price.to.ServiceClinicPriceTo;
import com.bjgoodwill.hip.ds.cis.medicineitem.serviceItem.service.ServiceClinicItemService;
import com.bjgoodwill.hip.ds.cis.medicineitem.serviceItem.to.OperationApplyTo;
import com.bjgoodwill.hip.ds.cis.medicineitem.serviceItem.to.ServiceClinicItemTo;
import com.bjgoodwill.hip.ds.cis.rule.drugauth.to.DoctCommitOrderMsgTo;
import com.bjgoodwill.hip.ds.org.api.service.WorkGroupService;
import com.bjgoodwill.hip.ds.org.api.to.WorkGroupTo;
import com.bjgoodwill.hip.ds.term.api.service.DictElementService;
import com.bjgoodwill.hip.ds.term.api.to.DictElementQto;
import com.bjgoodwill.hip.ds.term.api.to.DictElementTo;
import com.bjgoodwill.hip.security.util.HIPCurrentOrgUtils;
import com.bjgoodwill.hip.security.util.HIPSecurityUtils;
import io.micrometer.common.util.StringUtils;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.apache.seata.spring.annotation.GlobalTransactional;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> liangyuqing
 * @description :
 * @createDate : 2025/2/14 9:57
 */
@Service
public class OperationOrderIssuedServiceImpl implements OperationOrderIssuedService {

    @Resource
    private CisIpdCpoeService cisIpdCpoeService;

    @Resource
    private CisIpdDiagnoseService cisIpdDiagnoseService;

    @Resource(name = "com.bjgoodwill.hip.ds.cis.medicineitem.serviceItem.service.ServiceClinicItemTangibleService")
    private ServiceClinicItemService serviceClinicItemService;

    @Resource(name = "com.bjgoodwill.hip.ds.cis.apply.detail.service.ApplyWithDetialServiceFeign")
    private ApplyWithDetialService applyWithDetialService;

    @Resource
    private SubmitOrderVerifyUtil submitOrderVerifyUtil;

    @Resource
    private CisOrderCommonService cisOrderCommonService;

    @Resource
    private DictElementService dictElementService;

    @Resource
    private OrderIssuedAsAssembler orderIssuedAsAssembler;

    @Resource
    private CisOperationApplyService cisOperationApplyService;

    @Resource
    private OperationApplyIssuedAsAssembler operationApplyIssuedAsAssembler;

    @Resource
    private IpdOrderExecuteOrgUtil ipdOrderExecuteOrgUtil;

    @Resource
    private WorkGroupService workGroupService;

    @Override
    public List<IpdDiagnosisAsTo> getPatDiagnosis(String visitCode) {
        List<CisIpdDiagnoseTo> cisIpdDiagnoseToList = cisIpdDiagnoseService.getCisIpdDiagnoseByVisitCode(visitCode);

        List<IpdDiagnosisAsTo> ipdDiagnosisAsTos = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(cisIpdDiagnoseToList)) {
            // 返回术前诊断，若术前诊断为空，则返回全部西医诊断
            List<CisIpdDiagnoseTo> OTypeDIagnosisTos = cisIpdDiagnoseToList.stream().filter(diagnosis -> diagnosis.getDiagnosisType().equals(DiagnosisTypeEnum.O)).toList();
            if (CollectionUtils.isNotEmpty(OTypeDIagnosisTos)) {
                ipdDiagnosisAsTos = HIPBeanUtil.copy(OTypeDIagnosisTos, IpdDiagnosisAsTo.class);
            } else {
                cisIpdDiagnoseToList = cisIpdDiagnoseToList.stream().filter(diagnosis -> diagnosis.getDiagnosisClass().equals(DiagnosisEnum.WMD)).toList();
                ipdDiagnosisAsTos = HIPBeanUtil.copy(cisIpdDiagnoseToList, IpdDiagnosisAsTo.class);
            }
        }

        if (CollectionUtils.isNotEmpty(ipdDiagnosisAsTos)) {
            ipdDiagnosisAsTos.forEach(o -> {
                o.setDiagnosisTypeCode(o.getDiagnosisType().getCode());
                o.setDiagnosisTypeName(o.getDiagnosisType().getName());
                o.setDiagnosisClassCode(o.getDiagnosisClass().getCode());
                o.setDiagnosisClassName(o.getDiagnosisClass().getName());
            });
        }

        return ipdDiagnosisAsTos;
    }

    @Override
    public List<OperationApplyTo> getOperaServiceItems(String text) {
        List<OperationApplyTo> operationApplyTos = new ArrayList<>();
        List<SystemTypeEnum> systemTypeEnums = new ArrayList<>();
        systemTypeEnums.add(SystemTypeEnum.OPERATIONAPPLY);
        List<ServiceClinicItemTo> serviceClinicItemToList = serviceClinicItemService.queryServiceClinicItemListByinPutText(systemTypeEnums, 10000, text, HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode(), HospitalModelEnum.住院, HIPCurrentOrgUtils.getCurrentOrgInfo().getHospitalAreaCode());
        if (CollectionUtils.isNotEmpty(serviceClinicItemToList)) {
            operationApplyTos = HIPBeanUtil.copy(serviceClinicItemToList, OperationApplyTo.class);
        }
        return operationApplyTos;
    }

    @Override
    public void newOperationOrderSave(OperationApplyOrderAsNto operationOrderAsNto) {
        BusinessAssert.notNull(operationOrderAsNto, CisDocIpdBusinessErrorEnum.CIS_DOC_IPD_0002, "医嘱信息");

        List<CisTemporaryOrderNto> cisTemporaryOrderNtoList = new ArrayList<>();
        CisTemporaryOrderNto cisTemporaryOrderNto = orderIssuedAsAssembler.buildCisIpdTempOrderNto(operationOrderAsNto);

        cisTemporaryOrderNtoList.add(cisTemporaryOrderNto);
        cisIpdCpoeService.createIpdOrderBatch(Collections.unmodifiableList(cisTemporaryOrderNtoList));
    }

    @GlobalTransactional
    @Override
    public DoctCommitOrderMsgTo saveAndSubmitOperationOrder(OperationApplyOrderAsNto operationOrderAsNto) {
        // 保存手术医嘱
        BusinessAssert.notNull(operationOrderAsNto, CisDocIpdBusinessErrorEnum.CIS_DOC_IPD_0002, "医嘱信息");

        List<CisTemporaryOrderNto> cisTemporaryOrderNtoList = new ArrayList<>();
        CisTemporaryOrderNto cisTemporaryOrderNto = orderIssuedAsAssembler.buildCisIpdTempOrderNto(operationOrderAsNto);

        cisTemporaryOrderNtoList.add(cisTemporaryOrderNto);
        cisIpdCpoeService.createIpdOrderBatch(Collections.unmodifiableList(cisTemporaryOrderNtoList));

        // 签发校验
        LoginInfo loginInfo = HIPSecurityUtils.getLoginInfo();
        List<DocOrderCommitVerifyAsQto> docOrderCommitVerifyAsQtoList = new ArrayList<>();
        for (OperationApplyDetailAsNto detail : operationOrderAsNto.getOperationDetails()) {
            DocOrderCommitVerifyAsQto docOrderCommitVerifyAsQto = new DocOrderCommitVerifyAsQto();
            docOrderCommitVerifyAsQto.setId(operationOrderAsNto.getId());
            docOrderCommitVerifyAsQto.setOrderServiceCode(detail.getServiceItemCode());
            docOrderCommitVerifyAsQto.setOrderClassCode(SystemTypeEnum.OPERATIONAPPLY.getCode());
            docOrderCommitVerifyAsQto.setCreateOrgCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
            docOrderCommitVerifyAsQto.setExecuteOrgCode(operationOrderAsNto.getExecuteOrgCode());
            docOrderCommitVerifyAsQto.setStatusCodeCode("NEW");
            docOrderCommitVerifyAsQto.setSubmitStaffId(loginInfo.getStaffId());
            docOrderCommitVerifyAsQto.setVisitCode(operationOrderAsNto.getVisitCode());
            docOrderCommitVerifyAsQtoList.add(docOrderCommitVerifyAsQto);
        }
        List<DoctCommitOrderMsgTo> doctCommitOrderMsgTos = submitOrderVerifyUtil.orderCommitVerify(docOrderCommitVerifyAsQtoList);
        if (doctCommitOrderMsgTos != null) {
            // 如果校验返回值msg有值，说明有医嘱校验不合规，不能签发
            // 直接返回结果
            if (doctCommitOrderMsgTos.get(0).getMsg().size() > 0 ) {
                BusinessAssert.isTrue(doctCommitOrderMsgTos.get(0).getMsg().size() == 0, doctCommitOrderMsgTos.get(0).getMsg().get(0));
            }
        }

        // 签发
        cisIpdCpoeService.submitIpdOrderBatch(operationOrderAsNto.getVisitCode(), List.of(operationOrderAsNto.getId()));

        // 4.保存常用医嘱
        cisOrderCommonService.saveOrderCommons(this.buildCisOrderCommonNto(operationOrderAsNto));

        return doctCommitOrderMsgTos.get(0);
    }

    @Override
    public void updateOperationOrder(OperationApplyOrderAsEto operationOrderAsEto) {
        // 1.判断医嘱状态是否可以修改
        CisIpdOrderTo cisIpdOrderTo = cisIpdCpoeService.getCisIpdOrderById(operationOrderAsEto.getId());
        BusinessAssert.isTrue(CisStatusEnum.NEW.equals(cisIpdOrderTo.getStatusCode()), "医嘱不是新建状态，无法修改");

        operationOrderAsEto.setOrderType("2");
        CisIpdOrderEto cisIpdOrderEto = this.buildOperationOrderEto(operationOrderAsEto);

        CisOperationApplyEto cisOperationApplyEto = this.operationTodo(operationOrderAsEto, cisIpdOrderTo.getApplyCode());
        cisIpdOrderEto.setCisBaseApplyEto(cisOperationApplyEto);

//        // 2.需要删除的明细信息
//        if (CollectionUtils.isNotEmpty(operationOrderAsEto.getDeleteDetailIdList())) {
//            applyWithDetialService.deleteApplyWithDetialByDetails(operationOrderAsEto.getId(), operationOrderAsEto.getDeleteDetailIdList());
//        }
//
//        // 3.构造实体
//        CisTemporaryOrderEto cisTemporaryOrderEto = orderIssuedAsAssembler.buildCisIpdTempOrderEto(operationOrderAsEto);

        // 4.调用领域接口
        cisIpdCpoeService.updateIpdOrder(cisIpdOrderEto, cisIpdOrderTo.getId());
    }


    /**
     * 手术修改接口
     * @param operationOrderAsEto
     * @param applyId
     * @return
     */
    public CisOperationApplyEto operationTodo(OperationApplyOrderAsEto operationOrderAsEto, String applyId) {
        // 构建手术申请Eto
        CisOperationApplyEto cisOperationApplyEto = new CisOperationApplyEto();
        OperationApplyOrderAsEto operationApplyOrderAsEto = (OperationApplyOrderAsEto) operationOrderAsEto;
        cisOperationApplyEto = HIPBeanUtil.copy(operationApplyOrderAsEto, CisOperationApplyEto.class);

        if (StringUtils.isNotEmpty(operationOrderAsEto.getIsOlation())) {
            cisOperationApplyEto.setIsOlation(operationOrderAsEto.getIsOlation().equals("1"));
        } else {
            cisOperationApplyEto.setIsOlation(false);
        }

        cisOperationApplyEto.setReMark(operationApplyOrderAsEto.getReMark());
        cisOperationApplyEto.setIsCanPriorityFlag(operationApplyOrderAsEto.getIsCanPriorityFlag());
        cisOperationApplyEto.setVersion(operationApplyOrderAsEto.getVersion());
        cisOperationApplyEto.setFrequency(operationApplyOrderAsEto.getFrequency());
        cisOperationApplyEto.setFrequencyName(operationApplyOrderAsEto.getFrequencyName());
        cisOperationApplyEto.setStatusCode(operationOrderAsEto.getApplyStatusCode());
        cisOperationApplyEto.setNum(1d);
        cisOperationApplyEto.setVersion(operationOrderAsEto.getVersion());
        cisOperationApplyEto.setIsApply(!operationOrderAsEto.getNotApply());
        // -------------------------------------------------

        // 查询手术申请
        CisOperationApplyTo cisOperationApplyTo = cisOperationApplyService.getCisOperationApplyAllById(applyId);
        List<CisOperationApplyDetailTo> cisOperationApplyDetailTos = cisOperationApplyTo.getCisOperationApplyDetailToList();

        // 循环数据库查的明细
        List<String> deleteIds = new ArrayList<>();
        Map<String, OperationApplyDetailAsEto> updateEtoMap = new HashMap<>();
        Map<String, OperationApplyDetailAsEto> insertEtoMap = new HashMap<>();
        Map<String, OperationApplyDetailAsEto> reInsertEtoMap = new HashMap<>();
        Map<String, OperationApplyDetailAsEto> map = operationOrderAsEto.getOperationDetailEtos().stream().collect(Collectors.toMap(OperationApplyDetailAsEto::getId, Function.identity()));
        for (CisOperationApplyDetailTo cisOperationApplyDetailTo : cisOperationApplyDetailTos) {
            if (!map.containsKey(cisOperationApplyDetailTo.getId())) {
                // 某条数据库明细id，不在前端传的列表中，删除
                deleteIds.add(cisOperationApplyDetailTo.getId());
            } else {
                OperationApplyDetailAsEto asEto = map.get(cisOperationApplyDetailTo.getId());
                if (!cisOperationApplyDetailTo.getServiceItemCode().equals(asEto.getServiceItemCode())) {
                    // 数据库明细ID，在前端传的列表中，但是服务项目不同，也删除，重新生成，需要新增
                    deleteIds.add(asEto.getId());
                    reInsertEtoMap.put(asEto.getId(), asEto);
                } else {
                    // 数据库明细ID，在前端传的列表中，且服务项目相同，修改
                    updateEtoMap.put(asEto.getId(), asEto);
                }
            }
        }

        // 循环前端传的明细，如果某一条明细不存在于数据库中，需要新增
        Map<String, CisOperationApplyDetailTo> map2 = cisOperationApplyDetailTos.stream().collect(Collectors.toMap(CisOperationApplyDetailTo::getId, Function.identity()));
        for (OperationApplyDetailAsEto detailEto :  operationOrderAsEto.getOperationDetailEtos()) {
            if (!map2.containsKey(detailEto.getId())) {
                insertEtoMap.put(detailEto.getId(), detailEto);
            }
        }

        // 删除明细
        if (CollectionUtils.isNotEmpty(deleteIds)) {
            cisOperationApplyEto.setDeleteDetailIds(deleteIds);
        }

        // 构造修改明细信息
        String serviceItemCode = "";
        String serviceItemName = "";
        if (updateEtoMap.size() > 0) {
            List<CisOperationApplyDetailEto> cisOperationApplyDetailEtos = new ArrayList<>();
            List<OperationApplyDetailAsEto> operationApplyDetailAsEtos = updateEtoMap.values().stream().toList();
            for (OperationApplyDetailAsEto detailAsEto : operationApplyDetailAsEtos) {
                CisOperationApplyDetailEto cisOperationApplyDetailEto = HIPBeanUtil.copy(detailAsEto, CisOperationApplyDetailEto.class);
//                cisOperationApplyDetailEto.set(detailAsEto);
                cisOperationApplyDetailEtos.add(cisOperationApplyDetailEto);
                serviceItemCode += detailAsEto.getServiceItemCode() + ",";
                serviceItemName += detailAsEto.getServiceItemName() + ",";
            }
            cisOperationApplyEto.setDetailEtos(cisOperationApplyDetailEtos);
        }

        List<CisOperationApplyDetailNto> cisOperationApplyDetailNtos = new ArrayList<>();
        List<CisApplyChargeNto> chargeNtos = new ArrayList<>();
        if (insertEtoMap.size() > 0) {
            for (OperationApplyDetailAsEto detailAsEto : insertEtoMap.values()) {
                CisOperationApplyDetailNto cisOperationApplyDetailNto = HIPBeanUtil.copy(detailAsEto, CisOperationApplyDetailNto.class);
                Optional<String> opt = Optional.ofNullable(detailAsEto.getOperationCode());
                // todo数据问题，有的手术项目没绑定icd9
                cisOperationApplyDetailNto.setOperationCode(opt.orElse("没绑定"));
                cisOperationApplyDetailNto.setOperationName(opt.orElse("没绑定"));
                cisOperationApplyDetailNto.setVisitCode(operationOrderAsEto.getVisitCode());
                cisOperationApplyDetailNtos.add(cisOperationApplyDetailNto);
                chargeNtos.addAll(this.buildOperationCisApplyChargeNto(operationOrderAsEto, detailAsEto));

                serviceItemCode += detailAsEto.getServiceItemCode() + ",";
                serviceItemName += detailAsEto.getServiceItemName() + ",";
            }
        }

        if (reInsertEtoMap.size() > 0) {
            for (OperationApplyDetailAsEto detailAsEto : reInsertEtoMap.values()) {
                // 重新生成ID
                detailAsEto.setId(HIPIDUtil.getNextIdString());
                CisOperationApplyDetailNto cisOperationApplyDetailNto = HIPBeanUtil.copy(detailAsEto, CisOperationApplyDetailNto.class);
                cisOperationApplyDetailNto.setVisitCode(operationOrderAsEto.getVisitCode());
                // todo数据问题，有的手术项目没绑定icd9
                Optional<String> opt = Optional.ofNullable(detailAsEto.getOperationCode());
                cisOperationApplyDetailNto.setOperationCode(opt.orElse("没绑定"));
                cisOperationApplyDetailNto.setOperationName(opt.orElse("没绑定"));
                cisOperationApplyDetailNtos.add(cisOperationApplyDetailNto);
                chargeNtos.addAll(this.buildOperationCisApplyChargeNto(operationOrderAsEto, detailAsEto));

                serviceItemCode += detailAsEto.getServiceItemCode() + ",";
                serviceItemName += detailAsEto.getServiceItemName() + ",";
           }
        }

        cisOperationApplyEto.setCisApplyChargeNtos(chargeNtos);
        cisOperationApplyEto.setDetailNtos(cisOperationApplyDetailNtos);

        // 赋值申请单服务项目编码、名称
        cisOperationApplyEto.setServiceItemCode(serviceItemCode.substring(0, serviceItemCode.length() - 1));
        cisOperationApplyEto.setServiceItemName(serviceItemName.substring(0, serviceItemName.length() - 1));

        cisOperationApplyEto.setDiagnosisNtos(HIPBeanUtil.copy(operationOrderAsEto.getApplyDiagnosisAsNtos(), ApplyDiagnosisNto.class));

        return cisOperationApplyEto;
    }

    @GlobalTransactional
    @Override
    public DoctCommitOrderMsgTo updateAndSubmitOperationOrder(OperationApplyOrderAsEto operationOrderAsEto) {
        // 1.判断医嘱状态是否可以修改
        CisIpdOrderTo cisIpdOrderTo = cisIpdCpoeService.getCisIpdOrderById(operationOrderAsEto.getId());
        BusinessAssert.isTrue(CisStatusEnum.NEW.equals(cisIpdOrderTo.getStatusCode()), "医嘱不是新建状态，无法修改");

        operationOrderAsEto.setOrderType("2");
        CisIpdOrderEto cisIpdOrderEto = this.buildOperationOrderEto(operationOrderAsEto);

        CisOperationApplyEto cisOperationApplyEto = this.operationTodo(operationOrderAsEto, cisIpdOrderTo.getApplyCode());
        cisIpdOrderEto.setCisBaseApplyEto(cisOperationApplyEto);

        cisIpdCpoeService.updateIpdOrder(cisIpdOrderEto, cisIpdOrderTo.getId());

        // 5.签发校验
        LoginInfo loginInfo = HIPSecurityUtils.getLoginInfo();
        List<DocOrderCommitVerifyAsQto> docOrderCommitVerifyAsQtoList = new ArrayList<>();
        // detailNto的数据
        if (CollectionUtils.isNotEmpty(operationOrderAsEto.getOperationDetailNtos())) {
            for (OperationApplyDetailAsNto detail : operationOrderAsEto.getOperationDetailNtos()) {
                DocOrderCommitVerifyAsQto docOrderCommitVerifyAsQto = new DocOrderCommitVerifyAsQto();
                docOrderCommitVerifyAsQto.setId(operationOrderAsEto.getId());
                docOrderCommitVerifyAsQto.setOrderServiceCode(detail.getServiceItemCode());
                docOrderCommitVerifyAsQto.setOrderClassCode(SystemTypeEnum.DGIMG.getCode());
                docOrderCommitVerifyAsQto.setCreateOrgCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
                docOrderCommitVerifyAsQto.setExecuteOrgCode(operationOrderAsEto.getExecuteOrgCode());
                docOrderCommitVerifyAsQto.setStatusCodeCode("NEW");
                docOrderCommitVerifyAsQto.setSubmitStaffId(loginInfo.getStaffId());
                docOrderCommitVerifyAsQto.setVisitCode(operationOrderAsEto.getVisitCode());
                docOrderCommitVerifyAsQtoList.add(docOrderCommitVerifyAsQto);
            }
        }

        // detailEto的数据
        if (CollectionUtils.isNotEmpty(operationOrderAsEto.getOperationDetailEtos())) {
            for (OperationApplyDetailAsEto detail : operationOrderAsEto.getOperationDetailEtos()) {
                DocOrderCommitVerifyAsQto docOrderCommitVerifyAsQto = new DocOrderCommitVerifyAsQto();
                docOrderCommitVerifyAsQto.setId(operationOrderAsEto.getId());
                docOrderCommitVerifyAsQto.setOrderServiceCode(detail.getServiceItemCode());
                docOrderCommitVerifyAsQto.setOrderClassCode(SystemTypeEnum.DGIMG.getCode());
                docOrderCommitVerifyAsQto.setCreateOrgCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
                docOrderCommitVerifyAsQto.setExecuteOrgCode(operationOrderAsEto.getExecuteOrgCode());
                docOrderCommitVerifyAsQto.setStatusCodeCode("NEW");
                docOrderCommitVerifyAsQto.setSubmitStaffId(loginInfo.getStaffId());
                docOrderCommitVerifyAsQto.setVisitCode(operationOrderAsEto.getVisitCode());
                docOrderCommitVerifyAsQtoList.add(docOrderCommitVerifyAsQto);
            }
        }

        List<DoctCommitOrderMsgTo> doctCommitOrderMsgTos = submitOrderVerifyUtil.orderCommitVerify(docOrderCommitVerifyAsQtoList);
        if (doctCommitOrderMsgTos != null) {
            // 如果校验返回值msg有值，说明有医嘱校验不合规，不能签发
            // 直接返回结果
            if (doctCommitOrderMsgTos.get(0).getMsg().size() > 0 ) {
                BusinessAssert.isTrue(doctCommitOrderMsgTos.get(0).getMsg().size() == 0, doctCommitOrderMsgTos.get(0).getMsg().get(0));
            }
        }

        // 5.签发
        cisIpdCpoeService.submitIpdOrderBatch(operationOrderAsEto.getVisitCode(), List.of(operationOrderAsEto.getId()));

        // 6.保存常用医嘱
        cisOrderCommonService.saveOrderCommons(this.buildCisOrderCommonNto(operationOrderAsEto));

        return doctCommitOrderMsgTos.get(0);
    }

    @Override
    public List<DictElementTo> getAnaesthesiaMode(String typeCode) {
        DictElementQto dictElementQto = new DictElementQto();
        dictElementQto.setParentElementCode(typeCode);
        List<DictElementTo> dictElementTos = dictElementService.getCustomDictElements(dictElementQto);
        if (CollectionUtils.isNotEmpty(dictElementTos)) {
            return dictElementTos;
        }

        return new ArrayList<>();
    }

    @Override
    public OperationApplyOrderAsTo getOperationApplyOrder(String orderId) {
        // 1.医嘱信息查询
        CisTemporaryOrderTo cisIpdOrderTo = (CisTemporaryOrderTo) cisIpdCpoeService.getCisIpdOrderById(orderId);
        BusinessAssert.notNull(cisIpdOrderTo, CisDocIpdBusinessErrorEnum.CIS_DOC_IPD_0001, "手术申请医嘱信息");

        OperationApplyOrderAsTo result = HIPBeanUtil.copy(cisIpdOrderTo, OperationApplyOrderAsTo.class);
        // 补录时间就是医嘱开始时间
        result.setRepairTime(cisIpdOrderTo.getEffectiveLowDate());

        // 2.申请单信息查询
        CisOperationApplyTo cisOperationApplyTo = cisOperationApplyService.getCisOperationApplyAllById(cisIpdOrderTo.getApplyCode());
        BusinessAssert.notNull(cisOperationApplyTo, CisDocIpdBusinessErrorEnum.CIS_DOC_IPD_0001, "手术申请信息");

        result = operationApplyIssuedAsAssembler.buildOperationApplyOrderAsTo(result, cisOperationApplyTo);

        return result;
    }

    // 构造常用医嘱实体
    private List<CisOrderCommonNto> buildCisOrderCommonNto(OperationApplyOrderAsNto operationApplyOrderAsNto) {
        List<CisOrderCommonNto> cisOrderCommonNtos = new ArrayList<>();
        for (OperationApplyDetailAsNto detailAsNto : operationApplyOrderAsNto.getOperationDetails()) {
            CisOrderCommonNto cisOrderCommonNto = new CisOrderCommonNto();
            cisOrderCommonNto.setServiceItemCode(detailAsNto.getServiceItemCode());
            cisOrderCommonNto.setServiceItemName(detailAsNto.getServiceItemName());
            cisOrderCommonNto.setSystemType(SystemTypeEnum.OPERATIONAPPLY);
            cisOrderCommonNto.setDocCode(HIPSecurityUtils.getLoginStaffId());
            cisOrderCommonNto.setId(operationApplyOrderAsNto.getId());
            cisOrderCommonNto.setOrgCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
            cisOrderCommonNto.setSaveFlag(false);
            cisOrderCommonNto.setExecutiveOrg(operationApplyOrderAsNto.getExecuteOrgCode());
            cisOrderCommonNto.setIntegral(Long.valueOf("1"));
            cisOrderCommonNtos.add(cisOrderCommonNto);
        }
        return cisOrderCommonNtos;
    }

    // 构造常用医嘱实体
    private List<CisOrderCommonNto> buildCisOrderCommonNto(OperationApplyOrderAsEto operationApplyOrderAsEto) {
        List<CisOrderCommonNto> cisOrderCommonNtos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(operationApplyOrderAsEto.getOperationDetailNtos())) {
            for (OperationApplyDetailAsNto detailAsNto : operationApplyOrderAsEto.getOperationDetailNtos()) {
                CisOrderCommonNto cisOrderCommonNto = new CisOrderCommonNto();
                cisOrderCommonNto.setServiceItemCode(detailAsNto.getServiceItemCode());
                cisOrderCommonNto.setServiceItemName(detailAsNto.getServiceItemName());
                cisOrderCommonNto.setSystemType(SystemTypeEnum.OPERATIONAPPLY);
                cisOrderCommonNto.setDocCode(HIPSecurityUtils.getLoginStaffId());
                cisOrderCommonNto.setId(operationApplyOrderAsEto.getId());
                cisOrderCommonNto.setOrgCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
                cisOrderCommonNto.setSaveFlag(false);
                cisOrderCommonNto.setExecutiveOrg(operationApplyOrderAsEto.getExecuteOrgCode());
                cisOrderCommonNto.setIntegral(Long.valueOf("1"));
                cisOrderCommonNtos.add(cisOrderCommonNto);
            }
        }

        if (CollectionUtils.isNotEmpty(operationApplyOrderAsEto.getOperationDetailEtos())) {
            for (OperationApplyDetailAsEto detailAsEto : operationApplyOrderAsEto.getOperationDetailEtos()) {
                CisOrderCommonNto cisOrderCommonNto = new CisOrderCommonNto();
                cisOrderCommonNto.setServiceItemCode(detailAsEto.getServiceItemCode());
                cisOrderCommonNto.setServiceItemName(detailAsEto.getServiceItemName());
                cisOrderCommonNto.setSystemType(SystemTypeEnum.OPERATIONAPPLY);
                cisOrderCommonNto.setDocCode(HIPSecurityUtils.getLoginStaffId());
                cisOrderCommonNto.setId(detailAsEto.getId());
                cisOrderCommonNto.setOrgCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
                cisOrderCommonNto.setSaveFlag(false);
                cisOrderCommonNto.setExecutiveOrg(operationApplyOrderAsEto.getExecuteOrgCode());
                cisOrderCommonNto.setIntegral(Long.valueOf("1"));
                cisOrderCommonNtos.add(cisOrderCommonNto);
            }
        }

        return cisOrderCommonNtos;
    }

    public List<CisApplyChargeNto> buildOperationCisApplyChargeNto(CisIpdDocOrderAsEto cisIpdDocOrderAsEto, CisIpdDocOrderDetailsAsEto cisIpdDocOrderDetailsAsEto) {
        List<CisApplyChargeNto> cisApplyChargeNtos = new ArrayList<>();

        // 查询开立医嘱的服务项目
        ServiceClinicItemTo serviceClinicItemTo = serviceClinicItemService.getServiceClinicItemByCode(cisIpdDocOrderDetailsAsEto.getServiceItemCode());

        BusinessAssert.notNull(serviceClinicItemTo, CisDocIpdBusinessErrorEnum.CIS_DOC_IPD_0001,"服务项目");

        if (CollectionUtils.isNotEmpty(serviceClinicItemTo.getServiceClinicPrices())) {
            for (ServiceClinicPriceTo priceTo : serviceClinicItemTo.getServiceClinicPrices()) {
                CisApplyChargeNto chargeNto = new CisApplyChargeNto();


                // 雪花算法生成ID
                chargeNto.setId("CG_" + HIPIDUtil.getNextIdString());
                chargeNto.setOrderId(cisIpdDocOrderAsEto.getId());
                chargeNto.setCisBaseApplyId(cisIpdDocOrderAsEto.getApplyId());
                // 目前，行开立医嘱，除了药品，只有输血医嘱才有明细，赋值明细ID
                if (cisIpdDocOrderAsEto.getOrderClass().equals(SystemTypeEnum.INPUTBLOODAPPLY.getCode()) || cisIpdDocOrderAsEto.getOrderClass().equals(SystemTypeEnum.OPERATIONAPPLY.getCode())) {
                    chargeNto.setDetailId(cisIpdDocOrderDetailsAsEto.getId());
                }
                chargeNto.setVisitCode(cisIpdDocOrderAsEto.getVisitCode());
                chargeNto.setPriceItemCode(priceTo.getPriceItemCode());
                chargeNto.setPriceItemName(priceTo.getPriceItemName());
                chargeNto.setPrice(priceTo.getPrice());
                // 界面修改不了数量，直接赋值priceTo的Quantity
                chargeNto.setNum(priceTo.getQuantity());
                chargeNto.setChageAmount(chargeNto.getPrice().multiply(new BigDecimal(chargeNto.getNum() + "")));
                chargeNto.setIsFixed(priceTo.getIsFixed());

                chargeNto.setUnitName(priceTo.getUnitName());
                chargeNto.setUnit(priceTo.getUnitCode());
                chargeNto.setStatusCode(CisStatusEnum.NEW);
                chargeNto.setChargeType(CisChargeTypeEnum.DOCT);
                chargeNto.setChargeFrequency(cisIpdDocOrderAsEto.getFrequency());
                chargeNto.setLimitConformFlag(cisIpdDocOrderDetailsAsEto.getLimitConformFlag());
                chargeNto.setSystemItemClass(priceTo.getSystemItemClass());

                // 设置执行科室信息，根据医嘱物价项目对照信息
                String executeOrgCode = ipdOrderExecuteOrgUtil.buildCiaApplyChargeExecuteOrgCode(priceTo, cisIpdDocOrderAsEto.getVisitCode(), cisIpdDocOrderAsEto.getExecuteOrgCode());
                WorkGroupTo workGroupTo = workGroupService.getWorkGroup(executeOrgCode);
                chargeNto.setExecuteOrgCode(executeOrgCode);
                chargeNto.setExecuteOrgName(workGroupTo.getName());

                cisApplyChargeNtos.add(chargeNto);
            }
        }


        return cisApplyChargeNtos;
    }


    private CisIpdOrderEto buildOperationOrderEto(CisIpdDocOrderAsEto cisIpdDocOrderAsEto) {
        CisIpdOrderEto cisIpdOrderEto = null;
        if ("1".equals(cisIpdDocOrderAsEto.getOrderType())) {
            CisLongTermOrderEto longTermOrderEto = new CisLongTermOrderEto();
            longTermOrderEto.setFirstDayTimepoint(cisIpdDocOrderAsEto.getFirstDayTimepoint());
            longTermOrderEto.setVisitCode(cisIpdDocOrderAsEto.getVisitCode());
            longTermOrderEto.setOrderContent(cisIpdDocOrderAsEto.getOrderContent());
            longTermOrderEto.setOrderClass(SystemTypeEnum.getValue(cisIpdDocOrderAsEto.getOrderClass()));
            longTermOrderEto.setExecuteOrgCode(cisIpdDocOrderAsEto.getExecuteOrgCode());
            longTermOrderEto.setExecuteOrgName(cisIpdDocOrderAsEto.getExecuteOrgName());
            longTermOrderEto.setReMark(cisIpdDocOrderAsEto.getReMark());
            longTermOrderEto.setReceiveOrgCode(cisIpdDocOrderAsEto.getReceiveOrgCode());
            longTermOrderEto.setEffectiveLowDate(LocalDateTime.now());
            longTermOrderEto.setSkinFlag(cisIpdDocOrderAsEto.getSkinFlag());
            longTermOrderEto.setEffectiveHighDate(cisIpdDocOrderAsEto.getEffectiveHighDate());
            cisIpdOrderEto = longTermOrderEto;
        } else if ("2".equals(cisIpdDocOrderAsEto.getOrderType())) {
            CisTemporaryOrderEto temporaryOrderEto = new CisTemporaryOrderEto();
            temporaryOrderEto.setVisitCode(cisIpdDocOrderAsEto.getVisitCode());
            temporaryOrderEto.setOrderContent(cisIpdDocOrderAsEto.getOrderContent());
            temporaryOrderEto.setOrderClass(SystemTypeEnum.getValue(cisIpdDocOrderAsEto.getOrderClass()));
            temporaryOrderEto.setExecuteOrgCode(cisIpdDocOrderAsEto.getExecuteOrgCode());
            temporaryOrderEto.setExecuteOrgName(cisIpdDocOrderAsEto.getExecuteOrgName());
            temporaryOrderEto.setReceiveOrgCode(cisIpdDocOrderAsEto.getReceiveOrgCode());
            temporaryOrderEto.setApplyCode(cisIpdDocOrderAsEto.getApplyId());
            temporaryOrderEto.setReMark(cisIpdDocOrderAsEto.getReMark());
            temporaryOrderEto.setRepairFlag(cisIpdDocOrderAsEto.getRepairFlag());
            if (cisIpdDocOrderAsEto.getRepairFlag().equals("1") && cisIpdDocOrderAsEto.getRepairTime() != null) {
                temporaryOrderEto.setEffectiveLowDate(cisIpdDocOrderAsEto.getRepairTime());
            } else {
                temporaryOrderEto.setEffectiveLowDate(cisIpdDocOrderAsEto.getEffectiveLowDate());
            }
            cisIpdOrderEto = temporaryOrderEto;
        }
        cisIpdOrderEto.setApplyCode(cisIpdDocOrderAsEto.getApplyId());

        return cisIpdOrderEto;
    }
}
