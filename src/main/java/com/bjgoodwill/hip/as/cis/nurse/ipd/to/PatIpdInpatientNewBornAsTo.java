package com.bjgoodwill.hip.as.cis.nurse.ipd.to;

import com.bjgoodwill.hip.ds.pat.in.hospital.inpatient.to.PatIpdInpatientTo;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(
        description = "新生儿患者列表信息响应To"
)
public class PatIpdInpatientNewBornAsTo extends PatIpdInpatientTo {

    private List<PatIpdInpatientTo> childrens;

    public List<PatIpdInpatientTo> getChildrens() {
        return this.childrens;
    }

    public void setChildrens(List<PatIpdInpatientTo> childrens) {
        this.childrens = childrens;
    }
}

