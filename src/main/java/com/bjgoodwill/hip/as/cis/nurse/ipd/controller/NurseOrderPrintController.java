package com.bjgoodwill.hip.as.cis.nurse.ipd.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.bjgoodwill.hip.as.cis.nurse.ipd.enums.CisNurseIpdBusinessErrorEnum;
import com.bjgoodwill.hip.as.cis.nurse.ipd.service.CisOrderPrintAsService;
import com.bjgoodwill.hip.as.cis.nurse.ipd.service.OrdersProofreadingAsService;
import com.bjgoodwill.hip.as.cis.nurse.ipd.to.EconTemplateAsNto;
import com.bjgoodwill.hip.as.cis.nurse.ipd.to.NurseOrderPrintDto;
import com.bjgoodwill.hip.as.cis.nurse.ipd.to.NurseOrderPrintPatAsTo;
import com.bjgoodwill.hip.as.cis.nurse.ipd.to.NurseOrderQueryPatAsTo;
import com.bjgoodwill.hip.common.bean.LoginInfo;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.to.CisOrderExecPlanTo;
import com.bjgoodwill.hip.ds.cis.cpoe.print.to.CisOrderPrintQto;
import com.bjgoodwill.hip.ds.econ.price.service.EconTemplateService;
import com.bjgoodwill.hip.ds.econ.price.to.EconTemplateQto;
import com.bjgoodwill.hip.ds.econ.price.to.EconTemplateTo;
import com.bjgoodwill.hip.security.bean.CurrentOrgInfo;
import com.bjgoodwill.hip.security.util.HIPCurrentOrgUtils;
import com.bjgoodwill.hip.security.util.HIPSecurityUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.List;

@RestController
@SaCheckPermission("cisNurse:nurseWorkstation")
@Tag(name = "医嘱打印应用服务", description = "医嘱打印应用服务类")
@RequestMapping("/cis/nurse/ipd/orderPrint")
public class NurseOrderPrintController {

    @Autowired
    private CisOrderPrintAsService cisOrderPrintAsService;

    @Autowired
    private OrdersProofreadingAsService ordersProofreadingAsService;

    @Autowired
    private EconTemplateService econTemplateService;

    @Operation(summary = "通过患者流水号查询医嘱信息", description = "通过患者流水号查询医嘱信息")
    @ApiResponse(description = "返回患者医嘱信息", content = @Content(mediaType = "application/json", schema = @Schema(implementation = NurseOrderQueryPatAsTo.class)))
    @GetMapping(value = "/patOrders")
    public NurseOrderQueryPatAsTo getPatOrdersByVisitCode(@RequestParam("visitCode") String visitCode){
        return cisOrderPrintAsService.getIpdOrdersByVisitCode(visitCode);
    }

    @Operation(summary = "打印弹窗——全部医嘱", description = "查询需要打印的全部医嘱")
    @ApiResponse(description = "返回全部医嘱信息", content = @Content(mediaType = "application/json", schema = @Schema(implementation = NurseOrderPrintPatAsTo.class)))
    @PostMapping(value = "/allOrders")
    public NurseOrderPrintPatAsTo getAllPrintOrders(@RequestBody CisOrderPrintQto cisOrderPrintQto){
        BusinessAssert.notNull(cisOrderPrintQto.getVisitCode(), CisNurseIpdBusinessErrorEnum.CIS_NURSE_IPD_0002, "患者流水号");
        BusinessAssert.notNull(cisOrderPrintQto.getOrderType(), CisNurseIpdBusinessErrorEnum.CIS_NURSE_IPD_0002, "医嘱类型");
        return cisOrderPrintAsService.getAllPrintOrders(cisOrderPrintQto);
    }

    @Operation(summary = "打印弹窗——续打医嘱", description = "查询需要打印的续打医嘱")
    @ApiResponse(description = "返回续打医嘱信息", content = @Content(mediaType = "application/json", schema = @Schema(implementation = NurseOrderPrintPatAsTo.class)))
    @PostMapping(value = "/continueOrders")
    public NurseOrderPrintPatAsTo getContinuePrintOrders(@RequestBody CisOrderPrintQto cisOrderPrintQto){
        BusinessAssert.notNull(cisOrderPrintQto.getVisitCode(), CisNurseIpdBusinessErrorEnum.CIS_NURSE_IPD_0002, "患者流水号");
        BusinessAssert.notNull(cisOrderPrintQto.getOrderType(), CisNurseIpdBusinessErrorEnum.CIS_NURSE_IPD_0002, "医嘱类型");
        return cisOrderPrintAsService.getContinuePrintOrders(cisOrderPrintQto);
    }

    @Operation(summary = "打印弹窗——历史医嘱", description = "查询上次打印的历史医嘱")
    @ApiResponse(description = "返回历史打印医嘱信息", content = @Content(mediaType = "application/json", schema = @Schema(implementation = NurseOrderPrintPatAsTo.class)))
    @PostMapping(value = "/historyOrders")
    public NurseOrderPrintPatAsTo getHistoryPrintOrders(@RequestBody CisOrderPrintQto cisOrderPrintQto){
        BusinessAssert.notNull(cisOrderPrintQto.getVisitCode(), CisNurseIpdBusinessErrorEnum.CIS_NURSE_IPD_0002, "患者流水号");
        BusinessAssert.notNull(cisOrderPrintQto.getOrderType(), CisNurseIpdBusinessErrorEnum.CIS_NURSE_IPD_0002, "医嘱类型");
        return cisOrderPrintAsService.getHistoryPrintOrders(cisOrderPrintQto);
    }

    @Operation(summary = "打印当前", description = "打印当前")
    @ApiResponse(description = "", content = @Content(mediaType = "application/json"))
    @PostMapping(value = "/printPage")
    public void printPageOrders(@RequestBody @Valid NurseOrderPrintDto nurseOrderPrintDto) {
        BusinessAssert.notNull(nurseOrderPrintDto.getPrintDetailTos(), CisNurseIpdBusinessErrorEnum.CIS_NURSE_IPD_0002, "待打印医嘱数据");
        cisOrderPrintAsService.printPageOrders(nurseOrderPrintDto);
    }

    @Operation(summary = "打印全部", description = "打印全部")
    @ApiResponse(description = "", content = @Content(mediaType = "application/json"))
    @PostMapping(value = "/printAll")
    public void printAllPageOrders(@RequestBody @Valid NurseOrderPrintDto nurseOrderPrintDto) {
        BusinessAssert.notNull(nurseOrderPrintDto.getPrintDetailTos(), CisNurseIpdBusinessErrorEnum.CIS_NURSE_IPD_0002, "待打印医嘱数据");
        cisOrderPrintAsService.printAllPageOrders(nurseOrderPrintDto);
    }

    @Operation(summary = "获取登录病区下的费用组套模板", description = "获取登录病区下的费用组套模板下拉数据源")
    @ApiResponse(description = "", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = EconTemplateTo.class))))
    @GetMapping("/econTemplates")
    public List<EconTemplateTo> getEconTemplates() {
        EconTemplateQto econTemplateQto = new EconTemplateQto();
        CurrentOrgInfo loginInfo = HIPCurrentOrgUtils.getCurrentOrgInfo();
        BusinessAssert.notNull(loginInfo, CisNurseIpdBusinessErrorEnum.CIS_NURSE_IPD_0001, "登录信息");
        econTemplateQto.setDeptCode(loginInfo.getWorkGroupCode());
        return econTemplateService.getEconTemplates(econTemplateQto);
    }

    @Operation(summary = "补费绑定", description = "补费绑定")
    @ApiResponse(description = "", content = @Content(mediaType = "application/json"))
    @PostMapping(value = "/supplementBinding")
    public void supplementBinding(@RequestBody EconTemplateAsNto econTemplateAsNto) {
        ordersProofreadingAsService.supplementBinding(econTemplateAsNto);
    }

    @Operation(summary = "通过医嘱ID查询执行单信息", description = "通过医嘱ID查询执行单信息")
    @ApiResponse(description = "返回执行单信息集合", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = CisOrderExecPlanTo.class))))
    @GetMapping(value = "/orderPlans")
    public List<CisOrderExecPlanTo> getCisOrderExecPlanByOrderId(@RequestParam("orderId") String orderId){
        return cisOrderPrintAsService.getCisOrderExecPlanByOrderId(orderId);
    }
}
