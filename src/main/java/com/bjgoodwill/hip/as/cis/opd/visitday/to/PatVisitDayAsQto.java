package com.bjgoodwill.hip.as.cis.opd.visitday.to;

import com.bjgoodwill.hip.common.bean.BaseQto;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "排班出诊日模板")
public class PatVisitDayAsQto extends BaseQto implements Serializable {

	@Serial
    private static final long serialVersionUID = -2919456713681131080L;

    @Schema(description = "资源类型")
    private String sourceType;
    @Schema(description = "出诊科室编码")
    private String deptCode;
    @Schema(description = "行政科室编码")
    private String admdvsCode;
    @Schema(description = "出诊院区编码")
    private String hospitalAreaCode;
    @Schema(description = "出诊医生编码")
    private String docCode;

    public String getSourceType() {
    	return sourceType;
    }

    public void setSourceType(String sourceType) {
    	this.sourceType = sourceType;
    }

    public String getDeptCode() {
    	return deptCode;
    }

    public void setDeptCode(String deptCode) {
    	this.deptCode = deptCode;
    }

    public String getAdmdvsCode() {
        return admdvsCode;
    }

    public void setAdmdvsCode(String admdvsCode) {
        this.admdvsCode = admdvsCode;
    }

    public String getHospitalAreaCode() {
        return hospitalAreaCode;
    }

    public void setHospitalAreaCode(String hospitalAreaCode) {
        this.hospitalAreaCode = hospitalAreaCode;
    }

    public String getDocCode() {
        return docCode;
    }

    public void setDocCode(String docCode) {
        this.docCode = docCode;
    }
}