package com.bjgoodwill.hip.as.cis.doc.ipd.to.cdrug;

import com.bjgoodwill.hip.ds.econ.price.to.EconServicePriceTo;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> l<PERSON><PERSON><PERSON><PERSON>
 * @description : 中草药 每付包数、每包ml数据源返回值
 * @createDate : 2024/11/29 14:27
 */
@Schema(description = "草药每付包数、每包ml数据源")
public class CdrugOpenParameterTo {

    @Schema(description = "草药每付包数")
    private List<Integer> cdrugPackNumList;

    @Schema(description = "草药每包ml")
    private List<Integer> cdrugPackMlList;

    @Schema(description = "代煎是否默认勾选，1是0否")
    private Integer decoction;

    @Schema(description = "协定处方代煎是否默认勾选，1是0否")
    private Integer prescriptionDecoction;

    @Schema(description = "代煎费用物价项目实体")
    private EconServicePriceTo econServicePriceTo;

    public CdrugOpenParameterTo() {
        setCdrugPackMlList(new ArrayList<>());
        setCdrugPackNumList(new ArrayList<>());
    }

    public List<Integer> getCdrugPackNumList() {
        return cdrugPackNumList;
    }

    public void setCdrugPackNumList(List<Integer> cdrugPackNumList) {
        this.cdrugPackNumList = cdrugPackNumList;
    }

    public List<Integer> getCdrugPackMlList() {
        return cdrugPackMlList;
    }

    public void setCdrugPackMlList(List<Integer> cdrugPackMlList) {
        this.cdrugPackMlList = cdrugPackMlList;
    }

    public Integer getDecoction() {
        return decoction;
    }

    public void setDecoction(Integer decoction) {
        this.decoction = decoction;
    }

    public Integer getPrescriptionDecoction() {
        return prescriptionDecoction;
    }

    public void setPrescriptionDecoction(Integer prescriptionDecoction) {
        this.prescriptionDecoction = prescriptionDecoction;
    }

    public EconServicePriceTo getEconServicePriceTo() {
        return econServicePriceTo;
    }

    public void setEconServicePriceTo(EconServicePriceTo econServicePriceTo) {
        this.econServicePriceTo = econServicePriceTo;
    }
}
