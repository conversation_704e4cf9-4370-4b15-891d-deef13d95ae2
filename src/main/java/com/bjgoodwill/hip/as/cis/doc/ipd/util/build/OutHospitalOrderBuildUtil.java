package com.bjgoodwill.hip.as.cis.doc.ipd.util.build;

import com.bjgoodwill.hip.as.cis.doc.ipd.to.CisIpdDocOrderAsNto;
import com.bjgoodwill.hip.common.util.HIPBeanUtil;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyTo;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisOutHospitalApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisOutHospitalApplyTo;
import com.bjgoodwill.hip.ds.cis.cpoe.order.to.CisIpdOrderNto;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> lian<PERSON><PERSON><PERSON>
 * @description :
 * @createDate : 2025/3/24 11:36
 */
@Service
public class OutHospitalOrderBuildUtil {

    /**
     * 复制粘贴用新
     * @param cisIpdOrderNto
     * @param cisBaseApplyTo
     * @return
     */
    public CisOutHospitalApplyNto buildCisIpdOrderNto(CisIpdOrderNto cisIpdOrderNto, CisBaseApplyTo cisBaseApplyTo) {
        CisOutHospitalApplyTo outHospitalApplyTo = (CisOutHospitalApplyTo) cisBaseApplyTo;
        CisOutHospitalApplyNto cisOutHospitalApplyNto = HIPBeanUtil.copy(outHospitalApplyTo, CisOutHospitalApplyNto.class);
        cisOutHospitalApplyNto.setId(cisIpdOrderNto.getApplyCode());
        cisOutHospitalApplyNto.setOrderID(cisIpdOrderNto.getId());
        cisOutHospitalApplyNto.setVisitCode(cisIpdOrderNto.getVisitCode());
        cisOutHospitalApplyNto.setPatMiCode(cisIpdOrderNto.getPatMiCode());
        cisOutHospitalApplyNto.setDeptNurseCode(cisIpdOrderNto.getDeptNurseCode());
        cisOutHospitalApplyNto.setDeptNurseName(cisIpdOrderNto.getDeptNurseName());

        // todo 出院时间先默认赋当前时间
        cisOutHospitalApplyNto.setOutDate(LocalDateTime.now());

        return cisOutHospitalApplyNto;
    }

}
