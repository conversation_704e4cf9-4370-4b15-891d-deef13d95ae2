package com.bjgoodwill.hip.as.cis.opd.visitresource.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.bjgoodwill.hip.as.cis.opd.visitresource.service.PatOpdVisitResourceAsService;
import com.bjgoodwill.hip.as.cis.opd.visitresource.to.*;
import com.bjgoodwill.hip.ds.econ.fixed.service.EconOpdFixedService;
import com.bjgoodwill.hip.ds.econ.fixed.to.EconOpdFixedTo;
import com.bjgoodwill.hip.ds.org.api.service.DeptService;
import com.bjgoodwill.hip.ds.org.api.service.StaffService;
import com.bjgoodwill.hip.ds.org.api.to.HospitalAreaTo;
import com.bjgoodwill.hip.ds.org.api.to.StaffLocalTo;
import com.bjgoodwill.hip.ds.org.api.to.WorkGroupTo;
import com.bjgoodwill.hip.ds.pat.schedule.enmus.PatVisitSourceTypeEnum;
import com.bjgoodwill.hip.ds.pat.schedule.service.PatTimeDescribeService;
import com.bjgoodwill.hip.ds.pat.schedule.service.PatVisitResourceService;
import com.bjgoodwill.hip.ds.pat.schedule.to.PatTimeDescribeQto;
import com.bjgoodwill.hip.ds.pat.schedule.to.PatTimeDescribeTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * Description:门诊出诊资源维护
 *
 * <AUTHOR>
 * &#064;date 2025/3/19 下午3:04
 */
@RestController
@Tag(name = "门诊出诊资源维护", description = "门诊出诊资源维护服务类")
@RequestMapping("/cis/opd/visit-resource")
@SaCheckPermission("cisOpd:PatVisitResource")
public class PatOpdVisitResourceController {

    @Autowired
    PatOpdVisitResourceAsService patOpdVisitResourceAsService;

    @Autowired
    PatVisitResourceService patVisitResourceService;

    @Autowired
    PatTimeDescribeService patTimeDescribeService;

    @Autowired
    EconOpdFixedService econOpdFixedService;

    @Autowired
    StaffService staffService;

    @Autowired
    DeptService deptService;

    @Operation(summary = "根据条件查询门诊出诊资源")
    @ApiResponse(description = "根据条件查询门诊出诊资源", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = PatVisitResourceShowAsTo.class))))
    @PostMapping("/get/show-list")
    public List<PatVisitResourceShowAsTo> getList(@RequestBody PatVisitResourceAsQto qto) {
        return patOpdVisitResourceAsService.getList(qto);
    }

    @Operation(summary = "出诊资源维护弹窗显示")
    @ApiResponse(description = "出诊资源维护弹窗显示", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = PatVisitResourceAsTo.class))))
    @PostMapping("/get/resource-list")
    public List<PatVisitResourceAsTo> getResourceList(@RequestBody PatVisitResourceAsQto qto) {
        return patOpdVisitResourceAsService.getResourceList(qto);
    }

    @Operation(summary = "新增门诊科室号出诊资源")
    @PostMapping("/dept-resource")
    PatVisitResourceAsTo createDeptResource(@RequestBody @Valid PatVisitResourceAsNto nto) {
        return patOpdVisitResourceAsService.createDeptResource(nto);
    }

    @Operation(summary = "批量新增门诊科室号出诊资源")
    @PostMapping("/dept-resources")
    void createDeptResource(@RequestBody @Valid PatVisitResourceListAsNto nto) {
        patOpdVisitResourceAsService.createDeptResources(nto);
    }

    @Operation(summary = "新增门诊医生号出诊资源")
    @PostMapping("/doctor-resource")
    PatVisitResourceAsTo createDoctorResource(@RequestBody @Valid PatVisitResourceAsNto nto) {
        return patOpdVisitResourceAsService.createDoctorResource(nto);
    }

    @Operation(summary = "批量更新门诊科室/医生号出诊资源")
    @PutMapping("/update/list")
    public void updateDeptResource(@RequestBody @Valid List<PatVisitResourceAsEto> etos) {
        patOpdVisitResourceAsService.udpateResources(etos);
    }

    @Operation(summary = "根据ID启用出诊资源")
    @PutMapping("/{id}/enabled")
    void enabled(@PathVariable("id") String id) {
        patVisitResourceService.enable(id);
    }

    @Operation(summary = "根据ID禁用出诊资源")
    @PutMapping("/{id}/disable")
    void disable(@PathVariable("id") String id) {
        patVisitResourceService.disable(id);
    }

    @Operation(summary = "获取页面出诊资源类型显示数据源")
    @ApiResponse(description = "获取页面出诊资源类型显示数据源", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = CallBackAsTo.class))))
    @GetMapping("/get/source-types")
    public List<CallBackAsTo> getSourceTypes() {
        List<CallBackAsTo> list = new ArrayList<>();
        list.add(new CallBackAsTo("", "全部"));
        PatVisitSourceTypeEnum[] values = PatVisitSourceTypeEnum.values();
        for (PatVisitSourceTypeEnum value : values) {
            list.add(new CallBackAsTo(value.getCode(), value.getName()));
        }
        return list;
    }

    @Operation(summary = "获取页面院区显示数据源")
    @ApiResponse(description = "获取页面院区显示数据源", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = WorkGroupTo.class))))
    @GetMapping("/get/hosarea-list")
    List<HospitalAreaTo> getHospitalAreaList() {
        List<HospitalAreaTo> list = new ArrayList<>();
        HospitalAreaTo hospitalAreaTo = new HospitalAreaTo();
        hospitalAreaTo.setCode("");
        hospitalAreaTo.setName("全部");
        list.add(hospitalAreaTo);
        list.addAll(deptService.getAllHospital());
        return list;
    }

    @Operation(summary = "获取页面出诊科室显示数据源")
    @ApiResponse(description = "获取页面出诊科室显示数据源", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = WorkGroupTo.class))))
    @GetMapping("/get/dept-list")
    List<WorkGroupTo> getWorkGroupList(@RequestParam(value = "hospitalAreaCode", required = false) String hospitalAreaCode) {
        return patOpdVisitResourceAsService.getWorkGroupList(hospitalAreaCode);
    }

    @Operation(summary = "获取页面出诊午别显示数据源")
    @ApiResponse(description = "获取页面出诊午别显示数据源", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = PatTimeDescribeTo.class))))
    @GetMapping("/get/time-list")
    List<PatTimeDescribeTo> getPatTimeDescribeList(@RequestParam(value = "hospitalAreaCode", required = false) String hospitalAreaCode) {
        PatTimeDescribeQto qto = new PatTimeDescribeQto();
//        qto.setHospitalAreaCode(hospitalAreaCode);
        qto.setEnabled(Boolean.TRUE);
        return patTimeDescribeService.getPatTimeDescribes(qto);
    }

    @Operation(summary = "获取页面挂号级别显示数据源")
    @ApiResponse(description = "获取页面挂号级别显示数据源", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = EconOpdFixedTo.class))))
    @GetMapping("/get/fixed-list")
    List<EconOpdFixedTo> getEconOpdFixedList(@RequestParam(value = "hospitalAreaCode", required = false) String hospitalAreaCode) {
        return econOpdFixedService.getEnabledList(null);
    }

    @Operation(summary = "获取页面医生显示数据源")
    @ApiResponse(description = "获取页面医生显示数据源", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = StaffLocalTo.class))))
    @GetMapping("/get/doctor-list")
    List<StaffLocalTo> getDoctorList() {
        return staffService.getDoctorStaffLocal();
    }
}
