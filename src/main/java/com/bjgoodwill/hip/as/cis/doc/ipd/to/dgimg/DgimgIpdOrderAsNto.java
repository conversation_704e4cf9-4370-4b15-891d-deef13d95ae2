package com.bjgoodwill.hip.as.cis.doc.ipd.to.dgimg;

import com.bjgoodwill.hip.as.cis.doc.ipd.to.CisIpdDocOrderAsNto;
import com.bjgoodwill.hip.as.cis.doc.ipd.to.operation.ApplyDiagnosisAsNto;
import com.bjgoodwill.hip.ds.cis.apply.dgimg.to.CisDgimgApplyDetailNto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> liangyuqing
 * @description :
 * @createDate : 2024/12/6 14:51
 */
@Schema(description = "住院医生站-检查医嘱信息-新增")
public class DgimgIpdOrderAsNto extends CisIpdDocOrderAsNto implements Serializable {

    @Schema(description = "检查-检查注意事项")
    private String precautions;
    @Schema(description = "检查-病历及查体摘要")
    private String medrecordAndExamabstract;
    @Schema(description = "检查-体格及其他检查")
    private String physiqueAndExam;
    @Schema(description = "检查-分类")
    private String dgimgClass;
    @Schema(description = "检查-子分类")
    private String dgimgSubClass;
    @Schema(description = "检查-相关辅检")
    private String auxiliaryInspection;
    @Schema(description = "检查-检查目的")
    private String checkPurpose;
    @Schema(description = "检查-申请单预约标识")
    private String applyBookId;
    @Schema(description = "检查-既往病理检查结果")
    private String previousPathologicalExamin;
    @Schema(description = "检查-设备类型")
    private String deviceType;
    @Schema(description = "检查-是否过敏史")
    private Boolean allergicHistoryFlag;
    @Schema(description = "检查-是否职业病史")
    private Boolean occupationalDiseasesFlag;
    @Schema(description = "检查-临床病史")
    private String clinicalHistory;
    @Schema(description = "检查-是否传染病史")
    private Boolean contagiousDiseaseHistoryFlag;
    @Schema(description = "检查-检查申请明细信息")
    private List<DgimgIpdOrderDetailAsNto> dgimgIpdOrderDetailAsNtoList;

    @Schema(description = "检查-临床诊断列表")
    private List<ApplyDiagnosisAsNto> applyDiagnosisAsNtos;

    public String getPrecautions() {
        return precautions;
    }

    public void setPrecautions(String precautions) {
        this.precautions = StringUtils.trimToNull(precautions);
    }

    public String getMedrecordAndExamabstract() {
        return medrecordAndExamabstract;
    }

    public void setMedrecordAndExamabstract(String medrecordAndExamabstract) {
        this.medrecordAndExamabstract = StringUtils.trimToNull(medrecordAndExamabstract);
    }

    public String getPhysiqueAndExam() {
        return physiqueAndExam;
    }

    public void setPhysiqueAndExam(String physiqueAndExam) {
        this.physiqueAndExam = StringUtils.trimToNull(physiqueAndExam);
    }

    public String getDgimgClass() {
        return dgimgClass;
    }

    public void setDgimgClass(String dgimgClass) {
        this.dgimgClass = StringUtils.trimToNull(dgimgClass);
    }

    public String getDgimgSubClass() {
        return dgimgSubClass;
    }

    public void setDgimgSubClass(String dgimgSubClass) {
        this.dgimgSubClass = StringUtils.trimToNull(dgimgSubClass);
    }

    @NotBlank(message = "相关辅检不能为空！")
    public String getAuxiliaryInspection() {
        return auxiliaryInspection;
    }

    public void setAuxiliaryInspection(String auxiliaryInspection) {
        this.auxiliaryInspection = StringUtils.trimToNull(auxiliaryInspection);
    }

    @NotBlank(message = "检查目的不能为空！")
    public String getCheckPurpose() {
        return checkPurpose;
    }

    public void setCheckPurpose(String checkPurpose) {
        this.checkPurpose = StringUtils.trimToNull(checkPurpose);
    }

    @Size(max = 50, message = "申请单预约标识长度不能超过50个字符！")
    public String getApplyBookId() {
        return applyBookId;
    }

    public void setApplyBookId(String applyBookId) {
        this.applyBookId = StringUtils.trimToNull(applyBookId);
    }

    public String getPreviousPathologicalExamin() {
        return previousPathologicalExamin;
    }

    public void setPreviousPathologicalExamin(String previousPathologicalExamin) {
        this.previousPathologicalExamin = StringUtils.trimToNull(previousPathologicalExamin);
    }

    @NotEmpty(message = "设备类型不能为空！")
    public String getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(String deviceType) {
        this.deviceType = deviceType;
    }


    public Boolean getAllergicHistoryFlag() {
        return allergicHistoryFlag;
    }

    public void setAllergicHistoryFlag(Boolean allergicHistoryFlag) {
        this.allergicHistoryFlag = allergicHistoryFlag;
    }

    public Boolean getOccupationalDiseasesFlag() {
        return occupationalDiseasesFlag;
    }

    public void setOccupationalDiseasesFlag(Boolean occupationalDiseasesFlag) {
        this.occupationalDiseasesFlag = occupationalDiseasesFlag;
    }

    @NotBlank(message = "临床病史不能为空！")
    public String getClinicalHistory() {
        return clinicalHistory;
    }

    public void setClinicalHistory(String clinicalHistory) {
        this.clinicalHistory = clinicalHistory;
    }

    public Boolean getContagiousDiseaseHistoryFlag() {
        return contagiousDiseaseHistoryFlag;
    }

    public void setContagiousDiseaseHistoryFlag(Boolean contagiousDiseaseHistoryFlag) {
        this.contagiousDiseaseHistoryFlag = contagiousDiseaseHistoryFlag;
    }

    public List<DgimgIpdOrderDetailAsNto> getDgimgIpdOrderDetailAsNtoList() {
        return dgimgIpdOrderDetailAsNtoList;
    }

    public void setDgimgIpdOrderDetailAsNtoList(List<DgimgIpdOrderDetailAsNto> dgimgIpdOrderDetailAsNtoList) {
        this.dgimgIpdOrderDetailAsNtoList = dgimgIpdOrderDetailAsNtoList;
    }

    public List<ApplyDiagnosisAsNto> getApplyDiagnosisAsNtos() {
        return applyDiagnosisAsNtos;
    }

    public void setApplyDiagnosisAsNtos(List<ApplyDiagnosisAsNto> applyDiagnosisAsNtos) {
        this.applyDiagnosisAsNtos = applyDiagnosisAsNtos;
    }
}
