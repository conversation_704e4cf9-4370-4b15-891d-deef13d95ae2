package com.bjgoodwill.hip.as.cis.nurse.ipd.service;

import com.bjgoodwill.hip.as.cis.nurse.ipd.to.EconPrepayAsQto;
import com.bjgoodwill.hip.as.cis.nurse.ipd.to.EconPrepayAsTo;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * <AUTHOR>
 * @Date 2025/4/2 10:30
 */
@Tag(name = "住院患者预交金查询应用服务", description = "患者预交金查询应用服务类")
public interface PatPrePaymentInquireAsService {

    EconPrepayAsTo getPrePayInfo(EconPrepayAsQto econPrepayAsQto);
}
