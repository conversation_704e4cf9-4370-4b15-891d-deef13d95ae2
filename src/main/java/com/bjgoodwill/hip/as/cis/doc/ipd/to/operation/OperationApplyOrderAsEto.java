package com.bjgoodwill.hip.as.cis.doc.ipd.to.operation;

import com.bjgoodwill.hip.as.cis.doc.ipd.to.CisIpdDocOrderAsEto;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @description :
 * @createDate : 2025/2/24 17:03
 */
public class OperationApplyOrderAsEto extends CisIpdDocOrderAsEto implements Serializable {

    @Schema(description = "血型")
    private String bloodType;
    @Schema(description = "RH血型")
    private String bloodTypeRh;
    @Schema(description = "手术分类")
    private String operationType;
    @Schema(description = "手术分类名称")
    private String operationTypeName;
    @Schema(description = "是否隔离")
    private Boolean isOlation;
    @Schema(description = "麻醉类型")
    private String anaesthesiaType;
    @Schema(description = "麻醉类型名称")
    private String anaesthesiaTypeName;
    @Schema(description = "麻醉方式")
    private String anaesthesiaMode;
    @Schema(description = "麻醉方式名称")
    private String anaesthesiaModeName;
    @Schema(description = " 特殊手术属性")
    private String operationSpecialAttr;
    @Schema(description = "拟手术日期")
    private LocalDateTime operationDate;
    @Schema(description = "拟预计用时")
    private Double operationTime;
    @Schema(description = "切口类型")
    private String incisionType;
    @Schema(description = "切口等级 字典WoundGrade")
    private String incisionLevel;
    @Schema(description = "切口等级名称 字典WoundGrade")
    private String incisionLevelName;
    @Schema(description = "合并手术")
    private Boolean mergeFlag;
    @Schema(description = "手术审批类型：1常规手术，2急诊手术；默认1")
    private String apprOperTypc;
    @Schema(description = "手术医生")
    private String operationDoctor;
    @Schema(description = "手术医生名称")
    private String operationDoctorName;
    @Schema(description = "手术医生科室")
    private String operationDoctorOrg;
    @Schema(description = "手术医生科室名称")
    private String operationDoctorOrgName;
    @Schema(description = "基本操作")
    private String operation;
    @Schema(description = "入路")
    private String approach;
    @Schema(description = "入路名称")
    private String approachName;
    @Schema(description = "辅助器械")
    private String instrument;
    @Schema(description = "辅助器械名称")
    private String instrumentName;
    @Schema(description = "第一助手编码")
    private String firstAidesCode;
    @Schema(description = "第一助手名称")
    private String firstAidesName;
    @Schema(description = "第二助手编码")
    private String secondAidesCode;
    @Schema(description = "第二助手名称")
    private String secondAidesName;
    @Schema(description = "第三助手编码")
    private String thirdAidesCode;
    @Schema(description = "第三助手名称")
    private String thirdAidesName;
    @Schema(description = "第四助手编码")
    private String fourthAidesCode;
    @Schema(description = "第四助手名称")
    private String fourthAidesName;
    @Schema(description = "协作科室")
    private String coopDept;
    @Schema(description = "非计划再次重组手术")
    private Boolean reorganize;
    @Schema(description = "术后计划转ICU")
    private Boolean transferIcu;
    @Schema(description = "术中冰冻病理检查")
    private Boolean freezePathology;
    @Schema(description = "不开申请")
    private Boolean notApply;
    @Schema(description = "新增申请单明细")
    private List<OperationApplyDetailAsNto> operationDetailNtos;
    @Schema(description = "新增申请单明细")
    private List<OperationApplyDetailAsEto> operationDetailEtos;
    @Schema(description = "申请单诊断Nto")
    private List<ApplyDiagnosisAsNto> applyDiagnosisAsNtos;

    @Override
    public String getBloodType() {
        return bloodType;
    }

    @Override
    public void setBloodType(String bloodType) {
        this.bloodType = bloodType;
    }

    public String getBloodTypeRh() {
        return bloodTypeRh;
    }

    public void setBloodTypeRh(String bloodTypeRh) {
        this.bloodTypeRh = bloodTypeRh;
    }

    public String getOperationType() {
        return operationType;
    }

    public void setOperationType(String operationType) {
        this.operationType = operationType;
    }

    public String getOperationTypeName() {
        return operationTypeName;
    }

    public void setOperationTypeName(String operationTypeName) {
        this.operationTypeName = operationTypeName;
    }

    public Boolean getOlation() {
        return isOlation;
    }

    public void setOlation(Boolean olation) {
        isOlation = olation;
    }

    public String getAnaesthesiaType() {
        return anaesthesiaType;
    }

    public void setAnaesthesiaType(String anaesthesiaType) {
        this.anaesthesiaType = anaesthesiaType;
    }

    public String getAnaesthesiaTypeName() {
        return anaesthesiaTypeName;
    }

    public void setAnaesthesiaTypeName(String anaesthesiaTypeName) {
        this.anaesthesiaTypeName = anaesthesiaTypeName;
    }

    public String getAnaesthesiaMode() {
        return anaesthesiaMode;
    }

    public void setAnaesthesiaMode(String anaesthesiaMode) {
        this.anaesthesiaMode = anaesthesiaMode;
    }

    public String getAnaesthesiaModeName() {
        return anaesthesiaModeName;
    }

    public void setAnaesthesiaModeName(String anaesthesiaModeName) {
        this.anaesthesiaModeName = anaesthesiaModeName;
    }

    public String getOperationSpecialAttr() {
        return operationSpecialAttr;
    }

    public void setOperationSpecialAttr(String operationSpecialAttr) {
        this.operationSpecialAttr = operationSpecialAttr;
    }

    public LocalDateTime getOperationDate() {
        return operationDate;
    }

    public void setOperationDate(LocalDateTime operationDate) {
        this.operationDate = operationDate;
    }

    public Double getOperationTime() {
        return operationTime;
    }

    public void setOperationTime(Double operationTime) {
        this.operationTime = operationTime;
    }

    public String getIncisionType() {
        return incisionType;
    }

    public void setIncisionType(String incisionType) {
        this.incisionType = incisionType;
    }

    public String getIncisionLevel() {
        return incisionLevel;
    }

    public void setIncisionLevel(String incisionLevel) {
        this.incisionLevel = incisionLevel;
    }

    public String getIncisionLevelName() {
        return incisionLevelName;
    }

    public void setIncisionLevelName(String incisionLevelName) {
        this.incisionLevelName = incisionLevelName;
    }

    public Boolean getMergeFlag() {
        return mergeFlag;
    }

    public void setMergeFlag(Boolean mergeFlag) {
        this.mergeFlag = mergeFlag;
    }

    public String getApprOperTypc() {
        return apprOperTypc;
    }

    public void setApprOperTypc(String apprOperTypc) {
        this.apprOperTypc = apprOperTypc;
    }

    public String getOperationDoctor() {
        return operationDoctor;
    }

    public void setOperationDoctor(String operationDoctor) {
        this.operationDoctor = operationDoctor;
    }

    public String getOperationDoctorName() {
        return operationDoctorName;
    }

    public void setOperationDoctorName(String operationDoctorName) {
        this.operationDoctorName = operationDoctorName;
    }

    public String getOperationDoctorOrg() {
        return operationDoctorOrg;
    }

    public void setOperationDoctorOrg(String operationDoctorOrg) {
        this.operationDoctorOrg = operationDoctorOrg;
    }

    public String getOperationDoctorOrgName() {
        return operationDoctorOrgName;
    }

    public void setOperationDoctorOrgName(String operationDoctorOrgName) {
        this.operationDoctorOrgName = operationDoctorOrgName;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public String getApproach() {
        return approach;
    }

    public void setApproach(String approach) {
        this.approach = approach;
    }

    public String getApproachName() {
        return approachName;
    }

    public void setApproachName(String approachName) {
        this.approachName = approachName;
    }

    public String getInstrument() {
        return instrument;
    }

    public void setInstrument(String instrument) {
        this.instrument = instrument;
    }

    public String getInstrumentName() {
        return instrumentName;
    }

    public void setInstrumentName(String instrumentName) {
        this.instrumentName = instrumentName;
    }

    public String getFirstAidesCode() {
        return firstAidesCode;
    }

    public void setFirstAidesCode(String firstAidesCode) {
        this.firstAidesCode = firstAidesCode;
    }

    public String getFirstAidesName() {
        return firstAidesName;
    }

    public void setFirstAidesName(String firstAidesName) {
        this.firstAidesName = firstAidesName;
    }

    public String getSecondAidesCode() {
        return secondAidesCode;
    }

    public void setSecondAidesCode(String secondAidesCode) {
        this.secondAidesCode = secondAidesCode;
    }

    public String getSecondAidesName() {
        return secondAidesName;
    }

    public void setSecondAidesName(String secondAidesName) {
        this.secondAidesName = secondAidesName;
    }

    public String getThirdAidesCode() {
        return thirdAidesCode;
    }

    public void setThirdAidesCode(String thirdAidesCode) {
        this.thirdAidesCode = thirdAidesCode;
    }

    public String getThirdAidesName() {
        return thirdAidesName;
    }

    public void setThirdAidesName(String thirdAidesName) {
        this.thirdAidesName = thirdAidesName;
    }

    public String getFourthAidesCode() {
        return fourthAidesCode;
    }

    public void setFourthAidesCode(String fourthAidesCode) {
        this.fourthAidesCode = fourthAidesCode;
    }

    public String getFourthAidesName() {
        return fourthAidesName;
    }

    public void setFourthAidesName(String fourthAidesName) {
        this.fourthAidesName = fourthAidesName;
    }

    public String getCoopDept() {
        return coopDept;
    }

    public void setCoopDept(String coopDept) {
        this.coopDept = coopDept;
    }

    public Boolean getReorganize() {
        return reorganize;
    }

    public void setReorganize(Boolean reorganize) {
        this.reorganize = reorganize;
    }

    public Boolean getTransferIcu() {
        return transferIcu;
    }

    public void setTransferIcu(Boolean transferIcu) {
        this.transferIcu = transferIcu;
    }

    public Boolean getFreezePathology() {
        return freezePathology;
    }

    public void setFreezePathology(Boolean freezePathology) {
        this.freezePathology = freezePathology;
    }

    public List<OperationApplyDetailAsNto> getOperationDetailNtos() {
        return operationDetailNtos;
    }

    public void setOperationDetailNtos(List<OperationApplyDetailAsNto> operationDetailNtos) {
        this.operationDetailNtos = operationDetailNtos;
    }

    public List<OperationApplyDetailAsEto> getOperationDetailEtos() {
        return operationDetailEtos;
    }

    public void setOperationDetailEtos(List<OperationApplyDetailAsEto> operationDetailEtos) {
        this.operationDetailEtos = operationDetailEtos;
    }

    public List<ApplyDiagnosisAsNto> getApplyDiagnosisAsNtos() {
        return applyDiagnosisAsNtos;
    }

    public void setApplyDiagnosisAsNtos(List<ApplyDiagnosisAsNto> applyDiagnosisAsNtos) {
        this.applyDiagnosisAsNtos = applyDiagnosisAsNtos;
    }

    public Boolean getNotApply() {
        return notApply;
    }

    public void setNotApply(Boolean notApply) {
        this.notApply = notApply;
    }
}
