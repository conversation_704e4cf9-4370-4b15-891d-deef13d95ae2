package com.bjgoodwill.hip.as.cis.doc.opd.to.diagnosis;

import com.bjgoodwill.hip.ds.cis.opdcpoe.diag.to.CisOpdDiagnoseTo;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR>
 * @Date 2025/5/8 10:52
 */
@Schema(description = "门诊诊断")
public class CisOpdDiagnoseAsTo extends CisOpdDiagnoseTo {

    @Schema(
            description = "诊断前缀"
    )
    private String prefix;
    @Schema(
            description = "诊断后缀"
    )
    private String suffix;

    @Schema(
            description = "中医证型编码"
    )
    private String tcmSyndromeType;
    @Schema(
            description = "中医证型名称"
    )
    private String tcmSyndromeTypeName;

    public String getPrefix() {
        return prefix;
    }

    public void setPrefix(String prefix) {
        this.prefix = prefix;
    }

    public String getSuffix() {
        return suffix;
    }

    public void setSuffix(String suffix) {
        this.suffix = suffix;
    }

    public String getTcmSyndromeType() {
        return tcmSyndromeType;
    }

    public void setTcmSyndromeType(String tcmSyndromeType) {
        this.tcmSyndromeType = tcmSyndromeType;
    }

    public String getTcmSyndromeTypeName() {
        return tcmSyndromeTypeName;
    }

    public void setTcmSyndromeTypeName(String tcmSyndromeTypeName) {
        this.tcmSyndromeTypeName = tcmSyndromeTypeName;
    }
}
