package com.bjgoodwill.hip.as.cis.doc.opd.service.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.as.cis.doc.opd.enums.CisDocOpdBusinessErrorEnum;
import com.bjgoodwill.hip.as.cis.doc.opd.service.OperationOrderHandleService;
import com.bjgoodwill.hip.as.cis.doc.opd.to.doctor.CisApplyChargeAsTo;
import com.bjgoodwill.hip.as.cis.doc.opd.to.doctor.OpdOrderBaseApplyAsTo;
import com.bjgoodwill.hip.as.cis.doc.opd.to.operation.OperationApplyDetailAsNto;
import com.bjgoodwill.hip.as.cis.doc.opd.to.operation.OperationOrderDto;
import com.bjgoodwill.hip.as.cis.doc.opd.util.OpdOrderAsUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.DiagnosisEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.DiagnosisTypeEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.HospitalModelEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.common.util.HIPBeanUtil;
import com.bjgoodwill.hip.common.util.HIPIDUtil;
import com.bjgoodwill.hip.ds.base.mi.milist.service.MiLimitHilistService;
import com.bjgoodwill.hip.ds.base.mi.milist.to.MiLimitHilistTo;
import com.bjgoodwill.hip.ds.cis.apply.apply.service.CisBaseApplyService;
import com.bjgoodwill.hip.ds.cis.apply.charge.enmus.CisChargeTypeEnum;
import com.bjgoodwill.hip.ds.cis.apply.charge.to.CisApplyChargeNto;
import com.bjgoodwill.hip.ds.cis.apply.diag.to.ApplyDiagnosisNto;
import com.bjgoodwill.hip.ds.cis.apply.operation.service.CisOperationApplyService;
import com.bjgoodwill.hip.ds.cis.apply.operation.to.*;
import com.bjgoodwill.hip.ds.cis.medicineitem.price.to.ServiceClinicPriceTo;
import com.bjgoodwill.hip.ds.cis.medicineitem.serviceItem.service.ServiceClinicItemService;
import com.bjgoodwill.hip.ds.cis.medicineitem.serviceItem.to.OperationApplyTo;
import com.bjgoodwill.hip.ds.cis.medicineitem.serviceItem.to.ServiceClinicItemTo;
import com.bjgoodwill.hip.ds.cis.opdcpoe.diag.service.CisOpdDiagnoseService;
import com.bjgoodwill.hip.ds.cis.opdcpoe.diag.to.CisOpdDiagnoseTo;
import com.bjgoodwill.hip.ds.cis.opdcpoe.order.service.CisOpdOrderService;
import com.bjgoodwill.hip.ds.cis.opdcpoe.order.to.CisOpdEto;
import com.bjgoodwill.hip.ds.cis.opdcpoe.order.to.CisOpdOrderEto;
import com.bjgoodwill.hip.ds.cis.opdcpoe.order.to.CisOpdOrderNto;
import com.bjgoodwill.hip.ds.cis.opdcpoe.order.to.CisOpdOrderTo;
import com.bjgoodwill.hip.ds.term.api.service.DictElementService;
import com.bjgoodwill.hip.ds.term.api.to.DictElementQto;
import com.bjgoodwill.hip.ds.term.api.to.DictElementTo;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.seata.spring.annotation.GlobalTransactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> liangyuqing
 * @description :
 * @createDate : 2025/5/27 11:30
 */
@Service
public class OperationOrderHandleServiceImpl implements OperationOrderHandleService {

    @Autowired
    @Qualifier("com.bjgoodwill.hip.ds.cis.medicineitem.serviceItem.service.ServiceClinicItemTangibleService")
    private ServiceClinicItemService serviceClinicItemService;

    @Autowired
    private DictElementService dictElementService;

    @Autowired
    private OpdOrderAsUtil opdOrderAsUtil;

    @Autowired
    private CisOpdOrderService cisOpdOrderService;

    @Autowired
    @Qualifier("com.bjgoodwill.hip.ds.cis.apply.apply.service.CisBaseApplyServiceTangibleFeign")
    private CisBaseApplyService cisBaseApplyService;

    @Autowired
    private CisOperationApplyService cisOperationApplyService;

    @Autowired
    private CisOpdDiagnoseService cisOpdDiagnoseService;

    @Resource
    private MiLimitHilistService miLimitHilistService;

    private OperationOrderHandleService getSelf() {
        return SpringUtil.getBean(OperationOrderHandleService.class);
    }

    @Override
    public List<ApplyDiagnosisNto> getOperationDiagnosis(String visitCode, String treatmentCode) {
        List<ApplyDiagnosisNto> result = new ArrayList<>();

        List<CisOpdDiagnoseTo> cisOpdDiagnoseToList = cisOpdDiagnoseService.getCisOpdDiagnoseByTreatmentCode(treatmentCode);
        cisOpdDiagnoseToList = cisOpdDiagnoseToList.stream().filter(o -> o.getDiagnosisClass().equals(DiagnosisEnum.WMD)).toList();
        if (CollectionUtils.isNotEmpty(cisOpdDiagnoseToList)) {
            // 返回术前诊断，若术前诊断为空，则返回全部西医诊断
            List<CisOpdDiagnoseTo> OTypeDiagnosisList = cisOpdDiagnoseToList.stream().filter(cisOpdDiagnoseTo -> DiagnosisTypeEnum.O.equals(cisOpdDiagnoseTo.getDiagnosisType())).toList();
            if (CollectionUtils.isNotEmpty(OTypeDiagnosisList)) {
                OTypeDiagnosisList.forEach(cisOpdDiagnoseTo -> {
                    ApplyDiagnosisNto applyDiagnosisNto = new ApplyDiagnosisNto();
                    applyDiagnosisNto.setDiagCode(cisOpdDiagnoseTo.getDiagnosisCode());
                    applyDiagnosisNto.setDiagName(cisOpdDiagnoseTo.getDiagnosisName());
                    result.add(applyDiagnosisNto);
                });
            } else {
                cisOpdDiagnoseToList.forEach(cisOpdDiagnoseTo -> {
                    ApplyDiagnosisNto applyDiagnosisNto = new ApplyDiagnosisNto();
                    applyDiagnosisNto.setDiagCode(cisOpdDiagnoseTo.getDiagnosisCode());
                    applyDiagnosisNto.setDiagName(cisOpdDiagnoseTo.getDiagnosisName());
                    result.add(applyDiagnosisNto);
                });
            }
        }

        return result;
    }

    @Override
    public List<OperationApplyTo> getOperationItems(String text, String workGroupCode, String hospitalAreaCode) {
        List<OperationApplyTo> operationApplyTos = new ArrayList<>();
        List<SystemTypeEnum> systemTypeEnums = new ArrayList<>();
        systemTypeEnums.add(SystemTypeEnum.OPERATIONAPPLY);

        if (StringUtils.isNotEmpty(text)) {
            List<ServiceClinicItemTo> serviceClinicItemToList = serviceClinicItemService.queryServiceClinicItemListByinPutText(
                    systemTypeEnums, 10000, text, workGroupCode, HospitalModelEnum.门诊, hospitalAreaCode);
            if (CollectionUtils.isNotEmpty(serviceClinicItemToList)) {
                operationApplyTos = HIPBeanUtil.copy(serviceClinicItemToList, OperationApplyTo.class);
            }
        }

        return operationApplyTos;
    }

    @GlobalTransactional
    @Override
    public List<String> insertOrUpdateOperationOrder(OperationOrderDto operationOrderDto) {
        List<String> orderIds = new ArrayList<>();

        if (StringUtils.isEmpty(operationOrderDto.getId())) {
            // 新增检查医嘱
            // 医嘱通用信息赋值
            operationOrderDto.setId(HIPIDUtil.getNextIdString());
            operationOrderDto.setOrderClass(SystemTypeEnum.OPERATIONAPPLY);
            operationOrderDto.setExtTypeCode(operationOrderDto.getDetails().get(0).getExtTypeCode());

            operationOrderDto.getDetails().forEach(o -> o.setId(HIPIDUtil.getNextIdString()));

            CisOpdOrderNto cisOpdOrderNto = HIPBeanUtil.copy(operationOrderDto, CisOpdOrderNto.class);

            //医嘱申请单通用信息赋值
            CisOperationApplyNto cisOperationApplyNto = new CisOperationApplyNto();
            cisOperationApplyNto = (CisOperationApplyNto) opdOrderAsUtil.assignApplyCommonValues(operationOrderDto, cisOperationApplyNto);
            // 手术申请子类信息赋值
            cisOperationApplyNto.setBloodType(operationOrderDto.getBloodType());
            cisOperationApplyNto.setBloodTypeRh(operationOrderDto.getBloodTypeRh());
            cisOperationApplyNto.setOperationType(operationOrderDto.getOperationType());
            cisOperationApplyNto.setOperationTypeName(operationOrderDto.getOperationTypeName());
            cisOperationApplyNto.setIsOlation(operationOrderDto.getOpdOrderBaseApplyAsTo().getIsOlation());
            cisOperationApplyNto.setAnaesthesiaType(operationOrderDto.getAnaesthesiaType());
            cisOperationApplyNto.setAnaesthesiaTypeName(operationOrderDto.getAnaesthesiaTypeName());
            cisOperationApplyNto.setAnaesthesiaMode(operationOrderDto.getAnaesthesiaMode());
            cisOperationApplyNto.setAnaesthesiaModeName(operationOrderDto.getAnaesthesiaModeName());
            cisOperationApplyNto.setOperationSpecialAttr(operationOrderDto.getOperationSpecialAttr());
            cisOperationApplyNto.setOperationDate(operationOrderDto.getOperationDate());
            cisOperationApplyNto.setOperationTime(operationOrderDto.getOperationTime());
            cisOperationApplyNto.setIncisionType(operationOrderDto.getIncisionType());
            cisOperationApplyNto.setIncisionLevel(operationOrderDto.getIncisionLevel());
            cisOperationApplyNto.setIncisionLevelName(operationOrderDto.getIncisionLevelName());
            cisOperationApplyNto.setMergeFlag(operationOrderDto.getMergeFlag());
            cisOperationApplyNto.setApprOperTypc(operationOrderDto.getApprOperTypc());
            cisOperationApplyNto.setOperationDoctor(operationOrderDto.getOperationDoctor());
            cisOperationApplyNto.setOperationDoctorName(operationOrderDto.getOperationDoctorName());
            cisOperationApplyNto.setOperationDoctorOrg(operationOrderDto.getOperationDoctorOrg());
            cisOperationApplyNto.setOperationDoctorOrgName(operationOrderDto.getOperationDoctorOrgName());
            cisOperationApplyNto.setOperation(operationOrderDto.getOperation());
            cisOperationApplyNto.setApproach(operationOrderDto.getApproach());
            cisOperationApplyNto.setApproachName(operationOrderDto.getApproachName());
            cisOperationApplyNto.setInstrument(operationOrderDto.getInstrument());
            cisOperationApplyNto.setInstrumentName(operationOrderDto.getInstrumentName());
            cisOperationApplyNto.setFirstAidesCode(operationOrderDto.getFirstAidesCode());
            cisOperationApplyNto.setFirstAidesName(operationOrderDto.getFirstAidesName());
            cisOperationApplyNto.setSecondAidesCode(operationOrderDto.getSecondAidesCode());
            cisOperationApplyNto.setSecondAidesName(operationOrderDto.getSecondAidesName());
            cisOperationApplyNto.setThirdAidesCode(operationOrderDto.getThirdAidesCode());
            cisOperationApplyNto.setThirdAidesName(operationOrderDto.getThirdAidesName());
            cisOperationApplyNto.setFourthAidesCode(operationOrderDto.getFourthAidesCode());
            cisOperationApplyNto.setFourthAidesName(operationOrderDto.getFourthAidesName());
            cisOperationApplyNto.setCoopDept(operationOrderDto.getCoopDept());
            cisOperationApplyNto.setReorganize(operationOrderDto.getReorganize());
            cisOperationApplyNto.setTransferIcu(operationOrderDto.getTransferIcu());
            cisOperationApplyNto.setFreezePathology(operationOrderDto.getFreezePathology());
            cisOperationApplyNto.setNum(1d);

            // 服务项目编码，名称
            String serviceItemCode = "";
            String serviceItemName = "";

            // 手术申请明细及对应charge信息
            List<CisOperationApplyDetailNto> detailNtos = new ArrayList<>();
            List<CisApplyChargeNto> chargeNtos = new ArrayList<>();

            // 查询医嘱项目信息
            List<ServiceClinicItemTo> serviceClinicItemTos = serviceClinicItemService.getServiceClinicItemListByCode(
                    operationOrderDto.getDetails().stream().map(OperationApplyDetailAsNto::getServiceItemCode).toList());
            Map<String, ServiceClinicItemTo> serviceClinicItemToMap = serviceClinicItemTos.stream().collect(
                    Collectors.toMap(ServiceClinicItemTo::getServiceItemCode, v -> v));

            for (OperationApplyDetailAsNto detailAsNto : operationOrderDto.getDetails()) {
                CisOperationApplyDetailNto nto = new CisOperationApplyDetailNto();
                BusinessAssert.notNull(detailAsNto.getOperationCode(), CisDocOpdBusinessErrorEnum.BUS_CIS_DOC_OPD_0003, "【" + detailAsNto.getServiceItemName() + "】未维护对应术式，请维护后保存！");
                nto = HIPBeanUtil.copy(detailAsNto, CisOperationApplyDetailNto.class);
                nto.setId(HIPIDUtil.getNextIdString());
                nto.setVisitCode(operationOrderDto.getVisitCode());
                detailNtos.add(nto);

                serviceItemCode += nto.getServiceItemCode() + ",";
                serviceItemName += nto.getServiceItemName() + ",";

                // 构建charge信息
                // 收费项目信息
                Map<String, ServiceClinicPriceTo> priceToMap = serviceClinicItemToMap.get(nto.getServiceItemCode()).getServiceClinicPrices().stream().collect(
                        Collectors.toMap(ServiceClinicPriceTo::getPriceItemCode, v -> v));
                if (CollectionUtils.isNotEmpty(detailAsNto.getCisApplyChargeAs())) {
                    List<CisApplyChargeNto> insertChargeNtos = HIPBeanUtil.copy(detailAsNto.getCisApplyChargeAs(), CisApplyChargeNto.class);
                    for (CisApplyChargeNto chargeNto : insertChargeNtos) {
                        chargeNto.setId(HIPIDUtil.getNextIdString());
                        chargeNto.setDetailId(nto.getId());
                        chargeNto.setOrderId(operationOrderDto.getId());
                        chargeNto.setVisitCode(operationOrderDto.getVisitCode());
                        if (chargeNto.getPrice() != null && chargeNto.getNum() != null) {
                            chargeNto.setChageAmount(chargeNto.getPrice().multiply(BigDecimal.valueOf(chargeNto.getNum())).setScale(1, RoundingMode.HALF_UP));
                        }
                        // 构建执行科室
                        if (StringUtils.isNotEmpty(operationOrderDto.getOpdOrderBaseApplyAsTo().getExecutorOrgCode())) {
                            ServiceClinicPriceTo serviceClinicPriceTo = priceToMap.get(chargeNto.getPriceItemCode());
                            String executeOrgCode = opdOrderAsUtil.buildCiaApplyChargeExecuteOrgCode(serviceClinicPriceTo, operationOrderDto.getOpdOrderBaseApplyAsTo().getExecutorOrgCode());
                            String executeOrgName = opdOrderAsUtil.getWorkGroupTo(executeOrgCode).getName();

                            chargeNto.setExecuteOrgCode(executeOrgCode);
                            chargeNto.setExecuteOrgName(executeOrgName);
                        }

                        chargeNtos.add(chargeNto);
                    }
                }

            }

            cisOperationApplyNto.setServiceItemCode(serviceItemCode.substring(0, serviceItemCode.length() - 1));
            cisOperationApplyNto.setServiceItemName(serviceItemName.substring(0, serviceItemName.length() - 1));

            cisOperationApplyNto.setDetails(detailNtos);
            cisOperationApplyNto.setCisApplyCharges(chargeNtos);

            // 手术诊断
            if (CollectionUtils.isNotEmpty(operationOrderDto.getOpdOrderBaseApplyAsTo().getApplyDiagnosisNtos())) {
                cisOperationApplyNto.setApplyDiagnosisNtos(HIPBeanUtil.copy(operationOrderDto.getOpdOrderBaseApplyAsTo().getApplyDiagnosisNtos(), ApplyDiagnosisNto.class));
                cisOperationApplyNto.getApplyDiagnosisNtos().forEach(diagnosisNto -> {
                    diagnosisNto.setId(HIPIDUtil.getNextIdString());
                });
            }

            cisOpdOrderNto.setCisBaseApply(cisOperationApplyNto);
            cisOpdOrderNto.setOrderServiceCode(cisOperationApplyNto.getServiceItemCode());

            cisOpdOrderService.createCisOpdOrder(new ArrayList<>(Arrays.asList(cisOpdOrderNto)));
        } else {
            // 修改
            // 判断医嘱当前状态，如果结算，不允许修改
            BusinessAssert.isFalse(opdOrderAsUtil.judgeIsCharged(Arrays.asList(operationOrderDto.getId())), CisDocOpdBusinessErrorEnum.BUS_CIS_DOC_OPD_0003, "医嘱已计费，请退费后再进行修改！");

            CisOperationApplyTo cisOperationApplyTo = (CisOperationApplyTo) cisBaseApplyService.getCisBaseApplyById(operationOrderDto.getApplyCode());

            CisOpdOrderEto cisOpdOrderEto = HIPBeanUtil.copy(operationOrderDto, CisOpdOrderEto.class);

            CisOperationApplyEto cisOperationApplyEto = HIPBeanUtil.copy(operationOrderDto.getOpdOrderBaseApplyAsTo(), CisOperationApplyEto.class);
            cisOperationApplyEto.setBloodType(operationOrderDto.getBloodType());
            cisOperationApplyEto.setBloodTypeRh(operationOrderDto.getBloodTypeRh());
            cisOperationApplyEto.setOperationType(operationOrderDto.getOperationType());
            cisOperationApplyEto.setOperationTypeName(operationOrderDto.getOperationTypeName());
            cisOperationApplyEto.setIsOlation(operationOrderDto.getOpdOrderBaseApplyAsTo().getIsOlation());
            cisOperationApplyEto.setAnaesthesiaType(operationOrderDto.getAnaesthesiaType());
            cisOperationApplyEto.setAnaesthesiaTypeName(operationOrderDto.getAnaesthesiaTypeName());
            cisOperationApplyEto.setAnaesthesiaMode(operationOrderDto.getAnaesthesiaMode());
            cisOperationApplyEto.setAnaesthesiaModeName(operationOrderDto.getAnaesthesiaModeName());
            cisOperationApplyEto.setOperationSpecialAttr(operationOrderDto.getOperationSpecialAttr());
            cisOperationApplyEto.setOperationDate(operationOrderDto.getOperationDate());
            cisOperationApplyEto.setOperationTime(operationOrderDto.getOperationTime());
            cisOperationApplyEto.setIncisionType(operationOrderDto.getIncisionType());
            cisOperationApplyEto.setIncisionLevel(operationOrderDto.getIncisionLevel());
            cisOperationApplyEto.setIncisionLevelName(operationOrderDto.getIncisionLevelName());
            cisOperationApplyEto.setMergeFlag(operationOrderDto.getMergeFlag());
            cisOperationApplyEto.setApprOperTypc(operationOrderDto.getApprOperTypc());
            cisOperationApplyEto.setOperationDoctor(operationOrderDto.getOperationDoctor());
            cisOperationApplyEto.setOperationDoctorName(operationOrderDto.getOperationDoctorName());
            cisOperationApplyEto.setOperationDoctorOrg(operationOrderDto.getOperationDoctorOrg());
            cisOperationApplyEto.setOperationDoctorOrgName(operationOrderDto.getOperationDoctorOrgName());
            cisOperationApplyEto.setOperation(operationOrderDto.getOperation());
            cisOperationApplyEto.setApproach(operationOrderDto.getApproach());
            cisOperationApplyEto.setApproachName(operationOrderDto.getApproachName());
            cisOperationApplyEto.setInstrument(operationOrderDto.getInstrument());
            cisOperationApplyEto.setInstrumentName(operationOrderDto.getInstrumentName());
            cisOperationApplyEto.setFirstAidesCode(operationOrderDto.getFirstAidesCode());
            cisOperationApplyEto.setFirstAidesName(operationOrderDto.getFirstAidesName());
            cisOperationApplyEto.setSecondAidesCode(operationOrderDto.getSecondAidesCode());
            cisOperationApplyEto.setSecondAidesName(operationOrderDto.getSecondAidesName());
            cisOperationApplyEto.setThirdAidesCode(operationOrderDto.getThirdAidesCode());
            cisOperationApplyEto.setThirdAidesName(operationOrderDto.getThirdAidesName());
            cisOperationApplyEto.setFourthAidesCode(operationOrderDto.getFourthAidesCode());
            cisOperationApplyEto.setFourthAidesName(operationOrderDto.getFourthAidesName());
            cisOperationApplyEto.setCoopDept(operationOrderDto.getCoopDept());
            cisOperationApplyEto.setReorganize(operationOrderDto.getReorganize());
            cisOperationApplyEto.setTransferIcu(operationOrderDto.getTransferIcu());
            cisOperationApplyEto.setFreezePathology(operationOrderDto.getFreezePathology());
            cisOperationApplyEto.setNum(1d);

            List<OperationApplyDetailAsNto> reInsertAsDetails = operationOrderDto.getDetails();
            // 构建新增明细及费用信息，明细重新插入，删除原明细
            if (CollectionUtils.isNotEmpty(reInsertAsDetails)) {
                List<CisOperationApplyDetailNto> insertDetailNtos = new ArrayList<>();
                List<CisApplyChargeNto> chargeNtos = new ArrayList<>();

                // 查询医嘱项目信息
                List<ServiceClinicItemTo> serviceClinicItemTos = serviceClinicItemService.getServiceClinicItemListByCode(
                        reInsertAsDetails.stream().map(OperationApplyDetailAsNto::getServiceItemCode).toList());
                Map<String, ServiceClinicItemTo> serviceClinicItemToMap = serviceClinicItemTos.stream().collect(
                        Collectors.toMap(ServiceClinicItemTo::getServiceItemCode, v -> v));

                reInsertAsDetails.forEach(insertDetailAsNto -> {
                    BusinessAssert.notNull(insertDetailAsNto.getOperationCode(), CisDocOpdBusinessErrorEnum.BUS_CIS_DOC_OPD_0003, "【" + insertDetailAsNto.getServiceItemName() + "】未维护对应术式，请维护后保存！");

                    insertDetailAsNto.setVisitCode(operationOrderDto.getVisitCode());
                    insertDetailAsNto.setId(HIPIDUtil.getNextIdString());
                    insertDetailNtos.add(HIPBeanUtil.copy(insertDetailAsNto, CisOperationApplyDetailNto.class));

                    // 收费项目信息
                    Map<String, ServiceClinicPriceTo> priceToMap = serviceClinicItemToMap.get(insertDetailAsNto.getServiceItemCode()).getServiceClinicPrices().stream().collect(
                            Collectors.toMap(ServiceClinicPriceTo::getPriceItemCode, v -> v));

                    insertDetailAsNto.getCisApplyChargeAs().forEach(cisApplyChargeAsTo -> {
                        CisApplyChargeNto chargeNto = HIPBeanUtil.copy(cisApplyChargeAsTo, CisApplyChargeNto.class);

                        chargeNto.setId(HIPIDUtil.getNextIdString());
                        chargeNto.setDetailId(insertDetailAsNto.getId());
                        chargeNto.setOrderId(operationOrderDto.getId());
                        chargeNto.setVisitCode(operationOrderDto.getVisitCode());
                        if (chargeNto.getPrice() != null && chargeNto.getNum() != null) {
                            chargeNto.setChageAmount(chargeNto.getPrice().multiply(BigDecimal.valueOf(chargeNto.getNum())).setScale(1, RoundingMode.HALF_UP));
                        }
                        // 构建执行科室
                        if (StringUtils.isNotEmpty(operationOrderDto.getOpdOrderBaseApplyAsTo().getExecutorOrgCode())) {
                            ServiceClinicPriceTo serviceClinicPriceTo = priceToMap.get(chargeNto.getPriceItemCode());
                            String executeOrgCode = opdOrderAsUtil.buildCiaApplyChargeExecuteOrgCode(serviceClinicPriceTo, operationOrderDto.getOpdOrderBaseApplyAsTo().getExecutorOrgCode());
                            String executeOrgName = opdOrderAsUtil.getWorkGroupTo(executeOrgCode).getName();

                            chargeNto.setExecuteOrgCode(executeOrgCode);
                            chargeNto.setExecuteOrgName(executeOrgName);
                        }

                        chargeNtos.add(chargeNto);
                    });
                });

                cisOperationApplyEto.setCisApplyChargeNtos(chargeNtos);
                cisOperationApplyEto.setDetailNtos(insertDetailNtos);
            }

            // 删除明细id列表构造，原明细全部删除
            List<CisOperationApplyDetailTo> cisOperationApplyDetailTos = cisOperationApplyTo.getCisOperationApplyDetailToList();
            List<String> deleteDetailIds = cisOperationApplyDetailTos.stream().map(CisOperationApplyDetailTo::getId).toList();
            cisOperationApplyEto.setDeleteDetailIds(deleteDetailIds);


            // 诊断信息
            cisOperationApplyEto.setApplyDiagnosisNtos(HIPBeanUtil.copy(operationOrderDto.getOpdOrderBaseApplyAsTo().getApplyDiagnosisNtos(), ApplyDiagnosisNto.class));
            cisOperationApplyEto.getApplyDiagnosisNtos().forEach(applyDiagnosisNto -> {
                if (StringUtils.isEmpty(applyDiagnosisNto.getId())) {
                    applyDiagnosisNto.setId(HIPIDUtil.getNextIdString());
                }
            });

            // 项目编码和名称
            String serviceItemCode = "";
            String serviceItemName = "";
            for (OperationApplyDetailAsNto detail : operationOrderDto.getDetails()) {
                serviceItemCode += detail.getServiceItemCode() + ",";
                serviceItemName += detail.getServiceItemName() + ",";
            }
            cisOperationApplyEto.setServiceItemCode(serviceItemCode.substring(0, serviceItemCode.length() - 1));
            cisOperationApplyEto.setServiceItemName(serviceItemName.substring(0, serviceItemName.length() - 1));
            cisOpdOrderEto.setOrderServiceCode(cisOperationApplyEto.getServiceItemCode());
            cisOpdOrderEto.setCisBaseApply(cisOperationApplyEto);

            cisOpdOrderService.updateCisOpdOrder(operationOrderDto.getId(), cisOpdOrderEto);
        }

        orderIds.add(operationOrderDto.getId());
        return orderIds;
    }

    @GlobalTransactional
    @Override
    public void insertOrUpdateOperationOrderAndSubmit(OperationOrderDto operationOrderDto) {
        List<String> orderIds = this.getSelf().insertOrUpdateOperationOrder(operationOrderDto);
        cisOpdOrderService.submit(operationOrderDto.getVisitCode(), orderIds, new CisOpdEto());
    }

    @Override
    public List<DictElementTo> getAnaesthesiaMode(String typeCode) {
        DictElementQto dictElementQto = new DictElementQto();
        dictElementQto.setParentElementCode(typeCode);
        List<DictElementTo> dictElementTos = dictElementService.getCustomDictElements(dictElementQto);
        if (CollectionUtils.isNotEmpty(dictElementTos)) {
            return dictElementTos;
        }

        return new ArrayList<>();
    }

    @Override
    public OperationOrderDto getOperationOrder(String orderId) {
        OperationOrderDto operationOrderDto = new OperationOrderDto();

        CisOpdOrderTo cisOpdOrderTo = cisOpdOrderService.getCisOpdOrderById(orderId);
        BusinessAssert.notNull(cisOpdOrderTo, CisDocOpdBusinessErrorEnum.BUS_CIS_DOC_OPD_0003, "医嘱信息");

        operationOrderDto = HIPBeanUtil.copy(cisOpdOrderTo, OperationOrderDto.class);

        CisOperationApplyTo cisOperationApplyTo = cisOperationApplyService.getCisOperationApplyAllById(cisOpdOrderTo.getApplyCode());
        BusinessAssert.notNull(cisOperationApplyTo, CisDocOpdBusinessErrorEnum.BUS_CIS_DOC_OPD_0003, "手术申请单");
        OpdOrderBaseApplyAsTo applyAsTo = HIPBeanUtil.copy(cisOperationApplyTo, OpdOrderBaseApplyAsTo.class);
        applyAsTo.setIsOlation(cisOperationApplyTo.getIsOlation());
        applyAsTo.setApplyDiagnosisNtos(HIPBeanUtil.copy(cisOperationApplyTo.getApplyDiagnoses(), ApplyDiagnosisNto.class));
        operationOrderDto.setOpdOrderBaseApplyAsTo(applyAsTo);

        // 手术申请单子类属性
        operationOrderDto.setBloodType(cisOperationApplyTo.getBloodType());
        operationOrderDto.setBloodTypeRh(cisOperationApplyTo.getBloodTypeRh());
        operationOrderDto.setOperationType(cisOperationApplyTo.getOperationType());
        operationOrderDto.setOperationTypeName(cisOperationApplyTo.getOperationTypeName());
        operationOrderDto.setAnaesthesiaType(cisOperationApplyTo.getAnaesthesiaType());
        operationOrderDto.setAnaesthesiaTypeName(cisOperationApplyTo.getAnaesthesiaTypeName());
        operationOrderDto.setAnaesthesiaMode(cisOperationApplyTo.getAnaesthesiaMode());
        operationOrderDto.setAnaesthesiaModeName(cisOperationApplyTo.getAnaesthesiaModeName());
        operationOrderDto.setOperationSpecialAttr(cisOperationApplyTo.getOperationSpecialAttr());
        operationOrderDto.setOperationDate(cisOperationApplyTo.getOperationDate());
        operationOrderDto.setOperationTime(cisOperationApplyTo.getOperationTime());
        operationOrderDto.setIncisionType(cisOperationApplyTo.getIncisionType());
        operationOrderDto.setIncisionLevel(cisOperationApplyTo.getIncisionLevel());
        operationOrderDto.setIncisionLevelName(cisOperationApplyTo.getIncisionLevelName());
        operationOrderDto.setMergeFlag(cisOperationApplyTo.getMergeFlag());
        operationOrderDto.setApprOperTypc(cisOperationApplyTo.getApprOperTypc());
        operationOrderDto.setOperationDoctor(cisOperationApplyTo.getOperationDoctor());
        operationOrderDto.setOperationDoctorName(cisOperationApplyTo.getOperationDoctorName());
        operationOrderDto.setOperationDoctorOrg(cisOperationApplyTo.getOperationDoctorOrg());
        operationOrderDto.setOperationDoctorOrgName(cisOperationApplyTo.getOperationDoctorOrgName());
        operationOrderDto.setOperation(cisOperationApplyTo.getOperation());
        operationOrderDto.setApproach(cisOperationApplyTo.getApproach());
        operationOrderDto.setApproachName(cisOperationApplyTo.getApproachName());
        operationOrderDto.setInstrument(cisOperationApplyTo.getInstrument());
        operationOrderDto.setInstrumentName(cisOperationApplyTo.getInstrumentName());
        operationOrderDto.setFirstAidesCode(cisOperationApplyTo.getFirstAidesCode());
        operationOrderDto.setFirstAidesName(cisOperationApplyTo.getFirstAidesName());
        operationOrderDto.setSecondAidesCode(cisOperationApplyTo.getSecondAidesCode());
        operationOrderDto.setSecondAidesName(cisOperationApplyTo.getSecondAidesName());
        operationOrderDto.setThirdAidesCode(cisOperationApplyTo.getThirdAidesCode());
        operationOrderDto.setThirdAidesName(cisOperationApplyTo.getThirdAidesName());
        operationOrderDto.setFourthAidesCode(cisOperationApplyTo.getFourthAidesCode());
        operationOrderDto.setFourthAidesName(cisOperationApplyTo.getFourthAidesName());
        operationOrderDto.setCoopDept(cisOperationApplyTo.getCoopDept());
        operationOrderDto.setReorganize(cisOperationApplyTo.getReorganize());
        operationOrderDto.setTransferIcu(cisOperationApplyTo.getTransferIcu());
        operationOrderDto.setFreezePathology(cisOperationApplyTo.getFreezePathology());

        operationOrderDto.setDetails(HIPBeanUtil.copy(cisOperationApplyTo.getCisOperationApplyDetailToList(), OperationApplyDetailAsNto.class));

        // 构建费用信息
        List<CisApplyChargeAsTo> cisApplyChargeAsTos = HIPBeanUtil.copy(cisOperationApplyTo.getCisApplyCharges(), CisApplyChargeAsTo.class);
        this.buildApplyChargeMiLimit(cisApplyChargeAsTos);
        Map<String, List<CisApplyChargeAsTo>> cisApplyChargeAsTosMap = cisApplyChargeAsTos.stream().filter(cisApplyChargeAsTo -> StringUtils.isNotEmpty(cisApplyChargeAsTo.getDetailId())).collect(Collectors.groupingBy(CisApplyChargeAsTo::getDetailId));
        operationOrderDto.getDetails().forEach(operationApplyDetailAsNto -> {
            if (cisApplyChargeAsTosMap.containsKey(operationApplyDetailAsNto.getId())) {
                operationApplyDetailAsNto.setCisApplyChargeAs(cisApplyChargeAsTosMap.get(operationApplyDetailAsNto.getId()));
            }
        });

        return operationOrderDto;
    }

    @Override
    public List<CisApplyChargeAsTo> getServiceClincPriceList(String serviceItemCode) {
        ServiceClinicItemTo serviceClinicItemTo = serviceClinicItemService.getServiceClinicItemByCode(serviceItemCode);
        BusinessAssert.notNull(serviceClinicItemTo, CisDocOpdBusinessErrorEnum.BUS_CIS_DOC_OPD_0001, "医嘱项目");
        BusinessAssert.notEmpty(serviceClinicItemTo.getServiceClinicPrices(), CisDocOpdBusinessErrorEnum.BUS_CIS_DOC_OPD_0003, "医嘱项目未对照收费项目");

        List<CisApplyChargeAsTo> result = new ArrayList<>();
        for (ServiceClinicPriceTo serviceClinicPrice : serviceClinicItemTo.getServiceClinicPrices()) {
            CisApplyChargeAsTo chargeAsTo = new CisApplyChargeAsTo();

            chargeAsTo.setIsFixed(serviceClinicPrice.getIsFixed());
            chargeAsTo.setPriceItemCode(serviceClinicPrice.getPriceItemCode());
            chargeAsTo.setPriceItemName(serviceClinicPrice.getPriceItemName());
            chargeAsTo.setPackageSpec(serviceClinicPrice.getSpec());
            chargeAsTo.setPrice(serviceClinicPrice.getPrice());
            chargeAsTo.setUnit(serviceClinicPrice.getUnitCode());
            chargeAsTo.setUnitName(serviceClinicPrice.getUnitName());
            chargeAsTo.setNum(serviceClinicPrice.getQuantity());
            chargeAsTo.setChageAmount(chargeAsTo.getPrice().multiply(new BigDecimal(chargeAsTo.getNum())));
            chargeAsTo.setSystemItemClass(serviceClinicPrice.getSystemItemClass());
            chargeAsTo.setChargeType(CisChargeTypeEnum.DOCT);

            result.add(chargeAsTo);
        }

        // 查询限制维护项目
        this.buildApplyChargeMiLimit(result);

        return result;
    }

    private void buildApplyChargeMiLimit(List<CisApplyChargeAsTo> cisApplyChargeAsTos) {
        try {
            List<String> priceItemCodes = cisApplyChargeAsTos.stream().map(CisApplyChargeAsTo::getPriceItemCode).toList();
            Map<String, MiLimitHilistTo> miLimitHilistToMap = miLimitHilistService.getMiLimitHilistByHisCodes(priceItemCodes.toArray(new String[priceItemCodes.size()]));
            for (CisApplyChargeAsTo cisApplyChargeAsTo : cisApplyChargeAsTos) {
                if (miLimitHilistToMap != null && miLimitHilistToMap.containsKey(cisApplyChargeAsTo.getPriceItemCode())) {
                    MiLimitHilistTo miLimitHilistTo = miLimitHilistToMap.get(cisApplyChargeAsTo.getPriceItemCode());
                    cisApplyChargeAsTo.setMiId(miLimitHilistTo.getId());
                    cisApplyChargeAsTo.setLimitContent(miLimitHilistTo.getLimitContent());
                    cisApplyChargeAsTo.setLimitType(miLimitHilistTo.getLimitType());
                }
            }
        } catch (Exception e) {

        }
    }
}
