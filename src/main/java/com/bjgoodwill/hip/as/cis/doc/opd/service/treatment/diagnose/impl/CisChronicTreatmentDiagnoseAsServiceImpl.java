package com.bjgoodwill.hip.as.cis.doc.opd.service.treatment.diagnose.impl;

import com.bjgoodwill.hip.as.cis.doc.opd.service.treatment.diagnose.CisChronicTreatmentDiagnoseAsService;
import com.bjgoodwill.hip.as.cis.doc.opd.to.diagnosis.*;
import com.bjgoodwill.hip.as.cis.doc.opd.util.OpdDiagnosisUtil;
import com.bjgoodwill.hip.common.util.HIPBeanUtil;
import com.bjgoodwill.hip.common.util.HIPIDUtil;
import com.bjgoodwill.hip.ds.cis.opdcpoe.diag.service.CisOpdDiagnoseService;
import com.bjgoodwill.hip.ds.cis.opdcpoe.diag.to.CisOpdDiagnoseTo;
import com.bjgoodwill.hip.ds.cis.opdcpoe.treatment.service.CisChronicTreatmentService;
import com.bjgoodwill.hip.ds.cis.opdcpoe.treatment.to.CisChronicTreatmentNto;
import com.bjgoodwill.hip.ds.cis.opdcpoe.treatment.to.CisChronicTreatmentTo;
import io.micrometer.common.util.StringUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.seata.spring.annotation.GlobalTransactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> liangyuqing
 * @description :
 * @createDate : 2025/6/30 13:40
 */
@Service
public class CisChronicTreatmentDiagnoseAsServiceImpl implements CisChronicTreatmentDiagnoseAsService {

    @Autowired
    private OpdDiagnosisUtil opdDiagnosisUtil;

    @Autowired
    private CisOpdDiagnoseService cisOpdDiagnoseService;

    @Autowired
    private CisChronicTreatmentService cisChronicTreatmentService;

    @Override
    public List<OpdDiagnoseWestAsTo> getWestDiagnosisObscure(String text) {
        List<OpdDiagnoseWestAsTo> result = opdDiagnosisUtil.getWestDiagnosisObscure(text);
        return result;
    }

    @Override
    public List<OpdDiagnoseChineseAsTo> getChineseDiagnosisObscure(String text) {
        List<OpdDiagnoseChineseAsTo> result = opdDiagnosisUtil.getChineseDiagnosisObscure(text);
        return result;
    }

    @Override
    public List<OpdDiagnosisAsTo> getPatDiagnosis(TreatmentDiagnosisQto treatmentDiagnosisQto) {
        List<OpdDiagnosisAsTo> result = new ArrayList<>();
        if (StringUtils.isNotEmpty(treatmentDiagnosisQto.getTreatmentCode())) {
            List<CisOpdDiagnoseTo> diagnosis = cisOpdDiagnoseService.getCisOpdDiagnoseByTreatmentCode(treatmentDiagnosisQto.getTreatmentCode());
            if (CollectionUtils.isNotEmpty(diagnosis)) {
                result = opdDiagnosisUtil.buildPatDiagnoseShowTo(diagnosis);
            }
        }
        return result;
    }

    @GlobalTransactional
    @Override
    public void createCisOpdDiagnose(List<OpdDiagnosisAsNto> opdDiagnosisAsNtos) {
        OpdDiagnosisAsNto opdDiagnosisAsNto = opdDiagnosisAsNtos.get(0);
        // 如果诊疗号为空，创建诊疗信息
        CisChronicTreatmentNto cisChronicTreatmentNto = null;
        if (StringUtils.isEmpty(opdDiagnosisAsNto.getTreatmentCode())) {
            cisChronicTreatmentNto = HIPBeanUtil.copy(opdDiagnosisAsNto, CisChronicTreatmentNto.class);
            cisChronicTreatmentNto.setId(HIPIDUtil.getNextIdString());
            cisChronicTreatmentNto.setDocCode(opdDiagnosisAsNto.getCurLoginStaff());

            cisChronicTreatmentNto.setDiseaseCode(opdDiagnosisAsNto.getDiseaseCode());
            cisChronicTreatmentNto.setDiseaseName(opdDiagnosisAsNto.getDiseaseName());
            cisChronicTreatmentNto.setInsureType(opdDiagnosisAsNto.getInsureType());

            CisChronicTreatmentTo cisChronicTreatmentTo = cisChronicTreatmentService.createCisChronicTreatment(cisChronicTreatmentNto);
            opdDiagnosisAsNtos.forEach(nto -> nto.setTreatmentCode(cisChronicTreatmentTo.getId()));
        }

        opdDiagnosisUtil.saveOpdDiagnosis(opdDiagnosisAsNtos);
    }
}
