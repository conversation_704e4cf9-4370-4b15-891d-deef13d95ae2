package com.bjgoodwill.hip.as.cis.doc.ipd.util.paste;

import com.bjgoodwill.hip.as.cis.doc.ipd.util.build.*;
import com.bjgoodwill.hip.business.util.cis.common.enums.OrderTypeEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.common.util.HIPBeanUtil;
import com.bjgoodwill.hip.common.util.HIPIDUtil;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyTo;
import com.bjgoodwill.hip.ds.cis.cpoe.order.to.*;
import com.bjgoodwill.hip.ds.pat.in.hospital.inpatient.to.PatIpdInpatientTo;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> lian<PERSON><PERSON><PERSON>
 * @description :
 * @createDate : 2025/3/24 15:16
 */
@Service
public class OrderPasteUtil {

    @Resource
    private EDrugOrderBuildUtil eDrugOrderBuildUtil;

    @Resource
    private CDrugOrderBuildUtil cDrugOrderBuildUtil;

    @Resource
    private ManagementOrderBuildUtil managementOrderBuildUtil;

    @Resource
    private DgimgOrderBuildUtil dgimgOrderBuildUtil;

    @Resource
    private SpcobsOrderBuildUtil spcobsOrderBuildUtil;

    @Resource
    private OperationApplyOrderBuildUtil operationApplyOrderBuildUtil;

    @Resource
    private BloodOrderBuildUtil bloodOrderBuildUtil;

    @Resource
    private CommonOrderBuildUtil commonOrderBuildUtil;

    @Resource
    private PalgOrderBuildUtil palgOrderBuildUtil;

    @Resource
    private TreatmentOrderBuildUtil treatmentOrderBuildUtil;

    @Resource
    private OutHospitalOrderBuildUtil outHospitalOrderBuildUtil;

    @Resource
    private ChangeDeptOrderBuildUtil changeDeptOrderBuildUtil;

    @Resource
    private ConsulationOrderBuildUtil consulationOrderBuildUtil;

    @Resource
    private InputBloodOrderBuildUtil inputBloodOrderBuildUtil;

    @Resource
    private SkinTestOrderBuildUtil skinTestOrderBuildUtil;

    /**
     * 构建要粘贴的医嘱信息
     * @param cisIpdOrderToList
     * @param cisBaseApplyToList
     * @param patIpdInpatientTo
     * @return
     */
    public List<CisIpdOrderNto> buildPasteOrderList(List<CisIpdOrderTo> cisIpdOrderToList, List<CisBaseApplyTo> cisBaseApplyToList, PatIpdInpatientTo patIpdInpatientTo) {
        List<CisIpdOrderNto> cisIpdOrderNtoList = new ArrayList<>();
        Map<String, CisBaseApplyTo> cisBaseApplyToMap = cisBaseApplyToList.stream().collect(Collectors.toMap(CisBaseApplyTo::getOrderID, cisBaseApplyTo -> cisBaseApplyTo));
        for (int i = 0; i < cisIpdOrderToList.size(); i++) {
            CisIpdOrderNto cisIpdOrderNto = null;
            CisIpdOrderTo cisIpdOrderTo = cisIpdOrderToList.get(i);
            if (cisIpdOrderTo instanceof  CisLongTermOrderTo) {
                cisIpdOrderNto = this.buildPasteLongOrder(cisIpdOrderTo, patIpdInpatientTo);
            } else if (cisIpdOrderTo instanceof  CisTemporaryOrderTo) {
                cisIpdOrderNto = this.buildPasteTemporaryOrder(cisIpdOrderTo, patIpdInpatientTo);
            }

            // 如果cisIpdOrderToList有多条，则第一条是主医嘱，第二条是从医嘱，parentCode为第一条的id
            if (i == 1) {
                cisIpdOrderNto.setParentCode(cisIpdOrderNtoList.get(0).getId());
            }

            // 申请单信息构建
            cisIpdOrderNto.setCisBaseApplyNto(this.buildCisBaseApplyNto(cisIpdOrderNto, cisBaseApplyToMap.get(cisIpdOrderTo.getId())));

            cisIpdOrderNtoList.add(cisIpdOrderNto);
        }

        return cisIpdOrderNtoList;
    }

    public CisIpdOrderNto buildPasteLongOrder(CisIpdOrderTo cisIpdOrderTo, PatIpdInpatientTo patIpdInpatientTo) {
        CisLongTermOrderTo cisLongTermOrderTo = (CisLongTermOrderTo) cisIpdOrderTo;
        CisLongTermOrderNto cisLongTermOrderNto = HIPBeanUtil.copy(cisLongTermOrderTo, CisLongTermOrderNto.class);

        cisLongTermOrderNto.setId("OD_" + HIPIDUtil.getNextIdString());
        cisLongTermOrderNto.setApplyCode("AP_" + HIPIDUtil.getNextIdString());
        cisLongTermOrderNto.setEffectiveLowDate(LocalDateTime.now());
        cisLongTermOrderNto.setVisitCode(patIpdInpatientTo.getVisitCode());
        cisLongTermOrderNto.setPatMiCode(patIpdInpatientTo.getPatCode());
        cisLongTermOrderNto.setDeptNurseCode(patIpdInpatientTo.getDeptNurseCode());
        cisLongTermOrderNto.setDeptNurseName(patIpdInpatientTo.getDeptNurseName());
        cisLongTermOrderNto.setFirstDayTimepoint(cisLongTermOrderTo.getFirstDayTimepoint());

        return cisLongTermOrderNto;
    }

    public CisIpdOrderNto buildPasteTemporaryOrder(CisIpdOrderTo cisIpdOrderTo, PatIpdInpatientTo patIpdInpatientTo) {
        CisTemporaryOrderTo cisTemporaryOrderTo = (CisTemporaryOrderTo) cisIpdOrderTo;
        CisTemporaryOrderNto cisTemporaryOrderNto = HIPBeanUtil.copy(cisTemporaryOrderTo, CisTemporaryOrderNto.class);

        cisTemporaryOrderNto.setId("OD_" + HIPIDUtil.getNextIdString());
        cisTemporaryOrderNto.setApplyCode("AP_" + HIPIDUtil.getNextIdString());
        cisTemporaryOrderNto.setEffectiveLowDate(LocalDateTime.now());
        cisTemporaryOrderNto.setVisitCode(patIpdInpatientTo.getVisitCode());
        cisTemporaryOrderNto.setPatMiCode(patIpdInpatientTo.getPatCode());
        cisTemporaryOrderNto.setDeptNurseCode(patIpdInpatientTo.getDeptNurseCode());
        cisTemporaryOrderNto.setDeptNurseName(patIpdInpatientTo.getDeptNurseName());

        return cisTemporaryOrderNto;
    }

    /**
     * 各类别医嘱构造申请单
     * @param cisIpdOrderNto
     * @param cisBaseApplyTo
     * @return
     */
    public CisBaseApplyNto buildCisBaseApplyNto(CisIpdOrderNto cisIpdOrderNto, CisBaseApplyTo cisBaseApplyTo) {
        if (cisIpdOrderNto.getOrderClass().equals(SystemTypeEnum.EDRUG)) {
            return eDrugOrderBuildUtil.buildCisIpdOrderNto(cisIpdOrderNto, cisBaseApplyTo);
        } else if (cisIpdOrderNto.getOrderClass().equals(SystemTypeEnum.CDRUG)) {
            return cDrugOrderBuildUtil.buildCisIpdOrderNto(cisIpdOrderNto, cisBaseApplyTo);
        } else if (cisIpdOrderNto.getOrderClass().equals(SystemTypeEnum.MANAGEMENT)) {
            return managementOrderBuildUtil.buildCisIpdOrderNto(cisIpdOrderNto, cisBaseApplyTo);
        } else if (cisIpdOrderNto.getOrderClass().equals(SystemTypeEnum.DGIMG)) {
            return dgimgOrderBuildUtil.buildCisIpdOrderNto(cisIpdOrderNto, cisBaseApplyTo);
        } else if (cisIpdOrderNto.getOrderClass().equals(SystemTypeEnum.SPCOBS)) {
            return spcobsOrderBuildUtil.buildCisIpdOrderNto(cisIpdOrderNto, cisBaseApplyTo);
        } else if (cisIpdOrderNto.getOrderClass().equals(SystemTypeEnum.OPERATIONAPPLY)) {
            return operationApplyOrderBuildUtil.buildCisIpdOrderNto(cisIpdOrderNto, cisBaseApplyTo);
        } else if (cisIpdOrderNto.getOrderClass().equals(SystemTypeEnum.BLOOD)) {
            return bloodOrderBuildUtil.buildCisIpdOrderNto(cisIpdOrderNto, cisBaseApplyTo);
        } else if (cisIpdOrderNto.getOrderClass().equals(SystemTypeEnum.ENTRUST)) {
            return commonOrderBuildUtil.buildCisIpdOrderNto(cisIpdOrderNto, cisBaseApplyTo);
        } else if (cisIpdOrderNto.getOrderClass().equals(SystemTypeEnum.MATERIAL)) {
            // todo 材料医嘱暂未实现
        } else if (cisIpdOrderNto.getOrderClass().equals(SystemTypeEnum.PALG)) {
            return palgOrderBuildUtil.buildCisIpdOrderNto(cisIpdOrderNto, cisBaseApplyTo);
        } else if (cisIpdOrderNto.getOrderClass().equals(SystemTypeEnum.TREATMENT)) {
            return treatmentOrderBuildUtil.buildCisIpdOrderNto(cisIpdOrderNto, cisBaseApplyTo);
        } else if (cisIpdOrderNto.getOrderClass().equals(SystemTypeEnum.OUTHOSPITAL)) {
            return outHospitalOrderBuildUtil.buildCisIpdOrderNto(cisIpdOrderNto, cisBaseApplyTo);
        } else if (cisIpdOrderNto.getOrderClass().equals(SystemTypeEnum.CHANGEDEPT)) {
            return changeDeptOrderBuildUtil.buildCisIpdOrderNto(cisIpdOrderNto, cisBaseApplyTo);
        } else if (cisIpdOrderNto.getOrderClass().equals(SystemTypeEnum.CONSULTATION)) {
            return consulationOrderBuildUtil.buildCisIpdOrderNto(cisIpdOrderNto, cisBaseApplyTo);
        } else if (cisIpdOrderNto.getOrderClass().equals(SystemTypeEnum.INPUTBLOODAPPLY)) {
            return inputBloodOrderBuildUtil.buildCisIpdOrderNto(cisIpdOrderNto, cisBaseApplyTo);
        } else if (cisIpdOrderNto.getOrderClass().equals(SystemTypeEnum.SKIN)) {
            return skinTestOrderBuildUtil.buildCisIpdOrderNto(cisIpdOrderNto, cisBaseApplyTo);
        }

        return null;
    }

}
