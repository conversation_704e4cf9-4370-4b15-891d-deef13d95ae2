package com.bjgoodwill.hip.as.cis.doc.ipd.to.consultation;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/12/17 9:45
 * @ClassName: ConsultationFinishAsTo
 * @Description: 会诊完成-提交
 */
@Schema(description = "会诊完成-提交")
public class ConsultationFinishAsETo implements Serializable {

    @Schema(description = "申请单标识")
    private String applyId;

    @Schema(description = "会诊意见")
    private String cnsltOpinion;

    @Schema(description = "状态(1:完成；2:取消完成)")
    private String statusCode;

    public String getApplyId() {
        return applyId;
    }

    public void setApplyId(String applyId) {
        this.applyId = applyId;
    }

    public String getCnsltOpinion() {
        return cnsltOpinion;
    }

    public void setCnsltOpinion(String cnsltOpinion) {
        this.cnsltOpinion = cnsltOpinion;
    }

    public String getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(String statusCode) {
        this.statusCode = statusCode;
    }
}
