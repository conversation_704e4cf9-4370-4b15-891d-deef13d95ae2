package com.bjgoodwill.hip.as.cis.doc.ipd.service.temp.impl;

import com.bjgoodwill.hip.as.cis.doc.ipd.enums.CisDocIpdBusinessErrorEnum;
import com.bjgoodwill.hip.as.cis.doc.ipd.enums.OrderTempMaintainBusinessErrorEnum;
import com.bjgoodwill.hip.as.cis.doc.ipd.service.temp.CisIpdOrderToTempService;
import com.bjgoodwill.hip.as.cis.doc.ipd.to.OrderIssuedPickUpsAsQto;
import com.bjgoodwill.hip.as.cis.doc.ipd.to.OrderIssuedPickUpsAsTo;
import com.bjgoodwill.hip.as.cis.doc.ipd.to.cdrug.CdrugOpenParameterTo;
import com.bjgoodwill.hip.as.cis.doc.ipd.to.serviceitem.ServiceClinicPriceAsTo;
import com.bjgoodwill.hip.as.cis.doc.ipd.to.temp.CisOrderTempDetailAsToOrderNto;
import com.bjgoodwill.hip.as.cis.doc.ipd.to.temp.DgimgItemTempDataSource;
import com.bjgoodwill.hip.as.cis.doc.ipd.util.OrderIssuedUtil;
import com.bjgoodwill.hip.as.cis.doc.ipd.util.temp.CisIpdOrderToTempUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.EDrugSystemTypeExtEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.common.bean.LoginInfo;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.common.util.HIPBeanUtil;
import com.bjgoodwill.hip.ds.base.cis.dict.usage.service.UsageService;
import com.bjgoodwill.hip.ds.base.cis.dict.usage.to.UsageTo;
import com.bjgoodwill.hip.ds.base.material.goods.service.MaterialGoodsService;
import com.bjgoodwill.hip.ds.base.material.goods.to.MaterialGoodsTo;
import com.bjgoodwill.hip.ds.cis.cpoe.order.service.CisIpdCpoeService;
import com.bjgoodwill.hip.ds.cis.cpoe.order.to.CisIpdOrderNto;
import com.bjgoodwill.hip.ds.cis.cpoe.order.to.CisLongTermOrderNto;
import com.bjgoodwill.hip.ds.cis.medicineitem.serviceItem.service.ServiceClinicItemService;
import com.bjgoodwill.hip.ds.cis.medicineitem.serviceItem.to.DgimgApplyTo;
import com.bjgoodwill.hip.ds.cis.medicineitem.serviceItem.to.ServiceClinicItemTo;
import com.bjgoodwill.hip.ds.cis.medicineitem.serviceItem.to.SpcobsApplyTo;
import com.bjgoodwill.hip.ds.drug.goods.enmus.DrugTypeEnum;
import com.bjgoodwill.hip.ds.drug.stock.stock.service.DrugStockService;
import com.bjgoodwill.hip.ds.drug.stock.stock.to.DrugStockDoctorTo;
import com.bjgoodwill.hip.ds.org.api.service.WorkGroupService;
import com.bjgoodwill.hip.ds.org.api.to.WorkGroupPharmacyTo;
import com.bjgoodwill.hip.ds.param.api.service.ParameterService;
import com.bjgoodwill.hip.ds.param.api.to.ParameterTo;
import com.bjgoodwill.hip.ds.term.api.service.DictElementService;
import com.bjgoodwill.hip.ds.term.api.to.DictElementTo;
import com.bjgoodwill.hip.enums.DictParameterEnum;
import com.bjgoodwill.hip.security.util.HIPCurrentOrgUtils;
import com.bjgoodwill.hip.security.util.HIPSecurityUtils;
import io.micrometer.common.util.StringUtils;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.apache.seata.spring.annotation.GlobalTransactional;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> liangyuqing
 * @description :
 * @createDate : 2025/1/9 11:28
 */
@Service
public class CisIpdOrderToTempServiceImpl implements CisIpdOrderToTempService {

    @Resource(name = "com.bjgoodwill.hip.ds.cis.medicineitem.serviceItem.service.ServiceClinicItemTangibleService")
    private ServiceClinicItemService serviceClinicItemService;

    @Resource
    private DictElementService dictElementService;

    @Resource
    private OrderIssuedUtil orderIssuedUtil;

    @Resource
    private MaterialGoodsService materialGoodsService;

    @Resource
    private DrugStockService drugStockService;

    @Resource
    private WorkGroupService workGroupService;

    @Resource
    private ParameterService parameterService;

    @Resource
    private UsageService usageService;

    @Resource
    private CisIpdOrderToTempUtil cisIpdOrderToTempUtil;

    @Resource
    private CisIpdCpoeService cisIpdCpoeService;

    public static final DecimalFormat df = new DecimalFormat("#.##");

    @Override
    public DgimgItemTempDataSource getDgimgItemResult(String deviceType) {
        DgimgItemTempDataSource dgimgItemTempDataSource = new DgimgItemTempDataSource();

        // 查询检查项目信息
        List<SystemTypeEnum> systemTypeEnums = new ArrayList<>();
        systemTypeEnums.add(SystemTypeEnum.DGIMG);
        List<ServiceClinicItemTo> serviceClinicItemToList = serviceClinicItemService.queryServiceClinicItemListByText(systemTypeEnums, 10000, "", HIPCurrentOrgUtils.getCurrentOrgInfo().getHospitalAreaCode());
        List<DgimgApplyTo> dgimgApplyToList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(serviceClinicItemToList)) {
            dgimgApplyToList = HIPBeanUtil.copy(serviceClinicItemToList, DgimgApplyTo.class);
            dgimgApplyToList = dgimgApplyToList.stream().filter(dgimgApplyTo -> dgimgApplyTo.getDeviceType().equals(deviceType)).toList();
            dgimgItemTempDataSource.setDgigmItemList(dgimgApplyToList);
        }

        List<String> methodList = dgimgItemTempDataSource.getDgigmItemList().stream().map(DgimgApplyTo:: getMethod).distinct().toList();
        List<String> humanOrgansList = dgimgItemTempDataSource.getDgigmItemList().stream().map(DgimgApplyTo :: getHumanOrgans).distinct().toList();
        dgimgItemTempDataSource.setHumanOrgansList(this.getHumanOrgansDictElement(humanOrgansList));
        dgimgItemTempDataSource.setMethodList(this.getMethodDictElement(methodList));

        return dgimgItemTempDataSource;
    }

    @Override
    public List<SpcobsApplyTo> getSpcobsItems() {
        List<SpcobsApplyTo> spcobsApplyToList = new ArrayList<>();
        List<SystemTypeEnum> systemTypeEnums = new ArrayList<>();
        systemTypeEnums.add(SystemTypeEnum.SPCOBS);
        List<ServiceClinicItemTo> serviceClinicItemTos = serviceClinicItemService.queryServiceClinicItemListByText(systemTypeEnums, 10000, "", HIPCurrentOrgUtils.getCurrentOrgInfo().getHospitalAreaCode());
        if (CollectionUtils.isNotEmpty(serviceClinicItemTos)) {
            spcobsApplyToList = serviceClinicItemTos.stream().filter(SpcobsApplyTo.class::isInstance).map(SpcobsApplyTo.class::cast).toList();
        }

        return spcobsApplyToList;
    }

    @Override
    public List<ServiceClinicPriceAsTo> getServiceClinicPrice(String serviceItemCode) {
        return orderIssuedUtil.searchDetails(serviceItemCode);
    }

    /**
     * 将服务项目转换为拾取器TO
     * @param systemTypeEnums
     * @param inputText
     * @return
     */
    public List<OrderIssuedPickUpsAsTo> transferServiceItemToOrderPickUp(List<SystemTypeEnum> systemTypeEnums, String inputText) {
        List<OrderIssuedPickUpsAsTo> orderIssuedPickUpsAsToList = new ArrayList<>();

        List<ServiceClinicItemTo> serviceClinicItemTos = serviceClinicItemService.queryServiceClinicItemListByText(systemTypeEnums, 100, inputText, HIPCurrentOrgUtils.getCurrentOrgInfo().getHospitalAreaCode());
        for (ServiceClinicItemTo serviceClinicItemTo : serviceClinicItemTos) {
            OrderIssuedPickUpsAsTo orderIssuedPickUpsAsTo = HIPBeanUtil.copy(serviceClinicItemTo, OrderIssuedPickUpsAsTo.class);
            orderIssuedPickUpsAsTo.setCollectionFlag(false);
            orderIssuedPickUpsAsTo.setOrgCode(serviceClinicItemTo.getDefaultExecOrgCode());
            orderIssuedPickUpsAsTo.setOrgName(serviceClinicItemTo.getDefaultExecOrgName());
            orderIssuedPickUpsAsTo.setExtCode(serviceClinicItemTo.getExtCode());
            if (!serviceClinicItemTo.getSystemType().equals("10")) { // 嘱托没有费用
                Optional<BigDecimal> opt = Optional.ofNullable(serviceClinicItemTo.getPrice());
                orderIssuedPickUpsAsTo.setPriceShow(df.format(opt.orElse(BigDecimal.ZERO)) + "元");
            }
            orderIssuedPickUpsAsToList.add(orderIssuedPickUpsAsTo);
        }

        return orderIssuedPickUpsAsToList;
    }

    @Override
    public List<OrderIssuedPickUpsAsTo> getServiceItems(OrderIssuedPickUpsAsQto orderIssuedPickUpsAsQto) {
        BusinessAssert.notNull(orderIssuedPickUpsAsQto.getSystemType(), CisDocIpdBusinessErrorEnum.CIS_DOC_IPD_0001, "医嘱类型");
        List<OrderIssuedPickUpsAsTo> orderIssuedPickUpsAsToList = new ArrayList<>();
        List<SystemTypeEnum> systemTypeEnums = new ArrayList<>();
        String systemType = orderIssuedPickUpsAsQto.getSystemType();

        // 转大写
        if (StringUtils.isNotEmpty(orderIssuedPickUpsAsQto.getInPutText())) {
            orderIssuedPickUpsAsQto.setInPutText(orderIssuedPickUpsAsQto.getInPutText().toUpperCase());
        }

        switch (systemType){
            case "ALL":
                systemTypeEnums.add(SystemTypeEnum.MANAGEMENT);
                systemTypeEnums.add(SystemTypeEnum.TREATMENT);
                systemTypeEnums.add(SystemTypeEnum.DGIMG);
                systemTypeEnums.add(SystemTypeEnum.SPCOBS);
                systemTypeEnums.add(SystemTypeEnum.SKIN);
                systemTypeEnums.add(SystemTypeEnum.NURSING);
                systemTypeEnums.add(SystemTypeEnum.PATIENT);
                if (StringUtils.isNotEmpty(orderIssuedPickUpsAsQto.getInPutText())) {
                    orderIssuedPickUpsAsToList.addAll(this.transferServiceItemToOrderPickUp(systemTypeEnums, orderIssuedPickUpsAsQto.getInPutText()));
                }
                break;
            case "15":
                systemTypeEnums.add(SystemTypeEnum.TREATMENT);
                systemTypeEnums.add(SystemTypeEnum.MANAGEMENT);
                if (StringUtils.isNotEmpty(orderIssuedPickUpsAsQto.getInPutText())) {
                    orderIssuedPickUpsAsToList.addAll(this.transferServiceItemToOrderPickUp(systemTypeEnums, orderIssuedPickUpsAsQto.getInPutText()));

//                    List<ServiceClinicItemTo> serviceClinicItemTos = serviceClinicItemService.queryServiceClinicItemListByText(systemTypeEnums, 100, orderIssuedPickUpsAsQto.getInPutText());
//                    if (CollectionUtils.isNotEmpty(serviceClinicItemTos)) {
//                        //治疗
//                        List<ServiceClinicItemTo> treatmentDistinctList = serviceClinicItemTos.stream().filter(a -> a.getSystemType().equals("15")).toList();
//                        if (CollectionUtils.isNotEmpty(treatmentDistinctList)) {
//                            List<TreatmentApplyTo> treatmentApplyToList = treatmentDistinctList.stream().filter(TreatmentApplyTo.class::isInstance)
//                                    .map(TreatmentApplyTo.class::cast).toList();
//                            for (TreatmentApplyTo treatmentApplyTo : treatmentApplyToList) {
//                                OrderIssuedPickUpsAsTo orderIssuedPickUpsAsTo = HIPBeanUtil.copy(treatmentApplyTo, OrderIssuedPickUpsAsTo.class);
//                                orderIssuedPickUpsAsTo.setOrgCode(treatmentApplyTo.getDefaultExecOrgCode());
//                                orderIssuedPickUpsAsTo.setOrgName(treatmentApplyTo.getDefaultExecOrgName());
//                                orderIssuedPickUpsAsTo.setPriceShow(df.format(treatmentApplyTo.getPrice()) + "元");
//                                orderIssuedPickUpsAsTo.setCollectionFlag(false);
//                                orderIssuedPickUpsAsToList.add(orderIssuedPickUpsAsTo);
//                            }
//                        }
//                        //处置
//                        List<ServiceClinicItemTo> managementDistinctList = serviceClinicItemTos.stream().filter(a -> a.getSystemType().equals("04")).toList();
//                        if (CollectionUtils.isNotEmpty(managementDistinctList)) {
//                            List<ManagementItemTo> managementApplyToList = managementDistinctList.stream().filter(ManagementItemTo.class::isInstance)
//                                    .map(ManagementItemTo.class::cast).toList();
//                            for (ManagementItemTo managementApplyTo : managementApplyToList) {
//                                OrderIssuedPickUpsAsTo orderIssuedPickUpsAsTo = HIPBeanUtil.copy(managementApplyTo, OrderIssuedPickUpsAsTo.class);
//                                orderIssuedPickUpsAsTo.setOrgCode(managementApplyTo.getDefaultExecOrgCode());
//                                orderIssuedPickUpsAsTo.setOrgName(managementApplyTo.getDefaultExecOrgName());
//                                orderIssuedPickUpsAsTo.setPriceShow(df.format(managementApplyTo.getPrice()) + "元");
//                                orderIssuedPickUpsAsTo.setCollectionFlag(false);
//                                orderIssuedPickUpsAsToList.add(orderIssuedPickUpsAsTo);
//                            }
//                        }
//                    }
                }
                break;
            case "05":
                systemTypeEnums.add(SystemTypeEnum.DGIMG);
                if (StringUtils.isNotEmpty(orderIssuedPickUpsAsQto.getInPutText())) {
                    orderIssuedPickUpsAsToList.addAll(this.transferServiceItemToOrderPickUp(systemTypeEnums, orderIssuedPickUpsAsQto.getInPutText()));

//                    List<ServiceClinicItemTo> serviceClinicItemTos = serviceClinicItemService.queryServiceClinicItemListByText(systemTypeEnums, 100, orderIssuedPickUpsAsQto.getInPutText());
//                    if (CollectionUtils.isNotEmpty(serviceClinicItemTos)) {
//                        List<DgimgApplyTo> dgimgApplyToList = serviceClinicItemTos.stream().filter(DgimgApplyTo.class::isInstance)
//                                .map(DgimgApplyTo.class::cast).toList();
//                        for (DgimgApplyTo dgimgApplyTo : dgimgApplyToList) {
//                            OrderIssuedPickUpsAsTo orderIssuedPickUpsAsTo = HIPBeanUtil.copy(dgimgApplyTo, OrderIssuedPickUpsAsTo.class);
//                            orderIssuedPickUpsAsTo.setOrgCode(dgimgApplyTo.getDefaultExecOrgCode());
//                            orderIssuedPickUpsAsTo.setOrgName(dgimgApplyTo.getDefaultExecOrgName());
//                            orderIssuedPickUpsAsTo.setPriceShow(df.format(dgimgApplyTo.getPrice()) + "元");
//                            orderIssuedPickUpsAsTo.setCollectionFlag(false);
//                            orderIssuedPickUpsAsToList.add(orderIssuedPickUpsAsTo);
//                        }
//                    }
                }
                break;
            case "06":
                systemTypeEnums.add(SystemTypeEnum.SPCOBS);
                if (StringUtils.isNotEmpty(orderIssuedPickUpsAsQto.getInPutText())) {
                    orderIssuedPickUpsAsToList.addAll(this.transferServiceItemToOrderPickUp(systemTypeEnums, orderIssuedPickUpsAsQto.getInPutText()));

//                    List<ServiceClinicItemTo> serviceClinicItemTos = serviceClinicItemService.queryServiceClinicItemListByText(systemTypeEnums, 100, orderIssuedPickUpsAsQto.getInPutText());
//                    if (CollectionUtils.isNotEmpty(serviceClinicItemTos)) {
//                        List<SpcobsApplyTo> spcobsApplyToList = serviceClinicItemTos.stream().filter(SpcobsApplyTo.class::isInstance)
//                                .map(SpcobsApplyTo.class::cast).toList();
//                        for (SpcobsApplyTo spcobsApplyTo : spcobsApplyToList) {
//                            OrderIssuedPickUpsAsTo orderIssuedPickUpsAsTo = HIPBeanUtil.copy(spcobsApplyTo, OrderIssuedPickUpsAsTo.class);
//                            orderIssuedPickUpsAsTo.setOrgCode(spcobsApplyTo.getDefaultExecOrgCode());
//                            orderIssuedPickUpsAsTo.setOrgName(spcobsApplyTo.getDefaultExecOrgName());
//                            orderIssuedPickUpsAsTo.setPriceShow(df.format(spcobsApplyTo.getPrice()) + "元");
//                            orderIssuedPickUpsAsTo.setCollectionFlag(false);
//                            orderIssuedPickUpsAsToList.add(orderIssuedPickUpsAsTo);
//                        }
//                    }
                }
                break;
            case "26":
                systemTypeEnums.add(SystemTypeEnum.SKIN);
                if (StringUtils.isNotEmpty(orderIssuedPickUpsAsQto.getInPutText())) {
                    orderIssuedPickUpsAsToList.addAll(this.transferServiceItemToOrderPickUp(systemTypeEnums, orderIssuedPickUpsAsQto.getInPutText()));

//                    List<ServiceClinicItemTo> serviceClinicItemTos = serviceClinicItemService.queryServiceClinicItemListByText(systemTypeEnums, 100, orderIssuedPickUpsAsQto.getInPutText());
//                    if (CollectionUtils.isNotEmpty(serviceClinicItemTos)) {
//                        for (ServiceClinicItemTo serviceClinicItemTo : serviceClinicItemTos) {
//                            OrderIssuedPickUpsAsTo orderIssuedPickUpsAsTo = HIPBeanUtil.copy(serviceClinicItemTo, OrderIssuedPickUpsAsTo.class);
//                            orderIssuedPickUpsAsTo.setOrgCode(serviceClinicItemTo.getDefaultExecOrgCode());
//                            orderIssuedPickUpsAsTo.setOrgName(serviceClinicItemTo.getDefaultExecOrgName());
//                            orderIssuedPickUpsAsTo.setCollectionFlag(false);
//                            orderIssuedPickUpsAsTo.setPriceShow(df.format(serviceClinicItemTo.getPrice()) + "元");
//                            orderIssuedPickUpsAsToList.add(orderIssuedPickUpsAsTo);
//                        }
//                    }
                }
                break;
            case "25":
                systemTypeEnums.add(SystemTypeEnum.PATIENT);
                systemTypeEnums.add(SystemTypeEnum.NURSING);
                if (StringUtils.isNotEmpty(orderIssuedPickUpsAsQto.getInPutText())) {
                    orderIssuedPickUpsAsToList.addAll(this.transferServiceItemToOrderPickUp(systemTypeEnums, orderIssuedPickUpsAsQto.getInPutText()));

//                    List<ServiceClinicItemTo> serviceClinicItemTos = serviceClinicItemService.queryServiceClinicItemListByText(systemTypeEnums, 100, orderIssuedPickUpsAsQto.getInPutText());
//                    if (CollectionUtils.isNotEmpty(serviceClinicItemTos)) {
//                        for (ServiceClinicItemTo serviceClinicItemTo : serviceClinicItemTos) {
//                            OrderIssuedPickUpsAsTo orderIssuedPickUpsAsTo = HIPBeanUtil.copy(serviceClinicItemTo, OrderIssuedPickUpsAsTo.class);
//                            orderIssuedPickUpsAsTo.setOrgCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
//                            orderIssuedPickUpsAsTo.setOrgName(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupName());
//                            orderIssuedPickUpsAsTo.setCollectionFlag(false);
//                            orderIssuedPickUpsAsTo.setUnit(serviceClinicItemTo.getUnit());
//                            orderIssuedPickUpsAsTo.setOrderExtCode(serviceClinicItemTo.getExtCode());
//                            orderIssuedPickUpsAsTo.setOrderExtName(serviceClinicItemTo.getExtName());
//                            orderIssuedPickUpsAsTo.setPriceShow(df.format(serviceClinicItemTo.getPrice()));
//                            orderIssuedPickUpsAsToList.add(orderIssuedPickUpsAsTo);
//                        }
//                    }
                }
                break;
            case "11":
                systemTypeEnums.add(SystemTypeEnum.MATERIAL);
                if (StringUtils.isNotEmpty(orderIssuedPickUpsAsQto.getInPutText())) {
                    List<MaterialGoodsTo> materialGoodsTo = materialGoodsService.getByName(orderIssuedPickUpsAsQto.getInPutText());
                    if (CollectionUtils.isNotEmpty(materialGoodsTo)) {
                        if (materialGoodsTo.size() > 100) {
                            materialGoodsTo = materialGoodsTo.subList(0, 99);
                        }
                        for (MaterialGoodsTo materialGoodsToBySearch : materialGoodsTo) {
                            if (materialGoodsToBySearch != null) {
                                OrderIssuedPickUpsAsTo orderIssuedPickUpsAsTo = new OrderIssuedPickUpsAsTo();
                                orderIssuedPickUpsAsTo.setSystemType("11");
                                orderIssuedPickUpsAsTo.setCollectionFlag(false);
                                orderIssuedPickUpsAsTo.setServiceItemCode(materialGoodsToBySearch.getMaterialCode());
                                orderIssuedPickUpsAsTo.setServiceItemName(materialGoodsToBySearch.getMaterialName());
                                orderIssuedPickUpsAsTo.setPrice(materialGoodsToBySearch.getSalePrice());
                                orderIssuedPickUpsAsTo.setPriceShow(df.format(materialGoodsToBySearch.getSalePrice()) + "元");
                                orderIssuedPickUpsAsTo.setUnit(materialGoodsToBySearch.getPackageUnitName());
                                orderIssuedPickUpsAsTo.setMaterialSpec(materialGoodsToBySearch.getMaterialSpec());
                                orderIssuedPickUpsAsToList.add(orderIssuedPickUpsAsTo);
                            }
                        }
                    }
                }
                break;
            case "01":
                List<String> storageCodeList = new ArrayList<>();
                List<WorkGroupPharmacyTo> pharmacyByHosAreaAndInPatientList = workGroupService.getPharmacyByHosAreaAndInPatient(HIPCurrentOrgUtils.getCurrentOrgInfo().getHospitalAreaCode());
                if (CollectionUtils.isNotEmpty(pharmacyByHosAreaAndInPatientList)) {
                    for (WorkGroupPharmacyTo pharmacyByHosAreaAndInPatient : pharmacyByHosAreaAndInPatientList) {
                        if (pharmacyByHosAreaAndInPatient != null && StringUtils.isNotEmpty(pharmacyByHosAreaAndInPatient.getDeptCode())) {
                            storageCodeList.add(pharmacyByHosAreaAndInPatient.getId());
                        }
                    }
                }

                systemTypeEnums.add(SystemTypeEnum.EDRUG);
                List<String> drugTypes = new ArrayList<>();
                drugTypes.add("01");
                drugTypes.add("02");
                if (StringUtils.isNotEmpty(orderIssuedPickUpsAsQto.getInPutText())) {
                    List<DrugStockDoctorTo> doctorDrugStockList = drugStockService.getDoctorDrugStock(storageCodeList, drugTypes, orderIssuedPickUpsAsQto.getInPutText(), 100);
                    if (doctorDrugStockList.size() > 100) {
                        doctorDrugStockList = doctorDrugStockList.subList(0, 99);
                    }

                    for (DrugStockDoctorTo doctorDrugStock : doctorDrugStockList) {
                        OrderIssuedPickUpsAsTo orderIssuedPickUpsAsTo = new OrderIssuedPickUpsAsTo();
                        orderIssuedPickUpsAsTo.setSystemType("01");
                        orderIssuedPickUpsAsTo.setCollectionFlag(false);
                        orderIssuedPickUpsAsTo.setServiceItemCode(doctorDrugStock.getDrugGoodsCode());
                        orderIssuedPickUpsAsTo.setServiceItemName(doctorDrugStock.getDrugGoodsName());
                        orderIssuedPickUpsAsTo.setMnemonicCode(doctorDrugStock.getMnemonicCode());
                        orderIssuedPickUpsAsTo.setDrugSpec(doctorDrugStock.getDrugSpec());
                        orderIssuedPickUpsAsTo.setInsuranceTypeValue(doctorDrugStock.getInsuranceTypeValue());
                        orderIssuedPickUpsAsTo.setOrgCode(doctorDrugStock.getStorageCode());
                        orderIssuedPickUpsAsTo.setOrgName(doctorDrugStock.getStorageName());
                        orderIssuedPickUpsAsTo.setQuantity(doctorDrugStock.getQuantityMax() + doctorDrugStock.getPackageUnitValue());
                        orderIssuedPickUpsAsTo.setDrugForm(doctorDrugStock.getDrugFormValue());
                        orderIssuedPickUpsAsTo.setInsuranceCode(doctorDrugStock.getInsuranceCode());
                        orderIssuedPickUpsAsTo.setInsuranceName(doctorDrugStock.getInsuranceName());
                        orderIssuedPickUpsAsTo.setPrice(doctorDrugStock.getSalePrice());
                        orderIssuedPickUpsAsTo.setAntibacterialFlag(doctorDrugStock.getAntibacterialFlag());
                        orderIssuedPickUpsAsTo.setDosageBase(doctorDrugStock.getDosageBase());
                        orderIssuedPickUpsAsTo.setDosageUnit(doctorDrugStock.getDosageUnit());
                        orderIssuedPickUpsAsTo.setDosageUnitValue(doctorDrugStock.getDosageUnitValue());
                        orderIssuedPickUpsAsTo.setMinUnit(doctorDrugStock.getMinUnit());
                        orderIssuedPickUpsAsTo.setUsage(doctorDrugStock.getUsage());
                        orderIssuedPickUpsAsTo.setUsageValue(doctorDrugStock.getUsageValue());
                        orderIssuedPickUpsAsTo.setFrequency(doctorDrugStock.getFrequency());
                        orderIssuedPickUpsAsTo.setFrequencyValue(doctorDrugStock.getFrequencyValue());
                        orderIssuedPickUpsAsTo.setMinUnitValue(doctorDrugStock.getMinUnitValue());
                        orderIssuedPickUpsAsTo.setSkinTest(doctorDrugStock.getSkinTest());
                        orderIssuedPickUpsAsTo.setPriceShow(df.format(doctorDrugStock.getSalePrice()) + "元/" + doctorDrugStock.getPackageUnitValue());
                        orderIssuedPickUpsAsTo.setDripSpeed(doctorDrugStock.getDripSpeed());
                        orderIssuedPickUpsAsTo.setDripSpeedUnit(doctorDrugStock.getDripSpeedUnit());
                        orderIssuedPickUpsAsTo.setExtCode(EDrugSystemTypeExtEnum.COMMON.getCode());
                        orderIssuedPickUpsAsToList.add(orderIssuedPickUpsAsTo);
                    }
                }
                break;
            default:
                break;

        }
        return orderIssuedPickUpsAsToList;
    }

    @Override
    public List<WorkGroupPharmacyTo> getCDrugWorkGroupPharmacyList(String herbType) {
        List<WorkGroupPharmacyTo> result = new ArrayList<>();
        // 获取登录院区信息
        LoginInfo loginInfo = HIPSecurityUtils.getLoginInfo();
        BusinessAssert.notNull(loginInfo, CisDocIpdBusinessErrorEnum.CIS_DOC_IPD_0001,"登录信息");
        BusinessAssert.notNull(HIPCurrentOrgUtils.getCurrentOrgInfo().getHospitalAreaCode(), CisDocIpdBusinessErrorEnum.CIS_DOC_IPD_0001,"登录院区信息");
        if (loginInfo != null && org.apache.commons.lang3.StringUtils.isNotEmpty(HIPCurrentOrgUtils.getCurrentOrgInfo().getHospitalAreaCode())) {
            // 领域接口查询
            List<WorkGroupPharmacyTo> workGroupPharmacyToList =
                    workGroupService.getPharmacyByHosAreaAndInPatient(HIPCurrentOrgUtils.getCurrentOrgInfo().getHospitalAreaCode());
            // 根据药品类型，筛选出草药药房
            if (CollectionUtils.isNotEmpty(workGroupPharmacyToList)) {
                result = workGroupPharmacyToList.stream()
                        .filter(o -> o.getProvideDrugType().contains(DrugTypeEnum.草药.getCode())
                                && CollectionUtils.isNotEmpty(o.getProvideHerbalProperties())
                                && o.getProvideHerbalProperties().contains(herbType))
                        .toList();
            }
        }
        return result;
    }

    @Override
    public List<OrderIssuedPickUpsAsTo> getCdrugOrderIssuedPickUpsList(String executeOrgCode) {
        // 判断执行科室必传
        List<OrderIssuedPickUpsAsTo> result = new ArrayList<>();

        List<String> storageCodeList = new ArrayList<>();
        storageCodeList.add(executeOrgCode);
        // 根据选择的药房，调用领域接口查库存
        List<DrugStockDoctorTo> doctorDrugStockList = drugStockService.getDoctorDrugStockHerbs(storageCodeList);
        if (CollectionUtils.isNotEmpty(doctorDrugStockList)) {
            doctorDrugStockList.forEach( obj -> {
                result.add(drugStockTransferToOrder(obj));
            });
        }

        return result;
    }

    @Override
    public CdrugOpenParameterTo getCdrugOpenParameter() {
        CdrugOpenParameterTo result = new CdrugOpenParameterTo();

        // 调用参数领域接口，查询每付包数、每包毫升数参数配置
        // 每付包数
        ParameterTo<String> packNumParameterTo = parameterService.getStringParameter(DictParameterEnum.ClinicCDrugPackNumParm.getCode());
        // 每包毫升数
        ParameterTo<String> packMLParameterTo = parameterService.getStringParameter(DictParameterEnum.ClinicCDrugPackMLParm.getCode());

        // 构造每付包数数据源
        if (packNumParameterTo != null && org.apache.commons.lang3.StringUtils.isNotEmpty(packNumParameterTo.getValue())) {
            List<Integer> cdrugPackNumList = new ArrayList<>();
            String[] cdrugPackNumArray = packNumParameterTo.getValue().split("\\|");
            Arrays.asList(cdrugPackNumArray).forEach(obj -> {
                cdrugPackNumList.add(Integer.parseInt(obj));
            });
            result.setCdrugPackNumList(cdrugPackNumList);
        }

        // 构造每包毫升数数据源
        if (packMLParameterTo != null && org.apache.commons.lang3.StringUtils.isNotEmpty(packMLParameterTo.getValue())) {
            List<Integer> cdrugPackMLList = new ArrayList<>();
            String[] cdrugPackMLArray = packMLParameterTo.getValue().split("\\|");
            Arrays.asList(cdrugPackMLArray).forEach(obj -> {
                cdrugPackMLList.add(Integer.parseInt(obj));
            });
            result.setCdrugPackMlList(cdrugPackMLList);
        }

        return result;
    }

    @Override
    public List<UsageTo> getCdrugUsageList() {
        List<UsageTo> result = new ArrayList<>();
        // 查询全部用法
        List<UsageTo> usageToList = usageService.getUsages();
        if (CollectionUtils.isNotEmpty(usageToList)) {
            // 筛选中药用法
            // todo 用法类别暂没找到枚举，先用字符
            result = usageToList.stream().filter(o -> "2".equals(o.getUsageType())).toList();
        }
        return result;
    }

    @GlobalTransactional
    @Override
    public void batchSaveIpdOrder(List<CisOrderTempDetailAsToOrderNto> cisOrderTempDetailAsNtoList) {
        BusinessAssert.notEmpty(cisOrderTempDetailAsNtoList, OrderTempMaintainBusinessErrorEnum.ORDER_TEMP_MAINTAIN_0004, "组套明细");

        // 根据组号groupNo分组，以组为单位，保存医嘱
        Map<String, List<CisOrderTempDetailAsToOrderNto>> detailNtoMap = cisOrderTempDetailAsNtoList.stream().collect(Collectors.groupingBy(CisOrderTempDetailAsToOrderNto::getGroupNo));

        // 非护理、患者状态医嘱列表
        List<CisIpdOrderNto> cisIpdOrderNtoList = new ArrayList<>();
        // 护理、患者状态医嘱列表
        List<CisLongTermOrderNto> nursingAndOrderNtoList = new ArrayList<>();

        // 构造医嘱信息
        detailNtoMap.forEach((groupNo, detailNtoList) -> {
            if (detailNtoList.get(0).getOrderClass().equals(SystemTypeEnum.NURSING) || detailNtoList.get(0).getOrderClass().equals(SystemTypeEnum.PATIENT)) {
                nursingAndOrderNtoList.add(cisIpdOrderToTempUtil.buildNursingAndPatientOrder(detailNtoList));
            } else {
                cisIpdOrderNtoList.add(cisIpdOrderToTempUtil.buildOrderByTempDetails(detailNtoList));
            }
        });

        // 护理、患者属性保存并签发
        if (CollectionUtils.isNotEmpty(nursingAndOrderNtoList)) {
            cisIpdOrderToTempUtil.nursingAndPatientOrderOperate(nursingAndOrderNtoList);

        }

        // 普通类别医嘱直接保存
        if (CollectionUtils.isNotEmpty(cisIpdOrderNtoList)) {
            cisIpdCpoeService.createIpdOrderBatch(cisIpdOrderNtoList);
        }
    }

    /**
     * 草药库存信息转换拾取器TO
     * @param drugStockDoctorTo
     * @return
     */
    private OrderIssuedPickUpsAsTo drugStockTransferToOrder(DrugStockDoctorTo drugStockDoctorTo) {
        OrderIssuedPickUpsAsTo orderIssuedPickUpsAsTo = new OrderIssuedPickUpsAsTo();
        orderIssuedPickUpsAsTo.setSystemType(SystemTypeEnum.CDRUG.getCode());
        orderIssuedPickUpsAsTo.setCollectionFlag(false);
        orderIssuedPickUpsAsTo.setServiceItemCode(drugStockDoctorTo.getDrugGoodsCode());
        orderIssuedPickUpsAsTo.setServiceItemName(drugStockDoctorTo.getDrugGoodsName());
        orderIssuedPickUpsAsTo.setMnemonicCode(drugStockDoctorTo.getMnemonicCode());
        orderIssuedPickUpsAsTo.setDrugSpec(drugStockDoctorTo.getDrugSpec());
        orderIssuedPickUpsAsTo.setInsuranceTypeValue(drugStockDoctorTo.getInsuranceTypeValue());
        orderIssuedPickUpsAsTo.setOrgCode(drugStockDoctorTo.getStorageCode());
        orderIssuedPickUpsAsTo.setOrgName(drugStockDoctorTo.getStorageName());
        orderIssuedPickUpsAsTo.setQuantity(drugStockDoctorTo.getQuantityMax() + drugStockDoctorTo.getPackageUnitValue());
        orderIssuedPickUpsAsTo.setDrugForm(drugStockDoctorTo.getDrugFormValue());
        orderIssuedPickUpsAsTo.setInsuranceCode(drugStockDoctorTo.getInsuranceCode());
        orderIssuedPickUpsAsTo.setInsuranceName(drugStockDoctorTo.getInsuranceName());
        orderIssuedPickUpsAsTo.setPrice(drugStockDoctorTo.getSalePrice());
        orderIssuedPickUpsAsTo.setAntibacterialFlag(drugStockDoctorTo.getAntibacterialFlag());
        orderIssuedPickUpsAsTo.setDosageBase(drugStockDoctorTo.getDosageBase());
        orderIssuedPickUpsAsTo.setDosageUnit(drugStockDoctorTo.getDosageUnit());
        orderIssuedPickUpsAsTo.setDosageUnitValue(drugStockDoctorTo.getDosageUnitValue());
        orderIssuedPickUpsAsTo.setMinUnit(drugStockDoctorTo.getMinUnit());
        orderIssuedPickUpsAsTo.setUsage(drugStockDoctorTo.getUsage());
        orderIssuedPickUpsAsTo.setUsageValue(drugStockDoctorTo.getUsageValue());
        orderIssuedPickUpsAsTo.setFrequency(drugStockDoctorTo.getFrequency());
        orderIssuedPickUpsAsTo.setFrequencyValue(drugStockDoctorTo.getFrequencyValue());
        orderIssuedPickUpsAsTo.setMinUnitValue(drugStockDoctorTo.getMinUnitValue());
        orderIssuedPickUpsAsTo.setSkinTest(drugStockDoctorTo.getSkinTest());
        orderIssuedPickUpsAsTo.setPackageUnit(drugStockDoctorTo.getPackageUnit());
        orderIssuedPickUpsAsTo.setPackageUnitValue(drugStockDoctorTo.getPackageUnitValue());
        orderIssuedPickUpsAsTo.setPackageNum(drugStockDoctorTo.getPackageNum());

        // 设置草药产地
        orderIssuedPickUpsAsTo.setManufactureAddr(drugStockDoctorTo.getManufactureAddr());
        orderIssuedPickUpsAsTo.setPriceShow(df.format(drugStockDoctorTo.getSalePrice()) + "元/" + drugStockDoctorTo.getPackageUnitValue());

        return orderIssuedPickUpsAsTo;
    }

    private List<DictElementTo> getHumanOrgansDictElement(List<String> humanOrgansCodes) {
        if (CollectionUtils.isNotEmpty(humanOrgansCodes)) {
            List<DictElementTo> humanOrgansDictElements =  dictElementService.getCustomDictElement("HumanOrgans");
            return humanOrgansDictElements.stream().filter(o -> humanOrgansCodes.contains(o.getElementCode())).toList();
        }

        return new ArrayList<>();
    }

    private List<DictElementTo> getMethodDictElement(List<String> methodCodes) {
        if (CollectionUtils.isNotEmpty(methodCodes)) {
            List<DictElementTo> methodDictElements =  dictElementService.getCustomDictElement("DgimgMethod");
            return methodDictElements.stream().filter(o -> methodCodes.contains(o.getElementCode())).toList();
        }

        return new ArrayList<>();
    }

}
