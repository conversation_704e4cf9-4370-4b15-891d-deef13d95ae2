package com.bjgoodwill.hip.as.cis.opd.schedule.service;

import com.bjgoodwill.hip.as.cis.opd.schedule.to.*;
import com.bjgoodwill.hip.ds.org.api.to.WorkGroupTo;
import com.bjgoodwill.hip.ds.pat.schedule.to.PatTimeDescribeQto;
import com.bjgoodwill.hip.ds.pat.schedule.to.PatTimeDescribeTo;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * Description:
 *
 * <AUTHOR>
 * &#064;date 2025/3/28 下午2:19
 */
@Tag(name = "门诊出诊排班维护应用服务接口", description = "门诊出诊排班维护应用服务接口")
public interface PatVisitScheduleAsService {

    /**
     * 查询主页面显示数据
     * 查询条件[院区编码，出诊资源类型，科室编码]
     *
     * @param qto
     * @return
     */
    List<PatVisitScheduleMainShowAsTo> getShowList(PatVisitScheduleMainAsQto qto);

    /**
     * 修改指定排班号数
     *
     * @param id    排班ID
     * @param asEto 修改排班号数
     */
    PatVisitScheduleAsTo updateNum(String id, PatVisitScheduleNumAsEto asEto);

    /**
     * 停诊指定排班
     *
     * @param id        排班ID
     * @param stopAsEto 停诊实体
     */
    PatVisitScheduleAsTo stopVisit(String id, PatVisitScheduleStopAsEto stopAsEto);

    /**
     * 创建排班
     *
     * @param asNtoList 排班生成记录
     */
    void create(List<PatVisitScheduleRecordAsNto> asNtoList);

    /**
     * 创建临时科室号排班
     *
     * @param asNto
     */
    void createTempDeptSchedule(PatVisitScheduleTempDeptAsNto asNto);

    /**
     * 创建临时医生号排班
     *
     * @param asNto
     */
    void createTempDocSchedule(PatVisitScheduleTempDocAsNto asNto);

    /**
     * 获取出诊科室列表
     *
     * @param hospitalAreaCode 院区编码
     * @return
     */
    List<WorkGroupTo> getWorkGroupList(String hospitalAreaCode);
}
