package com.bjgoodwill.hip.as.cis.nurse.ipd.service.impl;

import com.bjgoodwill.hip.as.cis.doc.ipd.to.CisIpdDocOrderAsTo;
import com.bjgoodwill.hip.as.cis.nurse.ipd.enums.CisNurseIpdBusinessErrorEnum;
import com.bjgoodwill.hip.as.cis.nurse.ipd.service.InOutAsService;
import com.bjgoodwill.hip.as.cis.nurse.ipd.to.*;
import com.bjgoodwill.hip.as.business.util.emr.EmrSyncService;
import com.bjgoodwill.hip.business.util.cis.common.CisOrgCommonNto;
import com.bjgoodwill.hip.business.util.cis.common.enums.CisStatusEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.DiagnosisTypeEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.OrderTypeEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.business.util.drug.enums.DrugIpdDataStatusEnum;
import com.bjgoodwill.hip.business.util.enums.dict.DictCodeEnum;
import com.bjgoodwill.hip.business.util.mq.to.pat.PatIpdCancelDischargeEto;
import com.bjgoodwill.hip.common.exception.BusinessException;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.common.util.HIPBeanUtil;
import com.bjgoodwill.hip.ds.cis.apply.apply.service.CisChangeDeptApplyService;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisChangeDeptApplyTo;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.service.CisOrderExecPlanService;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.to.CisOrderExecPlanChargeTo;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.to.CisOrderExecPlanTo;
import com.bjgoodwill.hip.ds.cis.cpoe.diagnose.service.CisIpdDiagnoseService;
import com.bjgoodwill.hip.ds.cis.cpoe.diagnose.to.CisIpdDiagnoseTo;
import com.bjgoodwill.hip.ds.cis.cpoe.order.service.CisIpdCpoeService;
import com.bjgoodwill.hip.ds.cis.cpoe.order.to.CisIpdOrderTo;
import com.bjgoodwill.hip.ds.cis.cpoe.order.to.CisLongTermOrderTo;
import com.bjgoodwill.hip.ds.drug.ipd.apply.service.DrugIpdApplyService;
import com.bjgoodwill.hip.ds.drug.ipd.apply.to.DrugIpdApplyRefundTo;
import com.bjgoodwill.hip.ds.drug.ipd.apply.to.DrugIpdApplyTo;
import com.bjgoodwill.hip.ds.econ.ipd.bill.amount.service.EconIpdAmountService;
import com.bjgoodwill.hip.ds.econ.ipd.bill.amount.to.EconIpdAmountTo;
import com.bjgoodwill.hip.ds.econ.ipd.bill.service.EconIpdApplyService;
import com.bjgoodwill.hip.ds.econ.ipd.bill.service.EconIpdBillService;
import com.bjgoodwill.hip.ds.econ.ipd.bill.to.*;
import com.bjgoodwill.hip.ds.org.api.service.StaffService;
import com.bjgoodwill.hip.ds.org.api.service.WorkGroupService;
import com.bjgoodwill.hip.ds.org.api.to.StaffLocalTo;
import com.bjgoodwill.hip.ds.org.api.to.WorkGroupInpatientNursingTo;
import com.bjgoodwill.hip.ds.org.api.to.WorkGroupTo;
import com.bjgoodwill.hip.ds.param.api.service.ParameterService;
import com.bjgoodwill.hip.ds.param.api.to.ParameterTo;
import com.bjgoodwill.hip.ds.pat.in.hospital.bed.service.PatServiceBedService;
import com.bjgoodwill.hip.ds.pat.in.hospital.bed.to.PatServiceBedTo;
import com.bjgoodwill.hip.ds.pat.in.hospital.changeout.service.PatIpdChangeDeptOutService;
import com.bjgoodwill.hip.ds.pat.in.hospital.changeout.to.PatIpdChangeDeptOutNto;
import com.bjgoodwill.hip.ds.pat.in.hospital.changeout.to.PatIpdChangeDeptOutQto;
import com.bjgoodwill.hip.ds.pat.in.hospital.changeout.to.PatIpdChangeDeptOutTo;
import com.bjgoodwill.hip.ds.pat.in.hospital.indept.service.ChangePatIpdInDeptService;
import com.bjgoodwill.hip.ds.pat.in.hospital.indept.service.NormalPatIpdInDeptService;
import com.bjgoodwill.hip.ds.pat.in.hospital.indept.to.ChangePatIpdInDeptNto;
import com.bjgoodwill.hip.ds.pat.in.hospital.indept.to.NormalPatIpdInDeptNto;
import com.bjgoodwill.hip.ds.pat.in.hospital.inpatient.enmus.InpatientStatusEnum;
import com.bjgoodwill.hip.ds.pat.in.hospital.inpatient.service.PatIpdInpatientExtService;
import com.bjgoodwill.hip.ds.pat.in.hospital.inpatient.service.PatIpdInpatientService;
import com.bjgoodwill.hip.ds.pat.in.hospital.inpatient.to.PatIpdInpatientExtTo;
import com.bjgoodwill.hip.ds.pat.in.hospital.inpatient.to.PatIpdInpatientQto;
import com.bjgoodwill.hip.ds.pat.in.hospital.inpatient.to.PatIpdInpatientTo;
import com.bjgoodwill.hip.ds.pat.in.hospital.inpatient.to.PatIpdInpatientTransferTreatedEto;
import com.bjgoodwill.hip.ds.pat.in.hospital.regist.service.PatIpdRegistService;
import com.bjgoodwill.hip.ds.pat.in.hospital.regist.to.PatIpdRegistTo;
import com.bjgoodwill.hip.ds.pat.index.enmus.PatIndexFeeTypeEnum;
import com.bjgoodwill.hip.ds.pat.index.enmus.PatSignsStatusEnum;
import com.bjgoodwill.hip.ds.pat.index.service.PatIndexService;
import com.bjgoodwill.hip.ds.pat.index.service.PatSignsService;
import com.bjgoodwill.hip.ds.pat.index.to.*;
import com.bjgoodwill.hip.ds.term.api.service.DictElementService;
import com.bjgoodwill.hip.ds.term.api.to.DictElementTo;
import com.bjgoodwill.hip.enums.DictParameterEnum;
import com.bjgoodwill.hip.security.bean.CurrentOrgInfo;
import com.bjgoodwill.hip.security.util.HIPCurrentOrgUtils;
import io.micrometer.common.util.StringUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.seata.spring.annotation.GlobalTransactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: zhangyunchuan
 * @Date: 2024/8/16 12:09
 * @PROJECT: hip-cis-ac
 */
@Service("com.bjgoodwill.hip.as.cis.nurse.ipd.service.InOutAsService")
public class InOutAsServiceImpl implements InOutAsService {

    private static final Logger logger = LoggerFactory.getLogger(InOutAsServiceImpl.class);

    @Autowired
    private PatIpdRegistService patIpdRegistService;

    @Autowired
    private PatIpdInpatientService patIpdInpatientService;

    @Autowired
    private PatServiceBedService patServiceBedService;

    @Autowired
    private PatIpdChangeDeptOutService patIpdChangeDeptOutService;

    @Autowired
    private NormalPatIpdInDeptService normalPatIpdInDeptService;

    @Autowired
    private ChangePatIpdInDeptService changePatIpdInDeptService;

    @Autowired
    private PatIndexService patIndexService;

    @Autowired
    private CisOrderExecPlanService cisOrderExecPlanService;

    @Autowired
    private EconIpdAmountService econIpdAmountService;

    @Autowired
    private DrugIpdApplyService drugIpdApplyService;

    @Autowired
    private PatSignsService patSignsService;

    @Autowired
    private EconIpdApplyService econIpdApplyService;

    @Autowired
    private CisIpdDiagnoseService cisIpdDiagnoseService;

    @Autowired
    private PatIpdInpatientExtService patIpdInpatientExtService;

    @Autowired
    private CisIpdCpoeService cisIpdCpoeService;

    @Autowired
    private CisChangeDeptApplyService cisChangeDeptApplyService;

    @Autowired
    private WorkGroupService workGroupService;

    @Autowired
    private DictElementService dictElementService;

    @Autowired
    private ParameterService parameterService;

    @Autowired
    private EconIpdBillService econIpdBillService;

    @Autowired
    private StaffService staffService;

    @Autowired
    private EmrSyncService emrSyncService;

    /**
     * 床位查询
     */
    @Override
    public List<PatServiceBedTo> getEmptyBedInfo(String deptNurseCode) {
        //当前登录组下床位
        Assert.notNull(deptNurseCode, "护理组不能为空！");
        return patServiceBedService.getEnableAvailablePatServiceBedsByNurseDeptCode(deptNurseCode);
    }

    @Override
    @GlobalTransactional
    public void transferDeptOut(PatIpdChangeDeptOutAsNto patIpdChangeDeptOutAsNto) {
        PatIpdChangeDeptOutNto patIpdChangeDeptOutNto = HIPBeanUtil.copy(patIpdChangeDeptOutAsNto, PatIpdChangeDeptOutNto.class);
        patIpdChangeDeptOutNto.setLoginDeptCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getDeptCode());
        PatIpdInpatientTo patIpdInpatientByVisitCode = patIpdInpatientService.getPatIpdInpatientByVisitCode(patIpdChangeDeptOutAsNto.getVisitCode());
        patIpdChangeDeptOutNto.setChangeOutDept(patIpdInpatientByVisitCode.getInDeptCode());
        patIpdChangeDeptOutNto.setChangeOutDeptName(patIpdInpatientByVisitCode.getInDeptName());
        patIpdChangeDeptOutNto.setChangeOutNurseDept(patIpdInpatientByVisitCode.getDeptNurseCode());
        patIpdChangeDeptOutNto.setChangeOutNurseDeptName(patIpdInpatientByVisitCode.getDeptNurseName());
        patIpdChangeDeptOutNto.setMasterDoctor(patIpdInpatientByVisitCode.getMasterDoctor());
        patIpdChangeDeptOutNto.setMasterDoctorName(patIpdInpatientByVisitCode.getMasterDoctorName());
        patIpdChangeDeptOutNto.setDirectorDoctor(patIpdInpatientByVisitCode.getDirectorDoctor());
        patIpdChangeDeptOutNto.setDirectorDoctorName(patIpdInpatientByVisitCode.getDirectorDoctorName());
        List<CisIpdOrderTo> cisIpdOrderToList = cisIpdCpoeService.getIpdOrdersByVisitCode(patIpdChangeDeptOutAsNto.getVisitCode());
        if (CollectionUtils.isNotEmpty(cisIpdOrderToList)) {
            //转科医嘱
            List<CisIpdOrderTo> changeDeptList = cisIpdOrderToList.stream().filter(a -> a.getStatusCode().equals(CisStatusEnum.PASS) && a.getOrderClass().equals(SystemTypeEnum.CHANGEDEPT)).toList();
            if (CollectionUtils.isNotEmpty(changeDeptList)) {
                CisIpdOrderTo cisIpdOrderTo = changeDeptList.get(0);
                patIpdChangeDeptOutNto.setOrderId(cisIpdOrderTo.getId());
                CisChangeDeptApplyTo cisChangeDeptApplyById = cisChangeDeptApplyService.getCisChangeDeptApplyById(cisIpdOrderTo.getApplyCode());
                if (cisChangeDeptApplyById != null) {
                    patIpdChangeDeptOutNto.setAcceptDept(cisChangeDeptApplyById.getInOrgCode());
                    if (StringUtils.isNotEmpty(cisChangeDeptApplyById.getInOrgCode())) {
                        WorkGroupTo workGroupTo = workGroupService.getWorkGroup(cisChangeDeptApplyById.getInOrgCode());
                        if (workGroupTo != null) {
                            patIpdChangeDeptOutNto.setAcceptDeptName(workGroupTo.getName());
                        }
                    }
                    patIpdChangeDeptOutNto.setAcceptNurseDept(cisChangeDeptApplyById.getInDeptNurseCode());
                    if (StringUtils.isNotEmpty(cisChangeDeptApplyById.getInDeptNurseCode())) {
                        WorkGroupInpatientNursingTo inpatientNursing = workGroupService.getInpatientNursing(cisChangeDeptApplyById.getInDeptNurseCode());
                        if (inpatientNursing != null) {
                            patIpdChangeDeptOutNto.setAcceptNurseDeptName(inpatientNursing.getName());
                        }
                    }
                } else {
                    throw new BusinessException("未查询到转科医嘱申请单信息");
                }
            } else {
                throw new BusinessException("未查询到转科医嘱信息");
            }
        } else {
            throw new BusinessException("未查询到转科医嘱信息");
        }
        //住院患者转科
        patIpdChangeDeptOutService.createPatIpdChangeDeptOut(patIpdChangeDeptOutNto);
        //执行单执行
        CisOrgCommonNto cisOrgCommonNto = new CisOrgCommonNto();
        cisOrgCommonNto.setOrgCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
        cisOrgCommonNto.setOrgName(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupName());
        cisOrderExecPlanService.specialExecuteCisOrderExecPlanBatch(patIpdChangeDeptOutAsNto.getVisitCode(), SystemTypeEnum.CHANGEDEPT, cisOrgCommonNto);
    }

    @Override
    public List<PatIpdInpatientAsTo> getToBeDischargePatInfo() {
        CurrentOrgInfo currentOrgInfo = HIPCurrentOrgUtils.getCurrentOrgInfo();
        BusinessAssert.notNull(currentOrgInfo, CisNurseIpdBusinessErrorEnum.CIS_NURSE_IPD_0001, "登录信息");
        BusinessAssert.notNull(currentOrgInfo.getWorkGroupCode(), CisNurseIpdBusinessErrorEnum.CIS_NURSE_IPD_0001, "登录科室");
        List<PatIpdInpatientAsTo> patIpdInpatientAsToList = new ArrayList<>();
        List<PatIpdInpatientTo> outingHospitalPatForInout = patIpdInpatientService.getOutingHospitalPatForInout(currentOrgInfo.getWorkGroupCode());
        if (CollectionUtils.isNotEmpty(outingHospitalPatForInout)) {
            patIpdInpatientAsToList = HIPBeanUtil.copy(outingHospitalPatForInout, PatIpdInpatientAsTo.class);
            patIpdInpatientAsToList.forEach(a -> {
                if (StringUtils.isNotEmpty(a.getFeeType())) {
                    PatIndexFeeTypeEnum[] values = PatIndexFeeTypeEnum.values();
                    if (CollectionUtils.isNotEmpty(Arrays.asList(values))) {
                        for (PatIndexFeeTypeEnum value : values) {
                            if (value.getCode().equals(a.getFeeType()) && StringUtils.isNotEmpty(value.getName())) {
                                a.setFeeTypeName(value.getName());
                            }
                        }
                    }
                }
                PatServiceBedTo patServiceBedsById = patServiceBedService.getPatServiceBedsById(a.getBedId());
                if (patServiceBedsById != null) {
                    a.setRoomNo(patServiceBedsById.getRoomNo());
                }
            });
        }
        return patIpdInpatientAsToList;
    }

    @Override
    public List<PatIpdInpatientAsTo> getPatIpdInpatientCommonQuery(PatIpdInpatientQto patIpdInpatientQto) {
        List<PatIpdInpatientAsTo> patIpdInpatientAsToList = new ArrayList<>();
        List<PatIpdInpatientTo> patIpdInpatientToList = patIpdInpatientService.getPatIpdInpatientCommonQuery(patIpdInpatientQto);
        if (CollectionUtils.isNotEmpty(patIpdInpatientToList)) {
            patIpdInpatientAsToList = HIPBeanUtil.copy(patIpdInpatientToList, PatIpdInpatientAsTo.class);
            for (PatIpdInpatientAsTo patIpdInpatientAsTo : patIpdInpatientAsToList) {
                if (StringUtils.isNotEmpty(patIpdInpatientAsTo.getFeeType())) {
                    patIpdInpatientAsTo.setFeeTypeName(patIpdInpatientAsTo.getFeeType() == null ? null : "08".equals(patIpdInpatientAsTo.getFeeType()) ? "自费" : "医保");
                }
                //诊断信息
                List<CisIpdDiagnoseTo> cisIpdDiagnoseToList = cisIpdDiagnoseService.getCisIpdDiagnoseByVisitCode(patIpdInpatientAsTo.getVisitCode());
                if (CollectionUtils.isNotEmpty(cisIpdDiagnoseToList)) {
                    //入院诊断
                    List<CisIpdDiagnoseTo> inHospitalDiagnosis = cisIpdDiagnoseToList.stream().filter(a -> a.getDiagnosisType().equals(DiagnosisTypeEnum.B)).toList();
                    if (CollectionUtils.isNotEmpty(inHospitalDiagnosis)) {
                        List<CisIpdDiagnoseTo> cisIpdDiagnosisList = inHospitalDiagnosis.stream().filter(CisIpdDiagnoseTo::getIsChief).toList();
                        if (CollectionUtils.isNotEmpty(cisIpdDiagnosisList)) {
                            patIpdInpatientAsTo.setInHospitalDiagnosis(cisIpdDiagnosisList.get(0).getDiagnosisName());
                        } else {
                            patIpdInpatientAsTo.setInHospitalDiagnosis(inHospitalDiagnosis.get(0).getDiagnosisName());
                        }
                    }
                    //出院诊断
                    List<CisIpdDiagnoseTo> outHospitalDiagnosis = cisIpdDiagnoseToList.stream().filter(a -> a.getDiagnosisType().equals(DiagnosisTypeEnum.C)).toList();
                    if (CollectionUtils.isNotEmpty(outHospitalDiagnosis)) {
                        List<CisIpdDiagnoseTo> cisIpdDiagnosisList = outHospitalDiagnosis.stream().filter(CisIpdDiagnoseTo::getIsChief).toList();
                        if (CollectionUtils.isNotEmpty(cisIpdDiagnosisList)) {
                            patIpdInpatientAsTo.setOutHospitalDiagnosis(cisIpdDiagnosisList.get(0).getDiagnosisName());
                        } else {
                            patIpdInpatientAsTo.setOutHospitalDiagnosis(outHospitalDiagnosis.get(0).getDiagnosisName());
                        }
                    }
                }
            }
        }
        return patIpdInpatientAsToList;
    }

    @Override
    public PatIpdInpatientTo dischargeRecallQuery(String visitCode) {
        return patIpdInpatientService.getPatIpdInpatientByVisitCode(visitCode);
    }

    @Override
    @GlobalTransactional
    public void executeCisOrderExecPlanBatch(String workGroupCode, List<String> execIds) {
        //医嘱执行
        CisOrgCommonNto cisOrgCommonNto = new CisOrgCommonNto();
        cisOrgCommonNto.setOrgCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
        cisOrgCommonNto.setOrgName(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupName());
        cisOrderExecPlanService.executeCisOrderExecPlanBatch(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode(), execIds, cisOrgCommonNto);
        //非药品医嘱计费方式
        ParameterTo<String> econNonDrugFeeMode = parameterService.getStringParameter(DictParameterEnum.EconNonDrugFeeMode.getCode());
        if (econNonDrugFeeMode != null && StringUtils.isNotEmpty(econNonDrugFeeMode.getValue())) {
            if (econNonDrugFeeMode.getValue().equals("Execute")) {
                //获取执行单信息
                List<CisOrderExecPlanTo> cisOrderExecPlanToList = cisOrderExecPlanService.findCisOrderExecPlanInExeIds(execIds);
                String visitCode = cisOrderExecPlanToList.get(0).getVisitCode();
                PatIpdInpatientTo patIpdInpatientByVisitCode = patIpdInpatientService.getPatIpdInpatientByVisitCode(visitCode);
                PatIndexTo patIndexById = patIndexService.getPatIndexById(patIpdInpatientByVisitCode.getPatCode());
                //医嘱计费
                List<EconIpdBillChargingTo> econIpdBillChargingToList = new ArrayList<>();
                for (CisOrderExecPlanTo cisOrderExecPlanTo : cisOrderExecPlanToList) {
                    if (cisOrderExecPlanTo.getOrderClass().equals(SystemTypeEnum.CDRUG) || cisOrderExecPlanTo.getOrderClass().equals(SystemTypeEnum.EDRUG) || cisOrderExecPlanTo.getOrderClass().equals(SystemTypeEnum.PATIENT) || cisOrderExecPlanTo.getOrderClass().equals(SystemTypeEnum.ENTRUST) || cisOrderExecPlanTo.getOrderClass().equals(SystemTypeEnum.LINE)) {
                        continue;
                    }
                    EconIpdBillChargingTo econIpdBillChargingTo = new EconIpdBillChargingTo();
                    econIpdBillChargingTo.setIdentity(patIndexById.getIdentity());
                    econIpdBillChargingTo.setPoor(patIndexById.getPoorType());
                    econIpdBillChargingTo.setSex(patIndexById.getSex());
                    econIpdBillChargingTo.setVisitCode(cisOrderExecPlanTo.getVisitCode());
                    econIpdBillChargingTo.setFeeType(patIpdInpatientByVisitCode.getFeeType());
                    econIpdBillChargingTo.setDeptNurse(patIpdInpatientByVisitCode.getDeptNurseCode());
                    econIpdBillChargingTo.setDeptNurseName(patIpdInpatientByVisitCode.getDeptNurseName());
                    econIpdBillChargingTo.setOrderId(cisOrderExecPlanTo.getOrderId());
                    econIpdBillChargingTo.setServiceItemCode(cisOrderExecPlanTo.getServiceItemCode());
                    econIpdBillChargingTo.setServiceItemName(cisOrderExecPlanTo.getServiceItemName());
                    econIpdBillChargingTo.setVisitOrg(cisOrderExecPlanTo.getCreateOrgCode());
                    econIpdBillChargingTo.setDoctorOrg(cisOrderExecPlanTo.getCreateOrgCode());
                    if (StringUtils.isNotEmpty(cisOrderExecPlanTo.getCreateOrgName())) {
                        econIpdBillChargingTo.setVisitOrgName(cisOrderExecPlanTo.getCreateOrgName());
                        econIpdBillChargingTo.setDoctorOrgName(cisOrderExecPlanTo.getCreateOrgName());
                    } else {
                        //人员信息
                        StaffLocalTo staffLocal = staffService.getStaffLocal(cisOrderExecPlanTo.getCreatedStaff());
                        if (staffLocal != null) {
                            econIpdBillChargingTo.setVisitOrgName(staffLocal.getAppointmentDeptName());
                            econIpdBillChargingTo.setDoctorOrgName(staffLocal.getAppointmentDeptName());
                        }
                    }
                    econIpdBillChargingTo.setDoctor(cisOrderExecPlanTo.getHeldStaff());
                    econIpdBillChargingTo.setDoctorName(cisOrderExecPlanTo.getHeldStaffName());
                    econIpdBillChargingTo.setExecPlanId(cisOrderExecPlanTo.getId());
                    econIpdBillChargingTo.setPreExecuteDate(cisOrderExecPlanTo.getExecPlanDate());
                    econIpdBillChargingTo.setExecuteOrg(cisOrderExecPlanTo.getExecOrgCode());
                    econIpdBillChargingTo.setExecuteOrgName(cisOrderExecPlanTo.getExecOrgName());
                    econIpdBillChargingTo.setCreateOrg(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
                    econIpdBillChargingTo.setCreateOrgName(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupName());
                    econIpdBillChargingTo.setCurWorkGroupCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
                    econIpdBillChargingTo.setCurWorkGroupName(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupName());
                    econIpdBillChargingTo.setBranchHospitalCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getHospitalAreaCode());
                    econIpdBillChargingTo.setBranchHospitalName(HIPCurrentOrgUtils.getCurrentOrgInfo().getHospitalAreaName());
                    List<EconIpdBillDetailChargingTo> chargingDetailToList = new ArrayList<>();
                    for (CisOrderExecPlanChargeTo cisOrderExecPlanChargeTo : cisOrderExecPlanTo.getCisOrderExecPlanChargeTos()) {
                        EconIpdBillDetailChargingTo econIpdBillDetailChargingTo = new EconIpdBillDetailChargingTo();
                        econIpdBillDetailChargingTo.setSystemItemClass(cisOrderExecPlanChargeTo.getSystemItemClass());
                        econIpdBillDetailChargingTo.setCode(cisOrderExecPlanChargeTo.getPriceItemCode());
                        econIpdBillDetailChargingTo.setPackagePrice(cisOrderExecPlanChargeTo.getPrice());
                        econIpdBillDetailChargingTo.setMinNum(BigDecimal.valueOf(cisOrderExecPlanChargeTo.getNum()));
                        econIpdBillDetailChargingTo.setExecPlanChargeId(cisOrderExecPlanChargeTo.getId());
                        if (cisOrderExecPlanChargeTo.getLimitConformFlag() != null) {
                            econIpdBillDetailChargingTo.setLimitConformFlag(cisOrderExecPlanChargeTo.getLimitConformFlag());
                        }
                        if (cisOrderExecPlanChargeTo.getIsFixed() != null) {
                            econIpdBillDetailChargingTo.setFixedFlag(cisOrderExecPlanChargeTo.getIsFixed());
                        }
                        chargingDetailToList.add(econIpdBillDetailChargingTo);
                    }
                    econIpdBillChargingTo.setChargingDetailToList(chargingDetailToList);
                    econIpdBillChargingToList.add(econIpdBillChargingTo);
                }
                econIpdBillService.cisCharging(econIpdBillChargingToList);
            }
        }
    }

    @Override
    @GlobalTransactional
    public void noExecuteCisOrderExecPlanBatch(String workGroupCode, List<String> execIds) {
        CisOrgCommonNto cisOrgCommonNto = new CisOrgCommonNto();
        cisOrgCommonNto.setOrgCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
        cisOrgCommonNto.setOrgName(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupName());
        cisOrderExecPlanService.noExecuteCisOrderExecPlanBatch(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode(), execIds, cisOrgCommonNto);
        //获取执行单信息
        List<CisOrderExecPlanTo> cisOrderExecPlanToList = cisOrderExecPlanService.findCisOrderExecPlanInExeIds(execIds);

        //待退费申请的药品数据
        List<CisOrderExecPlanTo> backFeeApplyList = new ArrayList<>();
        //待退药、作废领药的药品数据
        List<CisOrderExecPlanTo> backDrugList = new ArrayList<>();
        //待直接退费的药品数据
        List<CisOrderExecPlanTo> backFeeList = new ArrayList<>();

        //数据分堆处理
        cisOrderExecPlanToList.forEach(c -> {
            //1.已计费&己发药，生成退药申请单（退药时退费）。调用药品退药接口，退费接口需要在退药页面调用，此处不调用退费接口
            if (c.getIsCharge() != null && c.getIsCharge() && c.getDrugInoutType() != null
                    && c.getDrugInoutType().getCode().equals(DrugIpdDataStatusEnum.已发.getCode())) {
                backDrugList.add(c);
                backFeeApplyList.add(c);
            }
            //2.已计费&己发送&未发药领药申请，退费并作废领药申请。
            if (c.getIsCharge() != null && c.getIsCharge() && c.getDrugInoutType() != null
                    && c.getIsSend() != null && c.getIsSend() && c.getDrugInoutType() == null) {
                backDrugList.add(c);
                backFeeList.add(c);
            }
            //3.未计费&己发送领药申请，作废领药申请
            if (c.getIsCharge() == null && c.getIsSend() != null && c.getIsSend()) {
                backDrugList.add(c);
            }
        });
        //处理退药、作废领药申请数据，调用退药接口
        List<DrugIpdApplyTo> drugIpdApplyToList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(backDrugList)) {
            List<DrugIpdApplyRefundTo> drugIpdApplyRefundTos = new ArrayList<>();
            backDrugList.forEach(backDrug -> {
                DrugIpdApplyRefundTo drugIpdApplyRefundTo = new DrugIpdApplyRefundTo();
                drugIpdApplyRefundTo.setOrderExecuteId(backDrug.getId());
                if (backDrug.getIsCharge() != null && backDrug.getIsCharge()) {
                    drugIpdApplyRefundTo.setFeeFlag(true);
                } else {
                    drugIpdApplyRefundTo.setFeeFlag(false);
                }
                drugIpdApplyRefundTos.add(drugIpdApplyRefundTo);
            });
            drugIpdApplyToList = drugIpdApplyService.batchRefundApplyByCis(drugIpdApplyRefundTos);
        }

        //处理退费申请数据，调用
        if (CollectionUtils.isNotEmpty(backFeeApplyList)) {
            List<EconIpdBillStopDispenseDrugTo> stopDispenseDrugToList = new ArrayList<>();
            for (CisOrderExecPlanTo execPlanTo : backFeeApplyList) {
                for (DrugIpdApplyTo drugIpdApplyTo : drugIpdApplyToList) {
                    if (execPlanTo.getId().equals(drugIpdApplyTo.getOrderExecuteId())) {
                        EconIpdBillStopDispenseDrugTo econIpdBillStopDispenseDrugTo = new EconIpdBillStopDispenseDrugTo();
                        econIpdBillStopDispenseDrugTo.setDrugApplyId(drugIpdApplyTo.getId());
                        econIpdBillStopDispenseDrugTo.setDispenseDrugApplyId(drugIpdApplyTo.getRefundApplyId());
                        econIpdBillStopDispenseDrugTo.setMinNum(drugIpdApplyTo.getApplyNum());
                        stopDispenseDrugToList.add(econIpdBillStopDispenseDrugTo);
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(stopDispenseDrugToList)) {
                EconIpdBillStopDispenseDrugExecPlanTo econIpdBillStopDispenseDrugExecPlanTo = new EconIpdBillStopDispenseDrugExecPlanTo();
                econIpdBillStopDispenseDrugExecPlanTo.setCreateOrg(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
                econIpdBillStopDispenseDrugExecPlanTo.setCreateOrgName(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupName());
                econIpdBillStopDispenseDrugExecPlanTo.setCurWorkGroupCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
                econIpdBillStopDispenseDrugExecPlanTo.setCurWorkGroupName(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupName());
                econIpdBillStopDispenseDrugExecPlanTo.setBranchHospitalCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getHospitalAreaCode());
                econIpdBillStopDispenseDrugExecPlanTo.setBranchHospitalName(HIPCurrentOrgUtils.getCurrentOrgInfo().getHospitalAreaName());
                econIpdBillStopDispenseDrugExecPlanTo.setExecPlanIdList(backFeeApplyList.stream().map(CisOrderExecPlanTo::getId).distinct().toList());
                econIpdBillStopDispenseDrugExecPlanTo.setStopDispenseDrugToList(stopDispenseDrugToList);
                econIpdBillService.stopDispenseDrugExecPlan(econIpdBillStopDispenseDrugExecPlanTo);
            }
        }
        //处理直接退费数据，直接调用经济退费接口
        if (CollectionUtils.isNotEmpty(backFeeList)) {
            EconIpdBillRefundExecPlanTo econIpdBillRefundExecPlanTo = new EconIpdBillRefundExecPlanTo();
            econIpdBillRefundExecPlanTo.setCreateOrg(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
            econIpdBillRefundExecPlanTo.setCreateOrgName(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupName());
            econIpdBillRefundExecPlanTo.setCurWorkGroupCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
            econIpdBillRefundExecPlanTo.setCurWorkGroupName(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupName());
            econIpdBillRefundExecPlanTo.setBranchHospitalCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getHospitalAreaCode());
            econIpdBillRefundExecPlanTo.setBranchHospitalName(HIPCurrentOrgUtils.getCurrentOrgInfo().getHospitalAreaName());
            econIpdBillRefundExecPlanTo.setExecPlanIdList(backFeeList.stream().map(CisOrderExecPlanTo::getId).distinct().toList());
            econIpdBillService.refundExecPlan(econIpdBillRefundExecPlanTo);
        }
    }

    @Override
    @GlobalTransactional
    public void executeDischarge(String visitCode) {
        //患者出院
        patIpdInpatientService.executeDischarge(visitCode, HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode(), HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupName());
        //执行单执行
        CisOrgCommonNto cisOrgCommonNto = new CisOrgCommonNto();
        cisOrgCommonNto.setOrgCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
        cisOrgCommonNto.setOrgName(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupName());
        cisOrderExecPlanService.specialExecuteCisOrderExecPlanBatch(visitCode, SystemTypeEnum.OUTHOSPITAL, cisOrgCommonNto);
    }

    /**
     * 待入科-患者查询
     */
    @Override
    public List<PatIpdInpatientAsTo> getAdmissionInfo(String deptNurseCode) {
        Assert.notNull(deptNurseCode, "护理组不能为空！");
        List<PatIpdInpatientAsTo> inpatientAsToList = new ArrayList<>();
        //患者列表
        List<PatIpdInpatientTo> patIpdInpatientInDeptByQto = patIpdInpatientService.getInDeptPatForInout(deptNurseCode);
        if (CollectionUtils.isNotEmpty(patIpdInpatientInDeptByQto)) {
            List<PatIpdInpatientTo> registeredList = patIpdInpatientInDeptByQto.stream().filter(a -> a.getStatusCode() != null && a.getStatusCode().equals(InpatientStatusEnum.REGISTERED)).toList();
            if (CollectionUtils.isNotEmpty(registeredList)) {
                for (PatIpdInpatientTo patIpdInpatientTo : registeredList) {
                    PatIpdRegistTo patIpdRegistTo = patIpdRegistService.getByVisitCode(patIpdInpatientTo.getVisitCode());
                    PatIpdInpatientAsTo patIpdInpatientAsTo = HIPBeanUtil.copy(patIpdInpatientTo, PatIpdInpatientAsTo.class);
                    if (patIpdRegistTo != null) {
                        if (StringUtils.isNotBlank(patIpdRegistTo.getInRoute())) {
                            List<DictElementTo> customDictElement = dictElementService.getCustomDictElement("InpWay");
                            if (CollectionUtils.isNotEmpty(customDictElement)) {
                                List<DictElementTo> dictElementToList = customDictElement.stream().filter(a -> a.getElementCode().equals(patIpdRegistTo.getInRoute())).toList();
                                if (CollectionUtils.isNotEmpty(dictElementToList)) {
                                    patIpdInpatientAsTo.setInRoute(dictElementToList.get(0).getElementCode());
                                    patIpdInpatientAsTo.setInRouteName(dictElementToList.get(0).getElementName());
                                }
                            }
                        }
                        if (StringUtils.isNotBlank(patIpdRegistTo.getPatientClass())) {
                            List<DictElementTo> customDictElement = dictElementService.getCustomDictElement("PatientSource");
                            if (CollectionUtils.isNotEmpty(customDictElement)) {
                                List<DictElementTo> dictElementToList = customDictElement.stream().filter(a -> a.getElementCode().equals(patIpdRegistTo.getPatientClass())).toList();
                                if (CollectionUtils.isNotEmpty(dictElementToList)) {
                                    patIpdInpatientAsTo.setPatientClass(dictElementToList.get(0).getElementCode());
                                    patIpdInpatientAsTo.setPatientClassName(dictElementToList.get(0).getElementName());
                                }
                            }
                        }
                    }
                    inpatientAsToList.add(patIpdInpatientAsTo);
                }
            }
            List<PatIpdInpatientTo> transferingList = patIpdInpatientInDeptByQto.stream().filter(a -> a.getStatusCode() != null && a.getStatusCode().equals(InpatientStatusEnum.TRANSFERING)).toList();
            if (CollectionUtils.isNotEmpty(transferingList)) {
                for (PatIpdInpatientTo transfering : transferingList) {
                    PatIpdInpatientAsTo patIpdInpatientAsTo = HIPBeanUtil.copy(transfering, PatIpdInpatientAsTo.class);
                    PatIpdInpatientAsTo inpatientAsTo = constructPatIpdInpatientAsTo(patIpdInpatientAsTo, transfering.getPatIpdChangeDeptOutTo());
                    inpatientAsToList.add(inpatientAsTo);
                }
            }
        }
        return inpatientAsToList;
    }

    /**
     * 待入科-患者入科
     */
    @Override
    public void intervention(InOutAsNto inOutAsNto) {
        Assert.notNull(inOutAsNto.getId(), "标识不能为空！");
        Assert.notNull(inOutAsNto.getSignsId(), "体征标识不能为空！");
        //医生站护士站赋值(改为从页面取值，有问题再修改)
//        inOutAsNto.setInDept(HIPCurrentOrgUtils.getCurrentOrgInfo().getDeptCode());
//        inOutAsNto.setInDeptName(HIPCurrentOrgUtils.getCurrentOrgInfo().getDeptName());
//        inOutAsNto.setInNurseDept(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
//        inOutAsNto.setInNurseDeptName(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupName());
        PatIpdInpatientTo patIpdInpatientByVisitCode = patIpdInpatientService.getPatIpdInpatientByVisitCode(inOutAsNto.getVisitCode());
        if (patIpdInpatientByVisitCode != null && patIpdInpatientByVisitCode.getStatusCode().equals(InpatientStatusEnum.TRANSFERING)) {
            //转科入科
            ChangePatIpdInDeptNto changePatIpdInDeptNto = HIPBeanUtil.copy(inOutAsNto, ChangePatIpdInDeptNto.class);
            changePatIpdInDeptService.createChangePatIpdInDept(changePatIpdInDeptNto);
            // 转科入科成功后，异步调用EMR系统同步入科信息，但不能影响主流程
            // 转科入科成功后，异步调用EMR系统同步入科信息，但不能影响主流程
            syncChangeInDeptToEmr(changePatIpdInDeptNto, patIpdInpatientByVisitCode, HIPCurrentOrgUtils.getCurrentOrgInfo().getHospitalAreaCode());
        } else {
            PatSignsTo patSigns = new PatSignsTo();
            //体征数据
            PatSignsTo patSignsByVisitCode = patSignsService.getLatestPatSignsByVisitCode(inOutAsNto.getVisitCode());
            if (patSignsByVisitCode == null) {
                patSigns = patSignsService.createPatSigns(constructPatSigns(inOutAsNto));
            }
            try {
                //普通入科
                NormalPatIpdInDeptNto normalPatIpdInDeptNto = HIPBeanUtil.copy(inOutAsNto, NormalPatIpdInDeptNto.class);
                normalPatIpdInDeptService.createNormalPatIpdInDept(normalPatIpdInDeptNto);

                // 普通入科成功后，异步调用EMR系统同步入科信息，但不能影响主流程
                syncNormalInDeptToEmr(normalPatIpdInDeptNto, patIpdInpatientByVisitCode, HIPCurrentOrgUtils.getCurrentOrgInfo().getHospitalAreaCode());
            } catch (Exception e) {
                patSignsService.deletePatSigns(patSigns.getId());
            }
        }
    }


    /**
     * 将转科入科信息同步到EMR系统
     * 该方法内部处理异常，不会影响调用方
     *
     * @param changePatIpdInDeptNto 转科入科信息
     */
    private void syncChangeInDeptToEmr(ChangePatIpdInDeptNto changePatIpdInDeptNto, PatIpdInpatientTo patIpdInpatientByVisitCode, String hospitalAreaCode) {
        if (changePatIpdInDeptNto == null) {
            return;
        }
        try {
            emrSyncService.syncChangeInDeptToEmr(changePatIpdInDeptNto, patIpdInpatientByVisitCode, hospitalAreaCode);
            logger.info("已触发转科入科信息同步到EMR系统，患者visitCode: {}", changePatIpdInDeptNto.getVisitCode());
        } catch (Exception e) {
            // 记录异常但不抛出，确保不影响主流程
            logger.error("触发转科入科信息同步到EMR系统失败，但不影响入科操作，患者visitCode: {}",
                    changePatIpdInDeptNto.getVisitCode(), e);
        }
    }

    /**
     * 将普通入科信息同步到EMR系统
     * 该方法内部处理异常，不会影响调用方
     *
     * @param normalPatIpdInDeptNto 普通入科信息
     */
    private void syncNormalInDeptToEmr(NormalPatIpdInDeptNto normalPatIpdInDeptNto, PatIpdInpatientTo patIpdInpatientByVisitCode, String hospitalAreaCode) {
        if (normalPatIpdInDeptNto == null) {
            return;
        }
        try {

            emrSyncService.syncNormalInDeptToEmr(normalPatIpdInDeptNto, patIpdInpatientByVisitCode, hospitalAreaCode);
            logger.info("已触发普通入科信息同步到EMR系统，患者visitCode: {}", normalPatIpdInDeptNto.getVisitCode());
        } catch (Exception e) {
            // 记录异常但不抛出，确保不影响主流程
            logger.error("触发普通入科信息同步到EMR系统失败，但不影响入科操作，患者visitCode: {}",
                    normalPatIpdInDeptNto.getVisitCode(), e);
        }
    }

    /**
     * 待转出-患者列表查询
     */
    @Override
    public List<PatIpdInpatientAsTo> getTransferPatInfo(String deptNurseCode) {
        Assert.notNull(deptNurseCode, "护理组不能为空！");
        List<PatIpdInpatientAsTo> patIpdInpatientToList = new ArrayList<>();
        PatIpdInpatientQto qto = new PatIpdInpatientQto();
        qto.setDeptNurseCode(deptNurseCode);
        List<PatIpdInpatientTo> tansferPatIpdInpatientList = patIpdInpatientService.getChangeDeptPatForInout(deptNurseCode);
        if (CollectionUtils.isNotEmpty(tansferPatIpdInpatientList)) {
            List<PatIpdInpatientTo> transferList = tansferPatIpdInpatientList.stream().filter(a -> a.getStatusCode() != null && a.getStatusCode().equals(InpatientStatusEnum.TRANSFER)).toList();
            if (CollectionUtils.isNotEmpty(transferList)) {
                patIpdInpatientToList.addAll(HIPBeanUtil.copy(transferList, PatIpdInpatientAsTo.class));
            }
            List<PatIpdInpatientTo> transferingList = tansferPatIpdInpatientList.stream().filter(a -> a.getStatusCode() != null && a.getStatusCode().equals(InpatientStatusEnum.TRANSFERING)).toList();
            if (CollectionUtils.isNotEmpty(transferingList)) {
                for (PatIpdInpatientTo transfering : transferingList) {
                    PatIpdInpatientAsTo patIpdInpatientAsTo = HIPBeanUtil.copy(transfering, PatIpdInpatientAsTo.class);
                    PatIpdInpatientAsTo inpatientAsTo = constructPatIpdInpatientAsTo(patIpdInpatientAsTo, transfering.getPatIpdChangeDeptOutTo());
                    patIpdInpatientToList.add(inpatientAsTo);
                }
            }
        }
        return patIpdInpatientToList;
    }

    /**
     * 待入科-患者详细信息查询
     */
    @Override
    public InOutAsTo getPatInfoDetail(InOutAsQto inOutAsQto) {
        Assert.notNull(inOutAsQto.getId(), "标识不能为空！");
        Assert.notNull(inOutAsQto.getPatCode(), "患者主索引不能为空！");
        Assert.notNull(inOutAsQto.getVisitCode(), "患者流水号不能为空！");
        InOutAsTo inOutAsTo = new InOutAsTo();
        //患者信息
        PatIpdInpatientTo patIpdInPatientById = patIpdInpatientService.getPatIpdInPatientById(inOutAsQto.getId());
        if (patIpdInPatientById != null) {
            inOutAsTo.setVisitCode(patIpdInPatientById.getVisitCode());
            inOutAsTo.setInpatientCode(patIpdInPatientById.getInpatientCode());
            inOutAsTo.setName(patIpdInPatientById.getName());
            inOutAsTo.setBirthDate(patIpdInPatientById.getBirthDate());
            inOutAsTo.setAge(patIpdInPatientById.getAge());
            inOutAsTo.setPatCode(patIpdInPatientById.getPatCode());
            inOutAsTo.setSex(patIpdInPatientById.getSex());
            inOutAsTo.setSexName(patIpdInPatientById.getSexName());
            inOutAsTo.setFeeType(patIpdInPatientById.getFeeType());
            inOutAsTo.setStatusCode(patIpdInPatientById.getStatusCode().getCode());
            inOutAsTo.setInPatientDate(patIpdInPatientById.getInTime());
            inOutAsTo.setInTimes(patIpdInPatientById.getInTimes());
            inOutAsTo.setAcceptNurseDept(patIpdInPatientById.getDeptNurseCode());
            inOutAsTo.setAcceptNurseDeptName(patIpdInPatientById.getDeptNurseName());
            PatIndexTo patIndexById = patIndexService.getPatIndexById(patIpdInPatientById.getPatCode());
            if (patIndexById != null) {
                if (StringUtils.isNotEmpty(patIndexById.getTel())) {
                    inOutAsTo.setTel(patIndexById.getTel());
                }
            }
            if (patIpdInPatientById.getFeeType() != null) {
                List<DictElementTo> customDictElement = dictElementService.getCustomDictElement(DictCodeEnum.费别.getCode());
                List<DictElementTo> list = customDictElement.stream().filter(a -> a.getElementCode().equals(patIpdInPatientById.getFeeType())).toList();
                if (CollectionUtils.isNotEmpty(list)) {
                    inOutAsTo.setFeeTypeName(list.get(0).getElementName());
                }
            }
            if (patIpdInPatientById.getStatusCode().equals(InpatientStatusEnum.TRANSFERING)) {
                PatIpdChangeDeptOutQto patIpdChangeDeptOutQto = new PatIpdChangeDeptOutQto();
                patIpdChangeDeptOutQto.setVisitCode(inOutAsQto.getVisitCode());
                List<PatIpdChangeDeptOutTo> patIpdChangeDeptOuts = patIpdChangeDeptOutService.getPatIpdChangeDeptOuts(patIpdChangeDeptOutQto);
                if (CollectionUtils.isNotEmpty(patIpdChangeDeptOuts)) {
                    PatIpdChangeDeptOutTo patIpdChangeDeptOutTo = patIpdChangeDeptOuts.get(0);
                    inOutAsTo.setAdmissionDeptCode(patIpdChangeDeptOutTo.getAcceptDept());
                    inOutAsTo.setAdmissionDeptName(patIpdChangeDeptOutTo.getAcceptDeptName());
                    inOutAsTo.setAcceptNurseDept(patIpdChangeDeptOutTo.getAcceptNurseDept());
                    inOutAsTo.setAcceptNurseDeptName(patIpdChangeDeptOutTo.getAcceptDeptName());
                    inOutAsTo.setChangeDeptOutId(patIpdChangeDeptOutTo.getId());
                }
            } else {
                inOutAsTo.setAdmissionDeptCode(patIpdInPatientById.getAdmissionDeptCode());
                inOutAsTo.setAdmissionDeptName(patIpdInPatientById.getAdmissionDeptName());
            }
        }
        //联系人
        PatRelationQto patRelationQto = new PatRelationQto();
        patRelationQto.setPatIndexCode(inOutAsQto.getPatCode());
        List<PatRelationTo> patRelations = patIndexService.getPatRelations(patRelationQto);
        if (CollectionUtils.isNotEmpty(patRelations)) {
            StringBuilder relation = new StringBuilder();
            for (PatRelationTo patRelation : patRelations) {
                if (StringUtils.isNotEmpty(patRelation.getTypeName()) && StringUtils.isNotEmpty(patRelation.getTel())) {
                    relation = new StringBuilder(relation + patRelation.getTypeName() + " " + patRelation.getTel() + ";");
                }
            }
            inOutAsTo.setRelation(String.valueOf(relation));
        }
        //过敏原信息
        PatAllergenQto patAllergenQto = new PatAllergenQto();
        patAllergenQto.setPatIndexCode(inOutAsQto.getPatCode());
        List<PatAllergenTo> patAllergens = patIndexService.getPatAllergens(patAllergenQto);
        if (CollectionUtils.isNotEmpty(patAllergens)) {
            StringBuilder allergies = new StringBuilder();
            for (PatAllergenTo patAllergen : patAllergens) {
                if (StringUtils.isNotEmpty(patAllergen.getName())) {
                    allergies.append(patAllergen.getName()).append("、");
                }
            }
            inOutAsTo.setAllergies(String.valueOf(allergies));
        }
        return inOutAsTo;
    }

    /**
     * 待转出-取消转科
     */
    @Override
    @GlobalTransactional
    public void cancelTransfer(InOutAsEto inOutAsEto) {
        //取消转科
        patIpdChangeDeptOutService.cancelPatIpdChangeDeptOut(inOutAsEto.getTransferDeptId(), HIPBeanUtil.copy(inOutAsEto, PatIpdInpatientTransferTreatedEto.class));
        //执行单取消执行
        CisOrgCommonNto cisOrgCommonNto = new CisOrgCommonNto();
        cisOrgCommonNto.setOrgCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
        cisOrgCommonNto.setOrgName(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupName());
        cisOrderExecPlanService.specialCancelExecuteCisOrderExecPlanBatch(inOutAsEto.getVisitCode(), SystemTypeEnum.CHANGEDEPT, cisOrgCommonNto);
    }

    /**
     * 费用审核
     */
    @Override
    public void expenseReview(String visitCode) {
        Assert.notNull(visitCode, "患者流水号不能为空！");
        Assert.notNull(HIPCurrentOrgUtils.getCurrentOrgInfo().getDeptCode(), "当前登录科室号不能为空！");
        String message = "";
        //判断退费申请未确认数据
        List<EconIpdApplyAllTo> unConfirmApplyAlls = econIpdApplyService.getUnConfirmApplyAlls(visitCode);
        if (CollectionUtils.isNotEmpty(unConfirmApplyAlls)) {
            message += "请处理患者退费申请未确认信息\n";
        }
        //判断待取药待退药数据
        List<DrugIpdApplyTo> drugIpdAppliesByNotInout = drugIpdApplyService.getDrugIpdAppliesByNotInout(visitCode);
        if (CollectionUtils.isNotEmpty(drugIpdAppliesByNotInout)) {
            message += "请处理患者待取药待退药医嘱信息\n";
        }
        //判断未计费执行单数据
        List<CisOrderExecPlanTo> noChargeCisOrderExecPlan = cisOrderExecPlanService.findNoChargeCisOrderExecPlan(visitCode, false);
        if (CollectionUtils.isNotEmpty(noChargeCisOrderExecPlan)) {
            message += "请处理患者未计费执行单信息\n";
        }
        List<CisIpdOrderTo> ipdOrdersByVisitCode = cisIpdCpoeService.getIpdOrdersByVisitCode(visitCode);
        if (CollectionUtils.isNotEmpty(ipdOrdersByVisitCode)) {
            List<CisIpdOrderTo> effectiveList = ipdOrdersByVisitCode.stream().filter(a -> (a.getStatusCode().equals(CisStatusEnum.PASS) || a.getStatusCode().equals(CisStatusEnum.VOID) || a.getStatusCode().equals(CisStatusEnum.PRESTOP)) && !a.getOrderClass().equals(SystemTypeEnum.PATIENT) && !a.getOrderClass().equals(SystemTypeEnum.NURSING)).toList();
            if (CollectionUtils.isNotEmpty(effectiveList)) {
                message += "请处理患者有效状态医嘱\n";
            }
        }
        if (StringUtils.isNotEmpty(message)) {
            throw new BusinessException(message);
        }
        patIpdInpatientService.updatePatientExamineFlag(visitCode, HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
    }

    /**
     * 取消费用审核
     */
    @Override
    public void cancelExpenseReview(String visitCode) {
        Assert.notNull(visitCode, "患者流水号不能为空！");
        Assert.notNull(HIPCurrentOrgUtils.getCurrentOrgInfo().getDeptCode(), "当前登录科室号不能为空！");
        patIpdInpatientService.cancelPatientExamineFlag(visitCode, HIPCurrentOrgUtils.getCurrentOrgInfo().getDeptCode());
    }

    /**
     * 待发药、待退药
     */
    @Override
    public List<DrugIpdApplyTo> getDrugIpdAppliesByNotInout(String visitCode) {
        return drugIpdApplyService.getDrugIpdAppliesByNotInout(visitCode);
    }

    /**
     * 未执行执行单
     */
    @Override
    public List<UnExecOrderInfoAsTo> getUnBilledExecutionOrders(String visitCode) {
        List<UnExecOrderInfoAsTo> unExecOrderInfoAsToList = new ArrayList<>();
        List<CisOrderExecPlanTo> noExecCisOrderExecPlan = cisOrderExecPlanService.findNoExecCisOrderExecPlan(visitCode);
        if (CollectionUtils.isNotEmpty(noExecCisOrderExecPlan)) {
            List<CisOrderExecPlanTo> noChangeDept = noExecCisOrderExecPlan.stream().filter(a -> !a.getOrderClass().equals(SystemTypeEnum.CHANGEDEPT)).toList();
            if (CollectionUtils.isNotEmpty(noChangeDept)) {
                Map<String, List<CisOrderExecPlanTo>> orderIdCollect = noChangeDept.stream().collect(Collectors.groupingBy(CisOrderExecPlanTo::getOrderId));
                orderIdCollect.forEach((k, v) -> {
                    CisIpdOrderTo cisIpdOrderTo = cisIpdCpoeService.getCisIpdOrderById(k);
                    if (cisIpdOrderTo != null) {
                        UnExecOrderInfoAsTo unExecOrderInfoAsTo = new UnExecOrderInfoAsTo();
                        if (cisIpdOrderTo.getOrderType().equals(OrderTypeEnum.LONG_TERM_ORDER)) {
                            List<CisIpdOrderTo> cisIpdOrderToList = new ArrayList<>();
                            cisIpdOrderToList.add(cisIpdOrderTo);
                            List<CisLongTermOrderTo> cisLongTermOrderToList = cisIpdOrderToList.stream().filter(CisLongTermOrderTo.class::isInstance).map(CisLongTermOrderTo.class::cast).toList();
                            CisLongTermOrderTo cisLongTermOrderTo = cisLongTermOrderToList.get(0);
                            //医嘱停止时间
                            unExecOrderInfoAsTo.setEffectiveHighDate(cisLongTermOrderTo.getEffectiveHighDate());
                            //医嘱停止人
                            unExecOrderInfoAsTo.setStopStaff(cisLongTermOrderTo.getStopStaff());
                            //医嘱停止姓名
                            unExecOrderInfoAsTo.setStopStaffName(cisLongTermOrderTo.getStopStaffName());
                        }
                        //医嘱数据
                        unExecOrderInfoAsTo.setOrderId(cisIpdOrderTo.getId());
                        unExecOrderInfoAsTo.setOrderContent(cisIpdOrderTo.getOrderContent());
                        unExecOrderInfoAsTo.setOrderType(cisIpdOrderTo.getOrderType());
                        unExecOrderInfoAsTo.setOrderTypeCode(cisIpdOrderTo.getOrderType().getCode());
                        unExecOrderInfoAsTo.setOrderTypeName(cisIpdOrderTo.getOrderType().getName());
                        unExecOrderInfoAsTo.setOrderClass(cisIpdOrderTo.getOrderClass());
                        unExecOrderInfoAsTo.setOrderClassCode(cisIpdOrderTo.getOrderClass().getCode());
                        unExecOrderInfoAsTo.setOrderClassName(cisIpdOrderTo.getOrderClass().getName());
                        unExecOrderInfoAsTo.setEffectiveLowDate(cisIpdOrderTo.getEffectiveLowDate());
                        unExecOrderInfoAsTo.setCreatedStaff(cisIpdOrderTo.getCreatedStaff());
                        unExecOrderInfoAsTo.setCreatedStaffName(cisIpdOrderTo.getCreatedStaffName());
                        //医嘱执行
                        List<CisOrderExecPlanTo> cisOrderExecPlanTos = orderIdCollect.get(k);
                        if (CollectionUtils.isNotEmpty(cisOrderExecPlanTos)) {
                            List<ExecPlanDateAsTo> dateList = new ArrayList<>();
                            Map<String, List<CisOrderExecPlanTo>> collect = cisOrderExecPlanTos.stream().collect(Collectors.groupingBy(a -> String.format("%02d-%02d", a.getExecPlanDate().getMonthValue(), a.getExecPlanDate().getDayOfMonth())));
                            collect.forEach((m, n) -> {
                                ExecPlanDateAsTo date = new ExecPlanDateAsTo();
                                date.setDate(m);
                                date.setCisOrderExecPlanToList(collect.get(m));
                                dateList.add(date);
                            });
                            unExecOrderInfoAsTo.setDateList(dateList);
                        }
                        unExecOrderInfoAsToList.add(unExecOrderInfoAsTo);
                    }
                });
            }
        }
        return unExecOrderInfoAsToList;
    }

    /**
     * 出院召回
     */
    @Override
    @GlobalTransactional
    public void dischargeRecall(PatIpdCancelDischargeEto patIpdCancelDischargeEto) {
        //出院召回
        patIpdCancelDischargeEto.setLoginDeptCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getDeptCode());
        patIpdInpatientService.cancelDischarge(patIpdCancelDischargeEto);
        //执行单取消执行
        CisOrgCommonNto cisOrgCommonNto = new CisOrgCommonNto();
        cisOrgCommonNto.setOrgCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
        cisOrgCommonNto.setOrgName(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupName());
        cisOrderExecPlanService.specialCancelExecuteCisOrderExecPlanBatch(patIpdCancelDischargeEto.getVisitCode(), SystemTypeEnum.OUTHOSPITAL, cisOrgCommonNto);
    }

    /**
     * 待出院-医嘱查询
     */
    @Override
    public List<CisIpdDocOrderAsTo> orderInquiry(String visitCode) {
        Assert.notNull(visitCode, "患者流水号不能为空！");
        List<CisIpdDocOrderAsTo> sortList = new ArrayList<>();
        List<CisIpdOrderTo> ipdOrdersByVisitCodeList = cisIpdCpoeService.getIpdOrdersByVisitCode(visitCode);
        if (CollectionUtils.isNotEmpty(ipdOrdersByVisitCodeList)) {
            List<CisIpdDocOrderAsTo> cisIpdDocOrderAsToList = HIPBeanUtil.copy(ipdOrdersByVisitCodeList, CisIpdDocOrderAsTo.class).stream().map(cisIpdDocOrderAsTo -> {
                //医嘱类型
                SystemTypeEnum orderClass = cisIpdDocOrderAsTo.getOrderClass();
                if (orderClass != null) {
                    cisIpdDocOrderAsTo.setOrderClassCode(orderClass.getCode());
                    cisIpdDocOrderAsTo.setOrderClassName(orderClass.getName());
                }
                //医嘱类型(长/临)
                OrderTypeEnum orderType = cisIpdDocOrderAsTo.getOrderType();
                if (orderType != null) {
                    cisIpdDocOrderAsTo.setOrderTypeCode(orderType.getCode());
                    cisIpdDocOrderAsTo.setOrderTypeName(orderType.getName());
                }
                if (cisIpdDocOrderAsTo.getOrderClass().equals(SystemTypeEnum.LINE)) {
                    cisIpdDocOrderAsTo.setStatusCode(CisStatusEnum.COMPLETED);
                    cisIpdDocOrderAsTo.setCommitDate(cisIpdDocOrderAsTo.getCreatedDate());
                }
                //医嘱状态
                CisStatusEnum statusCode = cisIpdDocOrderAsTo.getStatusCode();
                if (statusCode != null) {
                    cisIpdDocOrderAsTo.setStatusCodeCode(statusCode.getCode());
                    cisIpdDocOrderAsTo.setStatusCodeName(statusCode.getName());
                }
                return cisIpdDocOrderAsTo;
            }).toList();
            if (CollectionUtils.isNotEmpty(cisIpdDocOrderAsToList)) {
                //暂存状态:通过sortNo排序,若sortNo相同以创建时间降序
                List<CisIpdDocOrderAsTo> newList = cisIpdDocOrderAsToList.stream().filter(a -> a.getStatusCode().equals(CisStatusEnum.NEW)).sorted(Comparator.comparing(CisIpdDocOrderAsTo::getSortNo)).toList();
                if (CollectionUtils.isNotEmpty(newList)) {
                    sortList.addAll(newList);
                }
                //剩余状态:以创建时间降序
                List<CisIpdDocOrderAsTo> notNewList = cisIpdDocOrderAsToList.stream().filter(a -> !a.getStatusCode().equals(CisStatusEnum.NEW)).sorted(Comparator.comparing(CisIpdDocOrderAsTo::getCreatedDate, Comparator.reverseOrder())).toList();
                if (CollectionUtils.isNotEmpty(notNewList)) {
                    sortList.addAll(notNewList);
                }
            }
            //主从医嘱
            List<CisIpdDocOrderAsTo> servantList = sortList.stream().filter(a -> StringUtils.isNotEmpty(a.getParentCode())).toList();
            if (CollectionUtils.isNotEmpty(servantList)) {
                for (CisIpdDocOrderAsTo servant : servantList) {
                    List<CisIpdDocOrderAsTo> list = sortList.stream().filter(a -> a.getId().equals(servant.getParentCode())).toList();
                    if (CollectionUtils.isNotEmpty(list) && list.size() == 1) {
                        CisIpdDocOrderAsTo cisIpdDocOrderAsTo = list.get(0);
                        servant.setSortNo(cisIpdDocOrderAsTo.getSortNo());
                        cisIpdDocOrderAsTo.setMainOrder(true);
                    }
                }
            }
        }
        return sortList;
    }

    /**
     * 待转出-患者详细信息查询
     */
    @Override
    public InOutAsTo getTransferPatInfoDetail(InOutAsQto inOutAsQto) {
        Assert.notNull(inOutAsQto.getId(), "标识不能为空！");
        Assert.notNull(inOutAsQto.getPatCode(), "患者主索引不能为空！");
        Assert.notNull(inOutAsQto.getVisitCode(), "患者流水号不能为空！");
        InOutAsTo inOutAsTo = new InOutAsTo();
        //患者信息
        PatIpdInpatientTo patIpdInPatientById = patIpdInpatientService.getPatIpdInPatientById(inOutAsQto.getId());
        if (patIpdInPatientById != null) {
            inOutAsTo.setVisitCode(patIpdInPatientById.getVisitCode());
            inOutAsTo.setInpatientCode(patIpdInPatientById.getInpatientCode());
            inOutAsTo.setName(patIpdInPatientById.getName());
            inOutAsTo.setBirthDate(patIpdInPatientById.getBirthDate());
            inOutAsTo.setPatCode(patIpdInPatientById.getPatCode());
            inOutAsTo.setSex(patIpdInPatientById.getSex());
            inOutAsTo.setSexName(patIpdInPatientById.getSexName());
            inOutAsTo.setFeeType(patIpdInPatientById.getFeeType());
            inOutAsTo.setBedId(patIpdInPatientById.getBedId());
            inOutAsTo.setBedName(patIpdInPatientById.getBedName());
            inOutAsTo.setAdmissionDeptCode(patIpdInPatientById.getAdmissionDeptCode());
            inOutAsTo.setAdmissionDeptName(patIpdInPatientById.getAdmissionDeptName());
            inOutAsTo.setMasterDoctor(patIpdInPatientById.getMasterDoctor());
            inOutAsTo.setMasterDoctorName(patIpdInPatientById.getMasterDoctorName());
            inOutAsTo.setMasterNurse(patIpdInPatientById.getMasterNurse());
            inOutAsTo.setMasterNurseName(patIpdInPatientById.getMasterNurseName());
            inOutAsTo.setDirectorDoctor(patIpdInPatientById.getDirectorDoctor());
            inOutAsTo.setDirectorDoctorName(patIpdInPatientById.getDirectorDoctorName());
            inOutAsTo.setStatusCode(patIpdInPatientById.getStatusCode().getCode());
            inOutAsTo.setInPatientDate(patIpdInPatientById.getInTime());
            if (patIpdInPatientById.getFeeType() != null) {
                List<DictElementTo> customDictElement = dictElementService.getCustomDictElement(DictCodeEnum.费别.getCode());
                List<DictElementTo> list = customDictElement.stream().filter(a -> a.getElementCode().equals(patIpdInPatientById.getFeeType())).toList();
                if (CollectionUtils.isNotEmpty(list)) {
                    inOutAsTo.setFeeTypeName(list.get(0).getElementName());
                }
            }
            List<CisIpdDiagnoseTo> cisIpdDiagnoseByVisitCode = cisIpdDiagnoseService.getCisIpdDiagnoseByVisitCode(patIpdInPatientById.getVisitCode());
            List<CisIpdDiagnoseTo> list = cisIpdDiagnoseByVisitCode.stream().filter(CisIpdDiagnoseTo::getIsChief).toList();
            if (CollectionUtils.isNotEmpty(list)) {
                StringBuilder impDiagnosis = new StringBuilder();
                for (CisIpdDiagnoseTo cisIpdDiagnoseTo : list) {
                    impDiagnosis.append(cisIpdDiagnoseTo.getDiagnosisName());
                }
                inOutAsTo.setImpDiagnosis(impDiagnosis.toString());
            }
            //患者转出数据
            List<CisIpdOrderTo> ipdOrdersByVisitCode = cisIpdCpoeService.getIpdOrdersByVisitCode(inOutAsQto.getVisitCode());
            List<CisIpdOrderTo> changeDeptList = ipdOrdersByVisitCode.stream().filter(a -> a.getOrderClass().equals(SystemTypeEnum.CHANGEDEPT) && a.getStatusCode().equals(CisStatusEnum.PASS)).toList();
            if (CollectionUtils.isNotEmpty(changeDeptList)) {
                CisIpdOrderTo cisIpdOrderTo = changeDeptList.get(0);
                CisChangeDeptApplyTo cisChangeDeptApplyById = cisChangeDeptApplyService.getCisChangeDeptApplyById(cisIpdOrderTo.getApplyCode());
                if (cisChangeDeptApplyById != null) {
                    inOutAsTo.setAcceptDept(cisChangeDeptApplyById.getInOrgCode());
                    inOutAsTo.setAcceptDeptName(workGroupService.getWorkGroup(cisChangeDeptApplyById.getInOrgCode()).getName());
                    inOutAsTo.setAcceptNurseDept(cisChangeDeptApplyById.getInDeptNurseCode());
                    inOutAsTo.setAcceptNurseDeptName(workGroupService.getWorkGroup(cisChangeDeptApplyById.getInDeptNurseCode()).getName());
                    inOutAsTo.setOrderId(cisIpdOrderTo.getId());
                }
            }
        }
        //患者扩展信息
        PatIpdInpatientExtTo patIpdInpatientExtById = patIpdInpatientExtService.getPatIpdInpatientExtById(inOutAsQto.getVisitCode());
        if (patIpdInpatientExtById != null) {
            //患者病情
            inOutAsTo.setCondition(patIpdInpatientExtById.getCriticalCarePatient());
            //患者病情名称
            inOutAsTo.setConditionName(patIpdInpatientExtById.getCriticalCarePatientName());
            //护理等级
            inOutAsTo.setNursingLevel(patIpdInpatientExtById.getRoutineCare());
            //护理等级名称
            inOutAsTo.setNursingLevelName(patIpdInpatientExtById.getRoutineCareName());
        }
        //过敏原信息
        PatAllergenQto patAllergenQto = new PatAllergenQto();
        patAllergenQto.setCode(inOutAsQto.getPatCode());
        List<PatAllergenTo> patAllergens = patIndexService.getPatAllergens(patAllergenQto);
        if (CollectionUtils.isNotEmpty(patAllergens)) {
            StringBuilder allergies = new StringBuilder();
            for (PatAllergenTo patAllergen : patAllergens) {
                if (StringUtils.isNotEmpty(patAllergen.getName())) {
                    allergies.append(patAllergen.getName()).append("、");
                }
            }
            inOutAsTo.setAllergies(String.valueOf(allergies));
        }
        //住院余额
        EconIpdAmountTo econIpdAmountById = econIpdAmountService.getEconIpdAmountByVisitCode(inOutAsQto.getVisitCode());
        if (econIpdAmountById != null) {
            inOutAsTo.setBalance(econIpdAmountById.getBalance());
            inOutAsTo.setPrepayAmount(econIpdAmountById.getPrepayAmount());
            inOutAsTo.setTotalAmount(econIpdAmountById.getTotalAmount());
        }

        return inOutAsTo;
    }

    /**
     * 待出院-患者详细信息查询
     */
    @Override
    public InOutAsTo dischargePatInfoDetail(InOutAsQto inOutAsQto) {
        Assert.notNull(inOutAsQto.getId(), "标识不能为空！");
        Assert.notNull(inOutAsQto.getPatCode(), "患者主索引不能为空！");
        Assert.notNull(inOutAsQto.getVisitCode(), "患者流水号不能为空！");
        InOutAsTo inOutAsTo = new InOutAsTo();
        //患者信息
        PatIpdInpatientTo patIpdInPatientById = patIpdInpatientService.getPatIpdInPatientById(inOutAsQto.getId());
        if (patIpdInPatientById != null) {
            inOutAsTo.setVisitCode(patIpdInPatientById.getVisitCode());
            inOutAsTo.setInpatientCode(patIpdInPatientById.getInpatientCode());
            inOutAsTo.setName(patIpdInPatientById.getName());
            inOutAsTo.setBirthDate(patIpdInPatientById.getBirthDate());
            inOutAsTo.setPatCode(patIpdInPatientById.getPatCode());
            inOutAsTo.setSex(patIpdInPatientById.getSex());
            inOutAsTo.setSexName(patIpdInPatientById.getSexName());
            inOutAsTo.setFeeType(patIpdInPatientById.getFeeType());
            inOutAsTo.setBedId(patIpdInPatientById.getBedId());
            inOutAsTo.setBedName(patIpdInPatientById.getBedName());
            inOutAsTo.setAdmissionDeptCode(patIpdInPatientById.getAdmissionDeptCode());
            inOutAsTo.setAdmissionDeptName(patIpdInPatientById.getAdmissionDeptName());
            inOutAsTo.setMasterDoctor(patIpdInPatientById.getMasterDoctor());
            inOutAsTo.setMasterDoctorName(patIpdInPatientById.getMasterDoctorName());
            inOutAsTo.setMasterNurse(patIpdInPatientById.getMasterNurse());
            inOutAsTo.setMasterNurseName(patIpdInPatientById.getMasterNurseName());
            inOutAsTo.setDirectorDoctor(patIpdInPatientById.getDirectorDoctor());
            inOutAsTo.setDirectorDoctorName(patIpdInPatientById.getDirectorDoctorName());
            inOutAsTo.setStatusCode(patIpdInPatientById.getStatusCode().getCode());
            inOutAsTo.setInPatientDate(patIpdInPatientById.getInTime());
            if (patIpdInPatientById.getFeeType() != null) {
                List<DictElementTo> customDictElement = dictElementService.getCustomDictElement(DictCodeEnum.费别.getCode());
                List<DictElementTo> list = customDictElement.stream().filter(a -> a.getElementCode().equals(patIpdInPatientById.getFeeType())).toList();
                if (CollectionUtils.isNotEmpty(list)) {
                    inOutAsTo.setFeeTypeName(list.get(0).getElementName());
                }
            }
            List<CisIpdDiagnoseTo> cisIpdDiagnoseByVisitCode = cisIpdDiagnoseService.getCisIpdDiagnoseByVisitCode(patIpdInPatientById.getVisitCode());
            List<CisIpdDiagnoseTo> list = cisIpdDiagnoseByVisitCode.stream().filter(CisIpdDiagnoseTo::getIsChief).toList();
            if (CollectionUtils.isNotEmpty(list)) {
                StringBuilder impDiagnosis = new StringBuilder();
                for (CisIpdDiagnoseTo cisIpdDiagnoseTo : list) {
                    impDiagnosis.append(cisIpdDiagnoseTo.getDiagnosisName());
                }
                inOutAsTo.setImpDiagnosis(impDiagnosis.toString());
            }
        }
        //患者扩展信息
        PatIpdInpatientExtTo patIpdInpatientExtById = patIpdInpatientExtService.getPatIpdInpatientExtById(inOutAsQto.getVisitCode());
        if (patIpdInpatientExtById != null) {
            //患者病情
            inOutAsTo.setCondition(patIpdInpatientExtById.getCriticalCarePatient());
            //患者病情名称
            inOutAsTo.setConditionName(patIpdInpatientExtById.getCriticalCarePatientName());
            //护理等级
            inOutAsTo.setNursingLevel(patIpdInpatientExtById.getRoutineCare());
            //护理等级名称
            inOutAsTo.setNursingLevelName(patIpdInpatientExtById.getRoutineCareName());
        }
        //过敏原信息
        PatAllergenQto patAllergenQto = new PatAllergenQto();
        patAllergenQto.setPatIndexCode(inOutAsQto.getPatCode());
        List<PatAllergenTo> patAllergens = patIndexService.getPatAllergens(patAllergenQto);
        if (CollectionUtils.isNotEmpty(patAllergens)) {
            StringBuilder allergies = new StringBuilder();
            for (PatAllergenTo patAllergen : patAllergens) {
                if (StringUtils.isNotEmpty(patAllergen.getName())) {
                    allergies.append(patAllergen.getName()).append("、");
                }
            }
            inOutAsTo.setAllergies(String.valueOf(allergies));
        }
        //住院余额
        EconIpdAmountTo econIpdAmountById = econIpdAmountService.getEconIpdAmountByVisitCode(inOutAsQto.getVisitCode());
        if (econIpdAmountById != null) {
            inOutAsTo.setBalance(econIpdAmountById.getBalance());
            inOutAsTo.setPrepayAmount(econIpdAmountById.getPrepayAmount());
            inOutAsTo.setTotalAmount(econIpdAmountById.getTotalAmount());
        }
        //TODO 出院诊断
        //TODO inOutAsTo.setDischargeDiagnosis();
        return inOutAsTo;
    }

    public PatSignsNto constructPatSigns(InOutAsNto inOutAsNto) {
        //体征
        PatSignsNto patSignsNto = new PatSignsNto();
        //id
        patSignsNto.setId(inOutAsNto.getSignsId());
        //主索引编码
        patSignsNto.setPatIndexCode(inOutAsNto.getPatCode());
        //就诊流水号
        patSignsNto.setVisitCode(inOutAsNto.getVisitCode());
        //身高
        patSignsNto.setHeight(Double.valueOf(inOutAsNto.getHeight()));
        //体重
        patSignsNto.setWeight(Double.valueOf(inOutAsNto.getWeight()));
        //体温
        patSignsNto.setTemperature(Double.valueOf(inOutAsNto.getTemperature()));
        //心率
        patSignsNto.setHeartRate(Long.valueOf(inOutAsNto.getHertRate()));
        //脉搏
        patSignsNto.setBreathing(Long.valueOf(inOutAsNto.getPulse()));
        //收缩压
        patSignsNto.setEndBloodPressure(Long.valueOf(inOutAsNto.getEndBloodPressure()));
        //舒张压
        patSignsNto.setHighBloodPressure(Long.valueOf(inOutAsNto.getHighBloodPressure()));
        //状态
        patSignsNto.setStatusCode(PatSignsStatusEnum.有效);
        //接诊类型
        patSignsNto.setVisitType("住院");
        return patSignsNto;
    }

    public PatIpdInpatientAsTo constructPatIpdInpatientAsTo(PatIpdInpatientAsTo patIpdInpatientAsTo, PatIpdChangeDeptOutTo patIpdChangeDeptOutTo) {
        patIpdInpatientAsTo.setTransferDeptId(patIpdChangeDeptOutTo.getId());
        patIpdInpatientAsTo.setChangeOutDept(patIpdChangeDeptOutTo.getChangeOutDept());
        patIpdInpatientAsTo.setChangeOutDeptName(patIpdChangeDeptOutTo.getChangeOutDeptName());
        patIpdInpatientAsTo.setChangeOutNurseDept(patIpdChangeDeptOutTo.getChangeOutNurseDept());
        patIpdInpatientAsTo.setChangeOutNurseDeptName(patIpdChangeDeptOutTo.getChangeOutNurseDeptName());
        patIpdInpatientAsTo.setChangeBedId(patIpdChangeDeptOutTo.getBedId());
        patIpdInpatientAsTo.setAcceptDept(patIpdChangeDeptOutTo.getAcceptDept());
        patIpdInpatientAsTo.setAcceptDeptName(patIpdChangeDeptOutTo.getAcceptDeptName());
        patIpdInpatientAsTo.setAcceptNurseDept(patIpdChangeDeptOutTo.getAcceptNurseDept());
        patIpdInpatientAsTo.setAcceptNurseDeptName(patIpdChangeDeptOutTo.getAcceptNurseDeptName());
        patIpdInpatientAsTo.setOrderId(patIpdChangeDeptOutTo.getOrderId());
        patIpdInpatientAsTo.setChangeAdmittedDoctor(patIpdChangeDeptOutTo.getAdmittedDoctor());
        patIpdInpatientAsTo.setChangeAdmittedDoctorName(patIpdChangeDeptOutTo.getAdmittedDoctorName());
        patIpdInpatientAsTo.setChangeMasterDoctor(patIpdChangeDeptOutTo.getMasterDoctor());
        patIpdInpatientAsTo.setChangeMasterDoctorName(patIpdChangeDeptOutTo.getMasterDoctorName());
        patIpdInpatientAsTo.setChangeMasterNurse(patIpdChangeDeptOutTo.getMasterNurse());
        patIpdInpatientAsTo.setChangeMasterNurseName(patIpdChangeDeptOutTo.getMasterNurseName());
        patIpdInpatientAsTo.setChangeDirectorDoctor(patIpdChangeDeptOutTo.getDirectorDoctor());
        patIpdInpatientAsTo.setChangeDirectorDoctorName(patIpdChangeDeptOutTo.getDirectorDoctorName());
        return patIpdInpatientAsTo;
    }
}
