package com.bjgoodwill.hip.as.cis.doc.ipd.util.build;

import com.bjgoodwill.hip.as.cis.doc.ipd.enums.CisDocIpdBusinessErrorEnum;
import com.bjgoodwill.hip.as.cis.doc.ipd.util.IpdOrderExecuteOrgUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.CisStatusEnum;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.common.util.HIPBeanUtil;
import com.bjgoodwill.hip.common.util.HIPIDUtil;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyTo;
import com.bjgoodwill.hip.ds.cis.apply.charge.enmus.CisChargeTypeEnum;
import com.bjgoodwill.hip.ds.cis.apply.charge.to.CisApplyChargeNto;
import com.bjgoodwill.hip.ds.cis.apply.palg.to.CisPalgApplyDetailNto;
import com.bjgoodwill.hip.ds.cis.apply.palg.to.CisPalgApplyExtNto;
import com.bjgoodwill.hip.ds.cis.apply.palg.to.CisPalgApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.palg.to.CisPalgApplyTo;
import com.bjgoodwill.hip.ds.cis.cpoe.order.to.CisIpdOrderNto;
import com.bjgoodwill.hip.ds.cis.medicineitem.price.to.ServiceClinicPriceTo;
import com.bjgoodwill.hip.ds.cis.medicineitem.serviceItem.service.ServiceClinicItemService;
import com.bjgoodwill.hip.ds.cis.medicineitem.serviceItem.to.ServiceClinicItemTo;
import com.bjgoodwill.hip.ds.org.api.service.WorkGroupService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> liangyuqing
 * @description :
 * @createDate : 2025/3/3 20:50
 */
@Service
public class PalgOrderBuildUtil {

    @Resource(name = "com.bjgoodwill.hip.ds.cis.medicineitem.serviceItem.service.ServiceClinicItemTangibleService")
    private ServiceClinicItemService serviceClinicItemService;

    @Resource
    private IpdOrderExecuteOrgUtil ipdOrderExecuteOrgUtil;

    @Resource
    private WorkGroupService workGroupService;

    /**
     * 复制粘贴用新
     * @param cisIpdOrderNto
     * @param cisBaseApplyTo
     * @return
     */
    public CisPalgApplyNto buildCisIpdOrderNto(CisIpdOrderNto cisIpdOrderNto, CisBaseApplyTo cisBaseApplyTo) {
        CisPalgApplyTo cisPalgApplyTo = (CisPalgApplyTo) cisBaseApplyTo;
        CisPalgApplyNto cisPalgApplyNto = HIPBeanUtil.copy(cisPalgApplyTo, CisPalgApplyNto.class);
        cisPalgApplyNto.setId(cisIpdOrderNto.getApplyCode());
        cisPalgApplyNto.setOrderID(cisIpdOrderNto.getId());
        cisPalgApplyNto.setVisitCode(cisIpdOrderNto.getVisitCode());
        cisPalgApplyNto.setPatMiCode(cisIpdOrderNto.getPatMiCode());
        cisPalgApplyNto.setDeptNurseCode(cisIpdOrderNto.getDeptNurseCode());
        cisPalgApplyNto.setDeptNurseName(cisIpdOrderNto.getDeptNurseName());

        // 申请单明细
        List<CisPalgApplyDetailNto> cisPalgApplyDetailNtos = HIPBeanUtil.copy(cisPalgApplyTo.getDetails(), CisPalgApplyDetailNto.class);
        cisPalgApplyDetailNtos.forEach(cisPalgApplyDetailNto -> {
            cisPalgApplyDetailNto.setId("DT_" + HIPIDUtil.getNextIdString());
            cisPalgApplyDetailNto.setVisitCode(cisIpdOrderNto.getVisitCode());
        });

        // 费用信息
        // 查询服务项目、收费项目明细
        List<CisApplyChargeNto> chargeNtoList = new ArrayList<>();
        ServiceClinicItemTo serviceClinicItemTo = serviceClinicItemService.getServiceClinicItemByCode(cisPalgApplyTo.getServiceItemCode());
        BusinessAssert.notNull(serviceClinicItemTo, CisDocIpdBusinessErrorEnum.CIS_DOC_IPD_0001, "医嘱项目");
        for (ServiceClinicPriceTo priceTo : serviceClinicItemTo.getServiceClinicPrices()) {
            CisApplyChargeNto chargeNto = new CisApplyChargeNto();
            chargeNto.setId("CH_" + HIPIDUtil.getNextIdString());
            chargeNto.setVisitCode(cisIpdOrderNto.getVisitCode());
            chargeNto.setOrderId(cisIpdOrderNto.getId());
            chargeNto.setCisBaseApplyId(cisIpdOrderNto.getApplyCode());
            chargeNto.setChargeType(CisChargeTypeEnum.DOCT);
            chargeNto.setPriceItemCode(priceTo.getPriceItemCode());
            chargeNto.setPriceItemName(priceTo.getPriceItemName());
            chargeNto.setIsFixed(priceTo.getIsFixed());
            chargeNto.setPrice(priceTo.getPrice());
            chargeNto.setUnit(priceTo.getUnitCode());
            chargeNto.setUnitName(priceTo.getUnitName());
            chargeNto.setNum(priceTo.getQuantity());
            chargeNto.setStatusCode(CisStatusEnum.NEW);
            chargeNto.setChageAmount(priceTo.getPrice().multiply(new BigDecimal(priceTo.getQuantity() + "")));
            chargeNto.setSystemItemClass(priceTo.getSystemItemClass());

            String executeOrgCode = ipdOrderExecuteOrgUtil.buildCiaApplyChargeExecuteOrgCode(priceTo, cisIpdOrderNto.getVisitCode(), cisIpdOrderNto.getExecuteOrgCode());
            chargeNto.setExecuteOrgCode(executeOrgCode);
            chargeNto.setExecuteOrgName(workGroupService.getWorkGroup(executeOrgCode).getName());

            chargeNtoList.add(chargeNto);
        }

        // 病理扩展实体信息
        CisPalgApplyExtNto cisPalgApplyExtNto = HIPBeanUtil.copy(cisPalgApplyTo.getPalgExt(), CisPalgApplyExtNto.class);
        cisPalgApplyExtNto.setId(HIPIDUtil.getNextIdString());
        cisPalgApplyExtNto.setApplyId(cisIpdOrderNto.getApplyCode());

        cisPalgApplyNto.setPalgExt(cisPalgApplyExtNto);
        cisPalgApplyNto.setCisApplyCharges(chargeNtoList);
        cisPalgApplyNto.setDetails(cisPalgApplyDetailNtos);
        return cisPalgApplyNto;
    }

}
