package com.bjgoodwill.hip.as.cis.nurse.ipd.to;

import com.bjgoodwill.hip.ds.pat.in.hospital.inpatient.to.PatIpdInpatientExtTo;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: zhangyunchuan
 * @Date: 2024/10/10 15:03
 * @PROJECT: hip-ac
 */
@Schema(description = "医嘱校对-患者信息")
public class CisIpdPatAsTo implements Serializable {

    @Schema(description = "患者标识")
    private String id;

    @Schema(description = "就诊流水号")
    private String visitCode;

    @Schema(description = "患者id")
    private String patCode;

    @Schema(description = "当前床位")
    private String bedId;

    @Schema(description = "当前床位名称")
    private String bedName;

    @Schema(description = "住院号")
    private String inpatientCode;

    @Schema(description = "姓名")
    private String name;

    @Schema(description = "出生日期")
    private LocalDateTime birthDate;

    @Schema(description = "性别")
    private String sex;

    @Schema(description = "性别名称")
    private String sexName;

    @Schema(description = "当前病情")
    private String condition;

    @Schema(description = "当前护理级别 字典CV06.00.220 ")
    private String nursingLevel;

    @Schema(description = "当前护理级别名称")
    private String nursingLevelName;

    @Schema(description = "费别")
    private String feeType;

    @Schema(description = "费别名称")
    private String feeTypeName;

    @Schema(description = "住院诊断")
    private String impDiagnosis;

    @Schema(description = "当前住院医师")
    private String admittedDoctor;

    @Schema(description = "当前住院医师名称")
    private String admittedDoctorName;

    @Schema(description = "预交余额")
    private BigDecimal balance;

    @Schema(description = "担保金额")
    private BigDecimal guaranteeAmount;

    @Schema(description = "新入院患者标识")
    private Boolean newPatFlag;

    @Schema(description = "当前护理组")
    private String deptNurseCode;

    @Schema(description = "当前护理组名称")
    private String deptNurseName;

    @Schema(description = "互斥医嘱信息")
    private List<CisIpdOrderAsTo> mutexOrderAsToList = new ArrayList<>();

    @Schema(description = "医嘱信息")
    private List<CisIpdOrderAsTo> cisIpdOrderAsToList = new ArrayList<>();

    public String getFeeTypeName() {
        return feeTypeName;
    }

    public void setFeeTypeName(String feeTypeName) {
        this.feeTypeName = feeTypeName;
    }

    public String getDeptNurseCode() {
        return deptNurseCode;
    }

    public void setDeptNurseCode(String deptNurseCode) {
        this.deptNurseCode = deptNurseCode;
    }

    public String getDeptNurseName() {
        return deptNurseName;
    }

    public void setDeptNurseName(String deptNurseName) {
        this.deptNurseName = deptNurseName;
    }

    public Boolean getNewPatFlag() {
        return newPatFlag;
    }

    public void setNewPatFlag(Boolean newPatFlag) {
        this.newPatFlag = newPatFlag;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    public String getPatCode() {
        return patCode;
    }

    public void setPatCode(String patCode) {
        this.patCode = patCode;
    }

    public String getBedId() {
        return bedId;
    }

    public void setBedId(String bedId) {
        this.bedId = bedId;
    }

    public String getBedName() {
        return bedName;
    }

    public void setBedName(String bedName) {
        this.bedName = bedName;
    }

    public String getInpatientCode() {
        return inpatientCode;
    }

    public void setInpatientCode(String inpatientCode) {
        this.inpatientCode = inpatientCode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public LocalDateTime getBirthDate() {
        return birthDate;
    }

    public void setBirthDate(LocalDateTime birthDate) {
        this.birthDate = birthDate;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getSexName() {
        return sexName;
    }

    public void setSexName(String sexName) {
        this.sexName = sexName;
    }

    public String getCondition() {
        return condition;
    }

    public void setCondition(String condition) {
        this.condition = condition;
    }

    public String getNursingLevel() {
        return nursingLevel;
    }

    public void setNursingLevel(String nursingLevel) {
        this.nursingLevel = nursingLevel;
    }

    public String getNursingLevelName() {
        return nursingLevelName;
    }

    public void setNursingLevelName(String nursingLevelName) {
        this.nursingLevelName = nursingLevelName;
    }

    public String getImpDiagnosis() {
        return impDiagnosis;
    }

    public void setImpDiagnosis(String impDiagnosis) {
        this.impDiagnosis = impDiagnosis;
    }

    public String getAdmittedDoctor() {
        return admittedDoctor;
    }

    public void setAdmittedDoctor(String admittedDoctor) {
        this.admittedDoctor = admittedDoctor;
    }

    public String getAdmittedDoctorName() {
        return admittedDoctorName;
    }

    public void setAdmittedDoctorName(String admittedDoctorName) {
        this.admittedDoctorName = admittedDoctorName;
    }

    public BigDecimal getBalance() {
        return balance;
    }

    public void setBalance(BigDecimal balance) {
        this.balance = balance;
    }

    public List<CisIpdOrderAsTo> getCisIpdOrderAsToList() {
        return cisIpdOrderAsToList;
    }

    public void setCisIpdOrderAsToList(List<CisIpdOrderAsTo> cisIpdOrderAsToList) {
        this.cisIpdOrderAsToList = cisIpdOrderAsToList;
    }

    public String getFeeType() {
        return feeType;
    }

    public void setFeeType(String feeType) {
        this.feeType = feeType;
    }

    public List<CisIpdOrderAsTo> getMutexOrderAsToList() {
        return mutexOrderAsToList;
    }

    public void setMutexOrderAsToList(List<CisIpdOrderAsTo> mutexOrderAsToList) {
        this.mutexOrderAsToList = mutexOrderAsToList;
    }

    public BigDecimal getGuaranteeAmount() {
        return guaranteeAmount;
    }

    public void setGuaranteeAmount(BigDecimal guaranteeAmount) {
        this.guaranteeAmount = guaranteeAmount;
    }
}
