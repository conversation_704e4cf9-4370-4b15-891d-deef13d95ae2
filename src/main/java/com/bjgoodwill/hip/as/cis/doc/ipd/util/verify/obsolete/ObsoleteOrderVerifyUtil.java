package com.bjgoodwill.hip.as.cis.doc.ipd.util.verify.obsolete;

import com.bjgoodwill.hip.business.util.cis.common.enums.CisStatusEnum;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.service.CisOrderExecPlanService;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.to.CisOrderExecPlanTo;
import com.bjgoodwill.hip.ds.cis.rule.drugauth.to.DoctCommitOrderMsgTo;
import com.bjgoodwill.hip.ds.econ.ipd.bill.service.EconIpdBillService;
import com.bjgoodwill.hip.ds.econ.ipd.bill.to.EconIpdBillOrderCostsTo;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> l<PERSON><PERSON><PERSON><PERSON>
 * @description :
 * @createDate : 2024/12/16 15:31
 */
@Service
public class ObsoleteOrderVerifyUtil {

    @Resource
    private EconIpdBillService econIpdBillService;

    @Resource
    private CisOrderExecPlanService cisOrderExecPlanService;

    /**
     * 校验医嘱作废
     * @param orderIds
     * @return
     */
    public List<DoctCommitOrderMsgTo> verifyOrderObsolete(List<String> orderIds) {
        List<DoctCommitOrderMsgTo> result = new ArrayList<>();
        for (String id : orderIds) {
            DoctCommitOrderMsgTo doctCommitOrderMsgTo = new DoctCommitOrderMsgTo();
            doctCommitOrderMsgTo.setId(id);
            List<String> errMsg = new ArrayList<>();
            List<String> list = new ArrayList<>();
            list.add(id);
            List<EconIpdBillOrderCostsTo> sumAmountByOrderIdList = econIpdBillService.getSumAmountByOrderIdList(list);
            if (CollectionUtils.isNotEmpty(sumAmountByOrderIdList)) {
                for (EconIpdBillOrderCostsTo sumAmountByOrderId : sumAmountByOrderIdList) {
                    if (sumAmountByOrderId != null) {
                        if (!sumAmountByOrderId.getCosts().equals(BigDecimal.ZERO)) {
                            //判断医嘱未被计费
                            String err = "金额已被计费,无法作废";
                            errMsg.add(err);
                            break;
                        }
                    }
                }
            }
            List<CisOrderExecPlanTo> cisOrderExecPlanToList = cisOrderExecPlanService.findCisOrderExecPlanByOrderId(id);
            if (CollectionUtils.isNotEmpty(cisOrderExecPlanToList)) {
                for (CisOrderExecPlanTo cisOrderExecPlanTo : cisOrderExecPlanToList) {
                    CisStatusEnum statusCode = cisOrderExecPlanTo.getStatusCode();
                    if (statusCode.equals(CisStatusEnum.NEW) || statusCode.equals(CisStatusEnum.EXCUTE)) {
                        //判断有无有效执行单:未执行(NEW)，已执行(EXCUTE) 即“有效”;不执行(REJECT)，取消执行(CANCELEXCUTE) 即“无效”
                        String err = "当前医嘱下包含有效的执行单,无法作废";
                        errMsg.add(err);
                        break;
                    }
                }
            }
            doctCommitOrderMsgTo.setMsg(errMsg);
            result.add(doctCommitOrderMsgTo);
        }

        return result;
    }

}
