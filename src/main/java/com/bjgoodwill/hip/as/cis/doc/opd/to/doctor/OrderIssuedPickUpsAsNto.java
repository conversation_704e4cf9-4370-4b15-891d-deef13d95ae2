package com.bjgoodwill.hip.as.cis.doc.opd.to.doctor;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

@Schema(description = "医嘱开立拾取器-新增")
public class OrderIssuedPickUpsAsNto implements Serializable {

    @Schema(description = "标识")
    private String id;

    @Schema(description = "医嘱类型")
    private String systemType;

    @Schema(description = "医嘱编码")
    private String serviceItemCode;

    @Schema(description = "医嘱名称")
    private String serviceItemName;

    public String getSystemType() {
        return systemType;
    }

    public void setSystemType(String systemType) {
        this.systemType = systemType;
    }

    public String getServiceItemName() {
        return serviceItemName;
    }

    public void setServiceItemName(String serviceItemName) {
        this.serviceItemName = serviceItemName;
    }

    public String getServiceItemCode() {
        return serviceItemCode;
    }

    public void setServiceItemCode(String serviceItemCode) {
        this.serviceItemCode = serviceItemCode;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
}
