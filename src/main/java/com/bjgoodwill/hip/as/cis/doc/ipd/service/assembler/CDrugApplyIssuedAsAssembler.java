package com.bjgoodwill.hip.as.cis.doc.ipd.service.assembler;

import com.bjgoodwill.hip.as.cis.doc.ipd.to.*;
import com.bjgoodwill.hip.as.cis.doc.ipd.to.cdrug.CDrugOrderDetailAsTo;
import com.bjgoodwill.hip.as.cis.doc.ipd.to.cdrug.CDrugOrderInfoAsTo;
import com.bjgoodwill.hip.business.util.cis.common.enums.*;
import com.bjgoodwill.hip.business.util.econ.enums.SystemItemClassEnum;
import com.bjgoodwill.hip.common.util.HIPIDUtil;
import com.bjgoodwill.hip.ds.cis.apply.charge.enmus.CisChargeTypeEnum;
import com.bjgoodwill.hip.ds.cis.apply.charge.to.CisApplyChargeEto;
import com.bjgoodwill.hip.ds.cis.apply.charge.to.CisApplyChargeNto;
import com.bjgoodwill.hip.ds.cis.apply.drug.to.*;
import com.bjgoodwill.hip.ds.econ.price.to.EconServicePriceTo;
import com.bjgoodwill.hip.security.util.HIPCurrentOrgUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> liangyuqing
 * @description :
 * @createDate : 2024/12/4 14:34
 */
@Service
public class CDrugApplyIssuedAsAssembler {

    /**
     * 保存草药医嘱
     * 构建草药申请单信息
     * @param cisIpdDocOrderAsNto
     * @return
     */
    public CisCDrugApplyNto buildCisCdrugApplyNto(CisIpdDocOrderAsNto cisIpdDocOrderAsNto) {
        CisCDrugApplyNto cisCDrugApplyNto = new CisCDrugApplyNto();

        // 申请单Nto属性构造
        cisCDrugApplyNto.setDoseNum(cisIpdDocOrderAsNto.getDoseNum().toString());
        cisCDrugApplyNto.setDecoction(cisIpdDocOrderAsNto.getDecoction());
        cisCDrugApplyNto.setCdrugPackNum(cisIpdDocOrderAsNto.getCdrugPackNum());
        cisCDrugApplyNto.setCdrugPackMl(cisIpdDocOrderAsNto.getCdrugPackMl());
        cisCDrugApplyNto.setUsage(cisIpdDocOrderAsNto.getUsage());
        cisCDrugApplyNto.setUsageName(cisIpdDocOrderAsNto.getUsageName());
        cisCDrugApplyNto.setReceiveOrg(cisIpdDocOrderAsNto.getReceiveOrgCode());
        cisCDrugApplyNto.setId(cisIpdDocOrderAsNto.getApplyId());
        cisCDrugApplyNto.setPatMiCode(cisIpdDocOrderAsNto.getPatMiCode());
        cisCDrugApplyNto.setVisitCode(cisIpdDocOrderAsNto.getVisitCode());
        cisCDrugApplyNto.setVisitType(VisitTypeEnum.IPD);
        cisCDrugApplyNto.setDeptNurseCode(cisIpdDocOrderAsNto.getDeptNurseCode());
        cisCDrugApplyNto.setDeptNurseName(cisIpdDocOrderAsNto.getDeptNurseName());
        cisCDrugApplyNto.setOrderID(cisIpdDocOrderAsNto.getId());
        cisCDrugApplyNto.setCreateOrgCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
        cisCDrugApplyNto.setCreateOrgName(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupName());
        cisCDrugApplyNto.setOrderType(OrderTypeEnum.TEMPORARY_ORDER);
        cisCDrugApplyNto.setFrequency(cisIpdDocOrderAsNto.getFrequency());
        cisCDrugApplyNto.setFrequencyName(cisIpdDocOrderAsNto.getFrequencyName());
        cisCDrugApplyNto.setNum(Double.valueOf(1));
        cisCDrugApplyNto.setExecutorOrgCode(cisIpdDocOrderAsNto.getExecuteOrgCode());
        cisCDrugApplyNto.setExecutorOrgName(cisIpdDocOrderAsNto.getExecuteOrgName());
        cisCDrugApplyNto.setVisitOrgCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
        cisCDrugApplyNto.setVisitOrgName(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupName());
        cisCDrugApplyNto.setServiceItemName(cisIpdDocOrderAsNto.getServiceItemName());
        cisCDrugApplyNto.setIsCanPriorityFlag(cisIpdDocOrderAsNto.getIsCanPriorityFlag());
        cisCDrugApplyNto.setHerbsProCode(cisIpdDocOrderAsNto.getHerbsProCode());

        // 草药明细信息构造
        List<CisDrugApplyDetailNto> cisDrugApplyDetailNtoList = new ArrayList<>();
        List<CisApplyChargeNto> cisApplyChargeNtoList = new ArrayList<>();
        String serviceItemCodes = "";
        for (CisIpdDocOrderDetailsAsNto cisIpdDocOrderDetailsAsNto : cisIpdDocOrderAsNto.getCisIpdDocOrderDetailsAsNtoList()) {
            //药品明细
            CisDrugApplyDetailNto cisDrugApplyDetailNto = this.cisDrugApplyDetailNtoToTo(cisIpdDocOrderDetailsAsNto, cisIpdDocOrderAsNto);
            //申请单收费信息
            CisApplyChargeNto cisApplyChargeNto = buildCDrugApplyChargeNto(cisIpdDocOrderDetailsAsNto, cisIpdDocOrderAsNto);
            cisDrugApplyDetailNtoList.add(cisDrugApplyDetailNto);
            cisApplyChargeNtoList.add(cisApplyChargeNto);
            serviceItemCodes += cisIpdDocOrderDetailsAsNto.getPriceItemCode() + ",";
        }

        cisCDrugApplyNto.setServiceItemCode(serviceItemCodes.substring(0, serviceItemCodes.length() - 1));
        cisCDrugApplyNto.setDetails(cisDrugApplyDetailNtoList);
        cisCDrugApplyNto.setCisApplyCharges(cisApplyChargeNtoList);

        return cisCDrugApplyNto;
    }

    /**
     * 药品明细申请单
     */
    public  CisDrugApplyDetailNto cisDrugApplyDetailNtoToTo(CisIpdDocOrderDetailsAsNto cisIpdDocOrderDetailsAsNto, CisIpdDocOrderAsNto cisIpdDocOrderAsNto) {
        if (cisIpdDocOrderDetailsAsNto == null) {
            return null;
        }
        CisDrugApplyDetailNto cisDrugApplyDetailNto = new CisDrugApplyDetailNto();
        cisDrugApplyDetailNto.setId(cisIpdDocOrderDetailsAsNto.getId());
        cisDrugApplyDetailNto.setVisitCode(cisIpdDocOrderAsNto.getVisitCode());
        cisDrugApplyDetailNto.setApplyId(cisIpdDocOrderAsNto.getApplyId());
        cisDrugApplyDetailNto.setDrugCode(cisIpdDocOrderDetailsAsNto.getPriceItemCode());
        cisDrugApplyDetailNto.setDrugName(cisIpdDocOrderDetailsAsNto.getPriceItemName());
        cisDrugApplyDetailNto.setDosage(cisIpdDocOrderDetailsAsNto.getDosage());
        cisDrugApplyDetailNto.setDosageUnit(cisIpdDocOrderDetailsAsNto.getDosageUnit());
        cisDrugApplyDetailNto.setDosageUnitName(cisIpdDocOrderDetailsAsNto.getDosageUnitValue());
        cisDrugApplyDetailNto.setPackageUnit(cisIpdDocOrderDetailsAsNto.getPackageUnit());
        cisDrugApplyDetailNto.setPackageUnitName(cisIpdDocOrderDetailsAsNto.getPackageUnitValue());
        cisDrugApplyDetailNto.setReceiveOrg(cisIpdDocOrderAsNto.getReceiveOrgCode());
        cisDrugApplyDetailNto.setSbadmWay(SbadmWayEnum.valueOf(cisIpdDocOrderDetailsAsNto.getSbadmWay()));
        cisDrugApplyDetailNto.setIsSkin(cisIpdDocOrderDetailsAsNto.getSkinFlag());
        cisDrugApplyDetailNto.setDecoctMethodCode(cisIpdDocOrderDetailsAsNto.getDecoctMethodCode());
        cisDrugApplyDetailNto.setSortNo(cisIpdDocOrderDetailsAsNto.getSortNo());
        cisDrugApplyDetailNto.setPackageNum(cisIpdDocOrderDetailsAsNto.getPackageNum());
        cisDrugApplyDetailNto.setAntimicrobialsPurpose(cisIpdDocOrderDetailsAsNto.getAntimicrobialsPurpose());
        cisDrugApplyDetailNto.setExtTypeCode(EDrugSystemTypeExtEnum.COMMON.getCode());
        return cisDrugApplyDetailNto;
    }

    /**
     * 修改草药医嘱
     * 构造草药申请单修改Eto
     * @param cisIpdDocOrderAsEto
     * @return
     */
    public CisCDrugApplyEto buildCisCDrugApplyEto(CisIpdDocOrderAsEto cisIpdDocOrderAsEto) {
        CisCDrugApplyEto cisCDrugApplyEto = new CisCDrugApplyEto();

        List<CisApplyChargeNto> cisApplyChargeNtoList = new ArrayList<>();
        List<CisApplyChargeEto> cisApplyChargeEtoList = new ArrayList<>();

        // 申请单Eto属性构造
        cisCDrugApplyEto.setCdrugPackNum(cisIpdDocOrderAsEto.getCdrugPackNum());
        cisCDrugApplyEto.setCdrugPackMl(cisIpdDocOrderAsEto.getCdrugPackMl());
        cisCDrugApplyEto.setFrequency(cisIpdDocOrderAsEto.getFrequency());
        cisCDrugApplyEto.setFrequencyName(cisIpdDocOrderAsEto.getFrequencyName());
        cisCDrugApplyEto.setUsage(cisIpdDocOrderAsEto.getUsage());
        cisCDrugApplyEto.setUsageName(cisIpdDocOrderAsEto.getUsageName());
        cisCDrugApplyEto.setReceiveOrg(cisIpdDocOrderAsEto.getReceiveOrgCode());
        cisCDrugApplyEto.setVersion(cisIpdDocOrderAsEto.getVersion());
        cisCDrugApplyEto.setStatusCode(cisIpdDocOrderAsEto.getApplyStatusCode());
        cisCDrugApplyEto.setDoseNum(cisIpdDocOrderAsEto.getDoseNum() + "");
        cisCDrugApplyEto.setServiceItemName(cisIpdDocOrderAsEto.getServiceItemName());
        cisCDrugApplyEto.setIsCanPriorityFlag(cisIpdDocOrderAsEto.getIsCanPriorityFlag());
        cisCDrugApplyEto.setHerbsProCode(cisIpdDocOrderAsEto.getHerbsProCode());
        cisCDrugApplyEto.setNum(1d);


        String serviceItemCodes = "";
        // 申请单明细Nto属性构造、申请单chargeNto属性构造
        List<CisDrugApplyDetailNto> detailNtoList = new ArrayList<>();
        for (CisIpdDocOrderDetailsAsNto cisIpdDocOrderDetailsAsNto : cisIpdDocOrderAsEto.getCisIpdDocOrderDetailsAsNtoList()) {
            //申请单收费信息
            CisApplyChargeNto cisApplyChargeNto = buildCDrugApplyChargeNto(cisIpdDocOrderDetailsAsNto, cisIpdDocOrderAsEto);
            cisApplyChargeNtoList.add(cisApplyChargeNto);

            CisDrugApplyDetailNto cisDrugApplyDetailNto = new CisDrugApplyDetailNto();
            cisDrugApplyDetailNto.setId(cisIpdDocOrderDetailsAsNto.getApplyDetailId());
            cisDrugApplyDetailNto.setVisitCode(cisIpdDocOrderAsEto.getVisitCode());
            cisDrugApplyDetailNto.setApplyId(cisIpdDocOrderDetailsAsNto.getId());
            cisDrugApplyDetailNto.setDrugCode(cisIpdDocOrderDetailsAsNto.getPriceItemCode());
            cisDrugApplyDetailNto.setDrugName(cisIpdDocOrderDetailsAsNto.getPriceItemName());
            cisDrugApplyDetailNto.setDosage(cisIpdDocOrderDetailsAsNto.getDosage());
            cisDrugApplyDetailNto.setDosageUnit(cisIpdDocOrderDetailsAsNto.getDosageUnit());
            cisDrugApplyDetailNto.setDosageUnitName(cisIpdDocOrderDetailsAsNto.getDosageUnitValue());
            cisDrugApplyDetailNto.setPackageNum(cisIpdDocOrderDetailsAsNto.getPackageNum());
            cisDrugApplyDetailNto.setPackageUnit(cisIpdDocOrderDetailsAsNto.getPackageUnit());
            cisDrugApplyDetailNto.setPackageUnitName(cisIpdDocOrderDetailsAsNto.getPackageUnitValue());
            cisDrugApplyDetailNto.setReceiveOrg(cisIpdDocOrderAsEto.getReceiveOrgCode());
            cisDrugApplyDetailNto.setSortNo(cisIpdDocOrderDetailsAsNto.getSortNo());
            cisDrugApplyDetailNto.setDecoctMethodCode(cisIpdDocOrderDetailsAsNto.getDecoctMethodCode());
            cisDrugApplyDetailNto.setDecoctMethodName(cisIpdDocOrderDetailsAsNto.getDecoctMethodName());
            detailNtoList.add(cisDrugApplyDetailNto);

            serviceItemCodes += cisIpdDocOrderDetailsAsNto.getPriceItemCode() + ",";
        }

        // 申请单明细Eto属性构造、申请单chargeEto属性构造
        List<CisDrugApplyDetailEto> detailEtoList = new ArrayList<>();
        for (CisIpdDocOrderDetailsAsEto cisIpdDocOrderDetailsAsEto : cisIpdDocOrderAsEto.getCisIpdDocOrderDetailsAsEtoList()) {
            //申请单收费信息
            CisApplyChargeEto cisApplyChargeEto = buildCDrugApplyChargeEto(cisIpdDocOrderDetailsAsEto, cisIpdDocOrderAsEto);
            cisApplyChargeEtoList.add(cisApplyChargeEto);

            CisDrugApplyDetailEto cisDrugApplyDetailEto = new CisDrugApplyDetailEto();
            cisDrugApplyDetailEto.setId(cisIpdDocOrderDetailsAsEto.getId());
            cisDrugApplyDetailEto.setDrugCode(cisIpdDocOrderDetailsAsEto.getPriceItemCode());
            cisDrugApplyDetailEto.setDrugName(cisIpdDocOrderDetailsAsEto.getPriceItemName());
            cisDrugApplyDetailEto.setDosage(cisIpdDocOrderDetailsAsEto.getDosage());
            cisDrugApplyDetailEto.setDosageUnit(cisIpdDocOrderDetailsAsEto.getDosageUnit());
            cisDrugApplyDetailEto.setDosageUnitName(cisIpdDocOrderDetailsAsEto.getDosageUnitValue());
            cisDrugApplyDetailEto.setPackageNum(cisIpdDocOrderDetailsAsEto.getPackageNum());
            cisDrugApplyDetailEto.setPackageUnit(cisIpdDocOrderDetailsAsEto.getPackageUnit());
            cisDrugApplyDetailEto.setPackageUnitName(cisIpdDocOrderDetailsAsEto.getPackageUnitValue());
            cisDrugApplyDetailEto.setReceiveOrg(cisIpdDocOrderAsEto.getReceiveOrgCode());
            cisDrugApplyDetailEto.setSortNo(cisIpdDocOrderDetailsAsEto.getSortNo());
            cisDrugApplyDetailEto.setDecoctMethodCode(cisIpdDocOrderDetailsAsEto.getDecoctMethodCode());
            cisDrugApplyDetailEto.setDecoctMethodName(cisIpdDocOrderDetailsAsEto.getDecoctMethodName());
            detailEtoList.add(cisDrugApplyDetailEto);

            serviceItemCodes += cisIpdDocOrderDetailsAsEto.getPriceItemCode() + ",";
        }

        cisCDrugApplyEto.setServiceItemCode(serviceItemCodes.substring(0, serviceItemCodes.length() - 1));
        cisCDrugApplyEto.setDetailEtos(detailEtoList);
        cisCDrugApplyEto.setDetailNtos(detailNtoList);
        cisCDrugApplyEto.setCisApplyChargeEtos(cisApplyChargeEtoList);
        cisCDrugApplyEto.setCisApplyChargeNtos(cisApplyChargeNtoList);

        return cisCDrugApplyEto;
    }


    /**
     * 保存草药医嘱
     * 构建草药申请单费用列表
     * @param detailNto
     * @param orderNto
     * @return
     */
    public CisApplyChargeNto buildCDrugApplyChargeNto(CisIpdDocOrderDetailsAsNto detailNto, CisIpdDocOrderAsNto orderNto) {
        CisApplyChargeNto cisApplyChargeNto = new CisApplyChargeNto();
        cisApplyChargeNto.setId(detailNto.getChargeId());
        cisApplyChargeNto.setOrderId(orderNto.getId());
        cisApplyChargeNto.setVisitCode(orderNto.getVisitCode());
        cisApplyChargeNto.setPriceItemCode(detailNto.getPriceItemCode());
        cisApplyChargeNto.setPriceItemName(detailNto.getPriceItemName());
        cisApplyChargeNto.setPackageSpec(detailNto.getDrugSpec());
        cisApplyChargeNto.setPrice(detailNto.getPrice());
        cisApplyChargeNto.setUnit(detailNto.getUnit());
        cisApplyChargeNto.setChargeFrequency(orderNto.getFrequency());
        cisApplyChargeNto.setStatusCode(CisStatusEnum.NEW);
        cisApplyChargeNto.setExecuteOrgCode(orderNto.getExecuteOrgCode());
        cisApplyChargeNto.setExecuteOrgName(orderNto.getExecuteOrgName());
        cisApplyChargeNto.setLimitConformFlag(detailNto.getLimitConformFlag());
        cisApplyChargeNto.setCisBaseApplyId(orderNto.getApplyId());
        cisApplyChargeNto.setChargeType(CisChargeTypeEnum.DOCT);
        cisApplyChargeNto.setDetailId(detailNto.getId());
        cisApplyChargeNto.setPackageSpec(detailNto.getDrugSpec());

        // 每味药的金额 = 包装数量 * 单价
        BigDecimal doseNum = new BigDecimal(orderNto.getDoseNum() + "");
        BigDecimal packageNum = new BigDecimal(detailNto.getPackageNum() + ""); // 包装数量
        BigDecimal price = new BigDecimal(detailNto.getPrice() + "");
        BigDecimal chargeAmount = packageNum.multiply(doseNum).multiply(price);
        cisApplyChargeNto.setChageAmount(chargeAmount);

        // 传的是最小单位，数量 = 付数 * 数量
        BigDecimal num = new BigDecimal(detailNto.getNum() + ""); // 最小单位数量
        cisApplyChargeNto.setNum(num.multiply(doseNum).doubleValue());

        cisApplyChargeNto.setSystemItemClass(SystemItemClassEnum.DRUG);

        return cisApplyChargeNto;
    }

    /**
     * 修改草药医嘱
     * 构建修改的草药申请单费用列表
     * @param detailEto
     * @param orderEto
     * @return
     */
    public CisApplyChargeEto buildCDrugApplyChargeEto(CisIpdDocOrderDetailsAsEto detailEto, CisIpdDocOrderAsEto orderEto) {
        CisApplyChargeEto cisApplyChargeEto = new CisApplyChargeEto();
        cisApplyChargeEto.setId(detailEto.getChargeId());
        cisApplyChargeEto.setPriceItemCode(detailEto.getPriceItemCode());
        cisApplyChargeEto.setPriceItemName(detailEto.getPriceItemName());
        cisApplyChargeEto.setPackageSpec(detailEto.getDrugSpec());
        cisApplyChargeEto.setPrice(detailEto.getPrice());
        cisApplyChargeEto.setUnit(detailEto.getUnit());
        cisApplyChargeEto.setExecuteOrgCode(orderEto.getExecuteOrgCode());
        cisApplyChargeEto.setExecuteOrgName(orderEto.getExecuteOrgName());
        cisApplyChargeEto.setVersion(detailEto.getVersion());
        cisApplyChargeEto.setPackageSpec(detailEto.getDrugSpec());
        cisApplyChargeEto.setVersion(detailEto.getVersion());

        // 每味药的金额 = 包装数量 * 单价
        BigDecimal doseNum = new BigDecimal(orderEto.getDoseNum() + "");
        BigDecimal packageNum = new BigDecimal(detailEto.getPackageNum() + ""); // 包装数量
        BigDecimal price = new BigDecimal(detailEto.getPrice() + "");
        BigDecimal chargeAmount = packageNum.multiply(doseNum).multiply(price);
        cisApplyChargeEto.setChageAmount(chargeAmount);

        // 传的是最小单位，数量 = 付数 * 数量
        BigDecimal num = new BigDecimal(detailEto.getNum() + ""); // 最小单位数量
        cisApplyChargeEto.setNum(num.multiply(doseNum).doubleValue());
        cisApplyChargeEto.setSystemItemClass(SystemItemClassEnum.DRUG);

        return cisApplyChargeEto;
    }

    /**
     * 修改草药医嘱
     * 构建新增的草药申请单费用列表
     * @param detailNto
     * @param orderEto
     * @return
     */
    public CisApplyChargeNto buildCDrugApplyChargeNto(CisIpdDocOrderDetailsAsNto detailNto, CisIpdDocOrderAsEto orderEto) {
        CisApplyChargeNto cisApplyChargeNto = new CisApplyChargeNto();
        cisApplyChargeNto.setId(detailNto.getChargeId());
        cisApplyChargeNto.setOrderId(orderEto.getId());
        cisApplyChargeNto.setVisitCode(orderEto.getVisitCode());
        cisApplyChargeNto.setPriceItemCode(detailNto.getPriceItemCode());
        cisApplyChargeNto.setPriceItemName(detailNto.getPriceItemName());
        cisApplyChargeNto.setPackageSpec(detailNto.getDrugSpec());
        cisApplyChargeNto.setPrice(detailNto.getPrice());
        cisApplyChargeNto.setUnit(detailNto.getUnit());
        cisApplyChargeNto.setChargeFrequency(orderEto.getFrequency());
        cisApplyChargeNto.setStatusCode(CisStatusEnum.NEW);
        cisApplyChargeNto.setExecuteOrgCode(orderEto.getExecuteOrgCode());
        cisApplyChargeNto.setExecuteOrgName(orderEto.getExecuteOrgName());
        cisApplyChargeNto.setLimitConformFlag(detailNto.getLimitConformFlag());
        cisApplyChargeNto.setCisBaseApplyId(orderEto.getApplyId());
        cisApplyChargeNto.setChargeType(CisChargeTypeEnum.DOCT);
        cisApplyChargeNto.setDetailId(detailNto.getId());
        cisApplyChargeNto.setPackageSpec(detailNto.getDrugSpec());

        // 每味药的金额 = 包装数量 * 单价
        BigDecimal doseNum = new BigDecimal(orderEto.getDoseNum() + "");
        BigDecimal packageNum = new BigDecimal(detailNto.getPackageNum() + ""); // 包装数量
        BigDecimal price = new BigDecimal(detailNto.getPrice() + "");
        BigDecimal chargeAmount = packageNum.multiply(doseNum).multiply(price);
        cisApplyChargeNto.setChageAmount(chargeAmount);

        // 传的是最小单位，数量 = 付数 * 数量
        BigDecimal num = new BigDecimal(detailNto.getNum() + ""); // 最小单位数量
        cisApplyChargeNto.setNum(num.multiply(doseNum).doubleValue());
        cisApplyChargeNto.setSystemItemClass(SystemItemClassEnum.DRUG);

        return cisApplyChargeNto;
    }


    /**
     * 构造草药代煎费
     * @param cisIpdDocOrderAsNto
     * @param econServicePriceTo
     * @return
     */
    public CisApplyChargeNto buildCdrugDectionCharge(CisIpdDocOrderAsNto cisIpdDocOrderAsNto, EconServicePriceTo econServicePriceTo) {
        CisApplyChargeNto cisApplyChargeNto = new CisApplyChargeNto();

        // 雪花算法生成代煎费主键
        cisApplyChargeNto.setId(HIPIDUtil.getNextIdString());
        cisApplyChargeNto.setOrderId(cisIpdDocOrderAsNto.getId());
        cisApplyChargeNto.setVisitCode(cisIpdDocOrderAsNto.getVisitCode());
        cisApplyChargeNto.setPriceItemCode(econServicePriceTo.getCode());
        cisApplyChargeNto.setPriceItemName(econServicePriceTo.getName());
        cisApplyChargeNto.setPackageSpec(econServicePriceTo.getSpec());
        cisApplyChargeNto.setPrice(econServicePriceTo.getPrice());
        cisApplyChargeNto.setUnit(econServicePriceTo.getUnit());
        cisApplyChargeNto.setNum(cisIpdDocOrderAsNto.getNum() * cisIpdDocOrderAsNto.getDoseNum());
        cisApplyChargeNto.setChargeFrequency(cisIpdDocOrderAsNto.getFrequency());
        cisApplyChargeNto.setStatusCode(CisStatusEnum.NEW);
        cisApplyChargeNto.setExecuteOrgCode(cisIpdDocOrderAsNto.getExecuteOrgCode());
        cisApplyChargeNto.setExecuteOrgName(cisIpdDocOrderAsNto.getExecuteOrgName());
        cisApplyChargeNto.setCisBaseApplyId(cisIpdDocOrderAsNto.getApplyId());
        cisApplyChargeNto.setChargeType(CisChargeTypeEnum.PRESCRIPTION);
        cisApplyChargeNto.setSystemItemClass(econServicePriceTo.getSystemItemClass());

        // 煎药费的金额 = 付数 * 煎药费收费项目单价
        BigDecimal chargeAmount = (new BigDecimal(econServicePriceTo.getPrice() + "")).multiply(new BigDecimal(cisIpdDocOrderAsNto.getDoseNum()));
        cisApplyChargeNto.setChageAmount(chargeAmount);

        return cisApplyChargeNto;
    }

    public void buildCDrugOrderByApply(CDrugOrderInfoAsTo orderAsTo, CisCDrugApplyTo cisCDrugApplyTo) {
        orderAsTo.setServiceItemName(cisCDrugApplyTo.getServiceItemName());

        orderAsTo.setUsage(cisCDrugApplyTo.getUsage());
        orderAsTo.setUsageName(cisCDrugApplyTo.getUsageName());
        orderAsTo.setFrequency(cisCDrugApplyTo.getFrequency());
        orderAsTo.setFrequencyName(cisCDrugApplyTo.getFrequencyName());
        orderAsTo.setPrescriptionFlag(cisCDrugApplyTo.getPrescriptionFlag());
        orderAsTo.setReceiveOrg(cisCDrugApplyTo.getReceiveOrg());

        orderAsTo.setDoseNum(cisCDrugApplyTo.getDoseNum());
        orderAsTo.setDecoction(cisCDrugApplyTo.getDecoction());
        orderAsTo.setCdrugPackMl(cisCDrugApplyTo.getCdrugPackMl());
        orderAsTo.setCdrugPackNum(cisCDrugApplyTo.getCdrugPackNum());
        orderAsTo.setHerbsProCode(cisCDrugApplyTo.getHerbsProCode());
        orderAsTo.setApplyStatusCode(cisCDrugApplyTo.getStatusCode());

        orderAsTo.setApplyId(cisCDrugApplyTo.getId());
        orderAsTo.setIsCanPriorityFlag(cisCDrugApplyTo.getIsCanPriorityFlag());
        orderAsTo.setVersion(cisCDrugApplyTo.getVersion());
    }

    public CDrugOrderDetailAsTo buildCDrugOrderDetailByApplyDetail(CisDrugApplyDetailTo cisDrugApplyDetailTo) {
        CDrugOrderDetailAsTo orderDetailAsTo = new CDrugOrderDetailAsTo();

        orderDetailAsTo.setId(cisDrugApplyDetailTo.getId());
        orderDetailAsTo.setApplyId(cisDrugApplyDetailTo.getCisBaseDrugApplyId());
        orderDetailAsTo.setSortNo(cisDrugApplyDetailTo.getSortNo());
        orderDetailAsTo.setPriceItemCode(cisDrugApplyDetailTo.getDrugCode());
        orderDetailAsTo.setPriceItemName(cisDrugApplyDetailTo.getDrugName());
        orderDetailAsTo.setDosage(cisDrugApplyDetailTo.getDosage());
        orderDetailAsTo.setDosageUnit(cisDrugApplyDetailTo.getDosageUnit());
        orderDetailAsTo.setPackageNum(cisDrugApplyDetailTo.getPackageNum());
        orderDetailAsTo.setPackageUnit(cisDrugApplyDetailTo.getPackageUnit());
        orderDetailAsTo.setReceiveOrg(cisDrugApplyDetailTo.getReceiveOrg());
        orderDetailAsTo.setSbadmWay(cisDrugApplyDetailTo.getSbadmWay());
        orderDetailAsTo.setSkin(cisDrugApplyDetailTo.getIsSkin());
        orderDetailAsTo.setDecoctMethodCode(cisDrugApplyDetailTo.getDecoctMethodCode());
        orderDetailAsTo.setExistFlag(true);

        return orderDetailAsTo;
    }
}
