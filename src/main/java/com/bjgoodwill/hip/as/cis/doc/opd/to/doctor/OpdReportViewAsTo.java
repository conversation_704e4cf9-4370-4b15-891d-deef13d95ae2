package com.bjgoodwill.hip.as.cis.doc.opd.to.doctor;

import io.swagger.v3.oas.annotations.media.Schema;

public class OpdReportViewAsTo {

    @Schema(description = "流水号")
    private String visitCode;
    @Schema(description = "医嘱编码")
    private String serviceItemCode;
    @Schema(description = "医嘱名称")
    private String serviceItemName;
    @Schema(description = "格式化时间")
    private String dateFormatStr;
    @Schema(description = "申请单号")
    private String applyCode;
    @Schema(description = "给前端的冗余字段")
    private String caption;

    public String getCaption() {
        return caption;
    }

    public void setCaption(String caption) {
        this.caption = caption;
    }

    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    public String getServiceItemCode() {
        return serviceItemCode;
    }

    public void setServiceItemCode(String serviceItemCode) {
        this.serviceItemCode = serviceItemCode;
    }

    public String getServiceItemName() {
        return serviceItemName;
    }

    public void setServiceItemName(String serviceItemName) {
        this.serviceItemName = serviceItemName;
    }

    public String getDateFormatStr() {
        return dateFormatStr;
    }

    public void setDateFormatStr(String dateFormatStr) {
        this.dateFormatStr = dateFormatStr;
    }

    public String getApplyCode() {
        return applyCode;
    }

    public void setApplyCode(String applyCode) {
        this.applyCode = applyCode;
    }
}
