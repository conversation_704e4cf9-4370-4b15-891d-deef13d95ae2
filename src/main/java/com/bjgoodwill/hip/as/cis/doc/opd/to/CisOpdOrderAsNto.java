package com.bjgoodwill.hip.as.cis.doc.opd.to;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/5/23 16:08
 * Description: 门诊医嘱粘贴
 */
public class CisOpdOrderAsNto {
    @Schema(
            description = "流水号"
    )
    private String visitCode;
    @Schema(
            description = "诊疗号"
    )
    private String treatmentCode;
    @Schema(
            description = "主索引"
    )
    private String patMiCode;
    @Schema(
            description = "医嘱id"
    )
    private List<String> orderIds;
    @Schema(
            description = "处方版本号"
    )
    private String prescriptionVersion;
    @Schema(
            description = "当前登录人"
    )
    private String curLoginStaff;
    @Schema(
            description = "当前登录人姓名"
    )
    private String curLoginStaffName;
    @Schema(
            description = "院区编码"
    )
    private String hospitalCode;
    @Schema(
            description = "院区名称"
    )
    private String hospitalName;
    @Schema(
            description = "工作组编码"
    )
    private String curWorkGroupCode;
    @Schema(
            description = "工作组名称"
    )
    private String curWorkGroupName;

    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    public String getPatMiCode() {
        return patMiCode;
    }

    public void setPatMiCode(String patMiCode) {
        this.patMiCode = patMiCode;
    }

    public String getTreatmentCode() {
        return treatmentCode;
    }

    public void setTreatmentCode(String treatmentCode) {
        this.treatmentCode = treatmentCode;
    }

    public List<String> getOrderIds() {
        return orderIds;
    }

    public void setOrderIds(List<String> orderIds) {
        this.orderIds = orderIds;
    }

    public @NotBlank(
            message = "处方版本号不能为空！"
    ) String getPrescriptionVersion() {
        return this.prescriptionVersion;
    }

    public void setPrescriptionVersion(String prescriptionVersion) {
        this.prescriptionVersion = prescriptionVersion;
    }

    public String getCurLoginStaff() {
        return curLoginStaff;
    }

    public void setCurLoginStaff(String curLoginStaff) {
        this.curLoginStaff = curLoginStaff;
    }

    public String getCurLoginStaffName() {
        return curLoginStaffName;
    }

    public void setCurLoginStaffName(String curLoginStaffName) {
        this.curLoginStaffName = curLoginStaffName;
    }

    public String getHospitalCode() {
        return hospitalCode;
    }

    public void setHospitalCode(String hospitalCode) {
        this.hospitalCode = hospitalCode;
    }

    public String getHospitalName() {
        return hospitalName;
    }

    public void setHospitalName(String hospitalName) {
        this.hospitalName = hospitalName;
    }

    public String getCurWorkGroupCode() {
        return curWorkGroupCode;
    }

    public void setCurWorkGroupCode(String curWorkGroupCode) {
        this.curWorkGroupCode = curWorkGroupCode;
    }

    public String getCurWorkGroupName() {
        return curWorkGroupName;
    }

    public void setCurWorkGroupName(String curWorkGroupName) {
        this.curWorkGroupName = curWorkGroupName;
    }
}
