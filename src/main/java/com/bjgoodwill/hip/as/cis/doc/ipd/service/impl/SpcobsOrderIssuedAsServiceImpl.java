package com.bjgoodwill.hip.as.cis.doc.ipd.service.impl;

import com.bjgoodwill.hip.as.cis.doc.ipd.service.SpcobsOrderIssuedAsService;
import com.bjgoodwill.hip.as.cis.doc.ipd.service.assembler.OrderIssuedAsAssembler;
import com.bjgoodwill.hip.as.cis.doc.ipd.to.*;
import com.bjgoodwill.hip.as.cis.doc.ipd.util.verify.submit.SubmitOrderVerifyUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.CisStatusEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.HospitalModelEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.common.bean.LoginInfo;
import com.bjgoodwill.hip.common.exception.BusinessException;
import com.bjgoodwill.hip.common.util.HIPBeanUtil;
import com.bjgoodwill.hip.common.util.HIPIDUtil;
import com.bjgoodwill.hip.ds.base.mi.milist.service.MiLimitHilistService;
import com.bjgoodwill.hip.ds.base.mi.milist.to.MiLimitHilistTo;
import com.bjgoodwill.hip.ds.cis.apply.apply.service.CisBaseApplyService;
import com.bjgoodwill.hip.ds.cis.apply.charge.to.CisApplyChargeQto;
import com.bjgoodwill.hip.ds.cis.apply.charge.to.CisApplyChargeTo;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.service.CisOrderExecPlanService;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.to.CisOrderExecPlanTo;
import com.bjgoodwill.hip.ds.cis.apply.spcobs.service.CisSpcobsApplyService;
import com.bjgoodwill.hip.ds.cis.apply.spcobs.to.CisSpcobsApplyDetailTo;
import com.bjgoodwill.hip.ds.cis.apply.spcobs.to.CisSpcobsApplyTo;
import com.bjgoodwill.hip.ds.cis.cds.order.service.CisOrderCommonService;
import com.bjgoodwill.hip.ds.cis.cds.order.to.CisOrderCommonNto;
import com.bjgoodwill.hip.ds.cis.cds.order.to.CisOrderCommonQto;
import com.bjgoodwill.hip.ds.cis.cds.order.to.CisOrderCommonTo;
import com.bjgoodwill.hip.ds.cis.cpoe.order.service.CisIpdCpoeService;
import com.bjgoodwill.hip.ds.cis.cpoe.order.to.*;
import com.bjgoodwill.hip.ds.cis.medicineitem.price.to.ServiceClinicPriceOrgTo;
import com.bjgoodwill.hip.ds.cis.medicineitem.price.to.ServiceClinicPriceTo;
import com.bjgoodwill.hip.ds.cis.medicineitem.serviceItem.service.ServiceClinicItemService;
import com.bjgoodwill.hip.ds.cis.medicineitem.serviceItem.to.ServiceClinicItemTo;
import com.bjgoodwill.hip.ds.cis.medicineitem.serviceItem.to.SpcobsApplyTo;
import com.bjgoodwill.hip.ds.cis.rule.drugauth.to.DoctCommitOrderMsgTo;
import com.bjgoodwill.hip.ds.econ.ipd.bill.service.EconIpdBillService;
import com.bjgoodwill.hip.ds.econ.ipd.bill.to.EconIpdBillAllTo;
import com.bjgoodwill.hip.ds.econ.ipd.bill.to.EconIpdBillOrderCostsTo;
import com.bjgoodwill.hip.ds.econ.ipd.bill.to.EconIpdBillQto;
import com.bjgoodwill.hip.ds.econ.price.service.EconServicePriceService;
import com.bjgoodwill.hip.ds.econ.price.to.EconServicePriceTo;
import com.bjgoodwill.hip.ds.org.api.service.StaffService;
import com.bjgoodwill.hip.ds.org.api.service.WorkGroupService;
import com.bjgoodwill.hip.ds.org.api.to.StaffTo;
import com.bjgoodwill.hip.ds.org.api.to.WorkGroupTo;
import com.bjgoodwill.hip.ds.term.api.service.DictElementService;
import com.bjgoodwill.hip.ds.term.api.to.DictElementTo;
import com.bjgoodwill.hip.security.util.HIPCurrentOrgUtils;
import com.bjgoodwill.hip.security.util.HIPSecurityUtils;
import io.micrometer.common.util.StringUtils;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

/**
 * @Author: zhangyunchuan
 * @Date: 2024/11/18 16:58
 * @PROJECT: hip-ac
 */
@Service("com.bjgoodwill.hip.as.cis.doc.ipd.service.SpcobsOrderIssuedAsService")
public class SpcobsOrderIssuedAsServiceImpl implements SpcobsOrderIssuedAsService {

    @Autowired
    @Qualifier("com.bjgoodwill.hip.ds.cis.medicineitem.serviceItem.service.ServiceClinicItemTangibleService")
    private ServiceClinicItemService serviceClinicItemService;

    @Autowired
    private CisIpdCpoeService cisIpdCpoeService;

    @Autowired
    private CisSpcobsApplyService cisSpcobsApplyService;

    @Autowired
    private CisOrderCommonService cisOrderCommonService;

    @Autowired
    private EconServicePriceService econServicePriceService;

    @Autowired
    private WorkGroupService workGroupService;

    @Autowired
    private EconIpdBillService econIpdBillService;

    @Autowired
    private CisOrderExecPlanService cisOrderExecPlanService;

    @Autowired
    private SubmitOrderVerifyUtil submitOrderVerifyUtil;

    @Autowired
    private MiLimitHilistService miLimitHilistService;

    @Qualifier("com.bjgoodwill.hip.ds.cis.apply.apply.service.CisBaseApplyServiceTangibleFeign")
    @Autowired
    private CisBaseApplyService cisBaseApplyService;

    @Autowired
    private DictElementService dictElementService;

    @Autowired
    private StaffService staffService;

    @Resource
    private OrderIssuedAsAssembler orderIssuedAsAssembler;

    @Override
    public List<SpcobsApplyAsTo> getServiceClinicItems() {
        List<SpcobsApplyTo> spcobsApplyToList = new ArrayList<>();
        List<SystemTypeEnum> systemTypeEnums = new ArrayList<>();
        systemTypeEnums.add(SystemTypeEnum.SPCOBS);
        List<ServiceClinicItemTo> serviceClinicItemTos = serviceClinicItemService.queryServiceClinicItemListByinPutText(systemTypeEnums, 10000, "", HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode(), HospitalModelEnum.住院, HIPCurrentOrgUtils.getCurrentOrgInfo().getHospitalAreaCode());
        if (CollectionUtils.isNotEmpty(serviceClinicItemTos)) {
            spcobsApplyToList = serviceClinicItemTos.stream().filter(SpcobsApplyTo.class::isInstance).map(SpcobsApplyTo.class::cast).toList();
        }
        List<SpcobsApplyAsTo> spcobsApplyAsToList = HIPBeanUtil.copy(spcobsApplyToList, SpcobsApplyAsTo.class);
        CisOrderCommonQto cisOrderCommonQto = new CisOrderCommonQto();
        cisOrderCommonQto.setDocCode(HIPSecurityUtils.getLoginUserId());
        cisOrderCommonQto.setSystemType(SystemTypeEnum.SPCOBS);
        cisOrderCommonQto.setHospitalModel(HospitalModelEnum.住院);
        cisOrderCommonQto.setOrgCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
        cisOrderCommonQto.setHospitalAreaCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getHospitalAreaCode());
        List<CisOrderCommonTo> cisOrderCommons = cisOrderCommonService.getCisOrderCommons(cisOrderCommonQto);
        if (CollectionUtils.isNotEmpty(cisOrderCommons)) {
            List<SpcobsApplyAsTo> spcobsApplyAsToList1 = new ArrayList<>();
            for (CisOrderCommonTo cisOrderCommon : cisOrderCommons) {
                List<SpcobsApplyAsTo> list = spcobsApplyAsToList.stream().filter(a -> a.getServiceItemCode().equals(cisOrderCommon.getServiceItemCode())).toList();
                if (CollectionUtils.isNotEmpty(list) && list.size() == 1) {
                    list.get(0).setCollectionFlag(true);
                    list.get(0).setCollectionId(cisOrderCommon.getId());
                    spcobsApplyAsToList1.add(list.get(0));
                }
            }
            List<SpcobsApplyAsTo> list = spcobsApplyAsToList.stream().filter(a -> !cisOrderCommons.stream().map(CisOrderCommonTo::getServiceItemCode).toList().contains(a.getServiceItemCode())).toList();
            spcobsApplyAsToList1.addAll(list);
            return spcobsApplyAsToList1;
        }
        return spcobsApplyAsToList;
    }

    @Override
    public List<CisOrderCommonAsTo> commonSpcodsOrderSearch() {
        List<CisOrderCommonAsTo> cisOrderCommonAsToList = new ArrayList<>();
        CisOrderCommonQto cisOrderCommonQto = new CisOrderCommonQto();
        cisOrderCommonQto.setDocCode(HIPSecurityUtils.getLoginStaffId());
        cisOrderCommonQto.setSystemType(SystemTypeEnum.SPCOBS);
        cisOrderCommonQto.setHospitalModel(HospitalModelEnum.住院);
        cisOrderCommonQto.setOrgCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
        cisOrderCommonQto.setSaveFlag(false);
        cisOrderCommonQto.setHospitalAreaCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getHospitalAreaCode());
        List<CisOrderCommonTo> cisOrderCommons = cisOrderCommonService.getCisOrderCommons(cisOrderCommonQto);
        if (CollectionUtils.isNotEmpty(cisOrderCommons)) {
            //降序排列Comparator.reverseOrder()
            List<CisOrderCommonTo> cisOrderCommonToList = cisOrderCommons.stream().sorted(Comparator.comparing(CisOrderCommonTo::getIntegral, Comparator.reverseOrder())).toList();
            if (cisOrderCommonToList.size() > 100) {
                cisOrderCommonToList.subList(0, 99);
            }
            cisOrderCommonAsToList = HIPBeanUtil.copy(cisOrderCommonToList, CisOrderCommonAsTo.class).stream().map(cisOrderCommonAsTo -> {
                if (cisOrderCommonAsTo.getOrgCode() != null) {
                    cisOrderCommonAsTo.setDefaultExecOrgCode(cisOrderCommonAsTo.getOrgCode());
                    cisOrderCommonAsTo.setDefaultExecOrgName(workGroupService.getWorkGroup(cisOrderCommonAsTo.getOrgCode()).getName());
                }
                SpcobsApplyTo serviceClinicItemByCode = (SpcobsApplyTo) serviceClinicItemService.getServiceClinicItemByCode(cisOrderCommonAsTo.getServiceItemCode());
                if (serviceClinicItemByCode != null) {
                    if (StringUtils.isNotBlank(serviceClinicItemByCode.getExtCode())) {
                        cisOrderCommonAsTo.setExtCode(serviceClinicItemByCode.getExtCode());
                    }
                    if (StringUtils.isNotBlank(serviceClinicItemByCode.getDeviceType())) {
                        cisOrderCommonAsTo.setDeviceType(serviceClinicItemByCode.getDeviceType());
                    }
                }
                return cisOrderCommonAsTo;
            }).toList();
        }
        return cisOrderCommonAsToList;
    }

    @Override
    public List<EconServicePriceAsTo> searchDetails(String code) {
        List<ServiceClinicPriceTo> serviceClinicPrices = new ArrayList<>();
        ServiceClinicItemTo serviceClinicItemById = serviceClinicItemService.getServiceClinicItemByCode(code);
        if (serviceClinicItemById != null) {
            serviceClinicPrices = serviceClinicItemById.getServiceClinicPrices();
            if (CollectionUtils.isNotEmpty(serviceClinicPrices)) {
                for (ServiceClinicPriceTo serviceClinicPrice : serviceClinicPrices) {
                    if (serviceClinicPrice.getLimitType() != null) {
                        if (serviceClinicPrice.getLimitType().equals("DEP")) {
                            //科室过滤
                            if (CollectionUtils.isNotEmpty(serviceClinicPrice.getServiceClinicPriceOrgs())) {
                                List<ServiceClinicPriceOrgTo> list = serviceClinicPrice.getServiceClinicPriceOrgs().stream().filter(a -> StringUtils.isNotEmpty(a.getOrgCode()) && a.getOrgCode().equals(HIPCurrentOrgUtils.getCurrentOrgInfo().getDeptCode())).toList();
                                if (CollectionUtils.isNotEmpty(list)) {
                                    serviceClinicPrices.remove(serviceClinicPrice);
                                }
                            }
                        } else if (serviceClinicPrice.getLimitType().equals("OPD")) {
                            //门诊过滤
                            serviceClinicPrices.remove(serviceClinicPrice);
                        }
                    }
                }
            }
        }
        List<EconServicePriceAsTo> econServicePriceToAsList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(serviceClinicPrices)) {
            for (ServiceClinicPriceTo serviceClinicPrice : serviceClinicPrices) {
                if (StringUtils.isNotBlank(serviceClinicPrice.getPriceItemCode())) {
                    EconServicePriceTo econServicePriceById = econServicePriceService.getEconServicePriceById(serviceClinicPrice.getPriceItemCode());
                    if (econServicePriceById != null) {
                        EconServicePriceAsTo econServicePriceAsTo = HIPBeanUtil.copy(econServicePriceById, EconServicePriceAsTo.class);
                        try {
                            //医保限制
                            MiLimitHilistTo miLimitHilistTo = miLimitHilistService.getMiLimitHilistByHisCode(econServicePriceById.getCode());
                            if (miLimitHilistTo != null) {
                                econServicePriceAsTo.setMiId(miLimitHilistTo.getId());
                                econServicePriceAsTo.setLimitType(miLimitHilistTo.getLimitType());
                                econServicePriceAsTo.setLimitContent(miLimitHilistTo.getLimitContent());
                            }
                        } catch (Exception e) {

                        }
                        econServicePriceAsTo.setPriceItemCode(econServicePriceAsTo.getCode());
                        econServicePriceAsTo.setPriceItemName(econServicePriceAsTo.getName());
                        econServicePriceToAsList.add(econServicePriceAsTo);
                    }
                }
            }
        }
        return econServicePriceToAsList;
    }

    @Override
    public CisIpdApplySpcobsAsTo searchDetailsByApplyCode(String applyCode) {
        CisSpcobsApplyTo cisSpcobsApplyById = cisSpcobsApplyService.getCisSpcobsApplyById(applyCode);
        CisIpdApplySpcobsAsTo cisIpdApplySpcobsAsTo = new CisIpdApplySpcobsAsTo();
        //查询申请单明细
        if (cisSpcobsApplyById != null) {
            cisIpdApplySpcobsAsTo = HIPBeanUtil.copy(cisSpcobsApplyById, CisIpdApplySpcobsAsTo.class);
            if (cisSpcobsApplyById.getIsOlation()) {
                cisIpdApplySpcobsAsTo.setIsOlation("1");
            } else {
                cisIpdApplySpcobsAsTo.setIsOlation("0");
            }
            if (StringUtils.isNotBlank(cisSpcobsApplyById.getExecutorOrgCode())) {
                WorkGroupTo workGroupTo = workGroupService.getWorkGroup(cisSpcobsApplyById.getExecutorOrgCode());
                if (workGroupTo != null) {
                    cisIpdApplySpcobsAsTo.setExecutorOrgName(workGroupTo.getName());
                }
            }
            //医嘱主表
            CisIpdOrderTo cisIpdOrderById = cisIpdCpoeService.getCisIpdOrderById(cisSpcobsApplyById.getOrderID());
            cisIpdApplySpcobsAsTo.setSubmitStaffId(cisIpdOrderById.getSubmitStaffId());
            cisIpdApplySpcobsAsTo.setSubmitStaffName(cisIpdOrderById.getSubmitStaffName());
            cisIpdApplySpcobsAsTo.setCommitDate(cisIpdOrderById.getCommitDate());
            cisIpdApplySpcobsAsTo.setRepairFlag(cisIpdOrderById.getRepairFlag());
            if (StringUtils.isNotBlank(cisIpdOrderById.getRepairFlag()) && cisIpdOrderById.getRepairFlag().equals("1")) {
                cisIpdApplySpcobsAsTo.setRepairTime(cisIpdOrderById.getEffectiveLowDate());
            }
            CisApplyChargeQto cisApplyChargeQto = new CisApplyChargeQto();
            cisApplyChargeQto.setOrderId(cisIpdApplySpcobsAsTo.getOrderID());
            cisApplyChargeQto.setVisitCode(cisIpdApplySpcobsAsTo.getVisitCode());
            cisApplyChargeQto.setCisBaseApplyId(cisIpdApplySpcobsAsTo.getId());
            //检验申请单明细
            List<CisSpcobsApplyDetailTo> cisSpcobsApplyDetailByApplyId = cisSpcobsApplyService.getCisSpcobsApplyDetailByApplyId(cisSpcobsApplyById.getId());
            if (CollectionUtils.isNotEmpty(cisSpcobsApplyDetailByApplyId)) {
                cisIpdApplySpcobsAsTo.setCisSpcobsApplyDetails(cisSpcobsApplyDetailByApplyId);
            }
            //医嘱从属费用数据
            List<CisApplyChargeAsTo> cisApplyChargeAsToList = new ArrayList<>();
            List<CisApplyChargeTo> cisApplyChargeToList = cisBaseApplyService.getCisApplyCharges(cisIpdApplySpcobsAsTo.getId(), cisApplyChargeQto);
            for (CisApplyChargeTo cisApplyChargeTo : cisApplyChargeToList) {

                CisApplyChargeAsTo cisApplyChargeAsTo = HIPBeanUtil.copy(cisApplyChargeTo, CisApplyChargeAsTo.class);
                cisApplyChargeAsTo.setApplyDetailId(cisApplyChargeTo.getDetailId());
                try {
                    //医保限制
                    MiLimitHilistTo miLimitHilistTo = miLimitHilistService.getMiLimitHilistByHisCode(cisApplyChargeTo.getPriceItemCode());
                    if (miLimitHilistTo != null) {
                        cisApplyChargeAsTo.setMiId(miLimitHilistTo.getId());
                        cisApplyChargeAsTo.setLimitType(miLimitHilistTo.getLimitType());
                        cisApplyChargeAsTo.setLimitContent(miLimitHilistTo.getLimitContent());
                    }
                } catch (Exception e) {

                }
                List<CisSpcobsApplyDetailTo> cisSpcobsApplyDetailToList = cisSpcobsApplyDetailByApplyId.stream().filter(a -> a.getId().equals(cisApplyChargeTo.getDetailId())).toList();
                if (CollectionUtils.isNotEmpty(cisSpcobsApplyDetailToList) && cisSpcobsApplyDetailToList.size() == 1) {
                    CisSpcobsApplyDetailTo cisSpcobsApplyDetailTo = cisSpcobsApplyDetailToList.get(0);
                    //检验设备类型
                    cisApplyChargeAsTo.setDeviceType(cisSpcobsApplyDetailTo.getDeviceType());
                }
                cisApplyChargeAsToList.add(cisApplyChargeAsTo);
            }
            cisIpdApplySpcobsAsTo.setCisApplyCharges(cisApplyChargeAsToList);
        }
        return cisIpdApplySpcobsAsTo;
    }

    @Override
    public CisIpdApplySpcobsAsTo searchDetailsCompleted(String applyCode) {
        CisSpcobsApplyTo cisSpcobsApplyById = cisSpcobsApplyService.getCisSpcobsApplyById(applyCode);
        CisIpdApplySpcobsAsTo cisIpdApplySpcobsAsTo = new CisIpdApplySpcobsAsTo();
        //查询申请单明细
        if (cisSpcobsApplyById != null) {
            cisIpdApplySpcobsAsTo = HIPBeanUtil.copy(cisSpcobsApplyById, CisIpdApplySpcobsAsTo.class);
            if (cisSpcobsApplyById.getIsOlation()) {
                cisIpdApplySpcobsAsTo.setIsOlation("1");
            } else {
                cisIpdApplySpcobsAsTo.setIsOlation("0");
            }
            if (StringUtils.isNotBlank(cisSpcobsApplyById.getExecutorOrgCode())) {
                WorkGroupTo workGroupTo = workGroupService.getWorkGroup(cisSpcobsApplyById.getExecutorOrgCode());
                if (workGroupTo != null) {
                    cisIpdApplySpcobsAsTo.setExecutorOrgName(workGroupTo.getName());
                }
            }
            //医嘱主表
            CisIpdOrderTo cisIpdOrderById = cisIpdCpoeService.getCisIpdOrderById(cisSpcobsApplyById.getOrderID());
            cisIpdApplySpcobsAsTo.setRepairFlag(cisIpdOrderById.getRepairFlag());
            cisIpdApplySpcobsAsTo.setSubmitStaffId(cisIpdOrderById.getSubmitStaffId());
            cisIpdApplySpcobsAsTo.setSubmitStaffName(cisIpdOrderById.getSubmitStaffName());
            cisIpdApplySpcobsAsTo.setCommitDate(cisIpdOrderById.getCommitDate());
            cisIpdApplySpcobsAsTo.setProofStaffName(cisIpdOrderById.getProofStaffName());
            cisIpdApplySpcobsAsTo.setProofDate(cisIpdOrderById.getProofDate());
            cisIpdApplySpcobsAsTo.setProofStaff(cisIpdOrderById.getProofStaff());
            if (StringUtils.isNotBlank(cisIpdOrderById.getRepairFlag()) && cisIpdOrderById.getRepairFlag().equals("1")) {
                cisIpdApplySpcobsAsTo.setRepairTime(cisIpdOrderById.getEffectiveLowDate());
            }
            CisApplyChargeQto cisApplyChargeQto = new CisApplyChargeQto();
            cisApplyChargeQto.setOrderId(cisIpdApplySpcobsAsTo.getOrderID());
            cisApplyChargeQto.setVisitCode(cisIpdApplySpcobsAsTo.getVisitCode());
            cisApplyChargeQto.setCisBaseApplyId(cisIpdApplySpcobsAsTo.getId());
            //检验申请单明细
            List<CisSpcobsApplyDetailTo> cisSpcobsApplyDetailByApplyId = cisSpcobsApplyService.getCisSpcobsApplyDetailByApplyId(cisSpcobsApplyById.getId());
            if (CollectionUtils.isNotEmpty(cisSpcobsApplyDetailByApplyId)) {
                cisIpdApplySpcobsAsTo.setCisSpcobsApplyDetails(cisSpcobsApplyDetailByApplyId);
            }
            //医嘱从属费用数据
            List<CisApplyChargeAsTo> cisApplyChargeAsToList = new ArrayList<>();
            List<CisApplyChargeTo> cisApplyChargeToList = cisBaseApplyService.getCisApplyCharges(cisIpdApplySpcobsAsTo.getId(), cisApplyChargeQto);
            for (CisApplyChargeTo cisApplyChargeTo : cisApplyChargeToList) {

                CisApplyChargeAsTo cisApplyChargeAsTo = HIPBeanUtil.copy(cisApplyChargeTo, CisApplyChargeAsTo.class);
                cisApplyChargeAsTo.setApplyDetailId(cisApplyChargeTo.getDetailId());
                try {
                    //医保限制
                    MiLimitHilistTo miLimitHilistTo = miLimitHilistService.getMiLimitHilistByHisCode(cisApplyChargeTo.getPriceItemCode());
                    if (miLimitHilistTo != null) {
                        cisApplyChargeAsTo.setMiId(miLimitHilistTo.getId());
                        cisApplyChargeAsTo.setLimitType(miLimitHilistTo.getLimitType());
                        cisApplyChargeAsTo.setLimitContent(miLimitHilistTo.getLimitContent());
                    }
                } catch (Exception e) {

                }

                List<CisSpcobsApplyDetailTo> cisSpcobsApplyDetailToList = cisSpcobsApplyDetailByApplyId.stream().filter(a -> a.getId().equals(cisApplyChargeTo.getDetailId())).toList();
                if (CollectionUtils.isNotEmpty(cisSpcobsApplyDetailToList) && cisSpcobsApplyDetailToList.size() == 1) {
                    CisSpcobsApplyDetailTo cisSpcobsApplyDetailTo = cisSpcobsApplyDetailToList.get(0);
                    //检验设备类型
                    cisApplyChargeAsTo.setDeviceType(cisSpcobsApplyDetailTo.getDeviceType());
                }
                cisApplyChargeAsToList.add(cisApplyChargeAsTo);
            }
            cisIpdApplySpcobsAsTo.setCisApplyCharges(cisApplyChargeAsToList);
            //已计费收费信息
            List<EconIpdBillAllTo> econIpdBillAllToList = new ArrayList<>();
            EconIpdBillQto econIpdBillQto = new EconIpdBillQto();
            econIpdBillQto.setVisitCode(cisIpdOrderById.getVisitCode());
            econIpdBillQto.setOrderId(cisIpdOrderById.getId());
            List<EconIpdBillAllTo> econIpdBillAlls = econIpdBillService.getEconIpdBillAlls(econIpdBillQto);
            if (CollectionUtils.isNotEmpty(econIpdBillAlls)) {
                List<String> priceItemCodeList = econIpdBillAlls.stream().map(EconIpdBillAllTo::getCode).toList();
                if (CollectionUtils.isNotEmpty(priceItemCodeList)) {
                    for (String priceItemCode : priceItemCodeList) {
                        EconIpdBillAllTo econIpdBillAllTo = new EconIpdBillAllTo();
                        List<EconIpdBillAllTo> list = econIpdBillAlls.stream().filter(a -> a.getCode().equals(priceItemCode)).toList();
                        econIpdBillAllTo.setCode(list.get(0).getCode());
                        econIpdBillAllTo.setName(list.get(0).getName());
                        BigDecimal minNumSum = list.stream().map(EconIpdBillAllTo::getMinNum).reduce(BigDecimal.ZERO, BigDecimal::add);
                        econIpdBillAllTo.setMinNum(minNumSum);
                        econIpdBillAllTo.setOldPrice(list.get(0).getOldPrice());
                        BigDecimal costsSum = list.stream().map(EconIpdBillAllTo::getCosts).reduce(BigDecimal.ZERO, BigDecimal::add);
                        econIpdBillAllTo.setCosts(costsSum);
                        econIpdBillAllToList.add(econIpdBillAllTo);
                    }
                }
            }
            cisIpdApplySpcobsAsTo.setEconIpdBillAllToList(econIpdBillAllToList);
            //医嘱执行档
            List<CisOrderExecPlanTo> cisOrderExecPlanToList = cisOrderExecPlanService.findCisOrderExecPlanByOrderId(cisIpdOrderById.getId());
            if (CollectionUtils.isNotEmpty(cisOrderExecPlanToList)) {
                List<CisOrderExecPlanAsTo> cisOrderExecPlanAsToList = HIPBeanUtil.copy(cisOrderExecPlanToList, CisOrderExecPlanAsTo.class);
                cisOrderExecPlanAsToList.forEach(a -> {
                    if (a.getStatusCode().equals(CisStatusEnum.NEW)) {
                        a.setStatusCodeName("未执行");
                    } else {
                        a.setStatusCodeName(a.getStatusCode().getName());
                    }
                    a.setStatusCodeCode(a.getStatusCode().getCode());
                    if (StringUtils.isNotBlank(a.getExecStaff())) {
                        StaffTo staff = staffService.getStaff(a.getExecStaff());
                        if (staff != null) {
                            a.setExecStaffName(staff.getName());
                        }
                    }
                    if (StringUtils.isNotBlank(a.getExecOrgCode())) {
                        WorkGroupTo workGroup = workGroupService.getWorkGroup(a.getExecOrgCode());
                        if (workGroup != null) {
                            a.setExecOrgName(workGroup.getName());
                        }
                    }
                });
                cisIpdApplySpcobsAsTo.setCisOrderExecPlanAsToList(cisOrderExecPlanAsToList);
            }
        }
        return cisIpdApplySpcobsAsTo;
    }

    @Override
    public List<CisIpdDocOrderAsTo> getUnSubmittedOrder(String visitCode) {
        List<CisIpdDocOrderAsTo> cisIpdDocOrderAsToList = new ArrayList<>();
        CisIpdOrderQto cisIpdOrderQto = new CisIpdOrderQto();
        cisIpdOrderQto.setOrderClass(SystemTypeEnum.SPCOBS);
        cisIpdOrderQto.setVisitCode(visitCode);
        List<CisIpdOrderTo> cisIpdOrderNewToList = cisIpdCpoeService.getCisIpdOrderList(cisIpdOrderQto);
        if (CollectionUtils.isNotEmpty(cisIpdOrderNewToList)) {
            List<CisIpdOrderTo> cisIpdOrderToList = cisIpdOrderNewToList.stream().filter(a -> a.getStatusCode().equals(CisStatusEnum.BACK) || a.getStatusCode().equals(CisStatusEnum.NEW)).toList();
            if (CollectionUtils.isNotEmpty(cisIpdOrderToList)) {
                for (CisIpdOrderTo cisIpdOrderTo : cisIpdOrderToList) {
                    CisIpdDocOrderAsTo cisIpdDocOrderAsTo = HIPBeanUtil.copy(cisIpdOrderTo, CisIpdDocOrderAsTo.class);
                    if (StringUtils.isNotBlank(cisIpdDocOrderAsTo.getExecuteOrgCode())) {
                        WorkGroupTo workGroupTo = workGroupService.getWorkGroup(cisIpdDocOrderAsTo.getExecuteOrgCode());
                        if (workGroupTo != null) {
                            cisIpdDocOrderAsTo.setExecuteOrgName(workGroupTo.getName());
                        }
                    }
                    CisSpcobsApplyTo cisSpcobsApplyById = cisSpcobsApplyService.getCisSpcobsApplyById(cisIpdOrderTo.getApplyCode());
                    CisIpdApplySpcobsAsTo copy = HIPBeanUtil.copy(cisSpcobsApplyById, CisIpdApplySpcobsAsTo.class);
                    if (StringUtils.isNotBlank(cisSpcobsApplyById.getSpeciman())) {
                        DictElementTo speciman = dictElementService.getCustomDictElement("Speciman", cisSpcobsApplyById.getSpeciman());
                        if (speciman != null) {
                            copy.setSpecimanName(speciman.getElementName());
                        }
                    }
                    if (StringUtils.isNotBlank(cisSpcobsApplyById.getExecutorOrgCode())) {
                        WorkGroupTo workGroupTo = workGroupService.getWorkGroup(cisSpcobsApplyById.getExecutorOrgCode());
                        if (workGroupTo != null) {
                            copy.setExecutorOrgName(workGroupTo.getName());
                        }
                    }
                    cisIpdDocOrderAsTo.setCisIpdApplyAsTo(copy);
                    cisIpdDocOrderAsToList.add(cisIpdDocOrderAsTo);
                }
            }
        }
        return cisIpdDocOrderAsToList;
    }

    @Override
    public List<CisIpdDocOrderAsTo> getUnProofreadOrder(String visitCode) {
        List<CisIpdDocOrderAsTo> cisIpdDocOrderAsToList = new ArrayList<>();
        CisIpdOrderQto cisIpdOrderQto = new CisIpdOrderQto();
        cisIpdOrderQto.setOrderClass(SystemTypeEnum.SPCOBS);
        cisIpdOrderQto.setVisitCode(visitCode);
        List<CisIpdOrderTo> cisIpdOrderNewToList = cisIpdCpoeService.getCisIpdOrderList(cisIpdOrderQto);
        if (CollectionUtils.isNotEmpty(cisIpdOrderNewToList)) {
            List<CisIpdOrderTo> cisIpdOrderToList = cisIpdOrderNewToList.stream().filter(a -> a.getStatusCode().equals(CisStatusEnum.ACTIVE)).toList();
            if (CollectionUtils.isNotEmpty(cisIpdOrderToList)) {
                for (CisIpdOrderTo cisIpdOrderTo : cisIpdOrderToList) {
                    CisIpdDocOrderAsTo cisIpdDocOrderAsTo = HIPBeanUtil.copy(cisIpdOrderTo, CisIpdDocOrderAsTo.class);
                    CisSpcobsApplyTo cisSpcobsApplyById = cisSpcobsApplyService.getCisSpcobsApplyById(cisIpdOrderTo.getApplyCode());
                    CisIpdApplySpcobsAsTo copy = HIPBeanUtil.copy(cisSpcobsApplyById, CisIpdApplySpcobsAsTo.class);
                    if (StringUtils.isNotBlank(cisSpcobsApplyById.getSpeciman())) {
                        DictElementTo speciman = dictElementService.getCustomDictElement("Speciman", cisSpcobsApplyById.getSpeciman());
                        if (speciman != null) {
                            copy.setSpecimanName(speciman.getElementName());
                        }
                    }
                    if (StringUtils.isNotBlank(cisSpcobsApplyById.getExecutorOrgCode())) {
                        WorkGroupTo workGroupTo = workGroupService.getWorkGroup(cisSpcobsApplyById.getExecutorOrgCode());
                        if (workGroupTo != null) {
                            copy.setExecutorOrgName(workGroupTo.getName());
                        }
                    }
                    cisIpdDocOrderAsTo.setCisIpdApplyAsTo(copy);
                    cisIpdDocOrderAsToList.add(cisIpdDocOrderAsTo);
                }
            }
        }
        return cisIpdDocOrderAsToList;
    }

    @Override
    public List<CisIpdDocOrderAsTo> getProofreadOrder(String visitCode) {
        List<CisIpdDocOrderAsTo> cisIpdDocOrderAsToList = new ArrayList<>();
        CisIpdOrderQto cisIpdOrderQto = new CisIpdOrderQto();
        cisIpdOrderQto.setOrderClass(SystemTypeEnum.SPCOBS);
        cisIpdOrderQto.setVisitCode(visitCode);
        List<CisIpdOrderTo> cisIpdOrderNewToList = cisIpdCpoeService.getCisIpdOrderList(cisIpdOrderQto);
        if (CollectionUtils.isNotEmpty(cisIpdOrderNewToList)) {
            List<CisIpdOrderTo> cisIpdOrderToList = cisIpdOrderNewToList.stream().filter(a -> a.getStatusCode().equals(CisStatusEnum.PASS)).toList();
            if (CollectionUtils.isNotEmpty(cisIpdOrderToList)) {
                for (CisIpdOrderTo cisIpdOrderTo : cisIpdOrderToList) {
                    CisIpdDocOrderAsTo cisIpdDocOrderAsTo = HIPBeanUtil.copy(cisIpdOrderTo, CisIpdDocOrderAsTo.class);
                    if (StringUtils.isNotEmpty(cisIpdDocOrderAsTo.getExecuteOrgCode())) {
                        WorkGroupTo workGroupTo = workGroupService.getWorkGroup(cisIpdDocOrderAsTo.getExecuteOrgCode());
                        if (workGroupTo != null) {
                            cisIpdDocOrderAsTo.setExecuteOrgName(workGroupTo.getName());
                        }
                    }
                    CisSpcobsApplyTo cisSpcobsApplyById = cisSpcobsApplyService.getCisSpcobsApplyById(cisIpdOrderTo.getApplyCode());
                    CisIpdApplySpcobsAsTo copy = HIPBeanUtil.copy(cisSpcobsApplyById, CisIpdApplySpcobsAsTo.class);
                    if (StringUtils.isNotBlank(cisSpcobsApplyById.getSpeciman())) {
                        DictElementTo speciman = dictElementService.getCustomDictElement("Speciman", cisSpcobsApplyById.getSpeciman());
                        if (speciman != null) {
                            copy.setSpecimanName(speciman.getElementName());
                        }
                    }
                    if (StringUtils.isNotBlank(cisSpcobsApplyById.getExecutorOrgCode())) {
                        WorkGroupTo workGroupTo = workGroupService.getWorkGroup(cisSpcobsApplyById.getExecutorOrgCode());
                        if (workGroupTo != null) {
                            copy.setExecutorOrgName(workGroupTo.getName());
                        }
                    }
                    cisIpdDocOrderAsTo.setCisIpdApplyAsTo(copy);
                    cisIpdDocOrderAsToList.add(cisIpdDocOrderAsTo);
                }
            }
        }
        return cisIpdDocOrderAsToList;
    }

    @Override
    public List<CisIpdDocOrderAsTo> getObsoleteOrder(String visitCode) {
        List<CisIpdDocOrderAsTo> cisIpdDocOrderAsToList = new ArrayList<>();
        CisIpdOrderQto cisIpdOrderQto = new CisIpdOrderQto();
        cisIpdOrderQto.setOrderClass(SystemTypeEnum.SPCOBS);
        cisIpdOrderQto.setVisitCode(visitCode);
        List<CisIpdOrderTo> cisIpdOrderNewToList = cisIpdCpoeService.getCisIpdOrderList(cisIpdOrderQto);
        if (CollectionUtils.isNotEmpty(cisIpdOrderNewToList)) {
            List<CisIpdOrderTo> cisIpdOrderToList = cisIpdOrderNewToList.stream().filter(a -> a.getStatusCode().equals(CisStatusEnum.OBSOLETE)).toList();
            if (CollectionUtils.isNotEmpty(cisIpdOrderToList)) {
                for (CisIpdOrderTo cisIpdOrderTo : cisIpdOrderToList) {
                    CisIpdDocOrderAsTo cisIpdDocOrderAsTo = HIPBeanUtil.copy(cisIpdOrderTo, CisIpdDocOrderAsTo.class);
                    CisSpcobsApplyTo cisSpcobsApplyById = cisSpcobsApplyService.getCisSpcobsApplyById(cisIpdOrderTo.getApplyCode());
                    CisIpdApplySpcobsAsTo copy = HIPBeanUtil.copy(cisSpcobsApplyById, CisIpdApplySpcobsAsTo.class);
                    if (StringUtils.isNotBlank(cisSpcobsApplyById.getSpeciman())) {
                        DictElementTo speciman = dictElementService.getCustomDictElement("Speciman", cisSpcobsApplyById.getSpeciman());
                        if (speciman != null) {
                            copy.setSpecimanName(speciman.getElementName());
                        }
                    }
                    if (StringUtils.isNotBlank(cisSpcobsApplyById.getExecutorOrgCode())) {
                        WorkGroupTo workGroupTo = workGroupService.getWorkGroup(cisSpcobsApplyById.getExecutorOrgCode());
                        if (workGroupTo != null) {
                            copy.setExecutorOrgName(workGroupTo.getName());
                        }
                    }
                    cisIpdDocOrderAsTo.setCisIpdApplyAsTo(copy);
                    cisIpdDocOrderAsToList.add(cisIpdDocOrderAsTo);
                }
            }
        }
        return cisIpdDocOrderAsToList;
    }

    @Override
    public void newSpcobsSave(List<CisIpdDocOrderAsNto> cisIpdDocOrderAsNtoList) {
        //临嘱
        List<CisTemporaryOrderNto> cisTemporaryOrderNtoList = new ArrayList<>();
        //构建临嘱实体
        for (CisIpdDocOrderAsNto cisIpdDocOrderAsNto : cisIpdDocOrderAsNtoList) {
            CisTemporaryOrderNto cisTemporaryOrderNto = orderIssuedAsAssembler.buildCisIpdTempOrderNto(cisIpdDocOrderAsNto);
            cisTemporaryOrderNto.setOrderServiceCode(cisIpdDocOrderAsNto.getServiceItemCode());
            cisTemporaryOrderNtoList.add(cisTemporaryOrderNto);
        }
        cisIpdCpoeService.createIpdOrderBatch(Collections.unmodifiableList(cisTemporaryOrderNtoList));
    }

    @Override
    public void newSpcobsSubmit(List<CisIpdDocOrderAsNto> cisIpdDocOrderAsNtoList) {
        List<DocOrderCommitVerifyAsQto> docOrderCommitVerifyAsQtoList = new ArrayList<>();
        LoginInfo loginInfo = HIPSecurityUtils.getLoginInfo();
        //临嘱
        List<CisTemporaryOrderNto> cisTemporaryOrderNtoList = new ArrayList<>();
        //构建临嘱实体
        for (CisIpdDocOrderAsNto cisIpdDocOrderAsNto : cisIpdDocOrderAsNtoList) {
            CisTemporaryOrderNto cisTemporaryOrderNto = orderIssuedAsAssembler.buildCisIpdTempOrderNto(cisIpdDocOrderAsNto);
            cisTemporaryOrderNto.setOrderServiceCode(cisIpdDocOrderAsNto.getServiceItemCode());
            cisTemporaryOrderNtoList.add(cisTemporaryOrderNto);
            //医嘱校验
            DocOrderCommitVerifyAsQto docOrderCommitVerifyAsQto = new DocOrderCommitVerifyAsQto();
            docOrderCommitVerifyAsQto.setId(cisIpdDocOrderAsNto.getId());
            docOrderCommitVerifyAsQto.setOrderServiceCode(cisIpdDocOrderAsNto.getServiceItemCode());
            docOrderCommitVerifyAsQto.setOrderClassCode(SystemTypeEnum.SPCOBS.getCode());
            docOrderCommitVerifyAsQto.setCreateOrgCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
            docOrderCommitVerifyAsQto.setExecuteOrgCode(cisIpdDocOrderAsNto.getExecuteOrgCode());
            docOrderCommitVerifyAsQto.setStatusCodeCode("NEW");
            docOrderCommitVerifyAsQto.setSubmitStaffId(loginInfo.getStaffId());
            docOrderCommitVerifyAsQto.setVisitCode(cisIpdDocOrderAsNto.getVisitCode());
            docOrderCommitVerifyAsQtoList.add(docOrderCommitVerifyAsQto);
        }
        List<DoctCommitOrderMsgTo> doctCommitOrderMsgTos = submitOrderVerifyUtil.orderCommitVerify(docOrderCommitVerifyAsQtoList);
        if (doctCommitOrderMsgTos != null) {
            // 如果校验返回值msg有值，说明有医嘱校验不合规，不能签发
            List<DoctCommitOrderMsgTo> doctCommitOrderMsgToList = doctCommitOrderMsgTos.stream().filter(a -> CollectionUtils.isNotEmpty(a.getMsg())).toList();
            if (CollectionUtils.isNotEmpty(doctCommitOrderMsgToList)) {
                String errMsg = "";
                for (DoctCommitOrderMsgTo doctCommitOrderMsgTo : doctCommitOrderMsgToList) {
                    for (String msg : doctCommitOrderMsgTo.getMsg()) {
                        errMsg = errMsg + msg + ";";
                    }
                }
                throw new BusinessException(errMsg);
            }
        }
        cisIpdCpoeService.createAndSubmitIpdOrders(Collections.unmodifiableList(cisTemporaryOrderNtoList));
        //常用医嘱保存
        cisOrderCommonService.saveOrderCommons(getCisOrderCommonNto(cisIpdDocOrderAsNtoList));
    }

    @Override
    public void withdraw(List<String> ids) {
        for (String id : ids) {
            cisIpdCpoeService.withdrawIpdOrder(id);
        }
    }

    @Override
    public void disable(List<String> ids) {
        for (String id : ids) {
            cisIpdCpoeService.disableCisIpdOrder(id);
        }
    }

    @Override
    public void update(List<CisIpdDocOrderAsEto> cisIpdDocOrderAsEtoList) {
        //构建临嘱实体
        for (CisIpdDocOrderAsEto cisIpdDocOrderAsEto : cisIpdDocOrderAsEtoList) {
            CisIpdOrderTo cisIpdOrderById = cisIpdCpoeService.getCisIpdOrderById(cisIpdDocOrderAsEto.getId());
            if (StringUtils.isNotBlank(cisIpdOrderById.getApplyCode())) {
                cisIpdDocOrderAsEto.setApplyId(cisIpdOrderById.getApplyCode());
            }
            CisTemporaryOrderEto cisTemporaryOrderEto = orderIssuedAsAssembler.buildCisIpdTempOrderEto(cisIpdDocOrderAsEto);
            cisTemporaryOrderEto.setOrderServiceCode(cisIpdDocOrderAsEto.getServiceItemCode());
            cisTemporaryOrderEto.setOrderClass(SystemTypeEnum.SPCOBS);
            cisTemporaryOrderEto.setEffectiveLowDate(LocalDateTime.now());
            cisIpdCpoeService.updateIpdOrder(cisTemporaryOrderEto, cisIpdDocOrderAsEto.getId());
        }
    }

    @Override
    public void updateAndSubmit(List<CisIpdDocOrderAsEto> cisIpdDocOrderAsEtoList) {
        List<DocOrderCommitVerifyAsQto> docOrderCommitVerifyAsQtoList = new ArrayList<>();
        LoginInfo loginInfo = HIPSecurityUtils.getLoginInfo();
        //构建临嘱实体
        for (CisIpdDocOrderAsEto cisIpdDocOrderAsEto : cisIpdDocOrderAsEtoList) {
            //医嘱校验
            DocOrderCommitVerifyAsQto docOrderCommitVerifyAsQto = new DocOrderCommitVerifyAsQto();
            docOrderCommitVerifyAsQto.setId(cisIpdDocOrderAsEto.getId());
            docOrderCommitVerifyAsQto.setOrderServiceCode(cisIpdDocOrderAsEto.getServiceItemCode());
            docOrderCommitVerifyAsQto.setOrderClassCode(SystemTypeEnum.SPCOBS.getCode());
            docOrderCommitVerifyAsQto.setCreateOrgCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
            docOrderCommitVerifyAsQto.setExecuteOrgCode(cisIpdDocOrderAsEto.getExecuteOrgCode());
            docOrderCommitVerifyAsQto.setStatusCodeCode("NEW");
            docOrderCommitVerifyAsQto.setSubmitStaffId(loginInfo.getStaffId());
            docOrderCommitVerifyAsQto.setVisitCode(cisIpdDocOrderAsEto.getVisitCode());
            docOrderCommitVerifyAsQtoList.add(docOrderCommitVerifyAsQto);
        }
        List<DoctCommitOrderMsgTo> doctCommitOrderMsgTos = submitOrderVerifyUtil.orderCommitVerify(docOrderCommitVerifyAsQtoList);
        if (doctCommitOrderMsgTos != null) {
            // 如果校验返回值msg有值，说明有医嘱校验不合规，不能签发
            List<DoctCommitOrderMsgTo> doctCommitOrderMsgToList = doctCommitOrderMsgTos.stream().filter(a -> CollectionUtils.isNotEmpty(a.getMsg())).toList();
            if (CollectionUtils.isNotEmpty(doctCommitOrderMsgToList)) {
                String errMsg = "";
                for (DoctCommitOrderMsgTo doctCommitOrderMsgTo : doctCommitOrderMsgToList) {
                    for (String msg : doctCommitOrderMsgTo.getMsg()) {
                        errMsg = errMsg + msg + ";";
                    }
                }
                throw new BusinessException(errMsg);
            }
        }
        //构建临嘱实体
        for (CisIpdDocOrderAsEto cisIpdDocOrderAsEto : cisIpdDocOrderAsEtoList) {
            CisIpdOrderTo cisIpdOrderById = cisIpdCpoeService.getCisIpdOrderById(cisIpdDocOrderAsEto.getId());
            if (StringUtils.isNotBlank(cisIpdOrderById.getApplyCode())) {
                cisIpdDocOrderAsEto.setApplyId(cisIpdOrderById.getApplyCode());
            }
            CisTemporaryOrderEto cisTemporaryOrderEto = orderIssuedAsAssembler.buildCisIpdTempOrderEto(cisIpdDocOrderAsEto);
            cisTemporaryOrderEto.setOrderServiceCode(cisIpdDocOrderAsEto.getServiceItemCode());
            cisTemporaryOrderEto.setOrderClass(SystemTypeEnum.SPCOBS);
            cisTemporaryOrderEto.setEffectiveLowDate(LocalDateTime.now());
            cisIpdCpoeService.updateIpdOrder(cisTemporaryOrderEto, cisIpdDocOrderAsEto.getId());

            List<String> idList = new ArrayList<>();
            idList.add(cisIpdDocOrderAsEto.getId());
            cisIpdCpoeService.submitIpdOrderBatch(cisIpdDocOrderAsEto.getVisitCode(), idList);
        }
    }

    @Override
    public List<DoctCommitOrderMsgTo> voidOrdersVerify(List<String> ids) {
        List<DoctCommitOrderMsgTo> doctCommitOrderMsgToList = new ArrayList<>();
        for (String id : ids) {
            DoctCommitOrderMsgTo doctCommitOrderMsgTo = new DoctCommitOrderMsgTo();
            doctCommitOrderMsgTo.setId(id);
            List<String> errMsg = new ArrayList<>();
            List<String> list = new ArrayList<>();
            list.add(id);
            List<EconIpdBillOrderCostsTo> sumAmountByOrderIdList = econIpdBillService.getSumAmountByOrderIdList(list);
            if (CollectionUtils.isNotEmpty(sumAmountByOrderIdList)) {
                for (EconIpdBillOrderCostsTo sumAmountByOrderId : sumAmountByOrderIdList) {
                    if (sumAmountByOrderId != null) {
                        if (!sumAmountByOrderId.getCosts().equals(BigDecimal.ZERO)) {
                            //判断医嘱未被计费
                            String err = "金额已被计费,无法作废";
                            errMsg.add(err);
                            break;
                        }
                    }
                }
            }
            List<CisOrderExecPlanTo> cisOrderExecPlanToList = cisOrderExecPlanService.findCisOrderExecPlanByOrderId(id);
            if (CollectionUtils.isNotEmpty(cisOrderExecPlanToList)) {
                for (CisOrderExecPlanTo cisOrderExecPlanTo : cisOrderExecPlanToList) {
                    CisStatusEnum statusCode = cisOrderExecPlanTo.getStatusCode();
                    if (statusCode.equals(CisStatusEnum.NEW) || statusCode.equals(CisStatusEnum.EXCUTE)) {
                        //判断有无有效执行单:未执行(NEW)，已执行(EXCUTE) 即“有效”;不执行(REJECT)，取消执行(CANCELEXCUTE) 即“无效”
                        String err = "当前医嘱下包含有效的执行单,无法作废";
                        errMsg.add(err);
                        break;
                    }
                }
            }
            doctCommitOrderMsgTo.setMsg(errMsg);
            doctCommitOrderMsgToList.add(doctCommitOrderMsgTo);
        }
        return doctCommitOrderMsgToList;
    }

    @Override
    public void voidOrders(List<String> ids) {
        cisIpdCpoeService.obsoleteIpdOrder(ids, "检验医嘱作废");
    }

    @Override
    public void submitOrdersAgain(List<String> ids) {
        List<CisIpdDocOrderAsNto> cisIpdDocOrderAsNtoList = new ArrayList<>();
        CisIpdOrderCopyTo cisIpdOrderCopyTo = cisIpdCpoeService.copyIpdCpoeByOrderIds(ids);
        List<CisIpdOrderTo> cisIpdOrderToList = cisIpdOrderCopyTo.getCisIpdOrderTos();
        if (CollectionUtils.isNotEmpty(cisIpdOrderToList)) {
            for (CisIpdOrderTo cisIpdOrderTo : cisIpdOrderToList) {
                //申请单
                CisSpcobsApplyTo cisSpcobsApplyTo = cisSpcobsApplyService.getCisSpcobsApplyById(cisIpdOrderTo.getApplyCode());
                CisIpdDocOrderAsNto cisIpdDocOrderAsNto = new CisIpdDocOrderAsNto();
                cisIpdDocOrderAsNto.setId(HIPIDUtil.getNextIdString());
                cisIpdDocOrderAsNto.setApplyId(HIPIDUtil.getNextIdString());
                cisIpdDocOrderAsNto.setPatMiCode(cisIpdOrderTo.getPatMiCode());
                cisIpdDocOrderAsNto.setVisitCode(cisIpdOrderTo.getVisitCode());
                cisIpdDocOrderAsNto.setServiceItemCode(cisSpcobsApplyTo.getServiceItemCode());
                cisIpdDocOrderAsNto.setServiceItemName(cisSpcobsApplyTo.getServiceItemName());
                cisIpdDocOrderAsNto.setOrderContent(cisIpdOrderTo.getOrderContent());
                cisIpdDocOrderAsNto.setOrderClass("06");
                cisIpdDocOrderAsNto.setOrderType("2");
                cisIpdDocOrderAsNto.setExecuteOrgCode(cisIpdOrderTo.getExecuteOrgCode());
                cisIpdDocOrderAsNto.setExecuteOrgName(cisIpdOrderTo.getExecuteOrgName());
                cisIpdDocOrderAsNto.setReceiveOrgCode(cisIpdOrderTo.getReceiveOrgCode());
                cisIpdDocOrderAsNto.setDeptNurseCode(cisIpdOrderTo.getDeptNurseCode());
                cisIpdDocOrderAsNto.setDeptNurseName(cisIpdOrderTo.getDeptNurseName());
                cisIpdDocOrderAsNto.setSpcobsClass(cisSpcobsApplyTo.getSpcobsClass());
                cisIpdDocOrderAsNto.setSpeciman(cisSpcobsApplyTo.getSpeciman());
                cisIpdDocOrderAsNto.setFrequency("ST");
                cisIpdDocOrderAsNto.setFrequencyName("立即");
                cisIpdDocOrderAsNto.setRepairFlag("0");
                cisIpdDocOrderAsNto.setExtCode(cisIpdOrderTo.getExtTypeCode());
                List<CisIpdDocOrderDetailsAsNto> cisIpdDocOrderDetailsAsNtoList = new ArrayList<>();
                //申请单明细
                List<CisSpcobsApplyDetailTo> cisSpcobsApplyDetailToList = cisSpcobsApplyService.getCisSpcobsApplyDetailByApplyId(cisSpcobsApplyTo.getId());
                if (CollectionUtils.isNotEmpty(cisSpcobsApplyDetailToList)) {
                    for (int i = 0; i < cisSpcobsApplyDetailToList.size(); i++) {
                        CisApplyChargeQto cisApplyChargeQto = new CisApplyChargeQto();
                        cisApplyChargeQto.setOrderId(cisIpdOrderTo.getId());
                        cisApplyChargeQto.setVisitCode(cisIpdOrderTo.getVisitCode());
                        cisApplyChargeQto.setCisBaseApplyId(cisIpdOrderTo.getApplyCode());
                        List<CisApplyChargeTo> cisApplyChargeToList = cisBaseApplyService.getCisApplyCharges(cisIpdOrderTo.getApplyCode(), cisApplyChargeQto);
                        for (int m = 0; m < cisApplyChargeToList.size(); m++) {
                            CisIpdDocOrderDetailsAsNto cisIpdDocOrderDetailsAsNto = new CisIpdDocOrderDetailsAsNto();
                            cisIpdDocOrderDetailsAsNto.setChargeId(HIPIDUtil.getNextIdString());
                            cisIpdDocOrderDetailsAsNto.setPriceItemName(cisApplyChargeToList.get(m).getPriceItemName());
                            cisIpdDocOrderDetailsAsNto.setPriceItemCode(cisApplyChargeToList.get(m).getPriceItemCode());
                            cisIpdDocOrderDetailsAsNto.setPrice(cisApplyChargeToList.get(m).getPrice());
                            cisIpdDocOrderDetailsAsNto.setUnit(cisApplyChargeToList.get(m).getUnit());
                            cisIpdDocOrderDetailsAsNto.setUnitValue(cisApplyChargeToList.get(m).getUnitName());
                            cisIpdDocOrderDetailsAsNto.setNum(1.0);
                            cisIpdDocOrderDetailsAsNto.setLimitConformFlag(cisApplyChargeToList.get(i).getLimitConformFlag());
                            cisIpdDocOrderDetailsAsNto.setId(HIPIDUtil.getNextIdString());
                            cisIpdDocOrderDetailsAsNto.setApplyDetailId(HIPIDUtil.getNextIdString());
                            cisIpdDocOrderDetailsAsNto.setTestTubeId(cisSpcobsApplyDetailToList.get(i).getTestTubeId());
                            cisIpdDocOrderDetailsAsNto.setMethod(cisSpcobsApplyDetailToList.get(i).getMethod());
                            cisIpdDocOrderDetailsAsNto.setDeviceType(cisSpcobsApplyDetailToList.get(i).getDeviceType());
                            cisIpdDocOrderDetailsAsNtoList.add(cisIpdDocOrderDetailsAsNto);
                        }
                        cisIpdDocOrderAsNto.setCisIpdDocOrderDetailsAsNtoList(cisIpdDocOrderDetailsAsNtoList);
                        cisIpdDocOrderAsNtoList.add(cisIpdDocOrderAsNto);
                    }
                }
            }
        }
        List<DocOrderCommitVerifyAsQto> docOrderCommitVerifyAsQtoList = new ArrayList<>();
        LoginInfo loginInfo = HIPSecurityUtils.getLoginInfo();
        //临嘱
        List<CisTemporaryOrderNto> cisTemporaryOrderNtoList = new ArrayList<>();
        //构建临嘱实体
        for (CisIpdDocOrderAsNto cisIpdDocOrderAsNto : cisIpdDocOrderAsNtoList) {
            CisTemporaryOrderNto cisTemporaryOrderNto = orderIssuedAsAssembler.buildCisIpdTempOrderNto(cisIpdDocOrderAsNto);
            cisTemporaryOrderNto.setOrderServiceCode(cisIpdDocOrderAsNto.getServiceItemCode());
            cisTemporaryOrderNtoList.add(cisTemporaryOrderNto);
            //医嘱校验
            DocOrderCommitVerifyAsQto docOrderCommitVerifyAsQto = new DocOrderCommitVerifyAsQto();
            docOrderCommitVerifyAsQto.setId(cisIpdDocOrderAsNto.getId());
            docOrderCommitVerifyAsQto.setOrderServiceCode(cisIpdDocOrderAsNto.getServiceItemCode());
            docOrderCommitVerifyAsQto.setOrderClassCode(SystemTypeEnum.SPCOBS.getCode());
            docOrderCommitVerifyAsQto.setCreateOrgCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
            docOrderCommitVerifyAsQto.setExecuteOrgCode(cisIpdDocOrderAsNto.getExecuteOrgCode());
            docOrderCommitVerifyAsQto.setStatusCodeCode("NEW");
            docOrderCommitVerifyAsQto.setSubmitStaffId(loginInfo.getStaffId());
            docOrderCommitVerifyAsQto.setVisitCode(cisIpdDocOrderAsNto.getVisitCode());
            docOrderCommitVerifyAsQtoList.add(docOrderCommitVerifyAsQto);
        }
        List<DoctCommitOrderMsgTo> doctCommitOrderMsgTos = submitOrderVerifyUtil.orderCommitVerify(docOrderCommitVerifyAsQtoList);
        if (doctCommitOrderMsgTos != null) {
            // 如果校验返回值msg有值，说明有医嘱校验不合规，不能签发
            List<DoctCommitOrderMsgTo> doctCommitOrderMsgToList = doctCommitOrderMsgTos.stream().filter(a -> CollectionUtils.isNotEmpty(a.getMsg())).toList();
            if (CollectionUtils.isNotEmpty(doctCommitOrderMsgToList)) {
                String errMsg = "";
                for (DoctCommitOrderMsgTo doctCommitOrderMsgTo : doctCommitOrderMsgToList) {
                    for (String msg : doctCommitOrderMsgTo.getMsg()) {
                        errMsg = errMsg + msg + ";";
                    }
                }
                throw new BusinessException(errMsg);
            }
        }
        //签发
        cisIpdCpoeService.createAndSubmitIpdOrders(Collections.unmodifiableList(cisTemporaryOrderNtoList));
        //常用医嘱保存
        cisOrderCommonService.saveOrderCommons(getCisOrderCommonNto(cisIpdDocOrderAsNtoList));
    }

    @Override
    public void orderCommit(List<String> ids) {
        //医嘱校验
        List<DocOrderCommitVerifyAsQto> docOrderCommitVerifyAsQtoList = new ArrayList<>();

        List<CisIpdDocOrderAsNto> cisIpdDocOrderAsNtoList = new ArrayList<>();
        CisIpdOrderCopyTo cisIpdOrderCopyTo = cisIpdCpoeService.copyIpdCpoeByOrderIds(ids);
        List<CisIpdOrderTo> cisIpdOrderToList = cisIpdOrderCopyTo.getCisIpdOrderTos();
        if (CollectionUtils.isNotEmpty(cisIpdOrderToList)) {
            for (CisIpdOrderTo cisIpdOrderTo : cisIpdOrderToList) {
                //申请单
                CisSpcobsApplyTo cisSpcobsApplyTo = cisSpcobsApplyService.getCisSpcobsApplyById(cisIpdOrderTo.getApplyCode());
                CisIpdDocOrderAsNto cisIpdDocOrderAsNto = new CisIpdDocOrderAsNto();
                cisIpdDocOrderAsNto.setId(HIPIDUtil.getNextIdString());
                cisIpdDocOrderAsNto.setApplyId(HIPIDUtil.getNextIdString());
                cisIpdDocOrderAsNto.setPatMiCode(cisIpdOrderTo.getPatMiCode());
                cisIpdDocOrderAsNto.setVisitCode(cisIpdOrderTo.getVisitCode());
                cisIpdDocOrderAsNto.setServiceItemCode(cisSpcobsApplyTo.getServiceItemCode());
                cisIpdDocOrderAsNto.setServiceItemName(cisSpcobsApplyTo.getServiceItemName());
                cisIpdDocOrderAsNto.setOrderContent(cisIpdOrderTo.getOrderContent());
                cisIpdDocOrderAsNto.setOrderClass("06");
                cisIpdDocOrderAsNto.setOrderType("2");
                cisIpdDocOrderAsNto.setExecuteOrgCode(cisIpdOrderTo.getExecuteOrgCode());
                cisIpdDocOrderAsNto.setExecuteOrgName(cisIpdOrderTo.getExecuteOrgName());
                cisIpdDocOrderAsNto.setReceiveOrgCode(cisIpdOrderTo.getReceiveOrgCode());
                cisIpdDocOrderAsNto.setDeptNurseCode(cisIpdOrderTo.getDeptNurseCode());
                cisIpdDocOrderAsNto.setDeptNurseName(cisIpdOrderTo.getDeptNurseName());
                cisIpdDocOrderAsNto.setSpcobsClass(cisSpcobsApplyTo.getSpcobsClass());
                cisIpdDocOrderAsNto.setSpeciman(cisSpcobsApplyTo.getSpeciman());
                cisIpdDocOrderAsNto.setFrequency("ST");
                cisIpdDocOrderAsNto.setFrequencyName("立即");
                cisIpdDocOrderAsNto.setRepairFlag("0");
                cisIpdDocOrderAsNto.setExtCode(cisIpdOrderTo.getExtTypeCode());
                List<CisIpdDocOrderDetailsAsNto> cisIpdDocOrderDetailsAsNtoList = new ArrayList<>();
                //申请单明细
                List<CisSpcobsApplyDetailTo> cisSpcobsApplyDetailToList = cisSpcobsApplyService.getCisSpcobsApplyDetailByApplyId(cisSpcobsApplyTo.getId());
                if (CollectionUtils.isNotEmpty(cisSpcobsApplyDetailToList)) {
                    for (int i = 0; i < cisSpcobsApplyDetailToList.size(); i++) {
                        CisApplyChargeQto cisApplyChargeQto = new CisApplyChargeQto();
                        cisApplyChargeQto.setOrderId(cisIpdOrderTo.getId());
                        cisApplyChargeQto.setVisitCode(cisIpdOrderTo.getVisitCode());
                        cisApplyChargeQto.setCisBaseApplyId(cisIpdOrderTo.getApplyCode());
                        List<CisApplyChargeTo> cisApplyChargeToList = cisBaseApplyService.getCisApplyCharges(cisIpdOrderTo.getApplyCode(), cisApplyChargeQto);
                        for (int m = 0; m < cisApplyChargeToList.size(); m++) {
                            CisIpdDocOrderDetailsAsNto cisIpdDocOrderDetailsAsNto = new CisIpdDocOrderDetailsAsNto();
                            cisIpdDocOrderDetailsAsNto.setChargeId(HIPIDUtil.getNextIdString());
                            cisIpdDocOrderDetailsAsNto.setPriceItemName(cisApplyChargeToList.get(m).getPriceItemName());
                            cisIpdDocOrderDetailsAsNto.setPriceItemCode(cisApplyChargeToList.get(m).getPriceItemCode());
                            cisIpdDocOrderDetailsAsNto.setPrice(cisApplyChargeToList.get(m).getPrice());
                            cisIpdDocOrderDetailsAsNto.setUnit(cisApplyChargeToList.get(m).getUnit());
                            cisIpdDocOrderDetailsAsNto.setUnitValue(cisApplyChargeToList.get(m).getUnitName());
                            cisIpdDocOrderDetailsAsNto.setNum(1.0);
                            cisIpdDocOrderDetailsAsNto.setLimitConformFlag(cisApplyChargeToList.get(i).getLimitConformFlag());
                            cisIpdDocOrderDetailsAsNto.setId(HIPIDUtil.getNextIdString());
                            cisIpdDocOrderDetailsAsNto.setApplyDetailId(HIPIDUtil.getNextIdString());
                            cisIpdDocOrderDetailsAsNto.setTestTubeId(cisSpcobsApplyDetailToList.get(i).getTestTubeId());
                            cisIpdDocOrderDetailsAsNto.setMethod(cisSpcobsApplyDetailToList.get(i).getMethod());
                            cisIpdDocOrderDetailsAsNto.setDeviceType(cisSpcobsApplyDetailToList.get(i).getDeviceType());
                            cisIpdDocOrderDetailsAsNtoList.add(cisIpdDocOrderDetailsAsNto);
                        }
                        cisIpdDocOrderAsNto.setCisIpdDocOrderDetailsAsNtoList(cisIpdDocOrderDetailsAsNtoList);
                        cisIpdDocOrderAsNtoList.add(cisIpdDocOrderAsNto);
                    }
                }
            }
        }
        //构建校验实体
        for (CisIpdDocOrderAsNto cisIpdDocOrderAsNto : cisIpdDocOrderAsNtoList) {
            //医嘱校验
            DocOrderCommitVerifyAsQto docOrderCommitVerifyAsQto = new DocOrderCommitVerifyAsQto();
            docOrderCommitVerifyAsQto.setId(cisIpdDocOrderAsNto.getId());
            docOrderCommitVerifyAsQto.setOrderServiceCode(cisIpdDocOrderAsNto.getServiceItemCode());
            docOrderCommitVerifyAsQto.setOrderClassCode(SystemTypeEnum.SPCOBS.getCode());
            docOrderCommitVerifyAsQto.setCreateOrgCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
            docOrderCommitVerifyAsQto.setExecuteOrgCode(cisIpdDocOrderAsNto.getExecuteOrgCode());
            docOrderCommitVerifyAsQto.setStatusCodeCode("NEW");
            docOrderCommitVerifyAsQto.setSubmitStaffId(HIPSecurityUtils.getLoginStaffId());
            docOrderCommitVerifyAsQto.setVisitCode(cisIpdDocOrderAsNto.getVisitCode());
            docOrderCommitVerifyAsQtoList.add(docOrderCommitVerifyAsQto);
        }
        List<DoctCommitOrderMsgTo> doctCommitOrderMsgTos = submitOrderVerifyUtil.orderCommitVerify(docOrderCommitVerifyAsQtoList);
        if (doctCommitOrderMsgTos != null) {
            // 如果校验返回值msg有值，说明有医嘱校验不合规，不能签发
            List<DoctCommitOrderMsgTo> doctCommitOrderMsgToList = doctCommitOrderMsgTos.stream().filter(a -> CollectionUtils.isNotEmpty(a.getMsg())).toList();
            if (CollectionUtils.isNotEmpty(doctCommitOrderMsgToList)) {
                String errMsg = "";
                for (DoctCommitOrderMsgTo doctCommitOrderMsgTo : doctCommitOrderMsgToList) {
                    for (String msg : doctCommitOrderMsgTo.getMsg()) {
                        errMsg = errMsg + msg + ";";
                    }
                }
                throw new BusinessException(errMsg);
            }
        }
        //签发
        cisIpdCpoeService.submitIpdOrderBatch(cisIpdOrderToList.get(0).getVisitCode(), ids);
    }

    //常用医嘱实体编辑
    private static List<CisOrderCommonNto> getCisOrderCommonNto(List<CisIpdDocOrderAsNto> cisIpdDocOrderAsNtoList) {
        List<CisOrderCommonNto> cisOrderCommonNtos = new ArrayList<>();
        for (CisIpdDocOrderAsNto cisIpdDocOrderAsNto : cisIpdDocOrderAsNtoList) {
            CisOrderCommonNto cisOrderCommonNto = new CisOrderCommonNto();
            cisOrderCommonNto.setServiceItemCode(cisIpdDocOrderAsNto.getServiceItemCode());
            cisOrderCommonNto.setServiceItemName(cisIpdDocOrderAsNto.getServiceItemName());
            cisOrderCommonNto.setSystemType(SystemTypeEnum.SPCOBS);
            cisOrderCommonNto.setDocCode(HIPSecurityUtils.getLoginStaffId());
            cisOrderCommonNto.setId(cisIpdDocOrderAsNto.getId());
            cisOrderCommonNto.setOrgCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
            cisOrderCommonNto.setSaveFlag(false);
            cisOrderCommonNto.setSpeciman(cisIpdDocOrderAsNto.getSpeciman());
            cisOrderCommonNto.setExecutiveOrg(cisIpdDocOrderAsNto.getExecuteOrgCode());
            cisOrderCommonNto.setIntegral(Long.valueOf("1"));
            cisOrderCommonNtos.add(cisOrderCommonNto);
        }
        return cisOrderCommonNtos;
    }

}
