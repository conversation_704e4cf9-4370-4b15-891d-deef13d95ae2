package com.bjgoodwill.hip.as.cis.doc.ipd.to.temp;

import com.bjgoodwill.hip.as.cis.doc.ipd.to.operation.ApplyDiagnosisAsNto;
import com.bjgoodwill.hip.ds.cis.cds.enmus.TempRangeEnum;
import com.bjgoodwill.hip.ds.cis.cds.orderTemp.to.CisOrderTempDetailNto;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> liangyuqing
 * @description :
 * @createDate : 2025/1/10 9:25
 */
@Schema(description = "组套明细新增应用Nto")
public class CisOrderTempDetailAsToOrderNto extends CisOrderTempDetailNto {

    @Schema(description = "医嘱类型编码")
    private String orderClassCode;

    @Schema(description = "长临标识编码")
    private String orderTypeCode;

    @Schema(description = "医嘱开立用 - 患者主索引")
    private String patMiCode;

    @Schema(description = "医嘱开立用 - 就诊流水号")
    private String visitCode;

    @Schema(description = "医嘱开立用 - 所在病区编码")
    private String deptNurseCode;

    @Schema(description = "医嘱开立用 - 所在病区名称")
    private String deptNurseName;

    @Schema(description = "医嘱开立用 - 医嘱内容")
    private String orderContent;

    @Schema(description = "医嘱开立用 - 执行科室名称")
    private String executeOrgName;

    @Schema(description = "医嘱开立用 - 补录标识")
    private String repairFlag;

    @Schema(description = "医嘱开立用 - 补录时间")
    private LocalDateTime repairTime;

    @Schema(description = "医嘱开立用 - 组套类型")
    private TempRangeEnum tempRange;

    @Schema(description = "医嘱开立用 - 检查注意事项")
    private String precautions;

    @Schema(description = "临床诊断")
    private String clinicalDiagnosis;

    @Schema(description = "临床诊断列表")
    private List<ApplyDiagnosisAsNto> applyDiagnosisAsNtos;

    @Schema(description = "医嘱开立用 - 体格及其他检查")
    private String physiqueAndExam;

    @Schema(description = "医嘱开立用 - 相关辅检")
    private String auxiliaryInspection;

    @Schema(description = "医嘱开立用 - 检查目的")
    private String checkPurpose;

    @Schema(description = "医嘱开立用 - 既往病理检查结果")
    private String previousPathologicalExamin;

    @Schema(description = "医嘱开立用 - 是否过敏史")
    private Boolean allergicHistoryFlag;

    @Schema(description = "医嘱开立用 - 是否职业病史")
    private Boolean occupationalDiseasesFlag;

    @Schema(description = "医嘱开立用 - 临床病史")
    private String clinicalHistory;

    @Schema(description = "医嘱开立用 - 是否传染病史")
    private Boolean contagiousDiseaseHistoryFlag;

    @Schema(description = "医嘱开立用 - 备注")
    private String reMark;

    @Schema(description = "扩展编码")
    private String extCode;

    public String getOrderClassCode() {
        return orderClassCode;
    }

    public void setOrderClassCode(String orderClassCode) {
        this.orderClassCode = orderClassCode;
    }

    public String getPatMiCode() {
        return patMiCode;
    }

    public void setPatMiCode(String patMiCode) {
        this.patMiCode = patMiCode;
    }

    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    public String getDeptNurseCode() {
        return deptNurseCode;
    }

    public void setDeptNurseCode(String deptNurseCode) {
        this.deptNurseCode = deptNurseCode;
    }

    public String getDeptNurseName() {
        return deptNurseName;
    }

    public void setDeptNurseName(String deptNurseName) {
        this.deptNurseName = deptNurseName;
    }

    public String getOrderContent() {
        return orderContent;
    }

    public void setOrderContent(String orderContent) {
        this.orderContent = orderContent;
    }

    public String getExecuteOrgName() {
        return executeOrgName;
    }

    public void setExecuteOrgName(String executeOrgName) {
        this.executeOrgName = executeOrgName;
    }

    public String getRepairFlag() {
        return repairFlag;
    }

    public void setRepairFlag(String repairFlag) {
        this.repairFlag = repairFlag;
    }

    public LocalDateTime getRepairTime() {
        return repairTime;
    }

    public void setRepairTime(LocalDateTime repairTime) {
        this.repairTime = repairTime;
    }

    public TempRangeEnum getTempRange() {
        return tempRange;
    }

    public void setTempRange(TempRangeEnum tempRange) {
        this.tempRange = tempRange;
    }

    public String getPrecautions() {
        return precautions;
    }

    public void setPrecautions(String precautions) {
        this.precautions = precautions;
    }

    public String getClinicalDiagnosis() {
        return clinicalDiagnosis;
    }

    public void setClinicalDiagnosis(String clinicalDiagnosis) {
        this.clinicalDiagnosis = clinicalDiagnosis;
    }

    public List<ApplyDiagnosisAsNto> getApplyDiagnosisAsNtos() {
        return applyDiagnosisAsNtos;
    }

    public void setApplyDiagnosisAsNtos(List<ApplyDiagnosisAsNto> applyDiagnosisAsNtos) {
        this.applyDiagnosisAsNtos = applyDiagnosisAsNtos;
    }

    public String getPhysiqueAndExam() {
        return physiqueAndExam;
    }

    public void setPhysiqueAndExam(String physiqueAndExam) {
        this.physiqueAndExam = physiqueAndExam;
    }

    public String getAuxiliaryInspection() {
        return auxiliaryInspection;
    }

    public void setAuxiliaryInspection(String auxiliaryInspection) {
        this.auxiliaryInspection = auxiliaryInspection;
    }

    public String getCheckPurpose() {
        return checkPurpose;
    }

    public void setCheckPurpose(String checkPurpose) {
        this.checkPurpose = checkPurpose;
    }

    public String getPreviousPathologicalExamin() {
        return previousPathologicalExamin;
    }

    public void setPreviousPathologicalExamin(String previousPathologicalExamin) {
        this.previousPathologicalExamin = previousPathologicalExamin;
    }

    public Boolean getAllergicHistoryFlag() {
        return allergicHistoryFlag;
    }

    public void setAllergicHistoryFlag(Boolean allergicHistoryFlag) {
        this.allergicHistoryFlag = allergicHistoryFlag;
    }

    public Boolean getOccupationalDiseasesFlag() {
        return occupationalDiseasesFlag;
    }

    public void setOccupationalDiseasesFlag(Boolean occupationalDiseasesFlag) {
        this.occupationalDiseasesFlag = occupationalDiseasesFlag;
    }

    public String getClinicalHistory() {
        return clinicalHistory;
    }

    public void setClinicalHistory(String clinicalHistory) {
        this.clinicalHistory = clinicalHistory;
    }

    public Boolean getContagiousDiseaseHistoryFlag() {
        return contagiousDiseaseHistoryFlag;
    }

    public void setContagiousDiseaseHistoryFlag(Boolean contagiousDiseaseHistoryFlag) {
        this.contagiousDiseaseHistoryFlag = contagiousDiseaseHistoryFlag;
    }

    public String getReMark() {
        return reMark;
    }

    public void setReMark(String reMark) {
        this.reMark = reMark;
    }

    public String getOrderTypeCode() {
        return orderTypeCode;
    }

    public void setOrderTypeCode(String orderTypeCode) {
        this.orderTypeCode = orderTypeCode;
    }

    public String getExtCode() {
        return extCode;
    }

    public void setExtCode(String extCode) {
        this.extCode = extCode;
    }
}
