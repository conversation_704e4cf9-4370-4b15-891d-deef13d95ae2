package com.bjgoodwill.hip.as.cis.doc.ipd.to.diagnosis;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>an
 * @Date: 2025/2/12 17:40
 * @PROJECT: hip-ac
 */
@Schema(description = "住院医生站-诊断-删除")
public class IpdDiagnosisAsDto implements Serializable {

    @Schema(description = "标识")
    private String id;

    @Schema(description = "流水号")
    private String visitCode;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }
}
