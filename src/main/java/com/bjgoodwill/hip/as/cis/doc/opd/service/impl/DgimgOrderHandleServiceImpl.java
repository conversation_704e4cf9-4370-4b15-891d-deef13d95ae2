package com.bjgoodwill.hip.as.cis.doc.opd.service.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.as.cis.doc.opd.enums.CisDocOpdBusinessErrorEnum;
import com.bjgoodwill.hip.as.cis.doc.opd.service.DgimgOrderHandleService;
import com.bjgoodwill.hip.as.cis.doc.opd.to.dgimg.*;
import com.bjgoodwill.hip.as.cis.doc.opd.to.doctor.CisApplyChargeAsTo;
import com.bjgoodwill.hip.as.cis.doc.opd.to.doctor.OpdOrderBaseApplyAsTo;
import com.bjgoodwill.hip.as.cis.doc.opd.util.OpdOrderAsUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.CisStatusEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.HospitalModelEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.VisitTypeEnum;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.common.util.HIPBeanUtil;
import com.bjgoodwill.hip.common.util.HIPIDUtil;
import com.bjgoodwill.hip.ds.base.mi.milist.service.MiLimitHilistService;
import com.bjgoodwill.hip.ds.base.mi.milist.to.MiLimitHilistTo;
import com.bjgoodwill.hip.ds.cis.apply.apply.service.CisBaseApplyService;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyQto;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyTo;
import com.bjgoodwill.hip.ds.cis.apply.charge.enmus.CisChargeTypeEnum;
import com.bjgoodwill.hip.ds.cis.apply.charge.to.CisApplyChargeEto;
import com.bjgoodwill.hip.ds.cis.apply.charge.to.CisApplyChargeNto;
import com.bjgoodwill.hip.ds.cis.apply.charge.to.CisApplyChargeTo;
import com.bjgoodwill.hip.ds.cis.apply.detail.service.ApplyWithDetialService;
import com.bjgoodwill.hip.ds.cis.apply.dgimg.service.CisDgimgApplyService;
import com.bjgoodwill.hip.ds.cis.apply.dgimg.to.*;
import com.bjgoodwill.hip.ds.cis.apply.diag.to.ApplyDiagnosisNto;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.service.CisOrderExecPlanService;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.to.CisOrderExecPlanTo;
import com.bjgoodwill.hip.ds.cis.apply.medicalhistory.service.CisMedicalHistoryService;
import com.bjgoodwill.hip.ds.cis.apply.medicalhistory.to.CisMedicalHistoryNto;
import com.bjgoodwill.hip.ds.cis.apply.medicalhistory.to.CisMedicalHistoryTo;
import com.bjgoodwill.hip.ds.cis.cds.order.service.CisOrderCommonService;
import com.bjgoodwill.hip.ds.cis.cds.order.to.CisOrderCommonNto;
import com.bjgoodwill.hip.ds.cis.cds.order.to.CisOrderCommonQto;
import com.bjgoodwill.hip.ds.cis.cds.order.to.CisOrderCommonTo;
import com.bjgoodwill.hip.ds.cis.medicineitem.price.to.ServiceClinicPriceTo;
import com.bjgoodwill.hip.ds.cis.medicineitem.serviceItem.service.ServiceClinicItemService;
import com.bjgoodwill.hip.ds.cis.medicineitem.serviceItem.to.DgimgApplyTo;
import com.bjgoodwill.hip.ds.cis.medicineitem.serviceItem.to.ServiceClinicItemTo;
import com.bjgoodwill.hip.ds.cis.opdcpoe.order.service.CisOpdOrderService;
import com.bjgoodwill.hip.ds.cis.opdcpoe.order.to.*;
import com.bjgoodwill.hip.ds.term.api.service.DictElementService;
import com.bjgoodwill.hip.ds.term.api.to.DictElementTo;
import com.bjgoodwill.hip.security.util.HIPSecurityUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.seata.spring.annotation.GlobalTransactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> liangyuqing
 * @description :
 * @createDate : 2025/5/19 11:44
 */
@Service
public class DgimgOrderHandleServiceImpl implements DgimgOrderHandleService {

    @Autowired
    private CisOrderCommonService cisOrderCommonService;

    @Autowired
    @Qualifier("com.bjgoodwill.hip.ds.cis.medicineitem.serviceItem.service.ServiceClinicItemTangibleService")
    private ServiceClinicItemService serviceClinicItemService;

    @Autowired
    @Qualifier("com.bjgoodwill.hip.ds.cis.apply.apply.service.CisBaseApplyServiceTangibleFeign")
    private CisBaseApplyService cisBaseApplyService;

    @Autowired
    private DictElementService dictElementService;

    @Autowired
    private CisMedicalHistoryService cisMedicalHistoryService;

    @Autowired
    private MiLimitHilistService miLimitHilistService;

    @Autowired
    private OpdOrderAsUtil opdOrderAsUtil;

    @Autowired
    private CisOpdOrderService cisOpdOrderService;

    @Autowired
    private CisOrderExecPlanService cisExecOrderPlanService;

    @Autowired
    private CisDgimgApplyService cisDgimgApplyService;

    @Autowired
    @Qualifier("com.bjgoodwill.hip.ds.cis.apply.detail.service.ApplyWithDetialService")
    private ApplyWithDetialService applyWithDetialService;

    public DgimgOrderHandleService getSelf() {
        return SpringUtil.getBean(DgimgOrderHandleService.class);
    }

    @Override
    public OpdDgimgItemResult getOpdDgimgItemResult(String deviceType, String hospitalAreaCode, String workGroupCode, String visitCode) {
        OpdDgimgItemResult opdDgimgItemResult = new OpdDgimgItemResult();

        // 1.查询检查类型常用项目
        CisOrderCommonQto cisOrderCommonQto = new CisOrderCommonQto();
        cisOrderCommonQto.setDocCode(HIPSecurityUtils.getLoginUserId());
        cisOrderCommonQto.setSystemType(SystemTypeEnum.DGIMG);
        cisOrderCommonQto.setHospitalModel(HospitalModelEnum.门诊);
        cisOrderCommonQto.setOrgCode(workGroupCode);
        cisOrderCommonQto.setHospitalAreaCode(hospitalAreaCode);
        cisOrderCommonQto.setSaveFlag(true);
        List<CisOrderCommonTo> cisOrderCommons = cisOrderCommonService.getCisOrderCommons(cisOrderCommonQto);

        // 2.查询检查类型医嘱项目，缓存接口
        List<SystemTypeEnum> systemTypeEnums = new ArrayList<>();
        systemTypeEnums.add(SystemTypeEnum.DGIMG);
        List<ServiceClinicItemTo> serviceClinicItemToList = serviceClinicItemService.queryServiceClinicItemListByinPutText(systemTypeEnums, 10000, "", workGroupCode, HospitalModelEnum.住院, hospitalAreaCode);
        List<OpdDgimgServiceItemAsTo> dgimgServiceItemAsToList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(serviceClinicItemToList)) {
            dgimgServiceItemAsToList = HIPBeanUtil.copy(serviceClinicItemToList, OpdDgimgServiceItemAsTo.class);
            dgimgServiceItemAsToList = dgimgServiceItemAsToList.stream().filter(o -> deviceType.equals(o.getDeviceType())).toList();
        }

        // 3.匹配常用医嘱和检查项目
        if (CollectionUtils.isNotEmpty(cisOrderCommons)) {
            Map<String, CisOrderCommonTo> cisOrderCommonToMap = cisOrderCommons.stream().collect(
                    Collectors.toMap(CisOrderCommonTo::getServiceItemCode, Function.identity(), (key1, key2) -> key1));

            List<OpdDgimgServiceItemAsTo> matches = new ArrayList<>();
            Iterator<OpdDgimgServiceItemAsTo> iterator = dgimgServiceItemAsToList.iterator();
            while (iterator.hasNext()) {
                OpdDgimgServiceItemAsTo value = iterator.next();
                if (cisOrderCommonToMap.containsKey(value.getServiceItemCode())) {
                    value.setCollectionFlag(true);
                    value.setCollectionId(cisOrderCommonToMap.get(value.getServiceItemCode()).getId());
                    matches.add(value);
                    iterator.remove();
                }
            }
            dgimgServiceItemAsToList.addAll(0, matches);
            opdDgimgItemResult.setServiceItemAsTos(dgimgServiceItemAsToList);
        } else {
            opdDgimgItemResult.setServiceItemAsTos(dgimgServiceItemAsToList);
        }

        // 4.查询方法、部位字典信息
        List<String> methodList = opdDgimgItemResult.getServiceItemAsTos().stream().map(OpdDgimgServiceItemAsTo::getMethod).distinct().toList();
        List<String> humanOrgansList = opdDgimgItemResult.getServiceItemAsTos().stream().map(OpdDgimgServiceItemAsTo::getHumanOrgans).distinct().toList();
        opdDgimgItemResult.setHumanOrgansList(this.getHumanOrgansDictElement(humanOrgansList));
        opdDgimgItemResult.setMethodList(this.getMethodDictElement(methodList));

        // 5.查询患者病史信息
        if (StringUtils.isNotEmpty(visitCode)) {
            CisMedicalHistoryTo cisMedicalHistoryTo = cisMedicalHistoryService.getCisMedicalHistoryByVisitCode(visitCode);
            opdDgimgItemResult.setCisMedicalHistoryTo(cisMedicalHistoryTo);
        }


        return opdDgimgItemResult;
    }

    @Override
    public List<OpdDgimgServiceItemAsTo> commonDgimgOrderSearch(String deviceType, String hospitalAreaCode, String workGroupCode) {

        // 1.查询常用项目
        CisOrderCommonQto cisOrderCommonQto = new CisOrderCommonQto();
        cisOrderCommonQto.setSaveFlag(false);
        cisOrderCommonQto.setDocCode(HIPSecurityUtils.getLoginStaffId());
        cisOrderCommonQto.setSystemType(SystemTypeEnum.DGIMG);
        cisOrderCommonQto.setHospitalModel(HospitalModelEnum.门诊);
        cisOrderCommonQto.setOrgCode(workGroupCode);
        cisOrderCommonQto.setHospitalAreaCode(hospitalAreaCode);
        List<CisOrderCommonTo> cisOrderCommons = cisOrderCommonService.getCisOrderCommons(cisOrderCommonQto);

        if (CollectionUtils.isEmpty(cisOrderCommons)) {
            return new ArrayList<>();
        }

        cisOrderCommons = cisOrderCommons.stream().sorted(Comparator.comparing(CisOrderCommonTo::getIntegral, Comparator.reverseOrder())).toList();

        // 2.查询所有门诊检验项目
        List<DgimgApplyTo> dgimgApplyToList = new ArrayList<>();
        List<SystemTypeEnum> systemTypeEnums = new ArrayList<>();
        systemTypeEnums.add(SystemTypeEnum.DGIMG);
        List<ServiceClinicItemTo> serviceClinicItemTos = serviceClinicItemService.queryServiceClinicItemListByinPutText(systemTypeEnums, 10000, "", workGroupCode, HospitalModelEnum.门诊, hospitalAreaCode);
        if (CollectionUtils.isNotEmpty(serviceClinicItemTos)) {
            dgimgApplyToList = serviceClinicItemTos.stream().filter(DgimgApplyTo.class::isInstance).map(DgimgApplyTo.class::cast).toList();
        }
        List<OpdDgimgServiceItemAsTo> dgimgApplyAsToList = HIPBeanUtil.copy(dgimgApplyToList, OpdDgimgServiceItemAsTo.class);
        Map<String, OpdDgimgServiceItemAsTo> dgimgApplyAsToMap = dgimgApplyAsToList.stream().collect(Collectors.toMap(OpdDgimgServiceItemAsTo::getServiceItemCode, Function.identity()));

        List<OpdDgimgServiceItemAsTo> result = new ArrayList<>();
        for (CisOrderCommonTo cisOrderCommon : cisOrderCommons) {
            if (dgimgApplyAsToMap.containsKey(cisOrderCommon.getServiceItemCode())) {
                result.add(dgimgApplyAsToMap.get(cisOrderCommon.getServiceItemCode()));
            }
        }

        return result;
    }

    @Override
    public List<CisApplyChargeAsTo> getServiceClincPriceList(String serviceItemCode) {
        ServiceClinicItemTo serviceClinicItemTo = serviceClinicItemService.getServiceClinicItemByCode(serviceItemCode);
        BusinessAssert.notNull(serviceClinicItemTo, CisDocOpdBusinessErrorEnum.BUS_CIS_DOC_OPD_0001, "医嘱项目");
        BusinessAssert.notEmpty(serviceClinicItemTo.getServiceClinicPrices(), CisDocOpdBusinessErrorEnum.BUS_CIS_DOC_OPD_0003, "医嘱项目未对照收费项目");

        List<CisApplyChargeAsTo> result = new ArrayList<>();
        for (ServiceClinicPriceTo serviceClinicPrice : serviceClinicItemTo.getServiceClinicPrices()) {
            CisApplyChargeAsTo chargeAsTo = new CisApplyChargeAsTo();

            chargeAsTo.setIsFixed(serviceClinicPrice.getIsFixed());
            chargeAsTo.setPriceItemCode(serviceClinicPrice.getPriceItemCode());
            chargeAsTo.setPriceItemName(serviceClinicPrice.getPriceItemName());
            chargeAsTo.setPackageSpec(serviceClinicPrice.getSpec());
            chargeAsTo.setPrice(serviceClinicPrice.getPrice());
            chargeAsTo.setUnit(serviceClinicPrice.getUnitCode());
            chargeAsTo.setUnitName(serviceClinicPrice.getUnitName());
            chargeAsTo.setNum(serviceClinicPrice.getQuantity());
            chargeAsTo.setChageAmount(chargeAsTo.getPrice().multiply(new BigDecimal(chargeAsTo.getNum())));
            chargeAsTo.setSystemItemClass(serviceClinicPrice.getSystemItemClass());

            result.add(chargeAsTo);
        }

        // 查询限制维护项目
        this.buildApplyChargeMiLimit(result);

        return result;
    }

    private void buildApplyChargeMiLimit(List<CisApplyChargeAsTo> cisApplyChargeAsTos) {
        try {
            List<String> priceItemCodes = cisApplyChargeAsTos.stream().map(CisApplyChargeAsTo::getPriceItemCode).toList();
            Map<String, MiLimitHilistTo> miLimitHilistToMap = miLimitHilistService.getMiLimitHilistByHisCodes(priceItemCodes.toArray(new String[priceItemCodes.size()]));
            for (CisApplyChargeAsTo cisApplyChargeAsTo : cisApplyChargeAsTos) {
                if (miLimitHilistToMap != null && miLimitHilistToMap.containsKey(cisApplyChargeAsTo.getPriceItemCode())) {
                    MiLimitHilistTo miLimitHilistTo = miLimitHilistToMap.get(cisApplyChargeAsTo.getPriceItemCode());
                    cisApplyChargeAsTo.setMiId(miLimitHilistTo.getId());
                    cisApplyChargeAsTo.setLimitContent(miLimitHilistTo.getLimitContent());
                    cisApplyChargeAsTo.setLimitType(miLimitHilistTo.getLimitType());
                }
            }
        } catch (Exception e) {

        }
    }

    @GlobalTransactional
    @Override
    public List<String> insertOrUpdateDgimgOrders(DgimgOrderDto dgimgOrderDto) {
        List<String> orderIds = new ArrayList<>();
        if (StringUtils.isEmpty(dgimgOrderDto.getId())) {
            // 新增检查医嘱
            // 医嘱通用信息赋值
            dgimgOrderDto.setId(HIPIDUtil.getNextIdString());
            dgimgOrderDto.setOrderClass(SystemTypeEnum.DGIMG);
            dgimgOrderDto.setExtTypeCode(dgimgOrderDto.getDetails().get(0).getExtTypeCode());

            dgimgOrderDto.getDetails().forEach(o -> o.setId(HIPIDUtil.getNextIdString()));

            CisOpdOrderNto cisOpdOrderNto = HIPBeanUtil.copy(dgimgOrderDto, CisOpdOrderNto.class);
            //医嘱申请单通用信息赋值
            CisDgimgApplyNto cisDgimgApplyNto = new CisDgimgApplyNto();
            cisDgimgApplyNto = (CisDgimgApplyNto) opdOrderAsUtil.assignApplyCommonValues(dgimgOrderDto, cisDgimgApplyNto);
            //子类信息赋值
            cisDgimgApplyNto.setReMark(dgimgOrderDto.getOpdOrderBaseApplyAsTo().getReMark());
            cisDgimgApplyNto.setPrecautions(dgimgOrderDto.getPrecautions());
            cisDgimgApplyNto.setMedrecordAndExamabstract(dgimgOrderDto.getMedrecordAndExamabstract());
            cisDgimgApplyNto.setPhysiqueAndExam(dgimgOrderDto.getPhysiqueAndExam());
            cisDgimgApplyNto.setDgimgClass(dgimgOrderDto.getDgimgClass());
            cisDgimgApplyNto.setDgimgClassName(dgimgOrderDto.getDgimgClassName());
            cisDgimgApplyNto.setDgimgSubClass(dgimgOrderDto.getDgimgSubClass());
            cisDgimgApplyNto.setDgimgSubClassName(dgimgOrderDto.getDgimgSubClassName());
            cisDgimgApplyNto.setAuxiliaryInspection(dgimgOrderDto.getAuxiliaryInspection());
            cisDgimgApplyNto.setCheckPurpose(dgimgOrderDto.getCheckPurpose());
            cisDgimgApplyNto.setApplyBookId(dgimgOrderDto.getApplyBookId());
            cisDgimgApplyNto.setPreviousPathologicalExamin(dgimgOrderDto.getPreviousPathologicalExamin());
            cisDgimgApplyNto.setDeviceType(dgimgOrderDto.getDeviceType());
            cisDgimgApplyNto.setDeviceTypeName(dgimgOrderDto.getDeviceTypeName());
            cisDgimgApplyNto.setAllergicHistoryFlag(dgimgOrderDto.getAllergicHistoryFlag());
            cisDgimgApplyNto.setOccupationalDiseasesFlag(dgimgOrderDto.getOccupationalDiseasesFlag());
            cisDgimgApplyNto.setClinicalHistory(dgimgOrderDto.getClinicalHistory());
            cisDgimgApplyNto.setContagiousDiseaseHistoryFlag(dgimgOrderDto.getContagiousDiseaseHistoryFlag());
            cisDgimgApplyNto.setNum(1d);

            // 申请单诊断信息
            if (CollectionUtils.isNotEmpty(dgimgOrderDto.getOpdOrderBaseApplyAsTo().getApplyDiagnosisNtos())) {
                dgimgOrderDto.getOpdOrderBaseApplyAsTo().getApplyDiagnosisNtos().forEach(o -> o.setId(HIPIDUtil.getNextIdString()));
                cisDgimgApplyNto.setApplyDiagnosisNtos(dgimgOrderDto.getOpdOrderBaseApplyAsTo().getApplyDiagnosisNtos());
            }

            // 患者病史赋值
            CisMedicalHistoryNto cisMedicalHistoryNto = new CisMedicalHistoryNto();
            if (dgimgOrderDto.getCisMedicalHistoryNto() != null && StringUtils.isNotEmpty(dgimgOrderDto.getCisMedicalHistoryNto().getId())) {
                cisMedicalHistoryNto.setId(dgimgOrderDto.getCisMedicalHistoryNto().getId());
            } else {
                cisMedicalHistoryNto.setId(HIPIDUtil.getNextIdString());
            }
            cisMedicalHistoryNto.setVisitCode(dgimgOrderDto.getVisitCode());
            cisMedicalHistoryNto.setChiefComplaint(dgimgOrderDto.getClinicalHistory());
            cisMedicalHistoryNto.setPastMedicalHistory(dgimgOrderDto.getPreviousPathologicalExamin());
            cisDgimgApplyNto.setCisMedicalHistoryNto(cisMedicalHistoryNto);


            // 申请单明细信息及费用信息
            List<CisDgimgApplyDetailNto> cisDgimgApplyDetailNtoList = new ArrayList<>();
            List<CisApplyChargeNto> cisApplyChargeNtoList = new ArrayList<>();
            String serviceItemCode = "";
            String serviceItemName = "";

            List<ServiceClinicItemTo> serviceClinicItemTos = serviceClinicItemService.getServiceClinicItemListByCode(
                    dgimgOrderDto.getDetails().stream().map(CisDgimgApplyDetailAsNto::getDgimgCode).toList());
            Map<String, ServiceClinicItemTo> serviceClinicItemToMap = serviceClinicItemTos.stream().collect(
                    Collectors.toMap(ServiceClinicItemTo::getServiceItemCode, v -> v));

            for (CisDgimgApplyDetailAsNto detailAsNto : dgimgOrderDto.getDetails()) {
                serviceItemCode += detailAsNto.getDgimgCode() + ",";
                serviceItemName += detailAsNto.getDgimgName() + ",";
                CisDgimgApplyDetailNto cisDgimgApplyDetailNto = HIPBeanUtil.copy(detailAsNto, CisDgimgApplyDetailNto.class);
                cisDgimgApplyDetailNto.setId(HIPIDUtil.getNextIdString());
                cisDgimgApplyDetailNtoList.add(cisDgimgApplyDetailNto);

                Map<String, ServiceClinicPriceTo> priceToMap = serviceClinicItemToMap.get(detailAsNto.getDgimgCode()).getServiceClinicPrices().stream().collect(
                        Collectors.toMap(ServiceClinicPriceTo::getPriceItemCode, v -> v));

                for (CisApplyChargeAsTo cisApplyCharge : detailAsNto.getCisApplyChargeAs()) {
                    CisApplyChargeNto chargeNto = HIPBeanUtil.copy(cisApplyCharge, CisApplyChargeNto.class);
                    chargeNto.setDetailId(cisDgimgApplyDetailNto.getId());
                    chargeNto.setId(HIPIDUtil.getNextIdString());
                    chargeNto.setOrderId(cisOpdOrderNto.getId());
                    chargeNto.setVisitCode(cisOpdOrderNto.getVisitCode());
                    chargeNto.setChargeFrequency(cisDgimgApplyNto.getFrequency());
                    if (chargeNto.getPrice() != null && chargeNto.getNum() != null) {
                        chargeNto.setChageAmount(chargeNto.getPrice().multiply(BigDecimal.valueOf(chargeNto.getNum())).setScale(1, RoundingMode.HALF_UP));
                    }
                    // 收费项目执行科室构建
                    if (StringUtils.isNotEmpty(cisDgimgApplyNto.getExecutorOrgCode())) {
                        String executeOrgCode = opdOrderAsUtil.buildCiaApplyChargeExecuteOrgCode(priceToMap.get(cisApplyCharge.getPriceItemCode()), cisDgimgApplyNto.getExecutorOrgCode());
                        String executeOrgName = opdOrderAsUtil.getWorkGroupTo(executeOrgCode).getName();

                        chargeNto.setExecuteOrgCode(executeOrgCode);
                        chargeNto.setExecuteOrgName(executeOrgName);
                    }
                    chargeNto.setExecuteOrgCode(cisDgimgApplyNto.getExecutorOrgCode());
                    chargeNto.setExecuteOrgName(cisDgimgApplyNto.getExecutorOrgName());
                    chargeNto.setChargeType(CisChargeTypeEnum.DOCT);
                    cisApplyChargeNtoList.add(chargeNto);
                }

            }
            cisDgimgApplyNto.setDetails(cisDgimgApplyDetailNtoList);
            cisDgimgApplyNto.setCisApplyCharges(cisApplyChargeNtoList);

            // 医嘱项目编码赋值
            cisDgimgApplyNto.setServiceItemCode(serviceItemCode.substring(0, serviceItemCode.length() - 1));
            cisDgimgApplyNto.setServiceItemName(serviceItemName.substring(0, serviceItemName.length() - 1));
            cisOpdOrderNto.setOrderServiceCode(cisDgimgApplyNto.getServiceItemCode());

            cisOpdOrderNto.setCisBaseApply(cisDgimgApplyNto);
            //构建入参调用保存医嘱接口
            cisOpdOrderService.createCisOpdOrder(new ArrayList<>(Arrays.asList(cisOpdOrderNto)));
        } else {
            // 修改
            // 判断医嘱当前状态，如果结算，不允许修改
            BusinessAssert.isFalse(opdOrderAsUtil.judgeIsCharged(Arrays.asList(dgimgOrderDto.getId())), CisDocOpdBusinessErrorEnum.BUS_CIS_DOC_OPD_0003, "医嘱已计费，请退费后再进行修改！");
            this.updateOpdDgimgOrder(dgimgOrderDto);
        }

        orderIds.add(dgimgOrderDto.getId());
        return orderIds;
    }

    @Override
    public List<CisDgimgApplyAsTo> getNewDgimgApplys(String visitCode) {
        List<CisDgimgApplyAsTo> result = new ArrayList<>();

        CisBaseApplyQto cisBaseApplyQto = new CisBaseApplyQto();
        cisBaseApplyQto.setOrderClass(SystemTypeEnum.DGIMG.getCode());
        cisBaseApplyQto.setVisitCode(visitCode);
        cisBaseApplyQto.setVisitType(VisitTypeEnum.OPD);
        List<CisBaseApplyTo> cisBaseApplyTos = cisBaseApplyService.getCisBaseApplies(cisBaseApplyQto);

        if (CollectionUtils.isNotEmpty(cisBaseApplyTos)) {
            cisBaseApplyTos = cisBaseApplyTos.stream().
                    filter(cisBaseApplyTo -> cisBaseApplyTo.getStatusCode().equals(CisStatusEnum.NEW)
                            || cisBaseApplyTo.getStatusCode().equals(CisStatusEnum.BACK)).toList();
            if (CollectionUtils.isNotEmpty(cisBaseApplyTos)) {
                List<CisApplyChargeTo> chargeToList = cisBaseApplyService.getCisApplyChargesByCisBaseApplyIds(cisBaseApplyTos.stream().map(CisBaseApplyTo::getId).toList());
                Map<String, List<CisApplyChargeTo>> chargeMap = chargeToList.stream().collect(Collectors.groupingBy(CisApplyChargeTo::getCisBaseApplyId));
                List<CisDgimgApplyTo> dgimgApplyToList = cisBaseApplyTos.stream()
                        .sorted(Comparator.comparing(CisBaseApplyTo::getCreatedDate)).map(CisDgimgApplyTo.class::cast).toList();

                dgimgApplyToList.forEach(dgimgApplyTo -> {
                    CisDgimgApplyAsTo asTo = HIPBeanUtil.copy(dgimgApplyTo, CisDgimgApplyAsTo.class);
                    List<CisApplyChargeTo> chargeTos = chargeMap.get(dgimgApplyTo.getId());
                    // 求金额
                    BigDecimal amount = chargeTos.stream().map(CisApplyChargeTo::getChageAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    asTo.setChargeAmount(amount);
                    result.add(asTo);
                });
            }
        }

        return result;
    }

    @Override
    public List<CisDgimgApplyAsTo> getActiveDgimgApplys(String visitCode) {
        List<CisDgimgApplyAsTo> result = new ArrayList<>();

        CisBaseApplyQto cisBaseApplyQto = new CisBaseApplyQto();
        cisBaseApplyQto.setOrderClass(SystemTypeEnum.DGIMG.getCode());
        cisBaseApplyQto.setVisitCode(visitCode);
        cisBaseApplyQto.setVisitType(VisitTypeEnum.OPD);
        cisBaseApplyQto.setStatusCode(CisStatusEnum.ACTIVE);
        List<CisBaseApplyTo> cisBaseApplyTos = cisBaseApplyService.getCisBaseApplies(cisBaseApplyQto);

        if (CollectionUtils.isNotEmpty(cisBaseApplyTos)) {
            // 查询费用信息
            List<CisApplyChargeTo> chargeToList = cisBaseApplyService.getCisApplyChargesByCisBaseApplyIds(cisBaseApplyTos.stream().map(CisBaseApplyTo::getId).toList());
            Map<String, List<CisApplyChargeTo>> chargeMap = chargeToList.stream().collect(Collectors.groupingBy(CisApplyChargeTo::getCisBaseApplyId));

            List<CisDgimgApplyTo> dgimgApplyToList = cisBaseApplyTos.stream()
                    .sorted(Comparator.comparing(CisBaseApplyTo::getCreatedDate)).map(CisDgimgApplyTo.class::cast).toList();
            dgimgApplyToList.forEach(dgimgApplyTo -> {
                List<CisApplyChargeTo> chargeTos = chargeMap.get(dgimgApplyTo.getId());
                CisDgimgApplyAsTo asTo = HIPBeanUtil.copy(dgimgApplyTo, CisDgimgApplyAsTo.class);
                // 求金额
                BigDecimal amount = chargeTos.stream().map(CisApplyChargeTo::getChageAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                asTo.setChargeAmount(amount);
                result.add(asTo);
            });

            Map<String, CisDgimgApplyAsTo> resultMap = result.stream().collect(Collectors.toMap(CisDgimgApplyAsTo::getOrderID, v -> v));

            // 查询已结算执行单
            List<String> orderIds = dgimgApplyToList.stream().map(CisDgimgApplyTo::getOrderID).toList();
            List<CisOrderExecPlanTo> cisOrderExecPlanTos = cisExecOrderPlanService.queryCisOrderExecPlanByOrderIds(orderIds);
            if (CollectionUtils.isNotEmpty(cisOrderExecPlanTos)) {
                List<String> chargedOrderIds = cisOrderExecPlanTos.stream().map(CisOrderExecPlanTo::getOrderId).toList();

                for (String chargedOrderId : chargedOrderIds) {
                    if (resultMap.containsKey(chargedOrderId)) {
                        result.remove(resultMap.get(chargedOrderId));
                    }
                }
            }

        }

        return result;
    }

    @Override
    public DgimgOrderDto getDgimgOrder(String orderId) {
        DgimgOrderDto dgimgOrderDto = new DgimgOrderDto();

        CisOpdOrderTo cisOpdOrderTo = cisOpdOrderService.getCisOpdOrderById(orderId);
        BusinessAssert.notNull(cisOpdOrderTo, CisDocOpdBusinessErrorEnum.BUS_CIS_DOC_OPD_0001, "医嘱信息");
        dgimgOrderDto = HIPBeanUtil.copy(cisOpdOrderTo, DgimgOrderDto.class);

        CisDgimgApplyTo cisDgimgApplyTo = cisDgimgApplyService.getCisDgimgApplyAllById(cisOpdOrderTo.getApplyCode());
        BusinessAssert.notNull(cisDgimgApplyTo, CisDocOpdBusinessErrorEnum.BUS_CIS_DOC_OPD_0001, "检查申请单信息");

        dgimgOrderDto.setPrecautions(cisDgimgApplyTo.getPrecautions());
        dgimgOrderDto.setMedrecordAndExamabstract(cisDgimgApplyTo.getMedrecordAndExamabstract());
        dgimgOrderDto.setPhysiqueAndExam(cisDgimgApplyTo.getPhysiqueAndExam());
        dgimgOrderDto.setDgimgClass(cisDgimgApplyTo.getDgimgClass());
        dgimgOrderDto.setDgimgClassName(cisDgimgApplyTo.getDgimgClassName());
        dgimgOrderDto.setDgimgSubClass(cisDgimgApplyTo.getDgimgSubClass());
        dgimgOrderDto.setDgimgSubClassName(cisDgimgApplyTo.getDgimgSubClassName());
        dgimgOrderDto.setAuxiliaryInspection(cisDgimgApplyTo.getAuxiliaryInspection());
        dgimgOrderDto.setCheckPurpose(cisDgimgApplyTo.getCheckPurpose());
        dgimgOrderDto.setApplyBookId(cisDgimgApplyTo.getApplyBookId());
        dgimgOrderDto.setPreviousPathologicalExamin(cisDgimgApplyTo.getPreviousPathologicalExamin());
        dgimgOrderDto.setDeviceType(cisDgimgApplyTo.getDeviceType());
        dgimgOrderDto.setDeviceTypeName(cisDgimgApplyTo.getDeviceTypeName());
        dgimgOrderDto.setAllergicHistoryFlag(cisDgimgApplyTo.getAllergicHistoryFlag());
        dgimgOrderDto.setOccupationalDiseasesFlag(cisDgimgApplyTo.getOccupationalDiseasesFlag());
        dgimgOrderDto.setClinicalHistory(cisDgimgApplyTo.getClinicalHistory());
        dgimgOrderDto.setContagiousDiseaseHistoryFlag(cisDgimgApplyTo.getContagiousDiseaseHistoryFlag());

        // 查询患者病史信息
        CisMedicalHistoryTo cisMedicalHistoryTo = cisMedicalHistoryService.getCisMedicalHistoryByVisitCode(cisDgimgApplyTo.getVisitCode());
        if (cisMedicalHistoryTo != null) {
            dgimgOrderDto.setCisMedicalHistoryNto(HIPBeanUtil.copy(cisMedicalHistoryTo, CisMedicalHistoryNto.class));
        }

        OpdOrderBaseApplyAsTo opdOrderBaseApplyAsTo = HIPBeanUtil.copy(cisDgimgApplyTo, OpdOrderBaseApplyAsTo.class);
        opdOrderBaseApplyAsTo.setApplyDiagnosisNtos(HIPBeanUtil.copy(cisDgimgApplyTo.getApplyDiagnoses(), ApplyDiagnosisNto.class));
        dgimgOrderDto.setOpdOrderBaseApplyAsTo(opdOrderBaseApplyAsTo);

        // 构建申请单明细及对应charge信息
        List<CisApplyChargeAsTo> chargeAsToList = HIPBeanUtil.copy(cisDgimgApplyTo.getCisApplyCharges(), CisApplyChargeAsTo.class);
        // 查询限制维护项目
        this.buildApplyChargeMiLimit(chargeAsToList);
        Map<String, List<CisApplyChargeAsTo>> chargeToMap =
                chargeAsToList.stream().filter(o -> StringUtils.isNotEmpty(o.getDetailId()))
                        .collect(Collectors.groupingBy(CisApplyChargeTo::getDetailId));
        dgimgOrderDto.setDetails(HIPBeanUtil.copy(cisDgimgApplyTo.getCisDgimgApplyDetails(), CisDgimgApplyDetailAsNto.class));

        // 查询检查申请明细服务项目信息
        List<String> dgimgCodes = dgimgOrderDto.getDetails().stream().map(CisDgimgApplyDetailAsNto::getDgimgCode).toList();
        List<ServiceClinicItemTo> serviceClinicItemTos = serviceClinicItemService.getServiceClinicItemListByCode(dgimgCodes);
        Map<String, ServiceClinicItemTo> serviceClinicItemToMap = serviceClinicItemTos.stream().collect(Collectors.toMap(ServiceClinicItemTo::getServiceItemCode, v -> v));

        for (CisDgimgApplyDetailAsNto detail : dgimgOrderDto.getDetails()) {
            if (chargeToMap.containsKey(detail.getId())) {
                detail.setCisApplyChargeAs(HIPBeanUtil.copy(chargeToMap.get(detail.getId()), CisApplyChargeAsTo.class));
            }

            // 赋值服务项目限制执行科室列表
            if (serviceClinicItemToMap.containsKey(detail.getDgimgCode())) {
                detail.setLimitExecOrgToList(serviceClinicItemToMap.get(detail.getDgimgCode()).getLimitExecOrgToList());
            }
        }

        return dgimgOrderDto;
    }

    @GlobalTransactional
    @Override
    public void submitDgimgOrders(List<String> orderIds, String visitCode) {
        // 领域医嘱签发接口
        cisOpdOrderService.submit(visitCode, orderIds, new CisOpdEto());

        // 保存医嘱常用信息
        CisOpdOrderCopyTo copy = cisOpdOrderService.copyOpdCpoeByOrderIds(orderIds);
        List<CisOrderCommonNto> cisOrderCommonNtos = this.getCisOrderCommonNto(copy.getCisBaseApplyTos().stream().map(CisDgimgApplyTo.class::cast).toList());
        cisOrderCommonService.saveOrderCommons(cisOrderCommonNtos);
    }

    @GlobalTransactional
    @Override
    public void insertOrUpdateAndSubmitDgimgOrders(DgimgOrderDto dgimgOrderDto) {
        // 新增or修改医嘱信息
        getSelf().insertOrUpdateDgimgOrders(dgimgOrderDto);

        // 签发医嘱
        getSelf().submitDgimgOrders(Arrays.asList(dgimgOrderDto.getId()), dgimgOrderDto.getVisitCode());
    }

    @Override
    public void backDgimgOrders(List<String> orderIdList) {
        // 查询选中的申请单信息是否已计费
        List<CisOrderExecPlanTo> cisExecOrderPlans = cisExecOrderPlanService.queryCisOrderExecPlanByOrderIds(orderIdList);
        if (CollectionUtils.isNotEmpty(cisExecOrderPlans)) {
            String msg = "";
            for (CisOrderExecPlanTo cisExecOrderPlan : cisExecOrderPlans) {
                msg += "[" + cisExecOrderPlan.getServiceItemName() + "],";
            }
            msg += "已计费，请先退费！";
            BusinessAssert.isTrue(false, CisDocOpdBusinessErrorEnum.BUS_CIS_DOC_OPD_0003, msg);
        }

        // 查询已签发的医嘱及申请单信息
        CisOpdOrderCopyTo copy = cisOpdOrderService.copyOpdCpoeByOrderIds(orderIdList);
        String treatmentCode = copy.getCisOpdOrderTos().get(0).getTreatmentCode();
        List<String> prescriptionIds = copy.getCisOpdOrderTos().stream().map(CisOpdOrderTo::getPrescriptionID).distinct().toList();

        cisOpdOrderService.backup(treatmentCode, prescriptionIds);
    }

    @GlobalTransactional
    @Override
    public void deleteDgimgOrders(List<String> orderIdList) {
        // 查询要删除的医嘱和申请单信息
        CisOpdOrderCopyTo copy = cisOpdOrderService.copyOpdCpoeByOrderIds(orderIdList);
        String treatmentCode = copy.getCisOpdOrderTos().get(0).getTreatmentCode();
        List<String> prescriptionIds = copy.getCisOpdOrderTos().stream().map(CisOpdOrderTo::getPrescriptionID).distinct().toList();

        if (copy.getCisOpdOrderTos().get(0).getStatusCode().equals(CisStatusEnum.NEW)
                || copy.getCisOpdOrderTos().get(0).getStatusCode().equals(CisStatusEnum.BACK)) {
            cisOpdOrderService.cancel(treatmentCode, prescriptionIds);
        } else if (copy.getCisOpdOrderTos().get(0).getStatusCode().equals(CisStatusEnum.ACTIVE)) {
            // 查询选中的申请单信息是否已计费
            List<CisOrderExecPlanTo> cisExecOrderPlans = cisExecOrderPlanService.queryCisOrderExecPlanByOrderIds(orderIdList);
            if (CollectionUtils.isNotEmpty(cisExecOrderPlans)) {
                String msg = "";
                for (CisOrderExecPlanTo cisExecOrderPlan : cisExecOrderPlans) {
                    msg += "[" + cisExecOrderPlan.getServiceItemName() + "],";
                }
                msg += "已计费，请先退费！";
                BusinessAssert.isTrue(false, CisDocOpdBusinessErrorEnum.BUS_CIS_DOC_OPD_0003, msg);
            }

            // 先撤回再删除
            cisOpdOrderService.backup(treatmentCode, prescriptionIds);
            cisOpdOrderService.cancel(treatmentCode, prescriptionIds);
        }
    }

    @GlobalTransactional
    @Override
    public void deleteActiveDgimgDetail(String orderId, String detailId) {
        // 判断是否已缴费
        BusinessAssert.isFalse(opdOrderAsUtil.judgeIsCharged(Arrays.asList(orderId)), CisDocOpdBusinessErrorEnum.BUS_CIS_DOC_OPD_0003, "医嘱已计费，请先退费！");

        // 查询医嘱及申请单信息
        CisOpdOrderCopyTo copy = cisOpdOrderService.copyOpdCpoeByOrderIds(Arrays.asList(orderId));
        CisOpdOrderTo cisOpdOrderTo = copy.getCisOpdOrderTos().get(0);
        cisOpdOrderTo.setCisBaseApply(copy.getCisBaseApplyTos().get(0));

        // 撤回医嘱
        cisOpdOrderService.backup(cisOpdOrderTo.getTreatmentCode(), Arrays.asList(cisOpdOrderTo.getPrescriptionID()));

        // 删除申请单明细信息
        applyWithDetialService.deleteApplyWithDetialByDetails(cisOpdOrderTo.getApplyCode(), Arrays.asList(detailId));

        // 重新签发医嘱
        cisOpdOrderService.submit(cisOpdOrderTo.getVisitCode(), Arrays.asList(orderId), new CisOpdEto());
    }

    @GlobalTransactional
    @Override
    public void activeDgimgApplyReSubmit(DgimgOrderDto dgimgOrderDto) {
        // 判断医嘱当前状态，如果结算，不允许修改
        BusinessAssert.isFalse(opdOrderAsUtil.judgeIsCharged(Arrays.asList(dgimgOrderDto.getId())), CisDocOpdBusinessErrorEnum.BUS_CIS_DOC_OPD_0003, "医嘱已计费，请退费后再进行修改！");

        // 撤回医嘱
        cisOpdOrderService.backup(dgimgOrderDto.getTreatmentCode(), Arrays.asList(dgimgOrderDto.getPrescriptionID()));

        // 修改医嘱
        this.updateOpdDgimgOrder(dgimgOrderDto);

        // 重新签发医嘱
        getSelf().submitDgimgOrders(Arrays.asList(dgimgOrderDto.getId()), dgimgOrderDto.getVisitCode());
    }


    private List<DictElementTo> getHumanOrgansDictElement(List<String> humanOrgansCodes) {
        if (CollectionUtils.isNotEmpty(humanOrgansCodes)) {
            List<DictElementTo> humanOrgansDictElements = dictElementService.getCustomDictElement("HumanOrgans");
            return humanOrgansDictElements.stream().filter(o -> humanOrgansCodes.contains(o.getElementCode())).toList();
        }

        return new ArrayList<>();
    }

    private List<DictElementTo> getMethodDictElement(List<String> methodCodes) {
        if (CollectionUtils.isNotEmpty(methodCodes)) {
            List<DictElementTo> methodDictElements = dictElementService.getCustomDictElement("DgimgMethod");
            return methodDictElements.stream().filter(o -> methodCodes.contains(o.getElementCode())).toList();
        }

        return new ArrayList<>();
    }

    /**
     * 修改检查医嘱
     *
     * @param dgimgOrderDto
     */
    private void updateOpdDgimgOrder(DgimgOrderDto dgimgOrderDto) {
        CisDgimgApplyTo cisDgimgApplyTo = (CisDgimgApplyTo) cisBaseApplyService.getCisBaseApplyById(dgimgOrderDto.getApplyCode());

        CisOpdOrderEto cisOpdOrderEto = HIPBeanUtil.copy(dgimgOrderDto, CisOpdOrderEto.class);
        // 检查申请单属性修改
        CisDgimgApplyEto cisDgimgApplyEto = HIPBeanUtil.copy(dgimgOrderDto.getOpdOrderBaseApplyAsTo(), CisDgimgApplyEto.class);
        cisDgimgApplyEto.setReMark(dgimgOrderDto.getOpdOrderBaseApplyAsTo().getReMark());
        cisDgimgApplyEto.setPrecautions(dgimgOrderDto.getPrecautions());
        cisDgimgApplyEto.setMedrecordAndExamabstract(dgimgOrderDto.getMedrecordAndExamabstract());
        cisDgimgApplyEto.setDgimgClass(dgimgOrderDto.getDgimgClass());
        cisDgimgApplyEto.setDgimgClassName(dgimgOrderDto.getDgimgClassName());
        cisDgimgApplyEto.setDgimgSubClass(dgimgOrderDto.getDgimgSubClass());
        cisDgimgApplyEto.setDgimgSubClassName(dgimgOrderDto.getDgimgSubClassName());
        cisDgimgApplyEto.setAuxiliaryInspection(dgimgOrderDto.getAuxiliaryInspection());
        cisDgimgApplyEto.setCheckPurpose(dgimgOrderDto.getCheckPurpose());
        cisDgimgApplyEto.setPreviousPathologicalExamin(dgimgOrderDto.getPreviousPathologicalExamin());
        cisDgimgApplyEto.setAllergicHistoryFlag(dgimgOrderDto.getAllergicHistoryFlag());
        cisDgimgApplyEto.setOccupationalDiseasesFlag(dgimgOrderDto.getOccupationalDiseasesFlag());
        cisDgimgApplyEto.setClinicalHistory(dgimgOrderDto.getClinicalHistory());
        cisDgimgApplyEto.setContagiousDiseaseHistoryFlag(dgimgOrderDto.getContagiousDiseaseHistoryFlag());
        cisDgimgApplyEto.setStatusCode(dgimgOrderDto.getOpdOrderBaseApplyAsTo().getStatusCode());

        List<CisDgimgApplyDetailAsNto> insertDetails = dgimgOrderDto.getDetails().stream().filter(detail -> StringUtils.isEmpty(detail.getId())).toList();
        List<CisDgimgApplyDetailAsNto> updateDetails = dgimgOrderDto.getDetails().stream().filter(detail -> StringUtils.isNotEmpty(detail.getId())).toList();

        if (CollectionUtils.isNotEmpty(insertDetails)) {
            List<CisDgimgApplyDetailNto> cisDgimgApplyDetailNtoList = new ArrayList<>();
            List<CisApplyChargeNto> cisApplyChargeNtoList = new ArrayList<>();

            // 医嘱项目列表
            List<ServiceClinicItemTo> serviceClinicItemTos = serviceClinicItemService.getServiceClinicItemListByCode(
                    insertDetails.stream().map(CisDgimgApplyDetailAsNto::getDgimgCode).toList());
            Map<String, ServiceClinicItemTo> serviceClinicItemToMap = serviceClinicItemTos.stream().collect(
                    Collectors.toMap(ServiceClinicItemTo::getServiceItemCode, v -> v));

            for (CisDgimgApplyDetailAsNto detailAsNto : insertDetails) {
                CisDgimgApplyDetailNto cisDgimgApplyDetailNto = HIPBeanUtil.copy(detailAsNto, CisDgimgApplyDetailNto.class);
                cisDgimgApplyDetailNto.setId(HIPIDUtil.getNextIdString());
                cisDgimgApplyDetailNtoList.add(cisDgimgApplyDetailNto);

                // 收费项目信息
                Map<String, ServiceClinicPriceTo> priceToMap = serviceClinicItemToMap.get(detailAsNto.getDgimgCode()).getServiceClinicPrices().stream().collect(
                        Collectors.toMap(ServiceClinicPriceTo::getPriceItemCode, v -> v));

                for (CisApplyChargeAsTo cisApplyCharge : detailAsNto.getCisApplyChargeAs()) {
                    CisApplyChargeNto chargeNto = HIPBeanUtil.copy(cisApplyCharge, CisApplyChargeNto.class);
                    chargeNto.setDetailId(cisDgimgApplyDetailNto.getId());
                    chargeNto.setId(HIPIDUtil.getNextIdString());
                    chargeNto.setOrderId(dgimgOrderDto.getId());
                    chargeNto.setVisitCode(chargeNto.getVisitCode());
                    chargeNto.setChargeFrequency(cisDgimgApplyEto.getFrequency());
                    if (chargeNto.getPrice() != null && chargeNto.getNum() != null) {
                        chargeNto.setChageAmount(chargeNto.getPrice().multiply(BigDecimal.valueOf(chargeNto.getNum())).setScale(1, RoundingMode.HALF_UP));
                    }

                    // 收费项目执行科室构建
                    if (StringUtils.isNotEmpty(dgimgOrderDto.getOpdOrderBaseApplyAsTo().getExecutorOrgCode())) {
                        String executeOrgCode = opdOrderAsUtil.buildCiaApplyChargeExecuteOrgCode(priceToMap.get(cisApplyCharge.getPriceItemCode()), dgimgOrderDto.getOpdOrderBaseApplyAsTo().getExecutorOrgCode());
                        String executeOrgName = opdOrderAsUtil.getWorkGroupTo(executeOrgCode).getName();

                        chargeNto.setExecuteOrgCode(executeOrgCode);
                        chargeNto.setExecuteOrgName(executeOrgName);
                    }
                    chargeNto.setChargeType(CisChargeTypeEnum.DOCT);
                    cisApplyChargeNtoList.add(chargeNto);
                }
            }

            cisDgimgApplyEto.setDetailNtos(cisDgimgApplyDetailNtoList);
            cisDgimgApplyEto.setCisApplyChargeNtos(cisApplyChargeNtoList);
        }

        if (CollectionUtils.isNotEmpty(updateDetails)) {
            List<CisDgimgApplyDetailEto> cisDgimgApplyDetailEtoList = new ArrayList<>();
            List<CisApplyChargeEto> cisApplyChargeEtoList = new ArrayList<>();

            for (CisDgimgApplyDetailAsNto detailAsNto : updateDetails) {
                CisDgimgApplyDetailEto cisDgimgApplyDetailEto = HIPBeanUtil.copy(detailAsNto, CisDgimgApplyDetailEto.class);
                cisDgimgApplyDetailEtoList.add(cisDgimgApplyDetailEto);

                List<CisApplyChargeEto> cisApplyChargeEtos = HIPBeanUtil.copy(detailAsNto.getCisApplyChargeAs(), CisApplyChargeEto.class);
                cisApplyChargeEtoList.addAll(cisApplyChargeEtos);
            }

            cisDgimgApplyEto.setDetailEtos(cisDgimgApplyDetailEtoList);
            cisDgimgApplyEto.setCisApplyChargeEtos(cisApplyChargeEtoList);
        }

        // 删除申请单明细
        List<String> deleteDetailIds = new ArrayList<>();
        Map<String, CisDgimgApplyDetailAsNto> updateDetailMap = updateDetails.stream().collect(Collectors.toMap(CisDgimgApplyDetailAsNto::getId, Function.identity()));
        if (CollectionUtils.isNotEmpty(updateDetails)) {
            for (CisDgimgApplyDetailTo cisDgimgApplyDetail : cisDgimgApplyTo.getCisDgimgApplyDetails()) {
                if (!updateDetailMap.containsKey(cisDgimgApplyDetail.getId())) {
                    deleteDetailIds.add(cisDgimgApplyDetail.getId());
                }
            }
        }
        cisDgimgApplyEto.setDeleteDetailIds(deleteDetailIds);

        // 诊断重新赋值
        if (CollectionUtils.isNotEmpty(dgimgOrderDto.getOpdOrderBaseApplyAsTo().getApplyDiagnosisNtos())) {
            cisDgimgApplyEto.setApplyDiagnosisNtos(dgimgOrderDto.getOpdOrderBaseApplyAsTo().getApplyDiagnosisNtos());
            cisDgimgApplyEto.getApplyDiagnosisNtos().forEach(applyDiagnosisNto -> {
                if (StringUtils.isEmpty(applyDiagnosisNto.getId()))
                    applyDiagnosisNto.setId(HIPIDUtil.getNextIdString());
            });
        }

        // 患者病史重新赋值
        CisMedicalHistoryNto cisMedicalHistoryNto = new CisMedicalHistoryNto();
        cisMedicalHistoryNto.setId(dgimgOrderDto.getCisMedicalHistoryNto().getId());
        cisMedicalHistoryNto.setVisitCode(dgimgOrderDto.getVisitCode());
        cisMedicalHistoryNto.setChiefComplaint(dgimgOrderDto.getClinicalHistory());
        cisMedicalHistoryNto.setPastMedicalHistory(dgimgOrderDto.getPreviousPathologicalExamin());
        cisDgimgApplyEto.setCisMedicalHistoryNto(cisMedicalHistoryNto);

        cisOpdOrderEto.setCisBaseApply(cisDgimgApplyEto);

        if (cisDgimgApplyTo.getStatusCode().equals(CisStatusEnum.NEW)) {
            cisOpdOrderService.updateCisOpdOrder(dgimgOrderDto.getId(), cisOpdOrderEto);
        } else if (cisDgimgApplyTo.getStatusCode().equals(CisStatusEnum.BACK)) {
            cisOpdOrderService.updateSubmitOrder(dgimgOrderDto.getId(), cisOpdOrderEto);
        }
    }

    /**
     * 构建检验常用医嘱保存入参
     *
     * @param cisDgimgApplyToList
     * @return
     */
    private List<CisOrderCommonNto> getCisOrderCommonNto(List<CisDgimgApplyTo> cisDgimgApplyToList) {
        List<CisOrderCommonNto> cisOrderCommonNtos = new ArrayList<>();

        for (CisDgimgApplyTo cisDgimgApplyTo : cisDgimgApplyToList) {
            for (CisDgimgApplyDetailTo cisDgimgApplyDetailTo : cisDgimgApplyTo.getCisDgimgApplyDetails()) {
                CisOrderCommonNto cisOrderCommonNto = new CisOrderCommonNto();
                cisOrderCommonNto.setServiceItemCode(cisDgimgApplyDetailTo.getDgimgCode());
                cisOrderCommonNto.setServiceItemName(cisDgimgApplyDetailTo.getDgimgName());
                cisOrderCommonNto.setSystemType(SystemTypeEnum.DGIMG);
                cisOrderCommonNto.setDocCode(HIPSecurityUtils.getLoginStaffId());
                cisOrderCommonNto.setId(HIPIDUtil.getNextIdString());
                cisOrderCommonNto.setOrgCode(cisDgimgApplyTo.getCreateOrgCode());
                cisOrderCommonNto.setSaveFlag(false);
                cisOrderCommonNto.setIntegral(Long.valueOf("1"));
                cisOrderCommonNto.setExtCode(cisDgimgApplyDetailTo.getExtTypeCode());
                cisOrderCommonNtos.add(cisOrderCommonNto);
            }
        }

        return cisOrderCommonNtos;
    }
}
