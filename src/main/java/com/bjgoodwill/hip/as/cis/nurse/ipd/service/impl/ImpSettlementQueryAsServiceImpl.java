package com.bjgoodwill.hip.as.cis.nurse.ipd.service.impl;

import com.bjgoodwill.hip.as.cis.nurse.ipd.service.ImpSettlementQueryAsService;
import com.bjgoodwill.hip.as.cis.nurse.ipd.to.*;
import com.bjgoodwill.hip.business.util.enums.dict.DictCodeEnum;
import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.common.util.HIPBeanUtil;
import com.bjgoodwill.hip.ds.econ.ipd.bill.settle.service.EconIpdSettleService;
import com.bjgoodwill.hip.ds.econ.ipd.bill.settle.to.*;
import com.bjgoodwill.hip.ds.mi.ipd.fee.service.MiIpdFeelistReimService;
import com.bjgoodwill.hip.ds.mi.ipd.fee.to.MiIpdFeelistReimTo;
import com.bjgoodwill.hip.ds.org.api.service.WorkGroupService;
import com.bjgoodwill.hip.ds.org.api.to.*;
import com.bjgoodwill.hip.ds.pat.in.hospital.inpatient.service.PatIpdInpatientService;
import com.bjgoodwill.hip.ds.pat.in.hospital.inpatient.to.PatIpdInpatientQto;
import com.bjgoodwill.hip.ds.pat.in.hospital.inpatient.to.PatIpdInpatientTo;
import com.bjgoodwill.hip.ds.term.api.service.DictElementService;
import com.bjgoodwill.hip.ds.term.api.to.DictElementTo;
import io.micrometer.common.util.StringUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/4/7 15:46
 */
@Service("com.bjgoodwill.hip.as.cis.nurse.ipd.service.ImpSettlementQueryAsService")
public class ImpSettlementQueryAsServiceImpl implements ImpSettlementQueryAsService {

    @Autowired
    private PatIpdInpatientService patIpdInpatientService;
    @Autowired
    private EconIpdSettleService econIpdSettleService;
    @Autowired
    private DictElementService dictElementService;
    @Autowired
    private WorkGroupService workGroupService;
    @Autowired
    private MiIpdFeelistReimService miIpdFeelistReimService;

    @Override
    public GridResultSet<PatIpdInpatientTo> getInOutHospitalPat(ImpSettlementAsQto impSettlementAsQto) {

        //查询结算患者信息
        EconIpdSettleQto econIpdSettleQto = new EconIpdSettleQto();
        econIpdSettleQto.setEnabled(true);
        econIpdSettleQto.setMinQueryDate(impSettlementAsQto.getStartDate());
        econIpdSettleQto.setMaxQueryDate(impSettlementAsQto.getEndDate());
        List<EconIpdSettleTo> econIpdSettles = econIpdSettleService.getEconIpdSettles(econIpdSettleQto);
        List<String> visitCodes = econIpdSettles.stream().map(EconIpdSettleTo::getVisitCode).distinct().toList();
        if (CollectionUtils.isNotEmpty(visitCodes)) {
            //分页查询患者信息
            PatIpdInpatientQto patIpdInpatientQto = new PatIpdInpatientQto();
            if (StringUtils.isNotEmpty(impSettlementAsQto.getNurseDeptCode())) {
                patIpdInpatientQto.setDeptNurseCode(impSettlementAsQto.getNurseDeptCode());
            }
            if (StringUtils.isNotEmpty(impSettlementAsQto.getInpatientCode())) {
                patIpdInpatientQto.setInpatientCode(impSettlementAsQto.getInpatientCode());
            }
            if (StringUtils.isNotEmpty(impSettlementAsQto.getFeeType())) {
                patIpdInpatientQto.setFeeType(impSettlementAsQto.getFeeType());
            }
            patIpdInpatientQto.setVisitCodes(visitCodes);
            patIpdInpatientQto.setPageNum(impSettlementAsQto.getPageNum());
            patIpdInpatientQto.setPageSize(impSettlementAsQto.getPageSize());
            GridResultSet<PatIpdInpatientTo> patIpdInpatientPage = patIpdInpatientService.getPatIpdInpatientPage(patIpdInpatientQto);
            if (CollectionUtils.isNotEmpty(patIpdInpatientPage.getResult())) {
                List<DictElementTo> customDictElement = dictElementService.getCustomDictElement(DictCodeEnum.费别.getCode());
                List<PatIpdInpatientTo> patIpds = patIpdInpatientPage.getResult();
                patIpds.forEach(pat -> {
                    List<DictElementTo> list = customDictElement.stream().filter(a -> a.getElementCode().equals(pat.getFeeType())).toList();
                    if (CollectionUtils.isNotEmpty(list)) {
                        pat.setFeeType(list.get(0).getElementName());
                    }
                });
            }
            return patIpdInpatientPage;
        }
        return null;
    }

    @Override
    public List<PatSettlementDataAsTo> getPatSettlementData(PatSettlementDataAsQto patSettlementDataAsQto) {
        EconIpdSettleQto econIpdSettleQto = new EconIpdSettleQto();
        econIpdSettleQto.setEnabled(true);
        econIpdSettleQto.setVisitCode(patSettlementDataAsQto.getVisitCode());
        econIpdSettleQto.setMinQueryDate(patSettlementDataAsQto.getStartDate());
        econIpdSettleQto.setMaxQueryDate(patSettlementDataAsQto.getEndDate());
        List<EconIpdSettleTo> econIpdSettles = econIpdSettleService.getEconIpdSettles(econIpdSettleQto);

        PatIpdInpatientTo patIpdInPatientById = patIpdInpatientService.getPatIpdInpatientByVisitCode(patSettlementDataAsQto.getVisitCode());
        List<DictElementTo> customDictElement = dictElementService.getCustomDictElement(DictCodeEnum.费别.getCode());
        List<DictElementTo> list = customDictElement.stream().filter(a -> a.getElementCode().equals(patIpdInPatientById.getFeeType())).toList();
        List<PatSettlementDataAsTo> patSettlementDataAsTos = new ArrayList<>();
        econIpdSettles.forEach(econIpdSettle -> {
            PatSettlementDataAsTo patSettlementDataAsTo = HIPBeanUtil.copy(econIpdSettle, PatSettlementDataAsTo.class);
            if (CollectionUtils.isNotEmpty(list)) {
                patSettlementDataAsTo.setFeeType(list.get(0).getElementName());
            }
            if (econIpdSettle instanceof EconIpdMiSettleTo econIpdMiSettleTo) {
                patSettlementDataAsTo.setMiSettleId(econIpdMiSettleTo.getMiSettleId());
            }
            patSettlementDataAsTo.setNurseDeptCode(patIpdInPatientById.getDeptNurseName());
            patSettlementDataAsTo.setMasterDoctorName(patIpdInPatientById.getMasterDoctorName());
            patSettlementDataAsTo.setInTime(patIpdInPatientById.getInTime());
            patSettlementDataAsTo.setOutTime(patIpdInPatientById.getOutTime());
            patSettlementDataAsTo.setPatType("住院");
            patSettlementDataAsTos.add(patSettlementDataAsTo);
        });
        return patSettlementDataAsTos;
    }

    @Override
    public EconIpdSettleBillAllAsTo getPatSettlementDetail(PatSettlementDetailAsQto patSettlementDetailAsQto) {
        EconIpdSettleBillAllAsTo econIpdSettleBillAllAsTo = new EconIpdSettleBillAllAsTo();

        //费用明细
        EconIpdSettleBillQto econIpdSettleBillQto = new EconIpdSettleBillQto();
        econIpdSettleBillQto.setSettleId(patSettlementDetailAsQto.getSettleId());
        econIpdSettleBillQto.setPageSize(patSettlementDetailAsQto.getPageSize());
        econIpdSettleBillQto.setPageNum(patSettlementDetailAsQto.getPageNum());
        GridResultSet<EconIpdSettleBillAllTo> settleBillAllPage = econIpdSettleService.getSettleBillAllPage(econIpdSettleBillQto);
        GridResultSet<EconIpdSettleDetailAsTo> econIpdSettleDetails = new GridResultSet<>();
        econIpdSettleDetails.setPageNum(settleBillAllPage.getPageNum());
        econIpdSettleDetails.setPageSize(settleBillAllPage.getPageSize());
        econIpdSettleDetails.setTotal(settleBillAllPage.getTotal());
        econIpdSettleDetails.setRecords(settleBillAllPage.getRecords());
        if (CollectionUtils.isNotEmpty(settleBillAllPage.getResult())) {
            List<EconIpdSettleBillAllTo> econIpdSettleBillAllTo = settleBillAllPage.getResult();
            List<EconIpdSettleDetailAsTo> econIpdSettleDetailAsTos = HIPBeanUtil.copy(econIpdSettleBillAllTo, EconIpdSettleDetailAsTo.class);

            if (StringUtils.isNotEmpty(patSettlementDetailAsQto.getMiSettleId())) {
                List<MiIpdFeelistReimTo> miIpdFeelistReimsByMdtrtId = miIpdFeelistReimService.getMiIpdFeelistReimsByMdtrtId(patSettlementDetailAsQto.getMiSettleId());
                econIpdSettleDetailAsTos.forEach(econIpdSettleDetailAsTo -> {
                    List<MiIpdFeelistReimTo> miIpdFeelistReimTos = miIpdFeelistReimsByMdtrtId.stream().filter(feeList -> econIpdSettleDetailAsTo.getDetailId().equals(feeList.getFeedetlSn())).toList();
                    econIpdSettleDetailAsTo.setInsuranceTypeValue(CollectionUtils.isNotEmpty(miIpdFeelistReimTos) ? miIpdFeelistReimTos.get(0).getChrgitmLvName() : null);
                });
            }

            List<EconIpdSettleDetailAsTo> list = econIpdSettleDetailAsTos.stream().sorted(Comparator
                    // 根据收费时间倒序排序
                    .comparing(EconIpdSettleDetailAsTo::getDetailCreatedDate, Comparator.nullsLast(Comparator.reverseOrder()))
            ).toList();
            econIpdSettleDetails.setResult(list);
        }
        econIpdSettleBillAllAsTo.setEconIpdSettleBillAlls(econIpdSettleDetails);
        //费用汇总
        List<EconIpdSettleBillAllTo> econIpdSettleBillAlls = econIpdSettleService.getEconIpdSettleBillAlls(patSettlementDetailAsQto.getSettleId());
        if (CollectionUtils.isNotEmpty(econIpdSettleBillAlls)) {
            List<BillingItemsAsTo> billingItemsAsTos = new ArrayList<>();
            Map<String, List<EconIpdSettleBillAllTo>> collectByFeeClass = econIpdSettleBillAlls.stream().collect(Collectors.groupingBy(EconIpdSettleBillAllTo::getFeeClass));
            collectByFeeClass.forEach((feeClass, econIpdSettleBillAllTos) -> {
                BillingItemsAsTo billingItemsAsTo = new BillingItemsAsTo();
                billingItemsAsTo.setFeeClassName(econIpdSettleBillAllTos.get(0).getFeeClassName());
                BigDecimal reduce = econIpdSettleBillAllTos.stream().map(EconIpdSettleBillAllTo::getCosts).reduce(BigDecimal.ZERO, BigDecimal::add);
                billingItemsAsTo.setTotalAmount(reduce);
                billingItemsAsTos.add(billingItemsAsTo);
            });
            if (CollectionUtils.isNotEmpty(billingItemsAsTos)) {
                BillingItemsAsTo billingItemsAsTo = new BillingItemsAsTo();
                BigDecimal reduce = billingItemsAsTos.stream().map(BillingItemsAsTo::getTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                billingItemsAsTo.setFeeClassName("合计");
                billingItemsAsTo.setTotalAmount(reduce);
                billingItemsAsTos.add(billingItemsAsTo);
            }
            econIpdSettleBillAllAsTo.setBillingItemsAsTos(billingItemsAsTos);
        }
        //查询结算单信息
        EconIpdSettleTo econIpdSettleTo = econIpdSettleService.getById(patSettlementDetailAsQto.getSettleId());
        //发票明细
        List<EconIpdSettleInvoiceTo> econIpdSettleInvoices = econIpdSettleTo.getEconIpdSettleInvoices();
        List<EconIpdSettleInvoiceAsTo> econIpdSettleInvoiceAsTos = new ArrayList<>();
        for (EconIpdSettleInvoiceTo econIpdSettleInvoice : econIpdSettleInvoices) {
            if (econIpdSettleInvoice instanceof EconIpdSettlePlainInvoiceTo) {
                //纸质发票
                EconIpdSettleInvoiceAsTo econIpdSettleInvoiceAsTo = HIPBeanUtil.copy(econIpdSettleInvoice, EconIpdSettleInvoiceAsTo.class);
                econIpdSettleInvoiceAsTo.setInvoiceClass("纸质发票");
                econIpdSettleInvoiceAsTos.add(econIpdSettleInvoiceAsTo);
            }
            if (econIpdSettleInvoice instanceof EconIpdSettleEleInvoiceTo) {
                //电子发票
                EconIpdSettleInvoiceAsTo econIpdSettleInvoiceAsTo = HIPBeanUtil.copy(econIpdSettleInvoice, EconIpdSettleInvoiceAsTo.class);
                econIpdSettleInvoiceAsTo.setInvoiceClass("电子发票");
                econIpdSettleInvoiceAsTos.add(econIpdSettleInvoiceAsTo);
            }
        }
        econIpdSettleBillAllAsTo.setEconIpdSettleInvoices(econIpdSettleInvoiceAsTos);
        //支付明细
        econIpdSettleBillAllAsTo.setEconIpdSettlePays(econIpdSettleTo.getEconIpdSettlePays());
        return econIpdSettleBillAllAsTo;
    }

    @Override
    public EconIpdSettleBillAllAsTo getPatSettlementFeeDetail(PatSettlementDetailAsQto patSettlementDetailAsQto) {
        EconIpdSettleBillAllAsTo econIpdSettleBillAll = new EconIpdSettleBillAllAsTo();
        EconIpdSettleBillQto econIpdSettleBillQto = new EconIpdSettleBillQto();
        econIpdSettleBillQto.setPageNum(patSettlementDetailAsQto.getPageNum());
        econIpdSettleBillQto.setPageSize(patSettlementDetailAsQto.getPageSize());
        econIpdSettleBillQto.setSettleId(patSettlementDetailAsQto.getSettleId());
        econIpdSettleBillQto.setFeeClass(patSettlementDetailAsQto.getFeeClass());
        econIpdSettleBillQto.setName(patSettlementDetailAsQto.getName());
        econIpdSettleBillQto.setVisitOrg(patSettlementDetailAsQto.getCreateOrg());
        econIpdSettleBillQto.setExecuteOrg(patSettlementDetailAsQto.getExecuteOrg());
        //费用明细
        GridResultSet<EconIpdSettleBillAllTo> econIpdSettleBillAlls = econIpdSettleService.getSettleBillAllPage(econIpdSettleBillQto);
        GridResultSet<EconIpdSettleDetailAsTo> econIpdSettleDetails = new GridResultSet<>();
        econIpdSettleDetails.setPageNum(econIpdSettleBillAlls.getPageNum());
        econIpdSettleDetails.setPageSize(econIpdSettleBillAlls.getPageSize());
        econIpdSettleDetails.setTotal(econIpdSettleBillAlls.getTotal());
        econIpdSettleDetails.setRecords(econIpdSettleBillAlls.getRecords());
        if (CollectionUtils.isNotEmpty(econIpdSettleBillAlls.getResult())) {
            List<EconIpdSettleBillAllTo> econIpdSettleBillAllTo = econIpdSettleBillAlls.getResult();
            List<EconIpdSettleDetailAsTo> econIpdSettleDetailAsTos = HIPBeanUtil.copy(econIpdSettleBillAllTo, EconIpdSettleDetailAsTo.class);

            if (StringUtils.isNotEmpty(patSettlementDetailAsQto.getMiSettleId())) {
                List<MiIpdFeelistReimTo> miIpdFeelistReimsByMdtrtId = miIpdFeelistReimService.getMiIpdFeelistReimsByMdtrtId(patSettlementDetailAsQto.getMiSettleId());
                econIpdSettleDetailAsTos.forEach(econIpdSettleDetailAsTo -> {
                    List<MiIpdFeelistReimTo> miIpdFeelistReimTos = miIpdFeelistReimsByMdtrtId.stream().filter(feeList -> econIpdSettleDetailAsTo.getDetailId().equals(feeList.getFeedetlSn())).toList();
                    econIpdSettleDetailAsTo.setInsuranceTypeValue(CollectionUtils.isNotEmpty(miIpdFeelistReimTos) ? miIpdFeelistReimTos.get(0).getChrgitmLvName() : null);
                });
            }

            List<EconIpdSettleDetailAsTo> list = econIpdSettleDetailAsTos.stream().sorted(Comparator
                    // 根据收费时间倒序排序
                    .comparing(EconIpdSettleDetailAsTo::getDetailCreatedDate, Comparator.nullsLast(Comparator.reverseOrder()))
            ).toList();
            econIpdSettleDetails.setResult(list);
        }
        econIpdSettleBillAll.setEconIpdSettleBillAlls(econIpdSettleDetails);
        return econIpdSettleBillAll;
    }

    @Override
    public List<DeptDataSourcesAsTo> getExeDept() {
        List<WorkGroupPharmacyTo> result = workGroupService.getEnablePharmacy();
        List<DeptDataSourcesAsTo> deptDataSourcesAsTos = new ArrayList<>();
        result.forEach(workGroupPharmacyTo -> {
            DeptDataSourcesAsTo deptDataSourcesAsTo = new DeptDataSourcesAsTo();
            deptDataSourcesAsTo.setName(workGroupPharmacyTo.getName());
            deptDataSourcesAsTo.setCode(workGroupPharmacyTo.getId());
            deptDataSourcesAsTos.add(deptDataSourcesAsTo);
        });
        List<WorkGroupInpatientNursingTo> workGroupInpatientDeptTos = workGroupService.getInpatientNursing(new WorkGroupInpatientNursingQto());
        workGroupInpatientDeptTos.forEach(workGroupInpatientDeptTo -> {
            DeptDataSourcesAsTo deptDataSourcesAsTo = new DeptDataSourcesAsTo();
            deptDataSourcesAsTo.setName(workGroupInpatientDeptTo.getName());
            deptDataSourcesAsTo.setCode(workGroupInpatientDeptTo.getId());
            deptDataSourcesAsTos.add(deptDataSourcesAsTo);
        });
        return deptDataSourcesAsTos;
    }

}
