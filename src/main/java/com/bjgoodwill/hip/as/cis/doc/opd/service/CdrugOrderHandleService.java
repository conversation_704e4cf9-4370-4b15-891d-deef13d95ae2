package com.bjgoodwill.hip.as.cis.doc.opd.service;

import com.bjgoodwill.hip.as.cis.doc.opd.to.cdrug.CDrugOrderDto;
import com.bjgoodwill.hip.as.cis.doc.opd.to.cdrug.CdrugOpenParameterTo;
import com.bjgoodwill.hip.as.cis.doc.opd.to.doctor.OrderIssuedPickUpsAsTo;

import java.util.List;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @description :
 * @createDate : 2025/5/6 14:46
 */
public interface CdrugOrderHandleService {

    /**
     * 获取草药医嘱开立参数信息
     *
     * @return
     */
    CdrugOpenParameterTo getCdrugOpenParameter();

    /**
     * 查询草药库存信息
     *
     * @return
     */
    List<OrderIssuedPickUpsAsTo> searchCdrugOrderItems(String executeOrgCode);

    /**
     * 新增草药医嘱
     *
     * @param cDrugOrderDto
     * @return
     */
    void insertCDrugOrder(CDrugOrderDto cDrugOrderDto);

    /**
     * 新增并签发草药医嘱
     *
     * @param cDrugOrderDto
     * @return
     */
    void insertAndSubmitCDrugOrder(CDrugOrderDto cDrugOrderDto);

    /**
     * 查询草药医嘱及申请单信息
     *
     * @param orderId
     * @return
     */
    CDrugOrderDto getOpdCdrugOrder(String orderId);

    /**
     * 修改草药医嘱
     *
     * @param cDrugOrderDto
     */
    void updateCDrugOrder(CDrugOrderDto cDrugOrderDto);

    /**
     * 修改草药医嘱并签发
     *
     * @param cDrugOrderDto
     */
    void updateAndSubmitCDrugOrder(CDrugOrderDto cDrugOrderDto);
}
