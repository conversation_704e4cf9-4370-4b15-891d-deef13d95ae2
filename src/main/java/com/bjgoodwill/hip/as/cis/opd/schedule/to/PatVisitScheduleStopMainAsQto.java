package com.bjgoodwill.hip.as.cis.opd.schedule.to;

import com.bjgoodwill.hip.common.bean.BaseQto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;

@Schema(description = "门诊出诊排班停诊处理预约：主页面查询条件")
public class PatVisitScheduleStopMainAsQto extends BaseQto implements Serializable {

    @Serial
    private static final long serialVersionUID = -2919456713681131080L;

    @Schema(description = "出诊院区编码")
    private String hospitalAreaCode;

    @Schema(description = "开始日期")
    private LocalDate begnVisitDate;

    @Schema(description = "结束日期")
    private LocalDate endVisitDate;

    @NotNull(message = "院区编码不能为空")
    public String getHospitalAreaCode() {
        return hospitalAreaCode;
    }

    public void setHospitalAreaCode(String hospitalAreaCode) {
        this.hospitalAreaCode = hospitalAreaCode;
    }

    @NotNull(message = "开始日期不能为空")
    public LocalDate getBegnVisitDate() {
        return begnVisitDate;
    }

    public void setBegnVisitDate(LocalDate begnVisitDate) {
        this.begnVisitDate = begnVisitDate;
    }

    @NotNull(message = "结束日期不能为空")
    public LocalDate getEndVisitDate() {
        return endVisitDate;
    }

    public void setEndVisitDate(LocalDate endVisitDate) {
        this.endVisitDate = endVisitDate;
    }
}