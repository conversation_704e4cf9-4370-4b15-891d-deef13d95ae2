package com.bjgoodwill.hip.as.cis.doc.ipd.controller;

import com.bjgoodwill.hip.as.cis.doc.ipd.service.OrderIssuedPickUpsAsService;
import com.bjgoodwill.hip.as.cis.doc.ipd.to.OrderIssuedPickUpsAsNto;
import com.bjgoodwill.hip.as.cis.doc.ipd.to.OrderIssuedPickUpsAsQto;
import com.bjgoodwill.hip.as.cis.doc.ipd.to.OrderIssuedPickUpsAsTo;
import com.bjgoodwill.hip.ds.cis.cds.order.service.CisOrderCommonService;
import com.bjgoodwill.hip.ds.cis.cds.order.to.CisOrderCommonTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Author: zhangyunchuan
 * @Date: 2024/10/28 16:09
 * @PROJECT: hip-ac
 */
@RestController
@Tag(name = "住院医嘱拾取器应用服务", description = "住院医嘱拾取器服务类")
@RequestMapping("/cis/doc/ipd/issuedPickUps")
public class OrderIssuedPickUpsController {

    @Autowired
    private OrderIssuedPickUpsAsService orderIssuedPickUpsAsService;

    @Autowired
    private CisOrderCommonService cisOrderCommonService;

    @Operation(summary = "医嘱项目信息查询", description = "查询条件:医嘱类型,文本")
    @ApiResponse(description = "医嘱类型选择全部的时候传ALL", content = @Content(mediaType = "application/json", schema = @Schema(implementation = OrderIssuedPickUpsAsTo.class)))
    @PostMapping(value = "/searchOrderItems")
    public List<OrderIssuedPickUpsAsTo> searchOrderItems(@RequestBody @Valid OrderIssuedPickUpsAsQto orderIssuedPickUpsAsQto) {
        return orderIssuedPickUpsAsService.searchOrderItems(orderIssuedPickUpsAsQto);
    }

    @Operation(summary = "收藏医嘱项目", description = "")
    @ApiResponse(description = "会返回收藏项目实体(包含收藏ID)", content = @Content(mediaType = "application/json"))
    @PostMapping(value = "/collection")
    public CisOrderCommonTo collection(@RequestBody @Valid OrderIssuedPickUpsAsNto orderIssuedPickUpsAsNto) {
        return orderIssuedPickUpsAsService.collection(orderIssuedPickUpsAsNto);
    }

    @Operation(summary = "取消收藏医嘱项目", description = "条件:collectionID")
    @ApiResponse(description = "", content = @Content(mediaType = "application/json"))
    @GetMapping(value = "/cancelCollection/{id}")
    public void cancelCollection(@PathVariable("id") String id) {
        cisOrderCommonService.deleteCisOrderCommon(id);
    }

}
