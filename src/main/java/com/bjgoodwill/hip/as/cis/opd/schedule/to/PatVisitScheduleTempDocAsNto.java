package com.bjgoodwill.hip.as.cis.opd.schedule.to;

import com.alibaba.fastjson2.annotation.JSONField;
import com.bjgoodwill.hip.as.cis.opd.visitresource.to.PatVisitResourceAsNto;
import com.bjgoodwill.hip.as.cis.opd.visitresource.to.PatVisitResourceListAsNto;
import com.bjgoodwill.hip.ds.pat.schedule.enmus.PatVisitDayEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDate;

/**
 * Description:
 *
 * <AUTHOR>
 * &#064;date 2025/3/28 下午3:02
 */
@Schema(description = "门诊出诊排班：新增临时医生号排班")
public class PatVisitScheduleTempDocAsNto extends PatVisitResourceAsNto {

    @Schema(description = "院区编码")
    private String hospitalAreaCode;
    @Schema(description = "院区名称")
    private String hospitalAreaName;
    @Schema(description = "出诊日期")
    private LocalDate visitDate;
    @Schema(description = "出诊日")
    private String visitDay;

    @Schema(description = "是否临时排班")
    private boolean tempFlag = false;

    public String getHospitalAreaCode() {
        return hospitalAreaCode;
    }

    public void setHospitalAreaCode(String hospitalAreaCode) {
        this.hospitalAreaCode = hospitalAreaCode;
    }

    public String getHospitalAreaName() {
        return hospitalAreaName;
    }

    public void setHospitalAreaName(String hospitalAreaName) {
        this.hospitalAreaName = hospitalAreaName;
    }

    public LocalDate getVisitDate() {
        return visitDate;
    }

    public void setVisitDate(LocalDate visitDate) {
        this.visitDate = visitDate;
    }

    public String getVisitDay() {
        return visitDay;
    }

    public void setVisitDay(String visitDay) {
        this.visitDay = visitDay;
    }

    public boolean isTempFlag() {
        return tempFlag;
    }

    public void setTempFlag(boolean tempFlag) {
        this.tempFlag = tempFlag;
    }
}
