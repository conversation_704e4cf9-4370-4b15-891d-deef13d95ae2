package com.bjgoodwill.hip.as.cis.doc.ipd.to.consultation;

import com.bjgoodwill.hip.business.util.cis.common.enums.CisConsultationApplyStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2024/12/16 15:56
 * @ClassName: ConsultationManageAsQTo
 * @Description: 会诊患者查询
 */
@Schema(description = "会诊患者查询")
public class  ConsultationManageAsQTo implements Serializable {

    @Schema(description = "患者姓名")
    private String patName;

    @Schema(description = "住院号")
    private String inpatientCode;

    @Schema(description = "开始时间")
    private String beginDate;

    @Schema(description = "结束时间")
    private String endDate;

    @Schema(description = "会诊状态")
    private String cnsStatus;

    public String getPatName() {
        return patName;
    }

    public void setPatName(String patName) {
        this.patName = patName;
    }

    public String getInpatientCode() {
        return inpatientCode;
    }

    public void setInpatientCode(String inpatientCode) {
        this.inpatientCode = inpatientCode;
    }

    public String getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(String beginDate) {
        this.beginDate = beginDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getCnsStatus() {
        return cnsStatus;
    }

    public void setCnsStatus(String cnsStatus) {
        this.cnsStatus = cnsStatus;
    }
}
