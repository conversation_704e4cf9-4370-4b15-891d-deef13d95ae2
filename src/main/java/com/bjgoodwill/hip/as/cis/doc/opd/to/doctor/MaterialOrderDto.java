package com.bjgoodwill.hip.as.cis.doc.opd.to.doctor;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "门诊材料医嘱Dto")
public class MaterialOrderDto extends OpdOrderAsTo {

    @Schema(description = "高值")
    private Boolean highFlag;

    @Schema(description = "高值编码")
    private String barCode;

    public Boolean getHighFlag() {
        return highFlag;
    }

    public void setHighFlag(Boolean highFlag) {
        this.highFlag = highFlag;
    }

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }
}
