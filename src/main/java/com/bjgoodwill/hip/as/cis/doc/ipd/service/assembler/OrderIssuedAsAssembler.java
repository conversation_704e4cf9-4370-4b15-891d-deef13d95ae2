package com.bjgoodwill.hip.as.cis.doc.ipd.service.assembler;

import com.bjgoodwill.hip.as.cis.doc.ipd.enums.CisDocIpdBusinessErrorEnum;
import com.bjgoodwill.hip.as.cis.doc.ipd.to.*;
import com.bjgoodwill.hip.as.cis.doc.ipd.util.IpdOrderExecuteOrgUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.*;
import com.bjgoodwill.hip.business.util.econ.enums.SystemItemClassEnum;
import com.bjgoodwill.hip.common.bean.LoginInfo;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.common.util.HIPIDUtil;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.*;
import com.bjgoodwill.hip.ds.cis.apply.blood.to.CisBloodApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.blood.to.CisBloodComponentNto;
import com.bjgoodwill.hip.ds.cis.apply.blood.to.CisInputBloodApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.charge.enmus.CisChargeTypeEnum;
import com.bjgoodwill.hip.ds.cis.apply.charge.to.CisApplyChargeEto;
import com.bjgoodwill.hip.ds.cis.apply.charge.to.CisApplyChargeNto;
import com.bjgoodwill.hip.ds.cis.apply.dgimg.to.CisDgimgApplyDetailNto;
import com.bjgoodwill.hip.ds.cis.apply.dgimg.to.CisDgimgApplyEto;
import com.bjgoodwill.hip.ds.cis.apply.dgimg.to.CisDgimgApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.diag.to.ApplyDiagnosisNto;
import com.bjgoodwill.hip.ds.cis.apply.drug.to.CisCDrugApplyEto;
import com.bjgoodwill.hip.ds.cis.apply.drug.to.CisCDrugApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.drug.to.CisDrugApplyDetailNto;
import com.bjgoodwill.hip.ds.cis.apply.drug.to.CisEDrugApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.operation.to.CisOperationApplyDetailNto;
import com.bjgoodwill.hip.ds.cis.apply.operation.to.CisOperationApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.skin.to.CisSkinApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.spcobs.to.CisSpcobsApplyDetailEto;
import com.bjgoodwill.hip.ds.cis.apply.spcobs.to.CisSpcobsApplyDetailNto;
import com.bjgoodwill.hip.ds.cis.apply.spcobs.to.CisSpcobsApplyEto;
import com.bjgoodwill.hip.ds.cis.apply.spcobs.to.CisSpcobsApplyNto;
import com.bjgoodwill.hip.ds.cis.cpoe.order.to.CisIpdOrderNto;
import com.bjgoodwill.hip.ds.cis.cpoe.order.to.CisLongTermOrderNto;
import com.bjgoodwill.hip.ds.cis.cpoe.order.to.CisTemporaryOrderEto;
import com.bjgoodwill.hip.ds.cis.cpoe.order.to.CisTemporaryOrderNto;
import com.bjgoodwill.hip.ds.cis.medicineitem.price.service.ServiceClinicPriceService;
import com.bjgoodwill.hip.ds.cis.medicineitem.price.to.ServiceClinicPriceTo;
import com.bjgoodwill.hip.ds.cis.medicineitem.serviceItem.service.ServiceClinicItemService;
import com.bjgoodwill.hip.ds.cis.medicineitem.serviceItem.to.ServiceClinicItemTo;
import com.bjgoodwill.hip.ds.econ.price.service.EconServicePriceService;
import com.bjgoodwill.hip.ds.econ.price.to.EconServicePriceTo;
import com.bjgoodwill.hip.ds.org.api.service.StaffService;
import com.bjgoodwill.hip.ds.org.api.service.WorkGroupService;
import com.bjgoodwill.hip.ds.org.api.to.StaffLocalTo;
import com.bjgoodwill.hip.ds.org.api.to.WorkGroupTo;
import com.bjgoodwill.hip.enums.OwnDrugEnum;
import com.bjgoodwill.hip.security.bean.CurrentOrgInfo;
import com.bjgoodwill.hip.security.util.HIPCurrentOrgUtils;
import com.bjgoodwill.hip.security.util.HIPSecurityUtils;
import io.micrometer.common.util.StringUtils;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * @Author: zhangyunchuan
 * @Date: 2024/11/7 9:59
 * @PROJECT: hip-ac
 */
@Tag(name = "医嘱开立应用服务-汇编", description = "医嘱开立服务类-汇编")
@Service
public class OrderIssuedAsAssembler {

    @Resource
    private CDrugApplyIssuedAsAssembler cDrugApplyIssuedAsAssembler;

    @Resource
    private OperationApplyIssuedAsAssembler operationApplyIssuedAsAssembler;

    @Resource(name = "com.bjgoodwill.hip.ds.cis.medicineitem.serviceItem.service.ServiceClinicItemTangibleService")
    private ServiceClinicItemService serviceClinicItemService;

    @Autowired
    private IpdOrderExecuteOrgUtil ipdOrderExecuteOrgUtil;

    @Resource
    private WorkGroupService workGroupService;

    @Resource
    private DgimgApplyIssuedAsAssembler dgimgApplyIssuedAsAssembler;

    @Autowired
    private StaffService staffService;

    @Autowired
    private ServiceClinicPriceService serviceClinicPriceService;

    @Resource
    private EconServicePriceService econServicePriceService;

    /**
     * 治疗申请单
     */
    public CisTreatmentApplyNto cisTreatmentApplyNtoToTo(CisIpdDocOrderAsNto cisIpdDocOrderAsNto) {
        if (cisIpdDocOrderAsNto == null) {
            return null;
        }
        CisTreatmentApplyNto cisTreatmentApplyNto = new CisTreatmentApplyNto();
        cisTreatmentApplyNto.setId(cisIpdDocOrderAsNto.getApplyId());
        cisTreatmentApplyNto.setPatMiCode(cisIpdDocOrderAsNto.getPatMiCode());
        cisTreatmentApplyNto.setVisitCode(cisIpdDocOrderAsNto.getVisitCode());
        cisTreatmentApplyNto.setVisitType(VisitTypeEnum.IPD);
        cisTreatmentApplyNto.setDeptNurseCode(cisIpdDocOrderAsNto.getDeptNurseCode());
        cisTreatmentApplyNto.setDeptNurseName(cisIpdDocOrderAsNto.getDeptNurseName());
        cisTreatmentApplyNto.setOrderID(cisIpdDocOrderAsNto.getId());
        cisTreatmentApplyNto.setCreateOrgCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
        cisTreatmentApplyNto.setCreateOrgName(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupName());
        cisTreatmentApplyNto.setVisitOrgCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
        cisTreatmentApplyNto.setVisitOrgName(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupName());
        if (cisIpdDocOrderAsNto.getOrderType().equals("1")) {
            cisTreatmentApplyNto.setOrderType(OrderTypeEnum.LONG_TERM_ORDER);
        } else if (cisIpdDocOrderAsNto.getOrderType().equals("2")) {
            cisTreatmentApplyNto.setOrderType(OrderTypeEnum.TEMPORARY_ORDER);
        }
        cisTreatmentApplyNto.setFrequency(cisIpdDocOrderAsNto.getFrequency());
        cisTreatmentApplyNto.setFrequencyName(cisIpdDocOrderAsNto.getFrequencyName());
        cisTreatmentApplyNto.setNum(Double.valueOf(1));
        cisTreatmentApplyNto.setExecutorOrgCode(cisIpdDocOrderAsNto.getExecuteOrgCode());
        cisTreatmentApplyNto.setExecutorOrgName(cisIpdDocOrderAsNto.getExecuteOrgName());
        cisTreatmentApplyNto.setServiceItemName(cisIpdDocOrderAsNto.getServiceItemName());
        cisTreatmentApplyNto.setServiceItemCode(cisIpdDocOrderAsNto.getServiceItemCode());
        return cisTreatmentApplyNto;
    }

    /**
     * 处置申请单
     */
    public CisManagementApplyNto cisManagementApplyNtoToTo(CisIpdDocOrderAsNto cisIpdDocOrderAsNto) {
        if (cisIpdDocOrderAsNto == null) {
            return null;
        }
        CisManagementApplyNto cisManagementApplyNto = new CisManagementApplyNto();
        cisManagementApplyNto.setId(cisIpdDocOrderAsNto.getApplyId());
        cisManagementApplyNto.setPatMiCode(cisIpdDocOrderAsNto.getPatMiCode());
        cisManagementApplyNto.setVisitCode(cisIpdDocOrderAsNto.getVisitCode());
        cisManagementApplyNto.setVisitType(VisitTypeEnum.IPD);
        cisManagementApplyNto.setDeptNurseCode(cisIpdDocOrderAsNto.getDeptNurseCode());
        cisManagementApplyNto.setDeptNurseName(cisIpdDocOrderAsNto.getDeptNurseName());
        cisManagementApplyNto.setOrderID(cisIpdDocOrderAsNto.getId());
        cisManagementApplyNto.setCreateOrgCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
        cisManagementApplyNto.setCreateOrgName(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupName());
        cisManagementApplyNto.setVisitOrgCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
        cisManagementApplyNto.setVisitOrgName(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupName());
        if (cisIpdDocOrderAsNto.getOrderType().equals("1")) {
            cisManagementApplyNto.setOrderType(OrderTypeEnum.LONG_TERM_ORDER);
        } else if (cisIpdDocOrderAsNto.getOrderType().equals("2")) {
            cisManagementApplyNto.setOrderType(OrderTypeEnum.TEMPORARY_ORDER);
        }
        cisManagementApplyNto.setFrequency(cisIpdDocOrderAsNto.getFrequency());
        cisManagementApplyNto.setFrequencyName(cisIpdDocOrderAsNto.getFrequencyName());
        cisManagementApplyNto.setNum(cisIpdDocOrderAsNto.getNum());
        cisManagementApplyNto.setExecutorOrgCode(cisIpdDocOrderAsNto.getExecuteOrgCode());
        cisManagementApplyNto.setExecutorOrgName(cisIpdDocOrderAsNto.getExecuteOrgName());
        cisManagementApplyNto.setServiceItemName(cisIpdDocOrderAsNto.getServiceItemName());
        cisManagementApplyNto.setServiceItemCode(cisIpdDocOrderAsNto.getServiceItemCode());
        cisManagementApplyNto.setReMark(cisIpdDocOrderAsNto.getReMark());
        return cisManagementApplyNto;
    }

    /**
     * 皮试申请单
     */
    public CisSkinApplyNto cisSkinApplyNtoToTo(CisIpdDocOrderAsNto cisIpdDocOrderAsNto) {
        if (cisIpdDocOrderAsNto == null) {
            return null;
        }
        CisSkinApplyNto cisSkinApplyNto = new CisSkinApplyNto();
        cisSkinApplyNto.setDosage(cisIpdDocOrderAsNto.getDosage());
        cisSkinApplyNto.setDosageUnit(cisIpdDocOrderAsNto.getDosageUnit());
        cisSkinApplyNto.setUsage(cisIpdDocOrderAsNto.getUsage());
        cisSkinApplyNto.setUsageName(cisIpdDocOrderAsNto.getUsageName());
        cisSkinApplyNto.setReceiveOrg(cisIpdDocOrderAsNto.getReceiveOrgCode());
        cisSkinApplyNto.setId(cisIpdDocOrderAsNto.getApplyId());
        cisSkinApplyNto.setPatMiCode(cisIpdDocOrderAsNto.getPatMiCode());
        cisSkinApplyNto.setVisitCode(cisIpdDocOrderAsNto.getVisitCode());
        cisSkinApplyNto.setVisitType(VisitTypeEnum.IPD);
        cisSkinApplyNto.setDeptNurseCode(cisIpdDocOrderAsNto.getDeptNurseCode());
        cisSkinApplyNto.setDeptNurseName(cisIpdDocOrderAsNto.getDeptNurseName());
        cisSkinApplyNto.setOrderID(cisIpdDocOrderAsNto.getId());
        cisSkinApplyNto.setCreateOrgCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
        cisSkinApplyNto.setCreateOrgName(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupName());
        cisSkinApplyNto.setOrderType(OrderTypeEnum.TEMPORARY_ORDER);
        cisSkinApplyNto.setFrequency("ST");
        cisSkinApplyNto.setFrequencyName("立即");
        cisSkinApplyNto.setNum(1.0);
        cisSkinApplyNto.setExecutorOrgCode(cisIpdDocOrderAsNto.getExecuteOrgCode());
        cisSkinApplyNto.setExecutorOrgName(cisIpdDocOrderAsNto.getExecuteOrgName());
        cisSkinApplyNto.setServiceItemCode(cisIpdDocOrderAsNto.getServiceItemCode());
        cisSkinApplyNto.setServiceItemName(cisIpdDocOrderAsNto.getServiceItemName());
        cisSkinApplyNto.setVisitOrgCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
        cisSkinApplyNto.setVisitOrgName(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupName());
        return cisSkinApplyNto;
    }

    /**
     * 药品申请单
     */
    public CisEDrugApplyNto cisEDrugApplyNtoToTo(CisIpdDocOrderAsNto cisIpdDocOrderAsNto) {
        if (cisIpdDocOrderAsNto == null) {
            return null;
        }
        CisEDrugApplyNto cisEDrugApplyNto = new CisEDrugApplyNto();
        cisEDrugApplyNto.setDripSpeed(cisIpdDocOrderAsNto.getDripSpeed());
        cisEDrugApplyNto.setDripSpeedUnit(cisIpdDocOrderAsNto.getDripSpeedUnit());
        cisEDrugApplyNto.setUsage(cisIpdDocOrderAsNto.getUsage());
        cisEDrugApplyNto.setUsageName(cisIpdDocOrderAsNto.getUsageName());
        cisEDrugApplyNto.setReceiveOrg(cisIpdDocOrderAsNto.getReceiveOrgCode());
        cisEDrugApplyNto.setId(cisIpdDocOrderAsNto.getApplyId());
        cisEDrugApplyNto.setPatMiCode(cisIpdDocOrderAsNto.getPatMiCode());
        cisEDrugApplyNto.setVisitCode(cisIpdDocOrderAsNto.getVisitCode());
        cisEDrugApplyNto.setVisitType(VisitTypeEnum.IPD);
        cisEDrugApplyNto.setDeptNurseCode(cisIpdDocOrderAsNto.getDeptNurseCode());
        cisEDrugApplyNto.setDeptNurseName(cisIpdDocOrderAsNto.getDeptNurseName());
        cisEDrugApplyNto.setOrderID(cisIpdDocOrderAsNto.getId());
        cisEDrugApplyNto.setCreateOrgCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
        cisEDrugApplyNto.setCreateOrgName(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupName());
        if (cisIpdDocOrderAsNto.getOrderType().equals("1")) {
            cisEDrugApplyNto.setOrderType(OrderTypeEnum.LONG_TERM_ORDER);
        } else if (cisIpdDocOrderAsNto.getOrderType().equals("2")) {
            cisEDrugApplyNto.setOrderType(OrderTypeEnum.TEMPORARY_ORDER);
        }
        cisEDrugApplyNto.setFrequency(cisIpdDocOrderAsNto.getFrequency());
        cisEDrugApplyNto.setFrequencyName(cisIpdDocOrderAsNto.getFrequencyName());
        cisEDrugApplyNto.setNum(Double.valueOf(1));
        cisEDrugApplyNto.setExecutorOrgCode(cisIpdDocOrderAsNto.getExecuteOrgCode());
        cisEDrugApplyNto.setExecutorOrgName(cisIpdDocOrderAsNto.getExecuteOrgName());
        cisEDrugApplyNto.setVisitOrgCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
        cisEDrugApplyNto.setVisitOrgName(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupName());
//        cisEDrugApplyNto.setServiceItemCode();
//        cisEDrugApplyNto.setServiceItemName();
        return cisEDrugApplyNto;
    }

    /**
     * 嘱托申请单
     */
    public CisCommonNto cisEntrustNtoToTo(CisIpdDocOrderAsNto cisIpdDocOrderAsNto) {
        if (cisIpdDocOrderAsNto == null) {
            return null;
        }
        CisCommonNto cisCommonNto = new CisCommonNto();
        cisCommonNto.setSystemType(SystemTypeEnum.ENTRUST);
        cisCommonNto.setId(cisIpdDocOrderAsNto.getApplyId());
        cisCommonNto.setPatMiCode(cisIpdDocOrderAsNto.getPatMiCode());
        cisCommonNto.setVisitCode(cisIpdDocOrderAsNto.getVisitCode());
        cisCommonNto.setVisitType(VisitTypeEnum.IPD);
        cisCommonNto.setDeptNurseCode(cisIpdDocOrderAsNto.getDeptNurseCode());
        cisCommonNto.setDeptNurseName(cisIpdDocOrderAsNto.getDeptNurseName());
        cisCommonNto.setVisitOrgCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
        cisCommonNto.setVisitOrgName(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupName());
        cisCommonNto.setOrderID(cisIpdDocOrderAsNto.getId());
        cisCommonNto.setCreateOrgCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
        cisCommonNto.setCreateOrgName(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupName());
        if (cisIpdDocOrderAsNto.getOrderType().equals("1")) {
            cisCommonNto.setOrderType(OrderTypeEnum.LONG_TERM_ORDER);
        } else if (cisIpdDocOrderAsNto.getOrderType().equals("2")) {
            cisCommonNto.setOrderType(OrderTypeEnum.TEMPORARY_ORDER);
        }
        cisCommonNto.setFrequency(cisIpdDocOrderAsNto.getFrequency());
        cisCommonNto.setFrequencyName(cisIpdDocOrderAsNto.getFrequencyName());
        cisCommonNto.setNum(Double.valueOf(1));
        cisCommonNto.setExecutorOrgCode(cisIpdDocOrderAsNto.getExecuteOrgCode());
        cisCommonNto.setExecutorOrgName(cisIpdDocOrderAsNto.getExecuteOrgName());
        cisCommonNto.setServiceItemName(cisIpdDocOrderAsNto.getServiceItemName());
        cisCommonNto.setServiceItemCode(cisIpdDocOrderAsNto.getServiceItemCode());
        return cisCommonNto;
    }

    /**
     * 药品明细申请单
     */
    public CisDrugApplyDetailNto cisDrugApplyDetailNtoToTo(CisIpdDocOrderDetailsAsNto cisIpdDocOrderDetailsAsNto, CisIpdDocOrderAsNto cisIpdDocOrderAsNto) {
        if (cisIpdDocOrderDetailsAsNto == null) {
            return null;
        }
        CisDrugApplyDetailNto cisDrugApplyDetailNto = new CisDrugApplyDetailNto();
        cisDrugApplyDetailNto.setId(cisIpdDocOrderDetailsAsNto.getId());
        cisDrugApplyDetailNto.setVisitCode(cisIpdDocOrderAsNto.getVisitCode());
        cisDrugApplyDetailNto.setApplyId(cisIpdDocOrderAsNto.getApplyId());
        cisDrugApplyDetailNto.setDrugCode(cisIpdDocOrderDetailsAsNto.getPriceItemCode());
        cisDrugApplyDetailNto.setDrugName(cisIpdDocOrderDetailsAsNto.getPriceItemName());
        cisDrugApplyDetailNto.setDosage(cisIpdDocOrderDetailsAsNto.getDosage());
        cisDrugApplyDetailNto.setDosageUnit(cisIpdDocOrderDetailsAsNto.getDosageUnit());
        cisDrugApplyDetailNto.setDosageUnitName(cisIpdDocOrderDetailsAsNto.getDosageUnitValue());
        cisDrugApplyDetailNto.setPackageUnit(cisIpdDocOrderDetailsAsNto.getPackageUnit());
        cisDrugApplyDetailNto.setPackageUnitName(cisIpdDocOrderDetailsAsNto.getPackageUnitValue());
        if (StringUtils.isNotEmpty(cisIpdDocOrderAsNto.getReceiveOrgCode())) {
            cisDrugApplyDetailNto.setReceiveOrg(cisIpdDocOrderAsNto.getReceiveOrgCode());
        } else {
            cisDrugApplyDetailNto.setReceiveOrg(cisIpdDocOrderAsNto.getExecuteOrgCode());
        }
        cisDrugApplyDetailNto.setSbadmWay(SbadmWayEnum.valueOf(cisIpdDocOrderDetailsAsNto.getSbadmWay()));
        cisDrugApplyDetailNto.setIsSkin(cisIpdDocOrderDetailsAsNto.getSkinFlag());
        cisDrugApplyDetailNto.setDecoctMethodCode(cisIpdDocOrderDetailsAsNto.getDecoctMethodCode());
        cisDrugApplyDetailNto.setSortNo(cisIpdDocOrderDetailsAsNto.getSortNo());
        cisDrugApplyDetailNto.setPackageNum(cisIpdDocOrderDetailsAsNto.getPackageNum());
        cisDrugApplyDetailNto.setAntimicrobialsPurpose(cisIpdDocOrderDetailsAsNto.getAntimicrobialsPurpose());
        cisDrugApplyDetailNto.setExtTypeCode(cisIpdDocOrderDetailsAsNto.getExtCode());
        return cisDrugApplyDetailNto;
    }

    /**
     * 申请单收费信息
     */
    public CisApplyChargeNto cisApplyChargeNtoToTo(CisIpdDocOrderDetailsAsNto cisIpdDocOrderDetailsAsNto, CisIpdDocOrderAsNto cisIpdDocOrderAsNto) {
        if (cisIpdDocOrderDetailsAsNto == null) {
            return null;
        }
        CisApplyChargeNto cisApplyChargeNto = new CisApplyChargeNto();
        cisApplyChargeNto.setId(cisIpdDocOrderDetailsAsNto.getChargeId());
        cisApplyChargeNto.setOrderId(cisIpdDocOrderAsNto.getId());
        cisApplyChargeNto.setVisitCode(cisIpdDocOrderAsNto.getVisitCode());
        cisApplyChargeNto.setPriceItemCode(cisIpdDocOrderDetailsAsNto.getPriceItemCode());
        cisApplyChargeNto.setPriceItemName(cisIpdDocOrderDetailsAsNto.getPriceItemName());
        cisApplyChargeNto.setPrice(cisIpdDocOrderDetailsAsNto.getPrice());
        cisApplyChargeNto.setPackageSpec(cisIpdDocOrderDetailsAsNto.getDrugSpec());
        //单位
        cisApplyChargeNto.setUnit(cisIpdDocOrderDetailsAsNto.getUnit());
        //单位名称
        cisApplyChargeNto.setUnitName(cisIpdDocOrderDetailsAsNto.getUnitValue());
        if (cisIpdDocOrderAsNto.getOrderClass().equals(SystemTypeEnum.EDRUG.getCode())) {
            //单价(小包装单位)
            cisApplyChargeNto.setPrice(cisIpdDocOrderDetailsAsNto.getPrice().divide(BigDecimal.valueOf(cisIpdDocOrderDetailsAsNto.getPackageNum()), 2, RoundingMode.HALF_DOWN));
            if (cisIpdDocOrderAsNto.getOrderType().equals("1")) {
                //长嘱
                if (cisIpdDocOrderDetailsAsNto.getSbadmWay().equals(SbadmWayEnum.WHOLETAKE.getCode())) {
                    //数量(大包装数量)
                    BigDecimal divide = BigDecimal.valueOf(cisIpdDocOrderDetailsAsNto.getNum()).divide(BigDecimal.valueOf(cisIpdDocOrderDetailsAsNto.getPackageNum()), 0, RoundingMode.UP);
                    //num为小包装数量
                    cisApplyChargeNto.setNum(divide.multiply(BigDecimal.valueOf(cisIpdDocOrderDetailsAsNto.getPackageNum())).doubleValue());
                    //实收金额
                    cisApplyChargeNto.setChageAmount(cisApplyChargeNto.getPrice().multiply(BigDecimal.valueOf(cisApplyChargeNto.getNum())));
                } else {
                    //数量
                    cisApplyChargeNto.setNum(cisIpdDocOrderDetailsAsNto.getNum());
                    //金额 = 价格 * 数量
                    cisApplyChargeNto.setChageAmount(cisApplyChargeNto.getPrice().multiply(new BigDecimal(cisIpdDocOrderDetailsAsNto.getNum() + "")));
                }
            } else if (cisIpdDocOrderAsNto.getOrderType().equals("2")) {
                //临嘱
                if (cisIpdDocOrderDetailsAsNto.getSbadmWay().equals(SbadmWayEnum.WHOLETAKE.getCode())) {
                    //数量
                    cisApplyChargeNto.setNum(BigDecimal.valueOf(cisIpdDocOrderDetailsAsNto.getTotalAmount()).multiply(BigDecimal.valueOf(cisIpdDocOrderDetailsAsNto.getPackageNum())).doubleValue());
                    //金额
                    cisApplyChargeNto.setChageAmount(cisIpdDocOrderDetailsAsNto.getPrice().multiply(BigDecimal.valueOf(cisIpdDocOrderDetailsAsNto.getTotalAmount())));
                } else {
                    if (StringUtils.isNotEmpty(cisIpdDocOrderDetailsAsNto.getUnitFlag())) {
                        if (cisIpdDocOrderDetailsAsNto.getUnitFlag().equals("1")) {
                            //大包装
                            //数量
                            cisApplyChargeNto.setNum(BigDecimal.valueOf(cisIpdDocOrderDetailsAsNto.getTotalAmount()).multiply(BigDecimal.valueOf(cisIpdDocOrderDetailsAsNto.getPackageNum())).doubleValue());
                            //金额
                            cisApplyChargeNto.setChageAmount(cisIpdDocOrderDetailsAsNto.getPrice().multiply(BigDecimal.valueOf(cisIpdDocOrderDetailsAsNto.getTotalAmount())));
                        } else if (cisIpdDocOrderDetailsAsNto.getUnitFlag().equals("0")) {
                            //小包装
                            //数量
                            cisApplyChargeNto.setNum(cisIpdDocOrderDetailsAsNto.getTotalAmount());
                            //金额
                            BigDecimal divide = cisIpdDocOrderDetailsAsNto.getPrice().divide(BigDecimal.valueOf(cisIpdDocOrderDetailsAsNto.getPackageNum()), 2, RoundingMode.HALF_DOWN);
                            cisApplyChargeNto.setChageAmount(divide.multiply(BigDecimal.valueOf(cisIpdDocOrderDetailsAsNto.getTotalAmount())));
                        }
                    } else {
                        //若没有包装单位标识则按照大包装计算
                        //数量
                        cisApplyChargeNto.setNum(BigDecimal.valueOf(cisIpdDocOrderDetailsAsNto.getTotalAmount()).multiply(BigDecimal.valueOf(cisIpdDocOrderDetailsAsNto.getPackageNum())).doubleValue());
                        //金额
                        cisApplyChargeNto.setChageAmount(cisIpdDocOrderDetailsAsNto.getPrice().multiply(BigDecimal.valueOf(cisIpdDocOrderDetailsAsNto.getTotalAmount())));
                    }
                }
            }
        } else {
            //数量
            cisApplyChargeNto.setNum(cisIpdDocOrderDetailsAsNto.getNum());
            //金额
            cisApplyChargeNto.setChageAmount(cisIpdDocOrderDetailsAsNto.getPrice().multiply(new BigDecimal(cisIpdDocOrderDetailsAsNto.getNum() + "")));
        }
        cisApplyChargeNto.setChargeFrequency(cisIpdDocOrderAsNto.getFrequency());
        cisApplyChargeNto.setStatusCode(CisStatusEnum.NEW);
        cisApplyChargeNto.setExecuteOrgCode(cisIpdDocOrderAsNto.getExecuteOrgCode());
        cisApplyChargeNto.setExecuteOrgName(cisIpdDocOrderAsNto.getExecuteOrgName());
        cisApplyChargeNto.setLimitConformFlag(cisIpdDocOrderDetailsAsNto.getLimitConformFlag());
        cisApplyChargeNto.setCisBaseApplyId(cisIpdDocOrderAsNto.getApplyId());
        cisApplyChargeNto.setChargeType(CisChargeTypeEnum.DOCT);
        if (cisIpdDocOrderAsNto.getOrderClass().equals(SystemTypeEnum.EDRUG.getCode()) || cisIpdDocOrderAsNto.getOrderClass().equals(SystemTypeEnum.CDRUG.getCode())) {
            cisApplyChargeNto.setSystemItemClass(SystemItemClassEnum.DRUG);
        } else if (cisIpdDocOrderAsNto.getOrderClass().equals(SystemTypeEnum.MATERIAL.getCode())) {
            cisApplyChargeNto.setSystemItemClass(SystemItemClassEnum.MATE);
        } else {
            cisApplyChargeNto.setSystemItemClass(SystemItemClassEnum.PRICE);
        }
        // todo 手术、病理类型医嘱也有明细，要关联上
        if (cisIpdDocOrderAsNto.getOrderClass().equals(SystemTypeEnum.EDRUG.getCode())
                || cisIpdDocOrderAsNto.getOrderClass().equals(SystemTypeEnum.CDRUG.getCode())
                || cisIpdDocOrderAsNto.getOrderClass().equals(SystemTypeEnum.SPCOBS.getCode())
                || cisIpdDocOrderAsNto.getOrderClass().equals(SystemTypeEnum.DGIMG.getCode())
                || cisIpdDocOrderAsNto.getOrderClass().equals(SystemTypeEnum.BLOOD.getCode())
                || cisIpdDocOrderAsNto.getOrderClass().equals(SystemTypeEnum.INPUTBLOODAPPLY.getCode())
                || cisIpdDocOrderAsNto.getOrderClass().equals(SystemTypeEnum.OPERATIONAPPLY.getCode())
                || cisIpdDocOrderAsNto.getOrderClass().equals(SystemTypeEnum.PALG.getCode())) {
            cisApplyChargeNto.setDetailId(cisIpdDocOrderDetailsAsNto.getApplyDetailId());
        }

        return cisApplyChargeNto;
    }

    public List<CisApplyChargeNto> buildConsultationCisApplyChargeNto(CisIpdDocOrderAsNto cisIpdDocOrderAsNto) {
        List<CisApplyChargeNto> cisApplyChargeNtos = new ArrayList<>();
        ServiceClinicItemTo serviceClinicItemTo = serviceClinicItemService.getServiceClinicItemByCode(cisIpdDocOrderAsNto.getServiceItemCode());
        // 查询开立医嘱的服务项目
        for (CisIpdDocOrderDetailsAsNto cisIpdDocOrderDetailsAsNto : cisIpdDocOrderAsNto.getCisIpdDocOrderDetailsAsNtoList()) {
            if (serviceClinicItemTo != null && CollectionUtils.isNotEmpty(serviceClinicItemTo.getServiceClinicPrices())) {
                List<ServiceClinicPriceTo> priceToList = serviceClinicItemTo.getServiceClinicPrices().stream().filter(a -> a.getPriceItemCode().equals(cisIpdDocOrderDetailsAsNto.getPriceItemCode())).toList();
                for (ServiceClinicPriceTo priceTo : priceToList) {
                    CisApplyChargeNto chargeNto = new CisApplyChargeNto();
                    // 雪花算法生成ID
                    chargeNto.setId("CG_" + HIPIDUtil.getNextIdString());
                    chargeNto.setOrderId(cisIpdDocOrderAsNto.getId());
                    chargeNto.setCisBaseApplyId(cisIpdDocOrderAsNto.getApplyId());
                    if (cisIpdDocOrderAsNto.getOrderClass().equals(SystemTypeEnum.INPUTBLOODAPPLY.getCode())
                            || cisIpdDocOrderAsNto.getOrderClass().equals(SystemTypeEnum.OPERATIONAPPLY.getCode())) {
                        chargeNto.setDetailId(cisIpdDocOrderDetailsAsNto.getId());
                    }
                    chargeNto.setVisitCode(cisIpdDocOrderAsNto.getVisitCode());
                    chargeNto.setPriceItemCode(priceTo.getPriceItemCode());
                    chargeNto.setPriceItemName(priceTo.getPriceItemName());
                    chargeNto.setPrice(priceTo.getPrice());
                    chargeNto.setNum(1.0);
                    chargeNto.setChageAmount(chargeNto.getPrice().multiply(new BigDecimal(chargeNto.getNum() + "")));
                    chargeNto.setIsFixed(priceTo.getIsFixed());

                    chargeNto.setUnitName(priceTo.getUnitName());
                    chargeNto.setUnit(priceTo.getUnitCode());
                    chargeNto.setStatusCode(CisStatusEnum.NEW);
                    chargeNto.setChargeType(CisChargeTypeEnum.DOCT);
                    chargeNto.setChargeFrequency(cisIpdDocOrderAsNto.getFrequency());
                    chargeNto.setSystemItemClass(priceTo.getSystemItemClass());

                    // 设置执行科室信息，根据医嘱物价项目对照信息
                    String executeOrgCode = ipdOrderExecuteOrgUtil.buildCiaApplyChargeExecuteOrgCode(priceTo, cisIpdDocOrderAsNto.getVisitCode(), cisIpdDocOrderAsNto.getCnsltOrg());
                    WorkGroupTo workGroupTo = workGroupService.getWorkGroup(executeOrgCode);
                    chargeNto.setExecuteOrgCode(executeOrgCode);
                    chargeNto.setExecuteOrgName(workGroupTo.getName());

                    cisApplyChargeNtos.add(chargeNto);
                }
            }
        }
        return cisApplyChargeNtos;
    }

    public List<CisApplyChargeNto> buildUnDrugCisApplyChargeNto(CisIpdDocOrderAsNto cisIpdDocOrderAsNto) {
        List<CisApplyChargeNto> cisApplyChargeNtos = new ArrayList<>();

        // 查询开立医嘱的服务项目
        for (CisIpdDocOrderDetailsAsNto cisIpdDocOrderDetailsAsNto : cisIpdDocOrderAsNto.getCisIpdDocOrderDetailsAsNtoList()) {
            ServiceClinicItemTo serviceClinicItemTo = serviceClinicItemService.getServiceClinicItemByCode(cisIpdDocOrderDetailsAsNto.getPriceItemCode());

            BusinessAssert.notNull(serviceClinicItemTo, CisDocIpdBusinessErrorEnum.CIS_DOC_IPD_0001, "服务项目");

            Map<String, CisIpdOrderChargeMiShowAsTo> miShowAsToMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(cisIpdDocOrderDetailsAsNto.getMiShowAsToList())) {
                miShowAsToMap = cisIpdDocOrderDetailsAsNto.getMiShowAsToList().stream().collect(
                        Collectors.toMap(CisIpdOrderChargeMiShowAsTo :: getPriceItemCode, a -> a, (a, b) -> a));
            }

            if (CollectionUtils.isNotEmpty(serviceClinicItemTo.getServiceClinicPrices())) {
                for (ServiceClinicPriceTo priceTo : serviceClinicItemTo.getServiceClinicPrices()) {
                    CisApplyChargeNto chargeNto = new CisApplyChargeNto();

                    // 雪花算法生成ID
                    chargeNto.setId("CG_" + HIPIDUtil.getNextIdString());
                    chargeNto.setOrderId(cisIpdDocOrderAsNto.getId());
                    chargeNto.setCisBaseApplyId(cisIpdDocOrderAsNto.getApplyId());
                    // 目前，行开立医嘱，除了药品，只有输血医嘱才有明细，赋值明细ID
                    if (cisIpdDocOrderAsNto.getOrderClass().equals(SystemTypeEnum.INPUTBLOODAPPLY.getCode())
                            || cisIpdDocOrderAsNto.getOrderClass().equals(SystemTypeEnum.OPERATIONAPPLY.getCode())) {
                        chargeNto.setDetailId(cisIpdDocOrderDetailsAsNto.getId());
                    }
                    chargeNto.setVisitCode(cisIpdDocOrderAsNto.getVisitCode());
                    chargeNto.setPriceItemCode(priceTo.getPriceItemCode());
                    chargeNto.setPriceItemName(priceTo.getPriceItemName());
                    chargeNto.setPrice(priceTo.getPrice());
                    chargeNto.setNum(priceTo.getQuantity() * cisIpdDocOrderDetailsAsNto.getNum());
                    chargeNto.setChageAmount(chargeNto.getPrice().multiply(new BigDecimal(chargeNto.getNum() + "")));
                    chargeNto.setIsFixed(priceTo.getIsFixed());

                    chargeNto.setUnitName(priceTo.getUnitName());
                    chargeNto.setUnit(priceTo.getUnitCode());
                    chargeNto.setStatusCode(CisStatusEnum.NEW);
                    chargeNto.setChargeType(CisChargeTypeEnum.DOCT);
                    chargeNto.setChargeFrequency(cisIpdDocOrderAsNto.getFrequency());
                    chargeNto.setSystemItemClass(priceTo.getSystemItemClass());

                    // 设置执行科室信息，根据医嘱物价项目对照信息
                    String executeOrgCode = ipdOrderExecuteOrgUtil.buildCiaApplyChargeExecuteOrgCode(priceTo, cisIpdDocOrderAsNto.getVisitCode(), cisIpdDocOrderAsNto.getExecuteOrgCode());
                    WorkGroupTo workGroupTo = workGroupService.getWorkGroup(executeOrgCode);
                    chargeNto.setExecuteOrgCode(executeOrgCode);
                    chargeNto.setExecuteOrgName(workGroupTo.getName());

                    // 医保限制符合标识
                    if (miShowAsToMap.size() > 0) {
                        if (miShowAsToMap.containsKey(chargeNto.getPriceItemCode())) {
                            chargeNto.setLimitConformFlag(miShowAsToMap.get(chargeNto.getPriceItemCode()).getLimitConformFlag());
                        }
                    } else {
                        chargeNto.setLimitConformFlag(cisIpdDocOrderDetailsAsNto.getLimitConformFlag());
                    }


                    cisApplyChargeNtos.add(chargeNto);
                }
            }
        }


        return cisApplyChargeNtos;
    }

    public List<CisApplyChargeNto> buildUnDrugCisApplyChargeNto(CisIpdOrderNto cisIpdOrderNto, CisBaseApplyTo cisBaseApplyTo) {
        List<CisApplyChargeNto> cisApplyChargeNtos = new ArrayList<>();

        // 查询开立医嘱的服务项目
        ServiceClinicItemTo serviceClinicItemTo = serviceClinicItemService.getServiceClinicItemByCode(cisBaseApplyTo.getServiceItemCode());

        BusinessAssert.notNull(serviceClinicItemTo, CisDocIpdBusinessErrorEnum.CIS_DOC_IPD_0001, "服务项目");

        if (CollectionUtils.isNotEmpty(serviceClinicItemTo.getServiceClinicPrices())) {
            for (ServiceClinicPriceTo priceTo : serviceClinicItemTo.getServiceClinicPrices()) {
                CisApplyChargeNto chargeNto = new CisApplyChargeNto();

                // 雪花算法生成ID
                chargeNto.setId("CG_" + HIPIDUtil.getNextIdString());
                chargeNto.setOrderId(cisBaseApplyTo.getOrderID());
                chargeNto.setCisBaseApplyId(cisBaseApplyTo.getId());
                chargeNto.setVisitCode(cisBaseApplyTo.getVisitCode());
                chargeNto.setPriceItemCode(priceTo.getPriceItemCode());
                chargeNto.setPriceItemName(priceTo.getPriceItemName());
                chargeNto.setPrice(priceTo.getPrice());
                chargeNto.setNum(priceTo.getQuantity() * cisBaseApplyTo.getNum());
                chargeNto.setChageAmount(chargeNto.getPrice().multiply(new BigDecimal(chargeNto.getNum() + "")));
                chargeNto.setIsFixed(priceTo.getIsFixed());

                chargeNto.setUnitName(priceTo.getUnitName());
                chargeNto.setUnit(priceTo.getUnitCode());
                chargeNto.setStatusCode(CisStatusEnum.NEW);
                chargeNto.setChargeType(CisChargeTypeEnum.DOCT);
                chargeNto.setLimitConformFlag(cisBaseApplyTo.getCisApplyCharges().get(0).getLimitConformFlag());
                chargeNto.setSystemItemClass(priceTo.getSystemItemClass());

                // 设置执行科室信息，根据医嘱物价项目对照信息
                String executeOrgCode = ipdOrderExecuteOrgUtil.buildCiaApplyChargeExecuteOrgCode(priceTo, cisBaseApplyTo.getVisitCode(), cisIpdOrderNto.getExecuteOrgCode());
                WorkGroupTo workGroupTo = workGroupService.getWorkGroup(executeOrgCode);
                chargeNto.setExecuteOrgCode(executeOrgCode);
                chargeNto.setExecuteOrgName(workGroupTo.getName());

                cisApplyChargeNtos.add(chargeNto);
            }
        }


        return cisApplyChargeNtos;
    }

    /**
     * 申请单收费信息
     */
    public CisApplyChargeNto cisApplyChargeNtoToEto(CisIpdDocOrderDetailsAsNto cisIpdDocOrderDetailsAsNto, CisIpdDocOrderAsEto cisIpdDocOrderAsEto) {
        if (cisIpdDocOrderDetailsAsNto == null) {
            return null;
        }
        CisApplyChargeNto cisApplyChargeNto = new CisApplyChargeNto();
        cisApplyChargeNto.setId(cisIpdDocOrderDetailsAsNto.getChargeId());
        cisApplyChargeNto.setOrderId(cisIpdDocOrderAsEto.getId());
        cisApplyChargeNto.setVisitCode(cisIpdDocOrderAsEto.getVisitCode());
        cisApplyChargeNto.setPriceItemCode(cisIpdDocOrderDetailsAsNto.getPriceItemCode());
        cisApplyChargeNto.setPriceItemName(cisIpdDocOrderDetailsAsNto.getPriceItemName());
        cisApplyChargeNto.setPackageSpec(cisIpdDocOrderDetailsAsNto.getDrugSpec());
        cisApplyChargeNto.setPrice(cisIpdDocOrderDetailsAsNto.getPrice());
        cisApplyChargeNto.setUnit(cisIpdDocOrderDetailsAsNto.getUnit());
        cisApplyChargeNto.setUnitName(cisIpdDocOrderDetailsAsNto.getUnitValue());
        cisApplyChargeNto.setNum(cisIpdDocOrderDetailsAsNto.getNum());
        cisApplyChargeNto.setChargeFrequency(cisIpdDocOrderAsEto.getFrequency());
        cisApplyChargeNto.setStatusCode(CisStatusEnum.NEW);
        cisApplyChargeNto.setExecuteOrgCode(cisIpdDocOrderAsEto.getExecuteOrgCode());
        cisApplyChargeNto.setExecuteOrgName(cisIpdDocOrderAsEto.getExecuteOrgName());
        cisApplyChargeNto.setChageAmount(cisIpdDocOrderDetailsAsNto.getPrice());
        cisApplyChargeNto.setLimitConformFlag(cisIpdDocOrderDetailsAsNto.getLimitConformFlag());
        cisApplyChargeNto.setCisBaseApplyId(cisIpdDocOrderAsEto.getApplyId());
        cisApplyChargeNto.setChargeType(CisChargeTypeEnum.DOCT);
        cisApplyChargeNto.setDetailId(cisIpdDocOrderDetailsAsNto.getId());
        return cisApplyChargeNto;
    }

    /**
     * 申请单收费信息
     */
    public CisApplyChargeEto cisApplyChargeEtoToETo(CisIpdDocOrderDetailsAsEto cisIpdDocOrderDetailsAsEto, CisIpdDocOrderAsEto cisIpdDocOrderAsEto) {
        if (cisIpdDocOrderDetailsAsEto == null) {
            return null;
        }
        CisApplyChargeEto cisApplyChargeEto = new CisApplyChargeEto();
        cisApplyChargeEto.setId(cisIpdDocOrderDetailsAsEto.getChargeId());
        cisApplyChargeEto.setPriceItemCode(cisIpdDocOrderDetailsAsEto.getPriceItemCode());
        cisApplyChargeEto.setPriceItemName(cisIpdDocOrderDetailsAsEto.getPriceItemName());
        cisApplyChargeEto.setPackageSpec(cisIpdDocOrderDetailsAsEto.getDrugSpec());
        cisApplyChargeEto.setPrice(cisIpdDocOrderDetailsAsEto.getPrice());
        cisApplyChargeEto.setNum(cisIpdDocOrderDetailsAsEto.getNum());
        cisApplyChargeEto.setExecuteOrgCode(cisIpdDocOrderAsEto.getExecuteOrgCode());
        cisApplyChargeEto.setChageAmount(cisIpdDocOrderDetailsAsEto.getPrice());
        cisApplyChargeEto.setUnit(cisIpdDocOrderDetailsAsEto.getUnit());
        cisApplyChargeEto.setVersion(cisIpdDocOrderDetailsAsEto.getVersion());
        return cisApplyChargeEto;
    }

    /**
     * 行开立-构建长嘱实体
     */
    public CisLongTermOrderNto buildCisIpdLongOrderNto(CisIpdDocOrderAsNto cisIpdDocOrderAsNto) {
        List<CisApplyChargeNto> cisApplyChargeNtoList = new ArrayList<>();
        if (StringUtils.isEmpty(cisIpdDocOrderAsNto.getExecuteOrgCode())) {
            cisIpdDocOrderAsNto.setExecuteOrgCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
            cisIpdDocOrderAsNto.setExecuteOrgName(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupName());
        }
        CisLongTermOrderNto cisLongTermOrderNto = new CisLongTermOrderNto();
        cisLongTermOrderNto.setFirstDayTimepoint(cisIpdDocOrderAsNto.getFirstDayTimepoint());
        cisLongTermOrderNto.setId(cisIpdDocOrderAsNto.getId());
        cisLongTermOrderNto.setPatMiCode(cisIpdDocOrderAsNto.getPatMiCode());
        cisLongTermOrderNto.setVisitCode(cisIpdDocOrderAsNto.getVisitCode());
        cisLongTermOrderNto.setDeptNurseCode(cisIpdDocOrderAsNto.getDeptNurseCode());
        cisLongTermOrderNto.setDeptNurseName(cisIpdDocOrderAsNto.getDeptNurseName());
        cisLongTermOrderNto.setOrderContent(cisIpdDocOrderAsNto.getOrderContent());
        cisLongTermOrderNto.setOrderClass(SystemTypeEnum.getValue(cisIpdDocOrderAsNto.getOrderClass()));
        cisLongTermOrderNto.setExecuteOrgCode(cisIpdDocOrderAsNto.getExecuteOrgCode());
        cisLongTermOrderNto.setExecuteOrgName(cisIpdDocOrderAsNto.getExecuteOrgName());
        cisLongTermOrderNto.setCreateOrgCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
        cisLongTermOrderNto.setCreateOrgName(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupName());
        cisLongTermOrderNto.setThirdFlag(false);
        cisLongTermOrderNto.setReMark(cisIpdDocOrderAsNto.getReMark());
        cisLongTermOrderNto.setReceiveOrgCode(cisIpdDocOrderAsNto.getReceiveOrgCode());
        cisLongTermOrderNto.setHospitalCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getHospitalAreaCode());
        cisLongTermOrderNto.setHospitalName(HIPCurrentOrgUtils.getCurrentOrgInfo().getHospitalAreaName());
        cisLongTermOrderNto.setEffectiveLowDate(LocalDateTime.now());
        cisLongTermOrderNto.setSkinFlag(cisIpdDocOrderAsNto.getSkinFlag());
        cisLongTermOrderNto.setEffectiveHighDate(cisIpdDocOrderAsNto.getEffectiveHighDate());
        cisLongTermOrderNto.setCheckType(CheckTypeEnum.NONE);
        cisLongTermOrderNto.setExtTypeCode(cisIpdDocOrderAsNto.getExtCode());
        String orderServiceCode = "";
        String orderServiceName = "";
        if (cisIpdDocOrderAsNto.getOrderClass().equals(SystemTypeEnum.EDRUG.getCode())) {
            //todo delete 扩展类型编码
            cisLongTermOrderNto.setExtTypeCode(EDrugSystemTypeExtEnum.COMMON.getCode());
            //药品申请单
            CisEDrugApplyNto cisEDrugApplyNto = this.cisEDrugApplyNtoToTo(cisIpdDocOrderAsNto);
            List<CisDrugApplyDetailNto> cisDrugApplyDetailNtoList = new ArrayList<>();
            for (CisIpdDocOrderDetailsAsNto cisIpdDocOrderDetailsAsNto : cisIpdDocOrderAsNto.getCisIpdDocOrderDetailsAsNtoList()) {
                //药品明细
                CisDrugApplyDetailNto cisDrugApplyDetailNto = this.cisDrugApplyDetailNtoToTo(cisIpdDocOrderDetailsAsNto, cisIpdDocOrderAsNto);
                cisDrugApplyDetailNtoList.add(cisDrugApplyDetailNto);
                //申请单收费信息
                if (cisIpdDocOrderDetailsAsNto.getExtCode().equals(EDrugSystemTypeExtEnum.COMMON.getCode())) {
                    CisApplyChargeNto cisApplyChargeNto = this.cisApplyChargeNtoToTo(cisIpdDocOrderDetailsAsNto, cisIpdDocOrderAsNto);
                    cisApplyChargeNtoList.add(cisApplyChargeNto);
                }

                //医嘱编码
                orderServiceCode += cisIpdDocOrderDetailsAsNto.getPriceItemCode() + ",";
                //医嘱名称
                orderServiceName += cisIpdDocOrderDetailsAsNto.getPriceItemName() + ",";
            }
            cisEDrugApplyNto.setServiceItemName(orderServiceName);
            cisLongTermOrderNto.setOrderServiceCode(orderServiceCode);
            //药品明细List
            cisEDrugApplyNto.setDetails(cisDrugApplyDetailNtoList);
            //申请单收费信息List
            cisEDrugApplyNto.setCisApplyCharges(cisApplyChargeNtoList);
            cisLongTermOrderNto.setCisBaseApplyNto(cisEDrugApplyNto);
        } else if (cisIpdDocOrderAsNto.getOrderClass().equals(SystemTypeEnum.MANAGEMENT.getCode())) {
            //处置申请单
            CisManagementApplyNto cisManagementApplyNto = this.cisManagementApplyNtoToTo(cisIpdDocOrderAsNto);
            // 申请单费用
            cisApplyChargeNtoList = this.buildUnDrugCisApplyChargeNto(cisIpdDocOrderAsNto);
//            for (CisIpdDocOrderDetailsAsNto cisIpdDocOrderDetailsAsNto : cisIpdDocOrderAsNto.getCisIpdDocOrderDetailsAsNtoList()) {
//                //申请单收费信息
//                CisApplyChargeNto cisApplyChargeNto = OrderIssuedAsAssembler.cisApplyChargeNtoToTo(cisIpdDocOrderDetailsAsNto, cisIpdDocOrderAsNto);
//                cisApplyChargeNtoList.add(cisApplyChargeNto);
//                //医嘱编码
//                orderServiceCode += cisIpdDocOrderDetailsAsNto.getPriceItemCode() + ",";
//            }
//            cisLongTermOrderNto.setOrderServiceCode(orderServiceCode);
            //申请单收费信息List
            cisManagementApplyNto.setCisApplyCharges(cisApplyChargeNtoList);
            cisLongTermOrderNto.setCisBaseApplyNto(cisManagementApplyNto);
        } else if (cisIpdDocOrderAsNto.getOrderClass().equals(SystemTypeEnum.TREATMENT.getCode())) {
            //治疗申请单
            CisTreatmentApplyNto cisTreatmentApplyNto = this.cisTreatmentApplyNtoToTo(cisIpdDocOrderAsNto);
            // 申请单费用
            cisApplyChargeNtoList = this.buildUnDrugCisApplyChargeNto(cisIpdDocOrderAsNto);
//            for (CisIpdDocOrderDetailsAsNto cisIpdDocOrderDetailsAsNto : cisIpdDocOrderAsNto.getCisIpdDocOrderDetailsAsNtoList()) {
//                //申请单收费信息
//                CisApplyChargeNto cisApplyChargeNto = this.cisApplyChargeNtoToTo(cisIpdDocOrderDetailsAsNto, cisIpdDocOrderAsNto);
//                cisApplyChargeNtoList.add(cisApplyChargeNto);
//                //医嘱编码
//                orderServiceCode += cisIpdDocOrderDetailsAsNto.getPriceItemCode() + ",";
//            }
//            cisLongTermOrderNto.setOrderServiceCode(orderServiceCode);
            //申请单收费信息List
            cisTreatmentApplyNto.setCisApplyCharges(cisApplyChargeNtoList);
            cisLongTermOrderNto.setCisBaseApplyNto(cisTreatmentApplyNto);
        } else if (cisIpdDocOrderAsNto.getOrderClass().equals(SystemTypeEnum.ENTRUST.getCode())) {
            //嘱托申请单
            CisCommonNto cisCommonNto = this.cisEntrustNtoToTo(cisIpdDocOrderAsNto);
            cisLongTermOrderNto.setCisBaseApplyNto(cisCommonNto);
        } else if (cisIpdDocOrderAsNto.getOrderClass().equals(OwnDrugEnum.OWNDRUGTYPE.getCode())) {
            LoginInfo loginInfo = HIPSecurityUtils.getLoginInfo();
            // 自带药
            cisLongTermOrderNto.setOrderClass(SystemTypeEnum.EDRUG);
            cisLongTermOrderNto.setExtTypeCode(EDrugSystemTypeExtEnum.OWNDRUG.getCode());
            CisEDrugApplyNto cisEDrugApplyNto = this.cisEDrugApplyNtoToTo(cisIpdDocOrderAsNto);
            cisEDrugApplyNto.setExecutorOrgCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
            cisEDrugApplyNto.setExecutorOrgName(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupName());
            List<CisDrugApplyDetailNto> cisDrugApplyDetailNtoList = new ArrayList<>();
            for (CisIpdDocOrderDetailsAsNto cisIpdDocOrderDetailsAsNto : cisIpdDocOrderAsNto.getCisIpdDocOrderDetailsAsNtoList()) {
                //药品明细
                CisDrugApplyDetailNto cisDrugApplyDetailNto = this.cisDrugApplyDetailNtoToTo(cisIpdDocOrderDetailsAsNto, cisIpdDocOrderAsNto);
                cisDrugApplyDetailNto.setExtTypeCode(EDrugSystemTypeExtEnum.OWNDRUG.getCode());
                cisDrugApplyDetailNtoList.add(cisDrugApplyDetailNto);
                //医嘱编码
                orderServiceCode += cisIpdDocOrderDetailsAsNto.getPriceItemCode() + ",";
                //医嘱名称
                orderServiceName += cisIpdDocOrderDetailsAsNto.getPriceItemName() + ",";
            }

            cisEDrugApplyNto.setDetails(cisDrugApplyDetailNtoList);
            cisEDrugApplyNto.setServiceItemName(orderServiceName.substring(0, orderServiceName.length() - 1));
            cisEDrugApplyNto.setServiceItemCode(orderServiceCode.substring(0, orderServiceCode.length() - 1));
            cisLongTermOrderNto.setOrderServiceCode(cisEDrugApplyNto.getServiceItemCode());
            cisLongTermOrderNto.setCisBaseApplyNto(cisEDrugApplyNto);
        }
        return cisLongTermOrderNto;
    }

    /**
     * 编辑-构建临嘱实体
     */
    public CisTemporaryOrderEto buildCisIpdTempOrderEto(CisIpdDocOrderAsEto cisIpdDocOrderAsEto) {
        List<CisApplyChargeNto> cisApplyChargeNtoList = new ArrayList<>();
        List<CisApplyChargeEto> cisApplyChargeEtoList = new ArrayList<>();
        CisTemporaryOrderEto cisTemporaryOrderEto = new CisTemporaryOrderEto();
        cisTemporaryOrderEto.setVisitCode(cisIpdDocOrderAsEto.getVisitCode());
        cisTemporaryOrderEto.setOrderContent(cisIpdDocOrderAsEto.getOrderContent());
        cisTemporaryOrderEto.setOrderClass(SystemTypeEnum.getValue(cisIpdDocOrderAsEto.getOrderClass()));
        cisTemporaryOrderEto.setExecuteOrgCode(cisIpdDocOrderAsEto.getExecuteOrgCode());
        cisTemporaryOrderEto.setExecuteOrgName(cisIpdDocOrderAsEto.getExecuteOrgName());
        cisTemporaryOrderEto.setReceiveOrgCode(cisIpdDocOrderAsEto.getReceiveOrgCode());
        cisTemporaryOrderEto.setApplyCode(cisIpdDocOrderAsEto.getApplyId());
        cisTemporaryOrderEto.setRepairFlag(cisIpdDocOrderAsEto.getRepairFlag());
        cisTemporaryOrderEto.setReMark(cisIpdDocOrderAsEto.getReMark());
        if (cisIpdDocOrderAsEto.getRepairFlag().equals("1") && cisIpdDocOrderAsEto.getRepairTime() != null) {
            cisTemporaryOrderEto.setEffectiveLowDate(cisIpdDocOrderAsEto.getRepairTime());
        } else {
            cisTemporaryOrderEto.setEffectiveLowDate(cisIpdDocOrderAsEto.getEffectiveLowDate());
        }
        if (cisIpdDocOrderAsEto.getOrderClass().equals(SystemTypeEnum.SPCOBS.getCode())) {
            //检验申请单
            CisSpcobsApplyEto cisSpcobsApplyEto = this.cisSpcobsEtoToTo(cisIpdDocOrderAsEto);
            //检验申请单明细列表
            List<CisSpcobsApplyDetailNto> cisSpcobsApplyDetailNtoList = new ArrayList<>();
            for (CisIpdDocOrderDetailsAsNto cisIpdDocOrderDetailsAsNto : cisIpdDocOrderAsEto.getCisIpdDocOrderDetailsAsNtoList()) {
                //申请单收费信息
                CisApplyChargeNto cisApplyChargeNto = this.cisApplyChargeNtoToEto(cisIpdDocOrderDetailsAsNto, cisIpdDocOrderAsEto);
                cisApplyChargeNtoList.add(cisApplyChargeNto);
                //检验申请单明细
                CisSpcobsApplyDetailNto cisSpcobsApplyDetailNto = new CisSpcobsApplyDetailNto();
                cisSpcobsApplyDetailNto.setSpcobsCode(cisIpdDocOrderDetailsAsNto.getPriceItemCode());
                cisSpcobsApplyDetailNto.setSpcobsName(cisIpdDocOrderDetailsAsNto.getPriceItemName());
                cisSpcobsApplyDetailNto.setId(cisIpdDocOrderDetailsAsNto.getApplyDetailId());
                cisSpcobsApplyDetailNto.setVisitCode(cisIpdDocOrderAsEto.getVisitCode());
                cisSpcobsApplyDetailNto.setTestTubeId(cisIpdDocOrderDetailsAsNto.getTestTubeId());
                cisSpcobsApplyDetailNto.setDeviceType(cisIpdDocOrderDetailsAsNto.getDeviceType());
                cisSpcobsApplyDetailNto.setMethod(cisIpdDocOrderDetailsAsNto.getMethod());
                cisSpcobsApplyDetailNtoList.add(cisSpcobsApplyDetailNto);
            }
            //检验申请单明细列表
            List<CisSpcobsApplyDetailEto> cisSpcobsApplyDetailEtoList = new ArrayList<>();
            for (CisIpdDocOrderDetailsAsEto cisIpdDocOrderDetailsAsEto : cisIpdDocOrderAsEto.getCisIpdDocOrderDetailsAsEtoList()) {
                //申请单收费信息
                CisApplyChargeEto cisApplyChargeEto = this.cisApplyChargeEtoToETo(cisIpdDocOrderDetailsAsEto, cisIpdDocOrderAsEto);
                cisApplyChargeEto.setSystemItemClass(SystemItemClassEnum.PRICE);
                cisApplyChargeEtoList.add(cisApplyChargeEto);
                //检验申请单明细
                CisSpcobsApplyDetailEto cisSpcobsApplyDetailEto = new CisSpcobsApplyDetailEto();
                cisSpcobsApplyDetailEto.setId(cisIpdDocOrderDetailsAsEto.getApplyDetailId());
                cisSpcobsApplyDetailEto.setSpcobsCode(cisIpdDocOrderDetailsAsEto.getPriceItemCode());
                cisSpcobsApplyDetailEto.setSpcobsName(cisIpdDocOrderDetailsAsEto.getPriceItemName());
                cisSpcobsApplyDetailEto.setNo(cisIpdDocOrderDetailsAsEto.getNo());
                cisSpcobsApplyDetailEtoList.add(cisSpcobsApplyDetailEto);
            }
            cisSpcobsApplyEto.setDetailNtos(cisSpcobsApplyDetailNtoList);
            cisSpcobsApplyEto.setDetailEtos(cisSpcobsApplyDetailEtoList);
            cisSpcobsApplyEto.setCisApplyChargeNtos(cisApplyChargeNtoList);
            cisSpcobsApplyEto.setCisApplyChargeEtos(cisApplyChargeEtoList);
            cisTemporaryOrderEto.setCisBaseApplyEto(cisSpcobsApplyEto);
        } else if (cisIpdDocOrderAsEto.getOrderClass().equals(SystemTypeEnum.CDRUG.getCode())) {
            CisCDrugApplyEto cisCDrugApplyEto = cDrugApplyIssuedAsAssembler.buildCisCDrugApplyEto(cisIpdDocOrderAsEto);
            cisTemporaryOrderEto.setOrderServiceCode(cisCDrugApplyEto.getServiceItemCode());
            cisTemporaryOrderEto.setCisBaseApplyEto(cisCDrugApplyEto);
        } else if (cisIpdDocOrderAsEto.getOrderClass().equals(SystemTypeEnum.DGIMG.getCode())) {
            CisDgimgApplyEto cisDgimgApplyEto = dgimgApplyIssuedAsAssembler.buildCisDgimgApplyEto(cisIpdDocOrderAsEto);

            AtomicReference<String> orderServiceCode = new AtomicReference<>("");
            AtomicReference<String> orderServiceName = new AtomicReference<>("");
            if (CollectionUtils.isNotEmpty(cisDgimgApplyEto.getCisDgimgApplyDetailEtos())) {
                cisDgimgApplyEto.getCisDgimgApplyDetailEtos().forEach(o -> {
                    orderServiceCode.set(o.getDgimgCode() + ",");
                    orderServiceName.set(o.getDgimgName() + ",");
                });
            }

            if (CollectionUtils.isNotEmpty(cisDgimgApplyEto.getCisDgimgApplyDetailNtos())) {
                cisDgimgApplyEto.getCisDgimgApplyDetailNtos().forEach(o -> {
                    orderServiceCode.set(o.getDgimgCode() + ",");
                    orderServiceName.set(o.getDgimgName() + ",");
                });
            }

            cisDgimgApplyEto.setServiceItemCode(orderServiceCode.get().substring(0, orderServiceCode.get().length() - 1));
            cisDgimgApplyEto.setServiceItemName(orderServiceName.get().substring(0, orderServiceName.get().length() - 1));
            cisTemporaryOrderEto.setCisBaseApplyEto(cisDgimgApplyEto);
            cisTemporaryOrderEto.setOrderServiceCode(orderServiceCode.get());
        }
        return cisTemporaryOrderEto;
    }

    /**
     * 行开立-构建临嘱实体
     */
    public CisTemporaryOrderNto buildCisIpdTempOrderNto(CisIpdDocOrderAsNto cisIpdDocOrderAsNto) {
        List<CisApplyChargeNto> cisApplyChargeNtoList = new ArrayList<>();
        if (StringUtils.isEmpty(cisIpdDocOrderAsNto.getExecuteOrgCode())) {
            cisIpdDocOrderAsNto.setExecuteOrgCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
            cisIpdDocOrderAsNto.setExecuteOrgName(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupName());
        }

        CisTemporaryOrderNto cisTemporaryOrderNto = new CisTemporaryOrderNto();
        cisTemporaryOrderNto.setId(cisIpdDocOrderAsNto.getId());
        cisTemporaryOrderNto.setPatMiCode(cisIpdDocOrderAsNto.getPatMiCode());
        cisTemporaryOrderNto.setVisitCode(cisIpdDocOrderAsNto.getVisitCode());
        cisTemporaryOrderNto.setDeptNurseCode(cisIpdDocOrderAsNto.getDeptNurseCode());
        cisTemporaryOrderNto.setDeptNurseName(cisIpdDocOrderAsNto.getDeptNurseName());
        cisTemporaryOrderNto.setOrderContent(cisIpdDocOrderAsNto.getOrderContent());
        cisTemporaryOrderNto.setOrderClass(SystemTypeEnum.getValue(cisIpdDocOrderAsNto.getOrderClass()));
        cisTemporaryOrderNto.setExecuteOrgCode(cisIpdDocOrderAsNto.getExecuteOrgCode());
        cisTemporaryOrderNto.setExecuteOrgName(cisIpdDocOrderAsNto.getExecuteOrgName());
        cisTemporaryOrderNto.setCreateOrgCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
        cisTemporaryOrderNto.setCreateOrgName(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupName());
        cisTemporaryOrderNto.setThirdFlag(false);
        cisTemporaryOrderNto.setReMark(cisIpdDocOrderAsNto.getReMark());
        cisTemporaryOrderNto.setReceiveOrgCode(cisIpdDocOrderAsNto.getReceiveOrgCode());
        cisTemporaryOrderNto.setHospitalCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getHospitalAreaCode());
        cisTemporaryOrderNto.setHospitalName(HIPCurrentOrgUtils.getCurrentOrgInfo().getHospitalAreaName());
        cisTemporaryOrderNto.setRepairFlag(cisIpdDocOrderAsNto.getRepairFlag());
        cisTemporaryOrderNto.setSkinFlag(cisIpdDocOrderAsNto.getSkinFlag());
        cisTemporaryOrderNto.setCheckType(CheckTypeEnum.NONE);
        cisTemporaryOrderNto.setExtTypeCode(cisIpdDocOrderAsNto.getExtCode());
        cisTemporaryOrderNto.setPrescriptionFlag(cisIpdDocOrderAsNto.getPrescriptionFlag());
        if (cisIpdDocOrderAsNto.getDays() != null) {
            cisTemporaryOrderNto.setTreatmentCourse(Long.valueOf(cisIpdDocOrderAsNto.getDays()));
            cisTemporaryOrderNto.setTreatmentCourseUnit("天");
        }
        if (cisIpdDocOrderAsNto.getRepairFlag().equals("1") && cisIpdDocOrderAsNto.getRepairTime() != null) {
            cisTemporaryOrderNto.setEffectiveLowDate(cisIpdDocOrderAsNto.getRepairTime());
        } else {
            cisTemporaryOrderNto.setEffectiveLowDate(LocalDateTime.now());
        }
        String orderServiceCode = "";
        String orderServiceName = "";
        if (cisIpdDocOrderAsNto.getOrderClass().equals(SystemTypeEnum.EDRUG.getCode())) {
            //todo delete 扩展类型编码
            cisTemporaryOrderNto.setExtTypeCode(EDrugSystemTypeExtEnum.COMMON.getCode());
            //药品申请单
            CisEDrugApplyNto cisEDrugApplyNto = this.cisEDrugApplyNtoToTo(cisIpdDocOrderAsNto);
            List<CisDrugApplyDetailNto> cisDrugApplyDetailNtoList = new ArrayList<>();
            for (CisIpdDocOrderDetailsAsNto cisIpdDocOrderDetailsAsNto : cisIpdDocOrderAsNto.getCisIpdDocOrderDetailsAsNtoList()) {
                //药品明细
                CisDrugApplyDetailNto cisDrugApplyDetailNto = this.cisDrugApplyDetailNtoToTo(cisIpdDocOrderDetailsAsNto, cisIpdDocOrderAsNto);
                cisDrugApplyDetailNtoList.add(cisDrugApplyDetailNto);

                //申请单收费信息
                if (cisIpdDocOrderDetailsAsNto.getExtCode().equals(EDrugSystemTypeExtEnum.COMMON.getCode())) {
                    CisApplyChargeNto cisApplyChargeNto = this.cisApplyChargeNtoToTo(cisIpdDocOrderDetailsAsNto, cisIpdDocOrderAsNto);
                    cisApplyChargeNtoList.add(cisApplyChargeNto);
                }

                //医嘱编码
                orderServiceCode += cisIpdDocOrderDetailsAsNto.getPriceItemCode() + ",";
                orderServiceName += cisIpdDocOrderDetailsAsNto.getPriceItemName() + ",";
                if (cisIpdDocOrderDetailsAsNto.getSbadmWay().equals("DISCHARGE")) {
                    cisTemporaryOrderNto.setExtTypeCode("DISCHARGE");
                }
            }
            cisTemporaryOrderNto.setOrderServiceCode(orderServiceCode);
            //药品明细List
            cisEDrugApplyNto.setDetails(cisDrugApplyDetailNtoList);
            cisEDrugApplyNto.setServiceItemCode(orderServiceCode);
            cisEDrugApplyNto.setServiceItemName(orderServiceName.substring(0, orderServiceName.length() - 1));
            //申请单收费信息List
            cisEDrugApplyNto.setCisApplyCharges(cisApplyChargeNtoList);
            cisTemporaryOrderNto.setCisBaseApplyNto(cisEDrugApplyNto);
        } else if (cisIpdDocOrderAsNto.getOrderClass().equals(SystemTypeEnum.CDRUG.getCode())) {
            // 草药申请单
            CisCDrugApplyNto cisCDrugApplyNto = cDrugApplyIssuedAsAssembler.buildCisCdrugApplyNto(cisIpdDocOrderAsNto);
            cisTemporaryOrderNto.setOrderServiceCode(cisCDrugApplyNto.getServiceItemCode());
            cisTemporaryOrderNto.setExtTypeCode(EDrugSystemTypeExtEnum.COMMON.getCode());
            cisTemporaryOrderNto.setCisBaseApplyNto(cisCDrugApplyNto);
        } else if (cisIpdDocOrderAsNto.getOrderClass().equals(SystemTypeEnum.SKIN.getCode())) {
            //皮试申请单
            CisSkinApplyNto cisSkinApplyNto = this.cisSkinApplyNtoToTo(cisIpdDocOrderAsNto);
            // 申请单费用
            List<CisApplyChargeNto> cisApplyChargeNtos = this.buildUnDrugCisApplyChargeNto(cisIpdDocOrderAsNto);
            //申请单收费信息List
            cisSkinApplyNto.setCisApplyCharges(cisApplyChargeNtos);
            cisTemporaryOrderNto.setCisBaseApplyNto(cisSkinApplyNto);
            cisTemporaryOrderNto.setExtTypeCode(SkinSystemTypeExtEnum.SOL.getCode());
        } else if (cisIpdDocOrderAsNto.getOrderClass().equals(SystemTypeEnum.MANAGEMENT.getCode())) {
            //处置申请单
            CisManagementApplyNto cisManagementApplyNto = this.cisManagementApplyNtoToTo(cisIpdDocOrderAsNto);
            // 申请单费用
            List<CisApplyChargeNto> cisApplyChargeNtos = this.buildUnDrugCisApplyChargeNto(cisIpdDocOrderAsNto);
            //申请单收费信息List
            cisManagementApplyNto.setCisApplyCharges(cisApplyChargeNtos);
            cisTemporaryOrderNto.setCisBaseApplyNto(cisManagementApplyNto);
        } else if (cisIpdDocOrderAsNto.getOrderClass().equals(SystemTypeEnum.TREATMENT.getCode())) {
            //治疗申请单
            CisTreatmentApplyNto cisTreatmentApplyNto = this.cisTreatmentApplyNtoToTo(cisIpdDocOrderAsNto);
            // 申请单费用
            List<CisApplyChargeNto> cisApplyChargeNtos = this.buildUnDrugCisApplyChargeNto(cisIpdDocOrderAsNto);
            //申请单收费信息List
            cisTreatmentApplyNto.setCisApplyCharges(cisApplyChargeNtos);
            cisTemporaryOrderNto.setCisBaseApplyNto(cisTreatmentApplyNto);
        } else if (cisIpdDocOrderAsNto.getOrderClass().equals(SystemTypeEnum.SPCOBS.getCode())) {
            //检验申请单
            CisSpcobsApplyNto cisSpcobsApplyNto = this.cisSpcobsNtoToTo(cisIpdDocOrderAsNto);
            //检验申请单明细列表
            List<CisSpcobsApplyDetailNto> cisSpcobsApplyDetailNtoList = new ArrayList<>();
            //检验申请单明细
            CisSpcobsApplyDetailNto cisSpcobsApplyDetailNto = new CisSpcobsApplyDetailNto();
            cisSpcobsApplyDetailNto.setSpcobsCode(cisIpdDocOrderAsNto.getServiceItemCode());
            cisSpcobsApplyDetailNto.setSpcobsName(cisIpdDocOrderAsNto.getServiceItemName());
            cisSpcobsApplyDetailNto.setId(cisIpdDocOrderAsNto.getCisIpdDocOrderDetailsAsNtoList().get(0).getApplyDetailId());
            cisSpcobsApplyDetailNto.setVisitCode(cisIpdDocOrderAsNto.getVisitCode());
            cisSpcobsApplyDetailNto.setTestTubeId(cisIpdDocOrderAsNto.getCisIpdDocOrderDetailsAsNtoList().get(0).getTestTubeId());
            cisSpcobsApplyDetailNto.setDeviceType(cisIpdDocOrderAsNto.getCisIpdDocOrderDetailsAsNtoList().get(0).getDeviceType());
            cisSpcobsApplyDetailNto.setMethod(cisIpdDocOrderAsNto.getCisIpdDocOrderDetailsAsNtoList().get(0).getMethod());
            cisSpcobsApplyDetailNto.setExtTypeCode(cisIpdDocOrderAsNto.getExtCode());
            cisSpcobsApplyDetailNtoList.add(cisSpcobsApplyDetailNto);

            for (CisIpdDocOrderDetailsAsNto cisIpdDocOrderDetailsAsNto : cisIpdDocOrderAsNto.getCisIpdDocOrderDetailsAsNtoList()) {
                //申请单收费信息
                CisApplyChargeNto cisApplyChargeNto = this.cisApplyChargeNtoToTo(cisIpdDocOrderDetailsAsNto, cisIpdDocOrderAsNto);
                cisApplyChargeNtoList.add(cisApplyChargeNto);

                orderServiceName += cisIpdDocOrderDetailsAsNto.getPriceItemName() + ",";
            }
            //申请单收费信息List
            cisSpcobsApplyNto.setDetails(cisSpcobsApplyDetailNtoList);
            cisSpcobsApplyNto.setCisApplyCharges(cisApplyChargeNtoList);
            cisTemporaryOrderNto.setCisBaseApplyNto(cisSpcobsApplyNto);
        } else if (cisIpdDocOrderAsNto.getOrderClass().equals(SystemTypeEnum.DGIMG.getCode())) {
            // 构造申请单
            CisDgimgApplyNto cisDgimgApplyNto = dgimgApplyIssuedAsAssembler.buildCisDgimgApplyNto(cisIpdDocOrderAsNto);
            // 构造明细
            List<CisDgimgApplyDetailNto> cisDgimgApplyDetailNtoList = dgimgApplyIssuedAsAssembler.buildCisDgimgApplyDetailNtoList(cisIpdDocOrderAsNto);
            // 构造费用
            List<CisApplyChargeNto> cisApplyChargeNtos = dgimgApplyIssuedAsAssembler.buildDgimgApplyChargeNtoList(cisIpdDocOrderAsNto);
            cisDgimgApplyNto.setDetails(cisDgimgApplyDetailNtoList);
            cisDgimgApplyNto.setCisApplyCharges(cisApplyChargeNtos);

            cisTemporaryOrderNto.setCisBaseApplyNto(cisDgimgApplyNto);
            cisTemporaryOrderNto.setOrderServiceCode(cisDgimgApplyNto.getServiceItemCode());
            cisTemporaryOrderNto.setExtTypeCode(cisDgimgApplyDetailNtoList.get(0).getExtTypeCode());

        } else if (cisIpdDocOrderAsNto.getOrderClass().equals(SystemTypeEnum.ENTRUST.getCode())) {
            //嘱托申请单
            CisCommonNto cisCommonNto = this.cisEntrustNtoToTo(cisIpdDocOrderAsNto);
            cisTemporaryOrderNto.setCisBaseApplyNto(cisCommonNto);
        } else if (cisIpdDocOrderAsNto.getOrderClass().equals(SystemTypeEnum.BLOOD.getCode())) {
            cisTemporaryOrderNto.setCheckType(CheckTypeEnum.NONE);
            //输血申请单
            CisBloodApplyNto cisPreparationBloodApplyNto = this.cisBloodApplyNtoToTo(cisIpdDocOrderAsNto);
            cisTemporaryOrderNto.setOrderServiceCode(cisPreparationBloodApplyNto.getServiceItemCode());
            cisTemporaryOrderNto.setCisBaseApplyNto(cisPreparationBloodApplyNto);
        } else if (cisIpdDocOrderAsNto.getOrderClass().equals(SystemTypeEnum.INPUTBLOODAPPLY.getCode())) {
            CisInputBloodApplyNto cisInputBloodApplyNto = BloodApplyIssuedAsAssembler.buildInputBloodApplyNto(cisIpdDocOrderAsNto);
            List<CisApplyChargeNto> cisApplyChargeNtos = this.buildUnDrugCisApplyChargeNto(cisIpdDocOrderAsNto);
            cisInputBloodApplyNto.setCisApplyCharges(cisApplyChargeNtos);
            cisTemporaryOrderNto.setCisBaseApplyNto(cisInputBloodApplyNto);
        } else if (cisIpdDocOrderAsNto.getOrderClass().equals(SystemTypeEnum.OPERATIONAPPLY.getCode())) {
            // 构造手术申请单
            CisOperationApplyNto cisOperationApplyNto = operationApplyIssuedAsAssembler.buildCisOperationApplyNto(cisIpdDocOrderAsNto);

            // 构造手术申请明细
            List<CisOperationApplyDetailNto> cisOperationApplyDetailNtoList = operationApplyIssuedAsAssembler.buildOperationApplyDetails(cisIpdDocOrderAsNto);

            // 构造手术申请术前诊断信息
            List<ApplyDiagnosisNto> cisOperationApplyDiagnosisNtoList = operationApplyIssuedAsAssembler.buildOperationApplyDiagnsoisNtos(cisIpdDocOrderAsNto);

            // 构造手术申请收费信息
            List<CisApplyChargeNto> cisApplyChargeNtos = operationApplyIssuedAsAssembler.buildCisApplyChargeAsNtoList(cisIpdDocOrderAsNto);

            cisOperationApplyNto.setDetails(cisOperationApplyDetailNtoList);
            cisOperationApplyNto.setApplyDiagnosisNtos(cisOperationApplyDiagnosisNtoList);
            cisOperationApplyNto.setCisApplyCharges(cisApplyChargeNtos);

            cisTemporaryOrderNto.setCisBaseApplyNto(cisOperationApplyNto);
            cisTemporaryOrderNto.setExtTypeCode(cisOperationApplyDetailNtoList.get(0).getExtTypeCode());
        } else if (cisIpdDocOrderAsNto.getOrderClass().equals(OwnDrugEnum.OWNDRUGTYPE.getCode())) {
            LoginInfo loginInfo = HIPSecurityUtils.getLoginInfo();
            // 自带药
            cisTemporaryOrderNto.setOrderClass(SystemTypeEnum.EDRUG);
            cisTemporaryOrderNto.setExtTypeCode(EDrugSystemTypeExtEnum.OWNDRUG.getCode());
            CisEDrugApplyNto cisEDrugApplyNto = this.cisEDrugApplyNtoToTo(cisIpdDocOrderAsNto);
            cisEDrugApplyNto.setExecutorOrgCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
            cisEDrugApplyNto.setExecutorOrgName(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupName());
            List<CisDrugApplyDetailNto> cisDrugApplyDetailNtoList = new ArrayList<>();
            for (CisIpdDocOrderDetailsAsNto cisIpdDocOrderDetailsAsNto : cisIpdDocOrderAsNto.getCisIpdDocOrderDetailsAsNtoList()) {
                //药品明细
                CisDrugApplyDetailNto cisDrugApplyDetailNto = this.cisDrugApplyDetailNtoToTo(cisIpdDocOrderDetailsAsNto, cisIpdDocOrderAsNto);
                cisDrugApplyDetailNto.setExtTypeCode(EDrugSystemTypeExtEnum.OWNDRUG.getCode());
                cisDrugApplyDetailNtoList.add(cisDrugApplyDetailNto);
                //医嘱编码
                orderServiceCode += cisIpdDocOrderDetailsAsNto.getPriceItemCode() + ",";
                //医嘱名称
                orderServiceName += cisIpdDocOrderDetailsAsNto.getPriceItemName() + ",";
            }

            cisEDrugApplyNto.setDetails(cisDrugApplyDetailNtoList);
            cisEDrugApplyNto.setServiceItemName(orderServiceName.substring(0, orderServiceName.length() - 1));
            cisEDrugApplyNto.setServiceItemCode(orderServiceCode.substring(0, orderServiceCode.length() - 1));
            cisTemporaryOrderNto.setOrderServiceCode(cisEDrugApplyNto.getServiceItemCode());
            cisTemporaryOrderNto.setCisBaseApplyNto(cisEDrugApplyNto);
        }

        return cisTemporaryOrderNto;
    }

    /**
     * 检验申请单-开立
     */
    private CisSpcobsApplyNto cisSpcobsNtoToTo(CisIpdDocOrderAsNto cisIpdDocOrderAsNto) {
        if (cisIpdDocOrderAsNto == null) {
            return null;
        }
        CisSpcobsApplyNto cisSpcobsApplyNto = new CisSpcobsApplyNto();
        cisSpcobsApplyNto.setRemark(cisIpdDocOrderAsNto.getReMark());
        cisSpcobsApplyNto.setReMark(cisIpdDocOrderAsNto.getReMark());
        cisSpcobsApplyNto.setSpcobsClass(cisIpdDocOrderAsNto.getSpcobsClass());
        cisSpcobsApplyNto.setSpeciman(cisIpdDocOrderAsNto.getSpeciman());
        cisSpcobsApplyNto.setId(cisIpdDocOrderAsNto.getApplyId());
        cisSpcobsApplyNto.setPatMiCode(cisIpdDocOrderAsNto.getPatMiCode());
        cisSpcobsApplyNto.setVisitCode(cisIpdDocOrderAsNto.getVisitCode());
        cisSpcobsApplyNto.setVisitType(VisitTypeEnum.IPD);
        cisSpcobsApplyNto.setDeptNurseCode(cisIpdDocOrderAsNto.getDeptNurseCode());
        cisSpcobsApplyNto.setDeptNurseName(cisIpdDocOrderAsNto.getDeptNurseName());
        cisSpcobsApplyNto.setOrderID(cisIpdDocOrderAsNto.getId());
        //人员信息
        StaffLocalTo staffLocal = staffService.getStaffLocal(HIPSecurityUtils.getLoginInfo().getStaffId());
        cisSpcobsApplyNto.setCreateOrgCode(staffLocal.getAppointmentDept());
        cisSpcobsApplyNto.setCreateOrgName(staffLocal.getAppointmentDeptName());
        cisSpcobsApplyNto.setOrderType(OrderTypeEnum.TEMPORARY_ORDER);
        cisSpcobsApplyNto.setFrequency("ST");
        cisSpcobsApplyNto.setFrequencyName("立即");
        cisSpcobsApplyNto.setNum(1.0);
        cisSpcobsApplyNto.setExecutorOrgCode(cisIpdDocOrderAsNto.getExecuteOrgCode());
        cisSpcobsApplyNto.setExecutorOrgName(cisIpdDocOrderAsNto.getExecuteOrgName());
        cisSpcobsApplyNto.setVisitOrgCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
        cisSpcobsApplyNto.setVisitOrgName(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupName());
        cisSpcobsApplyNto.setIsCanPriorityFlag(cisIpdDocOrderAsNto.getIsCanPriorityFlag());
        if (StringUtils.isNotEmpty(cisIpdDocOrderAsNto.getIsOlation())) {
            cisSpcobsApplyNto.setIsOlation(cisIpdDocOrderAsNto.getIsOlation().equals("1"));
        } else {
            cisSpcobsApplyNto.setIsOlation(false);
        }
        cisSpcobsApplyNto.setServiceItemName(cisIpdDocOrderAsNto.getServiceItemName());
        cisSpcobsApplyNto.setServiceItemCode(cisIpdDocOrderAsNto.getServiceItemCode());
        return cisSpcobsApplyNto;
    }

    /**
     * 检验申请单-编辑
     */
    private CisSpcobsApplyEto cisSpcobsEtoToTo(CisIpdDocOrderAsEto cisIpdDocOrderAsEto) {
        if (cisIpdDocOrderAsEto == null) {
            return null;
        }
        CisSpcobsApplyEto cisSpcobsApplyEto = new CisSpcobsApplyEto();
        cisSpcobsApplyEto.setReMark(cisIpdDocOrderAsEto.getReMark());
        cisSpcobsApplyEto.setRemark(cisIpdDocOrderAsEto.getReMark());
        cisSpcobsApplyEto.setSpcobsClass(cisIpdDocOrderAsEto.getSpcobsClass());
        cisSpcobsApplyEto.setSpeciman(cisIpdDocOrderAsEto.getSpeciman());
        cisSpcobsApplyEto.setFrequency("ST");
        cisSpcobsApplyEto.setFrequencyName("立即");
        cisSpcobsApplyEto.setNum(1.0);
        cisSpcobsApplyEto.setIsCanPriorityFlag(cisIpdDocOrderAsEto.getIsCanPriorityFlag());
        cisSpcobsApplyEto.setStatusCode(cisIpdDocOrderAsEto.getStatusCode());
        cisSpcobsApplyEto.setIsOlation(cisIpdDocOrderAsEto.getIsOlation().equals("1"));
        cisSpcobsApplyEto.setVersion(cisIpdDocOrderAsEto.getVersion());
        cisSpcobsApplyEto.setServiceItemCode(cisIpdDocOrderAsEto.getServiceItemCode());
        cisSpcobsApplyEto.setServiceItemName(cisIpdDocOrderAsEto.getServiceItemName());
        return cisSpcobsApplyEto;
    }

    /**
     * 嘱托长嘱构建
     */
    public CisLongTermOrderNto buildEntrustSaveLongOrderNto(CisIpdDocOrderAsNto cisIpdDocOrderAsNto) {
        //构建长嘱实体
        CisLongTermOrderNto cisLongTermOrderNto = new CisLongTermOrderNto();
        cisLongTermOrderNto.setFirstDayTimepoint("首1");
        cisLongTermOrderNto.setId(cisIpdDocOrderAsNto.getId());
        cisLongTermOrderNto.setPatMiCode(cisIpdDocOrderAsNto.getPatMiCode());
        cisLongTermOrderNto.setVisitCode(cisIpdDocOrderAsNto.getVisitCode());
        cisLongTermOrderNto.setDeptNurseCode(cisIpdDocOrderAsNto.getDeptNurseCode());
        cisLongTermOrderNto.setDeptNurseName(cisIpdDocOrderAsNto.getDeptNurseName());
        cisLongTermOrderNto.setOrderContent(cisIpdDocOrderAsNto.getOrderContent());
        cisLongTermOrderNto.setOrderClass(SystemTypeEnum.ENTRUST);
        cisLongTermOrderNto.setExecuteOrgCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
        cisLongTermOrderNto.setExecuteOrgName(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupName());
        cisLongTermOrderNto.setCreateOrgCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
        cisLongTermOrderNto.setThirdFlag(false);
        cisLongTermOrderNto.setReMark(cisIpdDocOrderAsNto.getReMark());
        cisLongTermOrderNto.setHospitalCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getHospitalAreaCode());
        cisLongTermOrderNto.setHospitalName(HIPCurrentOrgUtils.getCurrentOrgInfo().getHospitalAreaName());
        cisLongTermOrderNto.setEffectiveLowDate(LocalDateTime.now());
        cisLongTermOrderNto.setOrderServiceCode(cisIpdDocOrderAsNto.getServiceItemCode());
        return cisLongTermOrderNto;
    }

    /**
     * 嘱托临嘱构建
     */
    public CisTemporaryOrderNto buildEntrustSaveTempOrderNto(CisIpdDocOrderAsNto cisIpdDocOrderAsNto) {
        //构建临嘱实体
        CisTemporaryOrderNto cisTemporaryOrderNto = new CisTemporaryOrderNto();
        cisTemporaryOrderNto.setId(cisIpdDocOrderAsNto.getId());
        cisTemporaryOrderNto.setPatMiCode(cisIpdDocOrderAsNto.getPatMiCode());
        cisTemporaryOrderNto.setVisitCode(cisIpdDocOrderAsNto.getVisitCode());
        cisTemporaryOrderNto.setDeptNurseCode(cisIpdDocOrderAsNto.getDeptNurseCode());
        cisTemporaryOrderNto.setDeptNurseName(cisIpdDocOrderAsNto.getDeptNurseName());
        cisTemporaryOrderNto.setOrderContent(cisIpdDocOrderAsNto.getOrderContent());
        cisTemporaryOrderNto.setOrderClass(SystemTypeEnum.ENTRUST);
        cisTemporaryOrderNto.setExecuteOrgCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
        cisTemporaryOrderNto.setExecuteOrgName(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupName());
        cisTemporaryOrderNto.setCreateOrgCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
        cisTemporaryOrderNto.setThirdFlag(false);
        cisTemporaryOrderNto.setReMark(cisIpdDocOrderAsNto.getReMark());
        cisTemporaryOrderNto.setHospitalCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getHospitalAreaCode());
        cisTemporaryOrderNto.setHospitalName(HIPCurrentOrgUtils.getCurrentOrgInfo().getHospitalAreaName());
        cisTemporaryOrderNto.setRepairFlag(cisIpdDocOrderAsNto.getRepairFlag());
        cisTemporaryOrderNto.setOrderServiceCode(cisIpdDocOrderAsNto.getServiceItemCode());
        cisTemporaryOrderNto.setEffectiveLowDate(LocalDateTime.now());
        return cisTemporaryOrderNto;
    }

    /**
     * 备血申请单
     */
    public CisBloodApplyNto cisBloodApplyNtoToTo(CisIpdDocOrderAsNto cisIpdDocOrderAsNto) {
        CisBloodApplyNto cisPreparationBloodApplyNto = cisIpdDocOrderAsNto.getCisBloodApplyNto();
        String applyId = "AP_" + HIPIDUtil.getNextIdString();
        CurrentOrgInfo currentOrgInfo = HIPCurrentOrgUtils.getCurrentOrgInfo();
        cisPreparationBloodApplyNto.setId(applyId);
        cisPreparationBloodApplyNto.setOrderID(cisIpdDocOrderAsNto.getId());
        cisPreparationBloodApplyNto.setVisitType(VisitTypeEnum.IPD);
        cisPreparationBloodApplyNto.setVisitOrgCode(currentOrgInfo.getDeptCode());
        cisPreparationBloodApplyNto.setVisitOrgName(currentOrgInfo.getDeptName());
        cisPreparationBloodApplyNto.setCreateOrgCode(currentOrgInfo.getDeptCode());
        cisPreparationBloodApplyNto.setCreateOrgName(currentOrgInfo.getDeptName());
        cisPreparationBloodApplyNto.setNum(1d);
        cisPreparationBloodApplyNto.setHospitalCode(currentOrgInfo.getHospitalAreaCode());
        cisPreparationBloodApplyNto.setHospitalName(currentOrgInfo.getHospitalAreaName());
        cisPreparationBloodApplyNto.setCreateOrgCode(currentOrgInfo.getWorkGroupCode());
        cisPreparationBloodApplyNto.setCreateOrgName(currentOrgInfo.getWorkGroupName());

        //备血申请单明细
        List<CisApplyChargeNto> cisApplyCharges = new ArrayList<>();
        List<CisBloodComponentNto> details = cisPreparationBloodApplyNto.getDetails();
        AtomicReference<String> serviceItemCode = new AtomicReference<>("");
        AtomicReference<String> serviceItemName = new AtomicReference<>("");
        details.forEach(cisBloodComponentNto -> {
            serviceItemCode.set(serviceItemCode.get() + "," + cisBloodComponentNto.getServiceItemCode());
            serviceItemName.set(serviceItemName.get() + "," + cisBloodComponentNto.getBloodComponent());
            cisBloodComponentNto.setCisBloodApplyId(applyId);
            cisBloodComponentNto.setVisitCode(cisPreparationBloodApplyNto.getVisitCode());
            List<ServiceClinicPriceTo> serviceClinicPriceTo = serviceClinicPriceService.getServiceClinicPricesInServiceItemCode(cisBloodComponentNto.getServiceItemCode());
            if (CollectionUtils.isNotEmpty(serviceClinicPriceTo)) {
                for (ServiceClinicPriceTo serviceClinicPrice : serviceClinicPriceTo) {
                    EconServicePriceTo econServicePriceById = econServicePriceService.getEconServicePriceById(serviceClinicPrice.getPriceItemCode());
                    //申请单收费信息
                    CisApplyChargeNto cisApplyChargeNto = new CisApplyChargeNto();
                    cisApplyChargeNto.setId(HIPIDUtil.getNextIdString());
                    cisApplyChargeNto.setOrderId(cisPreparationBloodApplyNto.getOrderID());
                    cisApplyChargeNto.setCisBaseApplyId(applyId);
                    cisApplyChargeNto.setVisitCode(cisPreparationBloodApplyNto.getVisitCode());
                    cisApplyChargeNto.setPriceItemCode(serviceClinicPrice.getPriceItemCode());
                    cisApplyChargeNto.setPriceItemName(serviceClinicPrice.getPriceItemName());
                    cisApplyChargeNto.setPrice(econServicePriceById.getPrice());
                    cisApplyChargeNto.setUnit(econServicePriceById.getUnitName());
                    cisApplyChargeNto.setNum(1.0);
                    cisApplyChargeNto.setStatusCode(CisStatusEnum.ACTIVE);
                    cisApplyChargeNto.setChargeType(CisChargeTypeEnum.DOCT);
                    cisApplyChargeNto.setExecuteOrgCode(cisPreparationBloodApplyNto.getExecutorOrgCode());
                    cisApplyChargeNto.setExecuteOrgName(cisPreparationBloodApplyNto.getExecutorOrgName());
                    cisApplyChargeNto.setChageAmount(econServicePriceById.getPrice());
                    cisApplyChargeNto.setSystemItemClass(serviceClinicPrice.getSystemItemClass());
                    cisApplyCharges.add(cisApplyChargeNto);
                }
            }
        });
        cisPreparationBloodApplyNto.setServiceItemCode(serviceItemCode.get().substring(1, serviceItemCode.get().length()));
        cisPreparationBloodApplyNto.setServiceItemName(serviceItemName.get().substring(1, serviceItemName.get().length()));
        cisPreparationBloodApplyNto.setCisApplyCharges(cisApplyCharges);
        return cisPreparationBloodApplyNto;
    }
}
