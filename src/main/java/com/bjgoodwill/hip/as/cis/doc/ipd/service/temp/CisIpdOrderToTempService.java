package com.bjgoodwill.hip.as.cis.doc.ipd.service.temp;

import com.bjgoodwill.hip.as.cis.doc.ipd.to.OrderIssuedPickUpsAsQto;
import com.bjgoodwill.hip.as.cis.doc.ipd.to.OrderIssuedPickUpsAsTo;
import com.bjgoodwill.hip.as.cis.doc.ipd.to.cdrug.CdrugOpenParameterTo;
import com.bjgoodwill.hip.as.cis.doc.ipd.to.serviceitem.ServiceClinicPriceAsTo;
import com.bjgoodwill.hip.as.cis.doc.ipd.to.temp.CisOrderTempDetailAsToOrderNto;
import com.bjgoodwill.hip.as.cis.doc.ipd.to.temp.DgimgItemTempDataSource;
import com.bjgoodwill.hip.ds.base.cis.dict.usage.to.UsageTo;
import com.bjgoodwill.hip.ds.cis.medicineitem.serviceItem.to.SpcobsApplyTo;
import com.bjgoodwill.hip.ds.org.api.to.WorkGroupPharmacyTo;

import java.util.List;

/**
 * <AUTHOR> liangyuqing
 * @description :
 * @createDate : 2025/1/9 11:26
 */
public interface CisIpdOrderToTempService {

    /**
     * 查询检查项目信息
     * @param deviceType
     * @return
     */
    DgimgItemTempDataSource getDgimgItemResult(String deviceType);

    /**
     * 查询检验项目信息
     * @return
     */
    List<SpcobsApplyTo> getSpcobsItems();

    /**
     * 查询服务项目对照收费项目信息
     * @param serviceItemCode
     * @return
     */
    List<ServiceClinicPriceAsTo> getServiceClinicPrice(String serviceItemCode);

    /**
     * 拾取器查询服务项目信息
     * @param orderIssuedPickUpsAsQto
     * @return
     */
    List<OrderIssuedPickUpsAsTo> getServiceItems(OrderIssuedPickUpsAsQto orderIssuedPickUpsAsQto);

    /**
     * 获取草药药房
     * @param herbType
     * @return
     */
    List<WorkGroupPharmacyTo> getCDrugWorkGroupPharmacyList(String herbType);

    /**
     * 获取草药项目
     * @param executeOrgCode
     * @return
     */
    List<OrderIssuedPickUpsAsTo> getCdrugOrderIssuedPickUpsList(String executeOrgCode);

    /**
     * 草药医嘱开立界面参数查询
     * @return
     */
    CdrugOpenParameterTo getCdrugOpenParameter();

    /**
     * 获取草药用法
     * @return
     */
    List<UsageTo> getCdrugUsageList();

    /**
     * 组套明细开立医嘱
     * @param cisOrderTempDetailAsNtoList
     */
    void batchSaveIpdOrder(List<CisOrderTempDetailAsToOrderNto> cisOrderTempDetailAsNtoList);
}
