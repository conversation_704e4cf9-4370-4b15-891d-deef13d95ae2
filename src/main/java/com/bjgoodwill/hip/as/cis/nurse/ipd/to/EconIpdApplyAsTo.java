package com.bjgoodwill.hip.as.cis.nurse.ipd.to;

import com.bjgoodwill.hip.ds.econ.ipd.bill.to.EconIpdApplyAllTo;
import io.swagger.v3.oas.annotations.media.Schema;

public class EconIpdApplyAsTo extends EconIpdApplyAllTo {

    @Schema(description = "姓名")
    private String name;

    @Schema(description = "当前床位ID")
    private String bedId;

    @Schema(description = "当前床位名称")
    private String bedName;

    @Override
    public String getName() {
        return name;
    }

    @Override
    public void setName(String name) {
        this.name = name;
    }

    public String getBedId() {
        return bedId;
    }

    public void setBedId(String bedId) {
        this.bedId = bedId;
    }

    public String getBedName() {
        return bedName;
    }

    public void setBedName(String bedName) {
        this.bedName = bedName;
    }
}
