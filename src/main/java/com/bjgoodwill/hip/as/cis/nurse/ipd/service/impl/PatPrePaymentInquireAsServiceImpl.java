package com.bjgoodwill.hip.as.cis.nurse.ipd.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.bjgoodwill.hip.as.cis.nurse.ipd.service.PatPrePaymentInquireAsService;
import com.bjgoodwill.hip.as.cis.nurse.ipd.to.EconPrepayAsQto;
import com.bjgoodwill.hip.as.cis.nurse.ipd.to.EconPrepayAsTo;
import com.bjgoodwill.hip.as.cis.nurse.ipd.to.EconPrepayDetailAsTo;
import com.bjgoodwill.hip.business.util.econ.enums.TransactionTypeEnum;
import com.bjgoodwill.hip.business.util.enums.dict.DictCodeEnum;
import com.bjgoodwill.hip.ds.econ.ipd.bill.amount.enmus.EconIpdBusinessTypeEnum;
import com.bjgoodwill.hip.ds.econ.ipd.bill.amount.enmus.PrepayInvoiceTypeEnum;
import com.bjgoodwill.hip.ds.econ.ipd.bill.amount.service.EconPrepayService;
import com.bjgoodwill.hip.ds.econ.ipd.bill.amount.to.EconPrepayInvoiceAllTo;
import com.bjgoodwill.hip.ds.econ.ipd.bill.amount.to.EconPrepayQto;
import com.bjgoodwill.hip.ds.econ.ipd.bill.amount.to.EconPrepayTo;
import com.bjgoodwill.hip.ds.pat.in.hospital.inpatient.service.PatIpdInpatientService;
import com.bjgoodwill.hip.ds.pat.in.hospital.inpatient.to.PatIpdInpatientTo;
import com.bjgoodwill.hip.ds.term.api.service.DictElementService;
import com.bjgoodwill.hip.ds.term.api.to.DictElementTo;
import io.micrometer.common.util.StringUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Date 2025/4/2 10:31
 */
@Service("com.bjgoodwill.hip.as.cis.nurse.ipd.service.PatPrePaymentInquireAsService")
public class PatPrePaymentInquireAsServiceImpl implements PatPrePaymentInquireAsService {

    @Autowired
    private EconPrepayService econPrepayService;
    @Autowired
    private PatIpdInpatientService patIpdInpatientService;
    @Autowired
    private DictElementService dictElementService;

    @Override
    public EconPrepayAsTo getPrePayInfo(EconPrepayAsQto econPrepayAsQto) {
        EconPrepayAsTo econPrepayAsTo = new EconPrepayAsTo();
        List<EconPrepayDetailAsTo> econPrepayDetails = new ArrayList<>();
        List<DictElementTo> customDictElement = dictElementService.getCustomDictElement(DictCodeEnum.费别.getCode());
        EconPrepayQto econPrepayQto = new EconPrepayQto();
        econPrepayQto.setCreatedStaff(econPrepayAsQto.getPayeeCode());
        econPrepayQto.setTransactionType(StringUtils.isEmpty(econPrepayAsQto.getTransactionType()) ? null : TransactionTypeEnum.valueOf(econPrepayAsQto.getTransactionType()));
        econPrepayQto.setBusinessType(econPrepayAsQto.getBusinessType().equals("1") ? null : EconIpdBusinessTypeEnum.TRADE);
        econPrepayQto.setMinQueryDate(econPrepayAsQto.getMinQueryDate());
        econPrepayQto.setMaxQueryDate(econPrepayAsQto.getMaxQueryDate());
        List<EconPrepayInvoiceAllTo> econPrepayInvoiceAll = econPrepayService.getEconPrepayInvoiceAll(econPrepayQto);

        //住院号、护理组编码条件过滤
        if (StringUtils.isNotEmpty(econPrepayAsQto.getInpatientCode()) || StringUtils.isNotEmpty(econPrepayAsQto.getNurseDeptCode())) {
            List<String> visitCodes = new ArrayList<>();
            List<String> visitCodeList = econPrepayInvoiceAll.stream().map(EconPrepayInvoiceAllTo::getVisitCode).distinct().toList();
            if (CollectionUtils.isNotEmpty(visitCodeList)) {
                List<PatIpdInpatientTo> patIpdInpatientTos = patIpdInpatientService.getConsultationsPat(visitCodeList);
                //过滤患者信息
                if (StringUtils.isNotEmpty(econPrepayAsQto.getInpatientCode()) && StringUtils.isNotEmpty(econPrepayAsQto.getNurseDeptCode())) {
                    visitCodes.addAll(patIpdInpatientTos.stream().filter(pat -> pat.getInpatientCode().equals(econPrepayAsQto.getInpatientCode()) && pat.getDeptNurseCode().equals(econPrepayAsQto.getNurseDeptCode())).map(PatIpdInpatientTo::getVisitCode).toList());
                }else if (StringUtils.isNotEmpty(econPrepayAsQto.getInpatientCode()) && StringUtils.isEmpty(econPrepayAsQto.getNurseDeptCode())) {
                    visitCodes.addAll(patIpdInpatientTos.stream().filter(pat -> pat.getInpatientCode().equals(econPrepayAsQto.getInpatientCode())).map(PatIpdInpatientTo::getVisitCode).toList());
                }else if (StringUtils.isEmpty(econPrepayAsQto.getInpatientCode()) && StringUtils.isNotEmpty(econPrepayAsQto.getNurseDeptCode())) {
                    visitCodes.addAll(patIpdInpatientTos.stream().filter(pat -> pat.getDeptNurseCode().equals(econPrepayAsQto.getNurseDeptCode())).map(PatIpdInpatientTo::getVisitCode).toList());
                }
            }
            if (CollectionUtils.isEmpty(visitCodes)) {
                econPrepayAsTo.setEconPrepayDetails(econPrepayDetails);
                return econPrepayAsTo;
            }
            econPrepayInvoiceAll = econPrepayInvoiceAll.stream().filter(econ -> visitCodes.contains(econ.getVisitCode())).toList();
        }

        Map<String, List<EconPrepayInvoiceAllTo>> prePaysByVisitCode = econPrepayInvoiceAll.stream().collect(Collectors.groupingBy(EconPrepayInvoiceAllTo::getVisitCode));
        //查询患者信息
        if (CollectionUtil.isNotEmpty(prePaysByVisitCode)) {
            prePaysByVisitCode.forEach((visitCode, econPrepayList) -> {
                PatIpdInpatientTo patInfo = patIpdInpatientService.getPatIpdInpatientByVisitCode(visitCode);
                econPrepayList.forEach(econPrepay -> {
                    EconPrepayDetailAsTo econPrepayDetailAsTo = new EconPrepayDetailAsTo();
                    econPrepayDetailAsTo.setInpatientNo(patInfo.getInpatientCode());
                    econPrepayDetailAsTo.setName(patInfo.getName());
                    econPrepayDetailAsTo.setSex(patInfo.getSexName());
                    econPrepayDetailAsTo.setFeeType(patInfo.getFeeType());
                    if (patInfo.getFeeType() != null) {
                        List<DictElementTo> list = customDictElement.stream().filter(a -> a.getElementCode().equals(patInfo.getFeeType())).toList();
                        if (CollectionUtils.isNotEmpty(list)) {
                            econPrepayDetailAsTo.setFeeTypeName(list.get(0).getElementName());
                        }
                    }
                    econPrepayDetailAsTo.setDeptNurseName(patInfo.getDeptNurseName());
                    econPrepayDetailAsTo.setName(patInfo.getName());
                    econPrepayDetailAsTo.setTotalPayDate(econPrepay.getCreatedDate());
                    econPrepayDetailAsTo.setBusinessType(EconIpdBusinessTypeEnum.BUS.equals(econPrepay.getBusinessType()) ? "是" : "-");
                    econPrepayDetailAsTo.setPrePayCode(econPrepay.getInvoiceNo());
                    if (econPrepay.getInvoiceType().equals(PrepayInvoiceTypeEnum.BACK) ||
                            econPrepay.getInvoiceType().equals(PrepayInvoiceTypeEnum.RECALL)) {
                        econPrepayDetailAsTo.setAmount(econPrepay.getAmount().abs().negate());
                    }else {
                        econPrepayDetailAsTo.setAmount(econPrepay.getAmount());
                    }
                    econPrepayDetailAsTo.setPaywayName(econPrepay.getPaywayName());
                    econPrepayDetailAsTo.setStatusCode(econPrepay.getTransactionType().getName()+"预交金");
                    econPrepayDetailAsTo.setPayeeName(econPrepay.getCreatedStaffName());
                    econPrepayDetailAsTo.setTallyFlag(econPrepay.isPrepayTallyFlag() ? "已结" : "未结");
                    econPrepayDetails.add(econPrepayDetailAsTo);
                });
            });
            econPrepayAsTo.setEconPrepayDetails(econPrepayDetails);
        }
        //计算金额总和
        BigDecimal reduce = econPrepayDetails.stream().map(EconPrepayDetailAsTo::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        econPrepayAsTo.setTotalAmount(reduce);
        //计算票据张数
        List<String> list = econPrepayInvoiceAll.stream().map(EconPrepayInvoiceAllTo::getInvoiceNo).distinct().toList();
        econPrepayAsTo.setInvoiceNum(CollectionUtils.isNotEmpty(list) ? String.valueOf(list.size()) : "0");

        return econPrepayAsTo;
    }
}
