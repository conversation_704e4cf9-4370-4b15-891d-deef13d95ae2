package com.bjgoodwill.hip.as.cis.nurse.ipd.service.impl;

import com.bjgoodwill.hip.as.cis.nurse.ipd.enums.CisNurseIpdBusinessErrorEnum;
import com.bjgoodwill.hip.as.cis.nurse.ipd.service.NurseFeeWarningQueryAsService;
import com.bjgoodwill.hip.as.cis.nurse.ipd.to.CisOrderExecAsTo;
import com.bjgoodwill.hip.as.cis.nurse.ipd.to.EconPrepayWarningAsTo;
import com.bjgoodwill.hip.business.util.enums.dict.DictCodeEnum;
import com.bjgoodwill.hip.common.bean.LoginInfo;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.common.util.HIPBeanUtil;
import com.bjgoodwill.hip.ds.cis.cpoe.diagnose.service.CisIpdDiagnoseService;
import com.bjgoodwill.hip.ds.cis.cpoe.diagnose.to.CisIpdDiagnoseTo;
import com.bjgoodwill.hip.ds.econ.ipd.bill.amount.service.EconIpdAmountService;
import com.bjgoodwill.hip.ds.econ.ipd.bill.amount.service.EconPrepayArrearWarningService;
import com.bjgoodwill.hip.ds.econ.ipd.bill.amount.to.*;
import com.bjgoodwill.hip.ds.pat.in.hospital.inpatient.service.PatIpdInpatientService;
import com.bjgoodwill.hip.ds.pat.in.hospital.inpatient.to.PatIpdInpatientQto;
import com.bjgoodwill.hip.ds.pat.in.hospital.inpatient.to.PatIpdInpatientTo;
import com.bjgoodwill.hip.ds.term.api.service.DictElementService;
import com.bjgoodwill.hip.ds.term.api.to.DictElementTo;
import com.bjgoodwill.hip.security.bean.CurrentOrgInfo;
import com.bjgoodwill.hip.security.util.HIPCurrentOrgUtils;
import com.bjgoodwill.hip.security.util.HIPSecurityUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

@Service("com.bjgoodwill.hip.as.cis.nurse.ipd.service.NurseFeeWarningQueryAsService")
public class NurseFeeWarningQueryAsServiceImpl implements NurseFeeWarningQueryAsService {

    @Autowired
    private EconIpdAmountService econIpdAmountService;

    @Autowired
    private PatIpdInpatientService patIpdInpatientService;

    @Autowired
    private EconPrepayArrearWarningService econPrepayArrearWarningService;

    @Autowired
    private DictElementService dictElementService;

    @Autowired
    private CisIpdDiagnoseService cisIpdDiagnoseService;

    @Override
    public List<EconPrepayArrearWarningTo> queryWarnings() {
        LoginInfo loginInfo = HIPSecurityUtils.getLoginInfo();
        BusinessAssert.notNull(loginInfo, CisNurseIpdBusinessErrorEnum.CIS_NURSE_IPD_0001, "登录信息");
        CurrentOrgInfo currentOrgInfo = HIPCurrentOrgUtils.getCurrentOrgInfo();
        List<EconPrepayArrearWarningTo> econPrepayArrearWarningToList = new ArrayList<>();
        List<DictElementTo> dictElementToList = dictElementService.getEnableCustomDictElement(DictCodeEnum.费别.getCode());
        EconPrepayArrearWarningQto econPrepayArrearWarningQto =new EconPrepayArrearWarningQto();
        econPrepayArrearWarningQto.setOrgCode(currentOrgInfo.getWorkGroupCode());
        List<EconPrepayArrearWarningTo> warningTos = econPrepayArrearWarningService.getEconPrepayArrearWarnings(econPrepayArrearWarningQto);
        if (CollectionUtils.isNotEmpty(dictElementToList)){
            Map<String,EconPrepayArrearWarningTo> map = null;
            if (CollectionUtils.isNotEmpty(warningTos)){
                map = warningTos.stream().collect(Collectors.toMap(EconPrepayArrearWarningTo:: getCode, each ->each,(value1, value2)->value1));
            }
            for (DictElementTo elementTo : dictElementToList){
                EconPrepayArrearWarningTo econPrepayArrearWarningTo = new EconPrepayArrearWarningTo();
                if (map != null && map.get(elementTo.getElementCode()) != null) {
                    econPrepayArrearWarningTo = HIPBeanUtil.copy(map.get(elementTo.getElementCode()),EconPrepayArrearWarningTo.class);
                }else {
                    econPrepayArrearWarningTo.setOrgCode(currentOrgInfo.getWorkGroupCode());
                    econPrepayArrearWarningTo.setOrgName(currentOrgInfo.getWorkGroupName());
                    econPrepayArrearWarningTo.setCode(elementTo.getElementCode());
                    econPrepayArrearWarningTo.setName(elementTo.getElementName());
                }
                econPrepayArrearWarningToList.add(econPrepayArrearWarningTo);
            }
        }
        return econPrepayArrearWarningToList;
    }

    @Override
    public List<EconPrepayWarningAsTo> getAmountWarningList(List<EconPrepayArrearWarningNto> warningNtos) {
        LoginInfo loginInfo = HIPSecurityUtils.getLoginInfo();
        BusinessAssert.notNull(loginInfo, CisNurseIpdBusinessErrorEnum.CIS_NURSE_IPD_0001, "登录信息");
        CurrentOrgInfo currentOrgInfo = HIPCurrentOrgUtils.getCurrentOrgInfo();
        List<EconPrepayWarningAsTo> econPrepayWarningAsToList = new ArrayList<>();
        List<PatIpdInpatientTo> patIpdInpatientToList = patIpdInpatientService.getInDeptPatForNurse(currentOrgInfo.getWorkGroupCode());
        if (CollectionUtils.isNotEmpty(patIpdInpatientToList)){
            HIPBeanUtil.copy(patIpdInpatientToList,econPrepayWarningAsToList,EconPrepayWarningAsTo.class);
            //患者诊断信息查询
            Map<String,List<CisIpdDiagnoseTo>> diagnoseMap =new HashMap<>();
            List<String> visitCodeList = patIpdInpatientToList.stream().map(PatIpdInpatientTo::getVisitCode).distinct().toList();
            List<CisIpdDiagnoseTo> cisIpdDiagnoseToList = cisIpdDiagnoseService.getCisIpdDiagnoseByVisitCode(visitCodeList);
            if (CollectionUtils.isNotEmpty(cisIpdDiagnoseToList)) {
                diagnoseMap = cisIpdDiagnoseToList.stream().collect(Collectors.groupingBy(CisIpdDiagnoseTo::getVisitCode));
            }
            Map<String, String> visitOfFeeMap = new HashMap<>();
            patIpdInpatientToList.forEach(p->{
                if (p.getFeeType() != null){
                    visitOfFeeMap.put(p.getVisitCode(),p.getFeeType());
                }
            });
            EconIpdAmountWarningQto qto = new EconIpdAmountWarningQto();
            qto.setOrgCode(currentOrgInfo.getWorkGroupCode());
            qto.setVisitOfFeeMap(visitOfFeeMap);
            if(CollectionUtils.isNotEmpty(warningNtos)){
                Map<String, BigDecimal> feeOfAmountMap = new HashMap<>();
                warningNtos.forEach(f -> {
                    feeOfAmountMap.put(f.getCode(),f.getAmount());
                });
                qto.setFeeOfAmountMap(feeOfAmountMap);
            }
            List<EconIpdAmountTo> amountWarningList = econIpdAmountService.getAmountWarningList(qto);
            Map<String,EconIpdAmountTo> econIpdAmountToMap = null;
            if (CollectionUtils.isNotEmpty(amountWarningList)){
                econIpdAmountToMap = amountWarningList.stream().collect(Collectors.toMap(EconIpdAmountTo:: getVisitCode, each ->each,(value1, value2)->value1));
            }
            for (EconPrepayWarningAsTo econPrepayWarningAsTo : econPrepayWarningAsToList){
                int inpatientDay = this.calculateDaysSinceAdmission(econPrepayWarningAsTo.getInTime(), LocalDateTime.now());
                econPrepayWarningAsTo.setInpatientDays(inpatientDay + "天");
                econPrepayWarningAsTo.setFeeTypeName(econPrepayWarningAsTo.getFeeType() == null ? null : "08".equals(econPrepayWarningAsTo.getFeeType()) ? "自费" : "医保");
                //诊断信息赋值
                List<CisIpdDiagnoseTo> diagnoseTos = diagnoseMap.get(econPrepayWarningAsTo.getVisitCode());
                if (CollectionUtils.isNotEmpty(diagnoseTos)){
                    StringJoiner diagnoseNames = new StringJoiner(",");
                    List<CisIpdDiagnoseTo> mains = diagnoseTos.stream().filter(CisIpdDiagnoseTo::getIsChief).toList();//主诊断
                    if (CollectionUtils.isNotEmpty(mains)){
                        List<CisIpdDiagnoseTo> tempList = new ArrayList<>(mains);
                        tempList.sort(Comparator.comparing(CisIpdDiagnoseTo::getCreatedDate, Comparator.nullsLast(Comparator.reverseOrder())).reversed());
                        tempList.forEach(d ->{
                            diagnoseNames.add(d.getDiagnosisName());
                        });
                    }
                    econPrepayWarningAsTo.setImpDiagnosis(diagnoseNames.toString());
                }

                if (econIpdAmountToMap !=null && econIpdAmountToMap.get(econPrepayWarningAsTo.getVisitCode()) != null){
                    EconIpdAmountTo econIpdAmountTo = econIpdAmountToMap.get(econPrepayWarningAsTo.getVisitCode());
                    econPrepayWarningAsTo.setTotalAmount(econIpdAmountTo.getTotalAmount());
                    econPrepayWarningAsTo.setPrepayAmount(econIpdAmountTo.getPrepayAmount());
                    econPrepayWarningAsTo.setGuaranteAmount(econIpdAmountTo.getGuaranteAmount());
                    //todo 医保报销金额改为调用医保接口获取
                    econPrepayWarningAsTo.setPubAmount("");
                    econPrepayWarningAsTo.setBalance(econIpdAmountTo.getBalance());
                }
            }
        }

        return econPrepayWarningAsToList;
    }

    private int calculateDaysSinceAdmission(LocalDateTime admissionTime, LocalDateTime currentTime) {
        if (currentTime == null) {
            currentTime = LocalDateTime.now();
        }
        // 计算入院时间到当前时间的小时数
        long hoursSinceAdmission = Duration.between(admissionTime, currentTime).toHours();
        // 计算天数
        int days = 0;
        if (hoursSinceAdmission <= 24) {
            // 未超过24小时，算第一天
            days = 1;
        } else {
            // 超过24小时，计算完整的天数
            days = (int) (hoursSinceAdmission / 24);
            // 检查是否过了00:00点
            LocalDateTime midnight = currentTime.plusDays(0).truncatedTo(ChronoUnit.DAYS);
            if (currentTime.isAfter(midnight)) {
                days += 1;
            }
        }
        return days;
    }
}
