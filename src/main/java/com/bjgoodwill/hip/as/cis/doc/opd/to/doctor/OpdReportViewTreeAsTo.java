package com.bjgoodwill.hip.as.cis.doc.opd.to.doctor;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(description = "门诊医生报告查看左侧树To")
public class OpdReportViewTreeAsTo {

    @Schema(description = "申请单信息")
    List<OpdReportViewAsTo> children;
    @Schema(description = "给前端的唯一标识")
    private String applyCode;
    @Schema(description = "医嘱编码")
    private String serviceItemCode;
    @Schema(description = "医嘱名称")
    private String serviceItemName;
    @Schema(description = "格式化时间")
    private String dateFormatStr;
    @Schema(description = "给前端的冗余字段")
    private String caption;

    public OpdReportViewTreeAsTo(String applyCode, String serviceItemCode, String serviceItemName, String dateFormatStr, String caption, List<OpdReportViewAsTo> children) {
        this.applyCode = applyCode;
        this.serviceItemCode = serviceItemCode;
        this.serviceItemName = serviceItemName;
        this.dateFormatStr = dateFormatStr;
        this.caption = caption;
        this.children = children;
    }

    public String getCaption() {
        return caption;
    }

    public void setCaption(String caption) {
        this.caption = caption;
    }

    public String getApplyCode() {
        return applyCode;
    }

    public void setApplyCode(String applyCode) {
        this.applyCode = applyCode;
    }

    public String getServiceItemCode() {
        return serviceItemCode;
    }

    public void setServiceItemCode(String serviceItemCode) {
        this.serviceItemCode = serviceItemCode;
    }

    public String getDateFormatStr() {
        return dateFormatStr;
    }

    public void setDateFormatStr(String dateFormatStr) {
        this.dateFormatStr = dateFormatStr;
    }

    public String getServiceItemName() {
        return serviceItemName;
    }

    public void setServiceItemName(String serviceItemName) {
        this.serviceItemName = serviceItemName;
    }

    public List<OpdReportViewAsTo> getChildren() {
        return children;
    }

    public void setChildren(List<OpdReportViewAsTo> children) {
        this.children = children;
    }

}
