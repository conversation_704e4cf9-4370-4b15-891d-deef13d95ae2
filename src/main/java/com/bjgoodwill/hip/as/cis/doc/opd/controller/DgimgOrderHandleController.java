package com.bjgoodwill.hip.as.cis.doc.opd.controller;

import com.bjgoodwill.hip.as.cis.doc.opd.service.DgimgOrderHandleService;
import com.bjgoodwill.hip.as.cis.doc.opd.to.dgimg.CisDgimgApplyAsTo;
import com.bjgoodwill.hip.as.cis.doc.opd.to.dgimg.DgimgOrderDto;
import com.bjgoodwill.hip.as.cis.doc.opd.to.dgimg.OpdDgimgItemResult;
import com.bjgoodwill.hip.as.cis.doc.opd.to.dgimg.OpdDgimgServiceItemAsTo;
import com.bjgoodwill.hip.as.cis.doc.opd.to.doctor.CisApplyChargeAsTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR> liangyuqing
 * @description :
 * @createDate : 2025/5/19 10:07
 */
@RestController
@Tag(name = "门诊检查医嘱开立应用服务", description = "检查医嘱开立服务类")
@RequestMapping("/cis/doc/opd/dgimg")
public class DgimgOrderHandleController {

    @Autowired
    private DgimgOrderHandleService dgimgOrderHandleService;

    @Operation(summary = "门诊检查项目查询", description = "")
    @ApiResponse(description = "", content = @Content(mediaType = "application/json", schema = @Schema(implementation = OpdDgimgItemResult.class)))
    @GetMapping("/item")
    public OpdDgimgItemResult getItem(@RequestParam(name = "deviceType", required = false) String deviceType,
                                      @RequestParam(name = "groupCode", required = false) String workGroupCode,
                                      @RequestParam(name = "areaCode", required = false) String hospitalAreaCode,
                                      @RequestParam(name = "visitCode", required = false) String visitCode) {
        return dgimgOrderHandleService.getOpdDgimgItemResult(deviceType, hospitalAreaCode, workGroupCode, visitCode);
    }

    @Operation(summary = "医生常用检查项目查询", description = "")
    @ApiResponse(description = "", content = @Content(mediaType = "application/json", schema = @Schema(implementation = OpdDgimgServiceItemAsTo.class)))
    @GetMapping("/common/item/{device-type}/{groupCode}/{areaCode}")
    public List<OpdDgimgServiceItemAsTo> getCommonItems(@PathVariable("device-type") String deviceType, @PathVariable("groupCode") String workGroupCode,
                                                        @PathVariable("areaCode") String hospitalAreaCode) {
        return dgimgOrderHandleService.commonDgimgOrderSearch(deviceType, hospitalAreaCode, workGroupCode);
    }

    @Operation(summary = "查询检查项目对应费用明细", description = "")
    @ApiResponse(description = "", content = @Content(mediaType = "application/json", schema = @Schema(implementation = CisApplyChargeAsTo.class)))
    @GetMapping("/price/item/{code}")
    public List<CisApplyChargeAsTo> getServiceClincPriceList(@PathVariable("code") String serviceItemCode) {
        return dgimgOrderHandleService.getServiceClincPriceList(serviceItemCode);
    }

    @Operation(summary = "新增/修改检查医嘱", description = "新增检验医嘱")
    @ApiResponse(description = "新增/修改检查医嘱", content = @Content(mediaType = "application/json"))
    @PostMapping(value = "/insert/update")
    public void insertOrUpdateDgimgOrders(@RequestBody @Valid DgimgOrderDto dgimgOrderDto) {
        dgimgOrderHandleService.insertOrUpdateDgimgOrders(dgimgOrderDto);
    }

    @Operation(summary = "查询未签发检查申请单列表", description = "查询未签发检验申请单")
    @ApiResponse(description = "查询未签发检查申请单列表", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = CisDgimgApplyAsTo.class))))
    @GetMapping(value = "/order/new/{visitCode}")
    public List<CisDgimgApplyAsTo> getNewDgimgApplys(@PathVariable("visitCode") String visitCode) {
        return dgimgOrderHandleService.getNewDgimgApplys(visitCode);
    }

    @Operation(summary = "查询已签发检查申请单列表", description = "查询已签发检验申请单")
    @ApiResponse(description = "查询已签发检查申请单列表", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = CisDgimgApplyAsTo.class))))
    @GetMapping(value = "/order/active/{visitCode}")
    public List<CisDgimgApplyAsTo> getActiveDgimgApplys(@PathVariable("visitCode") String visitCode) {
        return dgimgOrderHandleService.getActiveDgimgApplys(visitCode);
    }

    @Operation(summary = "查询检查医嘱信息", description = "查询检查医嘱信息")
    @ApiResponse(description = "查询检查医嘱信息", content = @Content(mediaType = "application/json", schema = @Schema(implementation = DgimgOrderDto.class)))
    @GetMapping(value = "/order/{orderId}")
    public DgimgOrderDto getDgimgOrderDto(@PathVariable("orderId") String orderId) {
        return dgimgOrderHandleService.getDgimgOrder(orderId);
    }

    @Operation(summary = "签发检查医嘱", description = "签发检查医嘱")
    @ApiResponse(description = "签发检查医嘱", content = @Content(mediaType = "application/json"))
    @PostMapping(value = "/submit/{visitCode}")
    public void submitDgimgOrder(@RequestBody List<String> orderIds, @PathVariable("visitCode") String visitCode) {
        dgimgOrderHandleService.submitDgimgOrders(orderIds, visitCode);
    }

    @Operation(summary = "新增/修改并签发检查医嘱", description = "新增/修改并签发检查医嘱")
    @ApiResponse(description = "新增/修改并签发检查医嘱", content = @Content(mediaType = "application/json"))
    @PostMapping(value = "/insert/update/submit")
    public void insertOrUpdateAndSubmitDgimgOrders(@RequestBody @Valid DgimgOrderDto dgimgOrderDto) {
        dgimgOrderHandleService.insertOrUpdateAndSubmitDgimgOrders(dgimgOrderDto);
    }

    @Operation(summary = "撤回检查医嘱", description = "撤回检查医嘱")
    @ApiResponse(description = "撤回检查医嘱", content = @Content(mediaType = "application/json"))
    @PostMapping(value = "/back")
    public void backDgimgOrder(@RequestBody List<String> orderIds) {
        dgimgOrderHandleService.backDgimgOrders(orderIds);
    }

    @Operation(summary = "删除检查医嘱", description = "删除检查医嘱")
    @ApiResponse(description = "删除检查医嘱", content = @Content(mediaType = "application/json"))
    @PostMapping(value = "/delete")
    public void deleteDgimgOrder(@RequestBody List<String> orderIds) {
        dgimgOrderHandleService.deleteDgimgOrders(orderIds);
    }

    @Operation(summary = "已签发检查医嘱，修改并签发", description = "已签发检查医嘱，修改并签发")
    @ApiResponse(description = "删除检查申请单明细", content = @Content(mediaType = "application/json"))
    @PostMapping(value = "/active/submit")
    public void activeDgimgApplyReSubmit(@RequestBody @Valid DgimgOrderDto dgimgOrderDto) {
        dgimgOrderHandleService.activeDgimgApplyReSubmit(dgimgOrderDto);
    }
}
