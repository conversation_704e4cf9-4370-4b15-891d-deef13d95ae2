package com.bjgoodwill.hip.as.cis.doc.opd.to;

import com.bjgoodwill.hip.ds.pat.index.to.PatAllergenTo;
import com.bjgoodwill.hip.ds.pat.index.to.PatCardTo;
import com.bjgoodwill.hip.ds.pat.index.to.PatRelationTo;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

@Schema(description = "患者信息")
public class PatIndexAsTo implements Serializable {

    @Serial
    private static final long serialVersionUID = -1092082148001626092L;

    @Schema(description = "索引编码")
    private String indexCode;
    @Schema(description = "患者编码")
    private String code;
    @Schema(description = "平台主索引")
    private String epmi;
    @Schema(description = "姓名")
    private String name;
    @Schema(description = "性别")
    private String sex;
    @Schema(description = "性别名称")
    private String sexName;
    @Schema(description = "证件类型")
    private String cardType;
    @Schema(description = "证件类型名称")
    private String cardTypeName;
    @Schema(description = "证件号")
    private String cardCode;
    @Schema(description = "出生日期")
    private LocalDateTime birthDate;
    @Schema(description = "年龄")
    private String age;
    @Schema(description = "费别")
    private String feeType;
    @Schema(description = "费别名称")
    private String feeTypeName;
    @Schema(description = "身份")
    private String identity;
    @Schema(description = "身份名称")
    private String identityName;
    @Schema(description = "重点人群分类")
    private String focusGroupsType;
    @Schema(description = "重点人群分类名称")
    private String focusGroupsTypeName;
    @Schema(description = "民族")
    private String nation;
    @Schema(description = "民族名称")
    private String nationName;
    @Schema(description = "国籍")
    private String nationality;
    @Schema(description = "国籍名称")
    private String nationalityName;
    @Schema(description = "婚姻")
    private String marriage;
    @Schema(description = "婚姻名称")
    private String marriageName;
    @Schema(description = "职业")
    private String work;
    @Schema(description = "职业名称")
    private String workName;
    @Schema(description = "联系电话")
    private String tel;
    @Schema(description = "籍贯")
    private String nativePlace;
    @Schema(description = "籍贯名称")
    private String nativePlaceName;
    @Schema(description = "户口地址")
    private String nativeAddr;
    @Schema(description = "户口地址名称")
    private String nativeAddrName;
    @Schema(description = "户口详细地址")
    private String nativeExactAddr;
    @Schema(description = "户口邮编")
    private String nativePostalCode;
    @Schema(description = "现住址")
    private String liveAddr;
    @Schema(description = "现住址名称")
    private String liveAddrName;
    @Schema(description = "现详细住址")
    private String liveExactAddr;
    @Schema(description = "现住址邮编")
    private String livePostalCode;
    @Schema(description = "工作单位名称")
    private String companyName;
    @Schema(description = "工作单位地址")
    private String companyAddr;
    @Schema(description = "工作单位联系电话")
    private String companyTel;
    @Schema(description = "贫困类型")
    private String poorType;
    @Schema(description = "贫困类型名称")
    private String poorTypeName;
    @Schema(description = "三无人员标志")
    private Boolean threeNoPersionnelFlag;
    @Schema(description = "职工标志")
    private Boolean staffFlag;
    @Schema(description = "abo血型")
    private String abo;
    @Schema(description = "Rh血型")
    private String rh;
    @Schema(description = "过敏标志")
    private Boolean allergyFlag;
    @Schema(description = "医生评价内容")
    private String doctorAssessment;
    @Schema(description = "出生地")
    private String birthPlace;
    @Schema(description = "出生体重")
    private String birthWeight;
    @Schema(description = "母亲患者编码")
    private String motherCode;
    @Schema(description = "母亲名称")
    private String motherName;
    @Schema(description = "已启用")
    private boolean enabled;
    @Schema(description = "创建的人员")
    private String createdStaff;
    @Schema(description = "创建的人员姓名")
    private String createdStaffName;
    @Schema(description = "创建的时间")
    private LocalDateTime createdDate;
    @Schema(description = "最后修改的人员")
    private String updatedStaff;
    @Schema(description = "最后修改的人员姓名")
    private String updatedStaffName;
    @Schema(description = "最后修改的时间")
    private LocalDateTime updatedDate;
    @Schema(description = "患者卡信息列表")
    private List<PatCardTo> patCards;
    @Schema(description = "患者联系人信息列表")
    private List<PatRelationTo> patRelations;
    @Schema(description = "患者过敏原列表")
    private List<PatAllergenTo> patAllergens;

    public String getAge() {
        return age;
    }

    public void setAge(String age) {
        this.age = age;
    }

    public String getIndexCode() {
        return indexCode;
    }

    public void setIndexCode(String indexCode) {
        this.indexCode = indexCode;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getEpmi() {
        return epmi;
    }

    public void setEpmi(String epmi) {
        this.epmi = epmi;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getSexName() {
        return sexName;
    }

    public void setSexName(String sexName) {
        this.sexName = sexName;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public String getCardTypeName() {
        return cardTypeName;
    }

    public void setCardTypeName(String cardTypeName) {
        this.cardTypeName = cardTypeName;
    }

    public String getCardCode() {
        return cardCode;
    }

    public void setCardCode(String cardCode) {
        this.cardCode = cardCode;
    }

    public LocalDateTime getBirthDate() {
        return birthDate;
    }

    public void setBirthDate(LocalDateTime birthDate) {
        this.birthDate = birthDate;
    }

    public String getFeeType() {
        return feeType;
    }

    public void setFeeType(String feeType) {
        this.feeType = feeType;
    }

    public String getFeeTypeName() {
        return feeTypeName;
    }

    public void setFeeTypeName(String feeTypeName) {
        this.feeTypeName = feeTypeName;
    }

    public String getIdentity() {
        return identity;
    }

    public void setIdentity(String identity) {
        this.identity = identity;
    }

    public String getIdentityName() {
        return identityName;
    }

    public void setIdentityName(String identityName) {
        this.identityName = identityName;
    }

    public String getFocusGroupsType() {
        return focusGroupsType;
    }

    public void setFocusGroupsType(String focusGroupsType) {
        this.focusGroupsType = focusGroupsType;
    }

    public String getFocusGroupsTypeName() {
        return focusGroupsTypeName;
    }

    public void setFocusGroupsTypeName(String focusGroupsTypeName) {
        this.focusGroupsTypeName = focusGroupsTypeName;
    }

    public String getNation() {
        return nation;
    }

    public void setNation(String nation) {
        this.nation = nation;
    }

    public String getNationName() {
        return nationName;
    }

    public void setNationName(String nationName) {
        this.nationName = nationName;
    }

    public String getNationality() {
        return nationality;
    }

    public void setNationality(String nationality) {
        this.nationality = nationality;
    }

    public String getNationalityName() {
        return nationalityName;
    }

    public void setNationalityName(String nationalityName) {
        this.nationalityName = nationalityName;
    }

    public String getMarriage() {
        return marriage;
    }

    public void setMarriage(String marriage) {
        this.marriage = marriage;
    }

    public String getMarriageName() {
        return marriageName;
    }

    public void setMarriageName(String marriageName) {
        this.marriageName = marriageName;
    }

    public String getWork() {
        return work;
    }

    public void setWork(String work) {
        this.work = work;
    }

    public String getWorkName() {
        return workName;
    }

    public void setWorkName(String workName) {
        this.workName = workName;
    }

    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    public String getNativePlace() {
        return nativePlace;
    }

    public void setNativePlace(String nativePlace) {
        this.nativePlace = nativePlace;
    }

    public String getNativePlaceName() {
        return nativePlaceName;
    }

    public void setNativePlaceName(String nativePlaceName) {
        this.nativePlaceName = nativePlaceName;
    }

    public String getNativeAddr() {
        return nativeAddr;
    }

    public void setNativeAddr(String nativeAddr) {
        this.nativeAddr = nativeAddr;
    }

    public String getNativeAddrName() {
        return nativeAddrName;
    }

    public void setNativeAddrName(String nativeAddrName) {
        this.nativeAddrName = nativeAddrName;
    }

    public String getNativeExactAddr() {
        return nativeExactAddr;
    }

    public void setNativeExactAddr(String nativeExactAddr) {
        this.nativeExactAddr = nativeExactAddr;
    }

    public String getNativePostalCode() {
        return nativePostalCode;
    }

    public void setNativePostalCode(String nativePostalCode) {
        this.nativePostalCode = nativePostalCode;
    }

    public String getLiveAddr() {
        return liveAddr;
    }

    public void setLiveAddr(String liveAddr) {
        this.liveAddr = liveAddr;
    }

    public String getLiveAddrName() {
        return liveAddrName;
    }

    public void setLiveAddrName(String liveAddrName) {
        this.liveAddrName = liveAddrName;
    }

    public String getLiveExactAddr() {
        return liveExactAddr;
    }

    public void setLiveExactAddr(String liveExactAddr) {
        this.liveExactAddr = liveExactAddr;
    }

    public String getLivePostalCode() {
        return livePostalCode;
    }

    public void setLivePostalCode(String livePostalCode) {
        this.livePostalCode = livePostalCode;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getCompanyAddr() {
        return companyAddr;
    }

    public void setCompanyAddr(String companyAddr) {
        this.companyAddr = companyAddr;
    }

    public String getCompanyTel() {
        return companyTel;
    }

    public void setCompanyTel(String companyTel) {
        this.companyTel = companyTel;
    }

    public String getPoorType() {
        return poorType;
    }

    public void setPoorType(String poorType) {
        this.poorType = poorType;
    }

    public String getPoorTypeName() {
        return poorTypeName;
    }

    public void setPoorTypeName(String poorTypeName) {
        this.poorTypeName = poorTypeName;
    }

    public Boolean getThreeNoPersionnelFlag() {
        return threeNoPersionnelFlag;
    }

    public void setThreeNoPersionnelFlag(Boolean threeNoPersionnelFlag) {
        this.threeNoPersionnelFlag = threeNoPersionnelFlag;
    }

    public Boolean getStaffFlag() {
        return staffFlag;
    }

    public void setStaffFlag(Boolean staffFlag) {
        this.staffFlag = staffFlag;
    }

    public String getAbo() {
        return abo;
    }

    public void setAbo(String abo) {
        this.abo = abo;
    }

    public String getRh() {
        return rh;
    }

    public void setRh(String rh) {
        this.rh = rh;
    }

    public Boolean getAllergyFlag() {
        return allergyFlag;
    }

    public void setAllergyFlag(Boolean allergyFlag) {
        this.allergyFlag = allergyFlag;
    }

    public String getDoctorAssessment() {
        return doctorAssessment;
    }

    public void setDoctorAssessment(String doctorAssessment) {
        this.doctorAssessment = doctorAssessment;
    }

    public String getBirthPlace() {
        return birthPlace;
    }

    public void setBirthPlace(String birthPlace) {
        this.birthPlace = birthPlace;
    }

    public String getBirthWeight() {
        return birthWeight;
    }

    public void setBirthWeight(String birthWeight) {
        this.birthWeight = birthWeight;
    }

    public String getMotherCode() {
        return motherCode;
    }

    public void setMotherCode(String motherCode) {
        this.motherCode = motherCode;
    }

    public String getMotherName() {
        return motherName;
    }

    public void setMotherName(String motherName) {
        this.motherName = motherName;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public String getCreatedStaff() {
        return createdStaff;
    }

    public void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    public String getCreatedStaffName() {
        return createdStaffName;
    }

    public void setCreatedStaffName(String createdStaffName) {
        this.createdStaffName = createdStaffName;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getUpdatedStaff() {
        return updatedStaff;
    }

    public void setUpdatedStaff(String updatedStaff) {
        this.updatedStaff = updatedStaff;
    }

    public String getUpdatedStaffName() {
        return updatedStaffName;
    }

    public void setUpdatedStaffName(String updatedStaffName) {
        this.updatedStaffName = updatedStaffName;
    }

    public LocalDateTime getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(LocalDateTime updatedDate) {
        this.updatedDate = updatedDate;
    }

    public List<PatCardTo> getPatCards() {
        return patCards;
    }

    public void setPatCards(List<PatCardTo> patCards) {
        this.patCards = patCards;
    }

    public List<PatRelationTo> getPatRelations() {
        return patRelations;
    }

    public void setPatRelations(List<PatRelationTo> patRelations) {
        this.patRelations = patRelations;
    }

    public List<PatAllergenTo> getPatAllergens() {
        return patAllergens;
    }

    public void setPatAllergens(List<PatAllergenTo> patAllergens) {
        this.patAllergens = patAllergens;
    }

    @Override
    public int hashCode() {
        return Objects.hash(code);
    }

}