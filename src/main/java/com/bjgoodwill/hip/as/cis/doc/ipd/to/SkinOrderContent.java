package com.bjgoodwill.hip.as.cis.doc.ipd.to;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR> l<PERSON>gy<PERSON><PERSON>
 * @description :
 * @createDate : 2024/12/25 16:40
 */
@Schema(description = "皮试医嘱OrderContent")
public class SkinOrderContent {

    @Schema(description = "急标识 有就写 没有就不写")
    private String isCanPriorityFlag;

    @Schema(description = "皮试医嘱名称【drugSpec】(executeOrgName)")
    private String leftText;

    @Schema(description = "remark")
    private String rightText;

    public String getIsCanPriorityFlag() {
        return isCanPriorityFlag;
    }

    public void setIsCanPriorityFlag(String isCanPriorityFlag) {
        this.isCanPriorityFlag = isCanPriorityFlag;
    }

    public String getLeftText() {
        return leftText;
    }

    public void setLeftText(String leftText) {
        this.leftText = leftText;
    }

    public String getRightText() {
        return rightText;
    }

    public void setRightText(String rightText) {
        this.rightText = rightText;
    }
}
