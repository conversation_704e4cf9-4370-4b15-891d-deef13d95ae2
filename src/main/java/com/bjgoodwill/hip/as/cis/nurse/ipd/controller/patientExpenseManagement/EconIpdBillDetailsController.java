package com.bjgoodwill.hip.as.cis.nurse.ipd.controller.patientExpenseManagement;

import com.bjgoodwill.hip.as.cis.nurse.ipd.service.EconIpdBillDetailsAsService;
import com.bjgoodwill.hip.as.cis.nurse.ipd.to.patientExpenseManagement.EconIpdBillDetailsAsQto;
import com.bjgoodwill.hip.as.cis.nurse.ipd.to.patientExpenseManagement.EconIpdBillFeeClassAsTo;
import com.bjgoodwill.hip.ds.org.api.to.WorkGroupTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * File: EconIpdBillDetailsController
 * Author: zhangyunchuan
 * Date: 2025/2/25
 * Description:
 */
@Tag(name = "患者费用管理-费用明细查询应用服务", description = "患者费用管理-费用明细查询应用服务类")
@RestController("com.bjgoodwill.hip.as.cis.nurse.ipd.controller.patientExpenseManagement.EconIpdBillDetailsController")
@RequestMapping(value = "/cis/nurse/ipd/econ/bill/details", produces = "application/json; charset=utf-8")
public class EconIpdBillDetailsController {

    @Autowired
    private EconIpdBillDetailsAsService econIpdBillDetailsAsService;

    @Operation(summary = "[在院科室]下拉框数据源获取", description = "")
    @ApiResponse(description = "", content = @Content(mediaType = "application/json", schema = @Schema(implementation = WorkGroupTo.class)))
    @GetMapping("/inHospitalDept/dataSource/{visitCode}")
    public List<WorkGroupTo> inHospitalDeptDataSource(@PathVariable("visitCode") String visitCode) {
        return econIpdBillDetailsAsService.inHospitalDeptDataSource(visitCode);
    }

    @Operation(summary = "费用明细查询(按费用类型汇总)", description = "")
    @ApiResponse(description = "", content = @Content(mediaType = "application/json", schema = @Schema(implementation = EconIpdBillFeeClassAsTo.class)))
    @PostMapping("/getEconIpdBillDetails/byFeeClass")
    public List<EconIpdBillFeeClassAsTo> getEconIpdBillDetailsByFeeClass(@RequestBody @Valid EconIpdBillDetailsAsQto econIpdBillDetailsAsQto) {
        return econIpdBillDetailsAsService.getEconIpdBillDetailsByFeeClass(econIpdBillDetailsAsQto);
    }

    @Operation(summary = "费用明细查询(按科室汇总)", description = "")
    @ApiResponse(description = "", content = @Content(mediaType = "application/json", schema = @Schema(implementation = EconIpdBillFeeClassAsTo.class)))
    @PostMapping("/getEconIpdBillDetails/byDeptCode")
    public List<EconIpdBillFeeClassAsTo> getEconIpdBillDetailsByDeptCode(@RequestBody @Valid EconIpdBillDetailsAsQto econIpdBillDetailsAsQto) {
        return econIpdBillDetailsAsService.getEconIpdBillDetailsByDeptCode(econIpdBillDetailsAsQto);
    }
}
