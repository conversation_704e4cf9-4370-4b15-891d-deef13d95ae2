package com.bjgoodwill.hip.as.cis.doc.ipd.to;

import com.bjgoodwill.hip.business.util.cis.common.enums.SkinSystemTypeExtEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @description :
 * @createDate : 2024/12/24 9:39
 */
@Schema(description = "皮试液替代物显示To")
public class SkinLimitReplaceMentAsTo implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "替代物代码")
    private String replacementCode;

    @Schema(description = "替代物名称")
    private String replacementName;

    @Schema(description = "是否可选")
    private boolean canSelected;

    @Schema(description = "DOPE(原液),SOL(皮试液),NO(不皮试)")
    private String type;

    public String getReplacementCode() {
        return replacementCode;
    }

    public void setReplacementCode(String replacementCode) {
        this.replacementCode = replacementCode;
    }

    public String getReplacementName() {
        return replacementName;
    }

    public void setReplacementName(String replacementName) {
        this.replacementName = replacementName;
    }

    public boolean isCanSelected() {
        return canSelected;
    }

    public void setCanSelected(boolean canSelected) {
        this.canSelected = canSelected;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
