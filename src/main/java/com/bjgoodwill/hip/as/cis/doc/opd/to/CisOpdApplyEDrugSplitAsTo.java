package com.bjgoodwill.hip.as.cis.doc.opd.to;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.math.BigDecimal;

@Schema(description = "药品类申请单-成药")
public class CisOpdApplyEDrugSplitAsTo implements Serializable {

    @Schema(description = "明细标识ID")
    private String detailId;

    @Schema(description = "医嘱编码")
    private String serviceItemCode;

    @Schema(description = "医嘱名称")
    private String serviceItemName;

    @Schema(description = "滴速")
    private String dripSpeed;

    @Schema(description = "滴速单位")
    private String dripSpeedUnit;

    @Schema(description = "每次剂量")
    private Double dosage;

    @Schema(description = "剂量单位")
    private String dosageUnit;

    @Schema(description = "通用-包装总量")
    private Double packageNum;

    @Schema(description = "通用-包装单位 MinUnit/PackageUnit")
    private String packageUnit;

    @Schema(description = "用法")
    private String usage;

    @Schema(description = "用法名称")
    private String usageName;

    @Schema(description = "频次")
    private String frequency;

    @Schema(description = "频次名称")
    private String frequencyName;

    @Schema(description = "疗程")
    private String treatmentCourse;

    @Schema(description = "疗程单位")
    private String treatmentCourseUnit;

    @Schema(description = "协定处方")
    private Boolean prescriptionFlag;

    @Schema(description = "领药科室")
    private String receiveOrg;

    @Schema(description = "规格")
    private String drugSpec;

    @Schema(description = "执行科室")
    private String executeOrgName;

    @Schema(description = "单价")
    private BigDecimal price;

    @Schema(description = "首日点")
    private String firstDayTimepoint;

    @Schema(description = "剂量单位名称 字典DosageUnit")
    private String dosageUnitName;

    @Schema(description = "抗菌药标识")
    private Boolean antibacterialFlag;

    @Schema(description = "毒理属性")
    private String toxiproperty;

    public String getDetailId() {
        return detailId;
    }

    public void setDetailId(String detailId) {
        this.detailId = detailId;
    }

    public String getServiceItemCode() {
        return serviceItemCode;
    }

    public void setServiceItemCode(String serviceItemCode) {
        this.serviceItemCode = serviceItemCode;
    }

    public String getServiceItemName() {
        return serviceItemName;
    }

    public void setServiceItemName(String serviceItemName) {
        this.serviceItemName = serviceItemName;
    }

    public String getDripSpeed() {
        return dripSpeed;
    }

    public void setDripSpeed(String dripSpeed) {
        this.dripSpeed = dripSpeed;
    }

    public String getDripSpeedUnit() {
        return dripSpeedUnit;
    }

    public void setDripSpeedUnit(String dripSpeedUnit) {
        this.dripSpeedUnit = dripSpeedUnit;
    }

    public Double getDosage() {
        return dosage;
    }

    public void setDosage(Double dosage) {
        this.dosage = dosage;
    }

    public String getDosageUnit() {
        return dosageUnit;
    }

    public void setDosageUnit(String dosageUnit) {
        this.dosageUnit = dosageUnit;
    }

    public Double getPackageNum() {
        return packageNum;
    }

    public void setPackageNum(Double packageNum) {
        this.packageNum = packageNum;
    }

    public String getPackageUnit() {
        return packageUnit;
    }

    public void setPackageUnit(String packageUnit) {
        this.packageUnit = packageUnit;
    }

    public String getUsage() {
        return usage;
    }

    public void setUsage(String usage) {
        this.usage = usage;
    }

    public String getUsageName() {
        return usageName;
    }

    public void setUsageName(String usageName) {
        this.usageName = usageName;
    }

    public String getFrequency() {
        return frequency;
    }

    public void setFrequency(String frequency) {
        this.frequency = frequency;
    }

    public String getFrequencyName() {
        return frequencyName;
    }

    public void setFrequencyName(String frequencyName) {
        this.frequencyName = frequencyName;
    }

    public String getTreatmentCourse() {
        return treatmentCourse;
    }

    public void setTreatmentCourse(String treatmentCourse) {
        this.treatmentCourse = treatmentCourse;
    }

    public String getTreatmentCourseUnit() {
        return treatmentCourseUnit;
    }

    public void setTreatmentCourseUnit(String treatmentCourseUnit) {
        this.treatmentCourseUnit = treatmentCourseUnit;
    }

    public Boolean getPrescriptionFlag() {
        return prescriptionFlag;
    }

    public void setPrescriptionFlag(Boolean prescriptionFlag) {
        this.prescriptionFlag = prescriptionFlag;
    }

    public String getReceiveOrg() {
        return receiveOrg;
    }

    public void setReceiveOrg(String receiveOrg) {
        this.receiveOrg = receiveOrg;
    }

    public String getDrugSpec() {
        return drugSpec;
    }

    public void setDrugSpec(String drugSpec) {
        this.drugSpec = drugSpec;
    }

    public String getExecuteOrgName() {
        return executeOrgName;
    }

    public void setExecuteOrgName(String executeOrgName) {
        this.executeOrgName = executeOrgName;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getFirstDayTimepoint() {
        return firstDayTimepoint;
    }

    public void setFirstDayTimepoint(String firstDayTimepoint) {
        this.firstDayTimepoint = firstDayTimepoint;
    }

    public String getDosageUnitName() {
        return dosageUnitName;
    }

    public void setDosageUnitName(String dosageUnitName) {
        this.dosageUnitName = dosageUnitName;
    }

    public Boolean getAntibacterialFlag() {
        return antibacterialFlag;
    }

    public void setAntibacterialFlag(Boolean antibacterialFlag) {
        this.antibacterialFlag = antibacterialFlag;
    }

    public String getToxiproperty() {
        return toxiproperty;
    }

    public void setToxiproperty(String toxiproperty) {
        this.toxiproperty = toxiproperty;
    }
}
