package com.bjgoodwill.hip.as.cis.nurse.ipd.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.bjgoodwill.hip.as.cis.nurse.ipd.service.ImpSettlementQueryAsService;
import com.bjgoodwill.hip.as.cis.nurse.ipd.to.*;
import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.common.util.HIPBeanUtil;
import com.bjgoodwill.hip.ds.org.api.service.DeptService;
import com.bjgoodwill.hip.ds.org.api.to.DeptTo;
import com.bjgoodwill.hip.ds.pat.in.hospital.inpatient.to.PatIpdInpatientTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <AUTHOR>
 * @Date 2025/4/7 15:39
 */
@RestController
@SaCheckPermission("cisNurse:impSettlementQuery")
@Tag(name = "住院结算查询应用服务", description = "住院结算查询应用服务类")
@RequestMapping("/cis/nurse/ipd/imp/settlement")
public class ImpSettlementQueryController {

    @Autowired
    private ImpSettlementQueryAsService impSettlementQueryAsService;
    @Autowired
    private DeptService deptService;

    @Operation(summary = "查询患者列表")
    @ApiResponse(description = "", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = PatIpdInpatientTo.class))))
    @PostMapping("/getInOutPatInpatent")
    public GridResultSet<PatIpdInpatientTo> getInOutHospitalPat(@RequestBody @Valid ImpSettlementAsQto impSettlementAsQto) {
        return impSettlementQueryAsService.getInOutHospitalPat(impSettlementAsQto);
    }

    @Operation(summary = "查询患者结算数据")
    @ApiResponse(description = "", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = PatSettlementDataAsTo.class))))
    @PostMapping("/getPatSettlementData")
    public List<PatSettlementDataAsTo> getPatSettlementData(@RequestBody @Valid PatSettlementDataAsQto patSettlementDataAsQto) {
        return impSettlementQueryAsService.getPatSettlementData(patSettlementDataAsQto);
    }

    @Operation(summary = "查询患者结算明细数据")
    @ApiResponse(description = "", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = EconIpdSettleBillAllAsTo.class))))
    @PostMapping("/getPatSettlementDetail")
    public EconIpdSettleBillAllAsTo getPatSettlementDetail(@RequestBody @Valid PatSettlementDetailAsQto patSettlementDetailAsQto) {
        return impSettlementQueryAsService.getPatSettlementDetail(patSettlementDetailAsQto);
    }

    @Operation(summary = "费用明细查询")
    @ApiResponse(description = "", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = EconIpdSettleBillAllAsTo.class))))
    @PostMapping("/getPatSettlementFeeDetail")
    public EconIpdSettleBillAllAsTo getPatSettlementFeeDetail(@RequestBody @Valid PatSettlementDetailAsQto patSettlementDetailAsQto) {
        return impSettlementQueryAsService.getPatSettlementFeeDetail(patSettlementDetailAsQto);
    }

    @Operation(summary = "获取执行科室数据源", description = "获取执行科室数据源")
    @ApiResponse(description = "获取执行科室数据源", content = @Content(mediaType = "application/json", schema = @Schema(implementation = DeptDataSourcesAsTo.class)))
    @GetMapping(value = "/getExeDept")
    public List<DeptDataSourcesAsTo> getExeDept() {
        return impSettlementQueryAsService.getExeDept();
    }

    @Operation(summary = "获取开立科室数据源", description = "获取开立科室数据源")
    @ApiResponse(description = "获取开立科室数据源", content = @Content(mediaType = "application/json", schema = @Schema(implementation = DeptDataSourcesAsTo.class)))
    @GetMapping("/dept/all")
    public List<DeptDataSourcesAsTo> getAllClinicDept() {
        List<DeptTo> allDept = deptService.getAllDept();
        return HIPBeanUtil.copy(allDept, DeptDataSourcesAsTo.class);
    }

}
