package com.bjgoodwill.hip.as.cis.nurse.ipd.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.bjgoodwill.hip.as.cis.nurse.ipd.enums.CisNurseIpdBusinessErrorEnum;
import com.bjgoodwill.hip.as.cis.nurse.ipd.service.OrdersProofreadingAsService;
import com.bjgoodwill.hip.as.cis.nurse.ipd.to.*;
import com.bjgoodwill.hip.common.bean.LoginInfo;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.cpoe.order.service.CisIpdCpoeService;
import com.bjgoodwill.hip.ds.cis.nurse.print.service.CisOrderExecTempService;
import com.bjgoodwill.hip.ds.cis.nurse.print.to.CisOrderExecTempQto;
import com.bjgoodwill.hip.ds.cis.nurse.print.to.CisOrderExecTempTo;
import com.bjgoodwill.hip.ds.econ.price.service.EconTemplateService;
import com.bjgoodwill.hip.ds.econ.price.to.EconTemplateQto;
import com.bjgoodwill.hip.ds.econ.price.to.EconTemplateTo;
import com.bjgoodwill.hip.ds.org.api.service.StaffService;
import com.bjgoodwill.hip.ds.org.api.to.StaffLocalTo;
import com.bjgoodwill.hip.security.bean.CurrentOrgInfo;
import com.bjgoodwill.hip.security.service.SecurityService;
import com.bjgoodwill.hip.security.util.HIPCurrentOrgUtils;
import com.bjgoodwill.hip.security.util.HIPSecurityUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: zhangyunchuan
 * @Date: 2024/10/9 16:22
 * @PROJECT: hip-ac
 */
@RestController
@SaCheckPermission("cisNurse:nurseWorkstation")
@Tag(name = "医嘱校对应用服务", description = "医嘱校对应用服务类")
@RequestMapping("/cis/nurse/ipd/ordersProofreading")
public class OrdersProofreadingController {

    @Autowired
    private CisIpdCpoeService cisIpdCpoeService;

    @Autowired
    private OrdersProofreadingAsService ordersProofreadingAsService;

    @Autowired
    private SecurityService securityService;

    @Autowired
    private StaffService staffService;

    @Autowired
    private EconTemplateService econTemplateService;

    @Autowired
    private CisOrderExecTempService cisOrderExecTempService;


    @Operation(summary = "未校对-全部", description = "查询条件:患者流水号集合或者护理组")
    @ApiResponse(description = "", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = CisIpdPatAsTo.class))))
    @PostMapping(value = "/unProofed/orderQuery/all")
    public List<CisIpdPatAsTo> unProofedIssuedOrderQuery(@RequestBody @Valid OrdersProofreadingQto ordersProofreadingQto) {
        return ordersProofreadingAsService.unProofedIssuedOrderQuery(ordersProofreadingQto);
    }

    @Operation(summary = "未校对-药品", description = "查询条件:患者流水号集合或者护理组")
    @ApiResponse(description = "", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = CisIpdPatAsTo.class))))
    @PostMapping(value = "/unProofed/orderQuery/drug")
    public List<CisIpdPatAsTo> unProofedOrderQueryDrug(@RequestBody @Valid OrdersProofreadingQto ordersProofreadingQto) {
        return ordersProofreadingAsService.unProofedOrderQueryDrug(ordersProofreadingQto);
    }

    @Operation(summary = "未校对-病区执行", description = "查询条件:患者流水号集合或者护理组")
    @ApiResponse(description = "", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = CisIpdPatAsTo.class))))
    @PostMapping(value = "/unProofed/orderQuery/deptNurseExe")
    public List<CisIpdPatAsTo> unProofedOrderQueryDeptNurseExe(@RequestBody @Valid OrdersProofreadingQto ordersProofreadingQto) {
        return ordersProofreadingAsService.unProofedOrderQueryDeptNurseExe(ordersProofreadingQto);
    }

    @Operation(summary = "未校对-他科执行", description = "查询条件:患者流水号集合或者护理组")
    @ApiResponse(description = "", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = CisIpdPatAsTo.class))))
    @PostMapping(value = "/unProofed/orderQuery/other")
    public List<CisIpdPatAsTo> unProofedOrderQueryOther(@RequestBody @Valid OrdersProofreadingQto ordersProofreadingQto) {
        return ordersProofreadingAsService.unProofedOrderQueryOther(ordersProofreadingQto);
    }

    @Operation(summary = "未校对-床头卡", description = "查询条件:患者流水号集合或者护理组")
    @ApiResponse(description = "", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = CisIpdPatAsTo.class))))
    @PostMapping(value = "/unProofed/orderQuery/bedCard")
    public List<CisIpdPatAsTo> unProofedOrderQueryBedCard(@RequestBody @Valid OrdersProofreadingQto ordersProofreadingQto) {
        return ordersProofreadingAsService.unProofedOrderQueryBedCard(ordersProofreadingQto);
    }

    @Operation(summary = "校对验证处理预停止医嘱", description = "末日执行次确认")
    @ApiResponse(description = "", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = CisIpdOrderAsTo.class))))
    @PostMapping(value = "/preStopOrders")
    public List<CisIpdOrderAsTo> handlePreStopOrders(@RequestBody List<CisIpdOrderAsTo> cisIpdOrderAsTos) {
        return ordersProofreadingAsService.handlePreStopOrders(cisIpdOrderAsTos);
    }

    @Operation(summary = "未校对-校对通过", description = "")
    @ApiResponse(description = "", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = CisIpdOrderAsTo.class))))
    @PostMapping(value = "/unProofed/saveAll")
    public List<CisIpdOrderAsTo> proofIpdOrders(@RequestBody List<CisIpdOrderAsTo> cisIpdOrderAsTos, @RequestParam("isForceProof") boolean isForceProof) {
        return ordersProofreadingAsService.proofIpdOrders(cisIpdOrderAsTos, isForceProof);
    }

    @Operation(summary = "未校对-退回", description = "")
    @ApiResponse(description = "", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = CisIpdOrderAsTo.class))))
    @PostMapping(value = "/unProofed/backAll")
    public List<CisIpdOrderAsTo> proofBackIpdOrderAll(@RequestBody List<CisIpdOrderAsTo> cisIpdOrderAsTos) {
        return ordersProofreadingAsService.proofBackIpdOrders(cisIpdOrderAsTos);
    }

    @Operation(summary = "已校对-医嘱查询", description = "查询条件:患者流水号集合、开始时间、结束时间")
    @ApiResponse(description = "", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = CisIpdPatAsTo.class))))
    @PostMapping(value = "/proofed/orderQuery")
    public List<CisIpdPatAsTo> proofedOrderQuery(@RequestBody @Valid OrdersProofreadingQto ordersProofreadingQto) {
        return ordersProofreadingAsService.proofedOrderQuery(ordersProofreadingQto);
    }

    @Operation(summary = "查询病区执行单模板信息", description = "查询病区执行单模板信息")
    @ApiResponse(description = "执行单模板信息", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = CisOrderExecTempTo.class))))
    @GetMapping("/tempDataSource")
    public List<CisOrderExecTempTo> getNurseExecTempDataSource() {
        CurrentOrgInfo loginInfo = HIPCurrentOrgUtils.getCurrentOrgInfo();
        CisOrderExecTempQto qto = new CisOrderExecTempQto();
        qto.setDeptNurseCode(loginInfo.getWorkGroupCode());
        qto.setEnabled(true);
        qto.setParentNode("EXECUTION");
        return cisOrderExecTempService.getCisOrderExecTemps(qto);
    }

    @Operation(summary = "已校对-修改校对时间", description = "查询条件:医嘱ID集合、要修改的时间")
    @ApiResponse(description = "", content = @Content(mediaType = "application/json"))
    @PostMapping(value = "/proofed/updateProofDate")
    public void updateProofDate(@RequestBody @Valid OrdersProofreadingEto ordersProofreadingEto) {
        ordersProofreadingAsService.updateProofDate(ordersProofreadingEto);
    }

    @Operation(summary = "已停止-医嘱查询", description = "查询条件:患者流水号集合或者护理组")
    @ApiResponse(description = "", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = CisIpdPatAsTo.class))))
    @PostMapping(value = "/stopped/orderQuery")
    public List<CisIpdPatAsTo> stoppedOrderQuery(@RequestBody @Valid OrdersProofreadingQto ordersProofreadingQto) {
        return ordersProofreadingAsService.stoppedOrderQuery(ordersProofreadingQto);
    }

    @Operation(summary = "已作废-医嘱查询", description = "查询条件:患者流水号集合或者护理组")
    @ApiResponse(description = "", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = CisIpdPatAsTo.class))))
    @PostMapping(value = "/invalid/orderQuery")
    public List<CisIpdPatAsTo> invalidOrderQuery(@RequestBody @Valid OrdersProofreadingQto ordersProofreadingQto) {
        return ordersProofreadingAsService.invalidOrderQuery(ordersProofreadingQto);
    }

    @Operation(summary = "补费绑定", description = "")
    @ApiResponse(description = "", content = @Content(mediaType = "application/json"))
    @PostMapping(value = "/supplementBinding")
    public void supplementBinding(@RequestBody EconTemplateAsNto econTemplateAsNto) {
        ordersProofreadingAsService.supplementBinding(econTemplateAsNto);
    }

    @Operation(summary = "住院医嘱预停止--有效期的医嘱", description = "患者列表加载完之后立马调用")
    @ApiResponse(description = "", content = @Content(mediaType = "application/json"))
    @PutMapping(value = "/preStop")
    public void preStopIpdOrder() {
        CurrentOrgInfo loginInfo = HIPCurrentOrgUtils.getCurrentOrgInfo();
        BusinessAssert.notNull(loginInfo, CisNurseIpdBusinessErrorEnum.CIS_NURSE_IPD_0001, "登录信息");
        cisIpdCpoeService.prestopIpdOrder(loginInfo.getWorkGroupCode(), LocalDateTime.now().minusHours(12));
    }

    @Operation(summary = "校验密码是否正确", description = "校验密码是否正确")
    @PostMapping("/users/passwords/validated")
    public boolean validatePassword(@RequestBody HeadNurseLoginAsTo headNurseLoginAsTo) {
        return securityService.validatePasswordByJobNo(headNurseLoginAsTo.getJobNo(), headNurseLoginAsTo.getPassword());
    }

    @Operation(summary = "获取登录病区下的护士长", description = "登录病区下的护士长下拉数据源")
    @ApiResponse(description = "", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = StaffLocalTo.class))))
    @GetMapping("/headNurses")
    public List<StaffLocalTo> getHeadNursesList() {
        CurrentOrgInfo loginInfo = HIPCurrentOrgUtils.getCurrentOrgInfo();
        BusinessAssert.notNull(loginInfo, CisNurseIpdBusinessErrorEnum.CIS_NURSE_IPD_0001, "登录信息");
        return staffService.getHeadNurseByRoles(loginInfo.getWorkGroupCode());
    }

    @Operation(summary = "获取登录病区下的费用组套模板", description = "获取登录病区下的费用组套模板下拉数据源")
    @ApiResponse(description = "", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = EconTemplateTo.class))))
    @GetMapping("/econTemplates")
    public List<EconTemplateTo> getEconTemplates() {
        EconTemplateQto econTemplateQto = new EconTemplateQto();
        CurrentOrgInfo loginInfo = HIPCurrentOrgUtils.getCurrentOrgInfo();
        BusinessAssert.notNull(loginInfo, CisNurseIpdBusinessErrorEnum.CIS_NURSE_IPD_0001, "登录信息");
        econTemplateQto.setDeptCode(loginInfo.getWorkGroupCode());
        return econTemplateService.getEconTemplates(econTemplateQto);
    }
}
