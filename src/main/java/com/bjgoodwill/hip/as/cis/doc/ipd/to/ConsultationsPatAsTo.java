package com.bjgoodwill.hip.as.cis.doc.ipd.to;

import com.bjgoodwill.hip.ds.pat.in.hospital.changeout.to.PatIpdChangeDeptOutTo;
import com.bjgoodwill.hip.ds.pat.in.hospital.inpatient.enmus.InpatientStatusEnum;
import com.bjgoodwill.hip.ds.pat.in.hospital.inpatient.to.PatIpdInpatientExtTo;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/12/4 13:09
 * @ClassName: ConsultationsPatAsTo
 * @Description:会诊医嘱
 */
@Schema(description = "会诊医嘱")
public class ConsultationsPatAsTo implements Serializable {

    @Schema(
            description = "标识"
    )
    private String id;
    @Schema(
            description = "就诊流水号"
    )
    private String visitCode;
    @Schema(
            description = "住院号"
    )
    private String inpatientCode;
    @Schema(
            description = "住院次"
    )
    private Integer inTimes;
    @Schema(
            description = "患者id"
    )
    private String patCode;
    @Schema(
            description = "姓名"
    )
    private String name;
    @Schema(
            description = "出生日期"
    )
    private LocalDateTime birthDate;
    @Schema(
            description = "性别"
    )
    private String sex;
    @Schema(
            description = "性别名称"
    )
    private String sexName;
    @Schema(
            description = "入院科室"
    )
    private String admissionDeptCode;
    @Schema(
            description = "入院科室名称"
    )
    private String admissionDeptName;
    @Schema(
            description = "入院时间"
    )
    private LocalDateTime inTime;
    @Schema(
            description = "出院科室"
    )
    private String outDeptCode;
    @Schema(
            description = "出院科室名称"
    )
    private String outDeptName;
    @Schema(
            description = "出院时间"
    )
    private LocalDateTime outTime;
    @Schema(
            description = "当前住院科室"
    )
    private String inDeptCode;
    @Schema(
            description = "当前住院科室名称"
    )
    private String inDeptName;
    @Schema(
            description = "当前床位"
    )
    private String bedId;
    @Schema(
            description = "当前床位名称"
    )
    private String bedName;
    @Schema(
            description = "当前住院医师"
    )
    private String admittedDoctor;
    @Schema(
            description = "当前住院医师名称"
    )
    private String admittedDoctorName;
    @Schema(
            description = "当前主治医师"
    )
    private String masterDoctor;
    @Schema(
            description = "当前主治医师名称"
    )
    private String masterDoctorName;
    @Schema(
            description = "当前主任医师"
    )
    private String directorDoctor;
    @Schema(
            description = "当前主任医师名称"
    )
    private String directorDoctorName;
    @Schema(
            description = "当前责任护士"
    )
    private String masterNurse;
    @Schema(
            description = "当前责任护士名称"
    )
    private String masterNurseName;
    @Schema(
            description = "当前护理组"
    )
    private String deptNurseCode;
    @Schema(
            description = "当前护理组名称"
    )
    private String deptNurseName;
    @Schema(
            description = "状态  REGISTERED:已登记 RECEIVED:已接诊 TRANSFER:待转科  TRANSFERING:转科中  DISCHARGE:待出院  DISCHARGED:已出院"
    )
    private InpatientStatusEnum statusCode;
    @Schema(
            description = "是否新生儿 1是 0否"
    )
    private boolean newbornFlag;
    @Schema(
            description = "新生儿出生体重"
    )
    private String newbornBirthWeight;
    @Schema(
            description = "新生儿入院体重"
    )
    private String newbornInWeight;
    @Schema(
            description = "新生儿入院类型（字典）  新生儿入院类型1正常新生儿2早产儿3有疾病新生儿4非无菌分娩9其它 "
    )
    private String newbornInType;
    @Schema(
            description = "最后修改的人员"
    )
    private String updatedStaff;
    @Schema(
            description = "最后修改的时间"
    )
    private LocalDateTime updatedDate;
    @Schema(
            description = "版本"
    )
    private Integer version;
    @Schema(
            description = "是否已结算"
    )
    private Boolean rcptFlag;
    @Schema(
            description = "是否支付"
    )
    private Boolean rcptPayFlag;
    @Schema(
            description = "费别"
    )
    private String feeType;
    @Schema(
            description = "年龄"
    )
    private String age;
    @Schema(
            description = "转科时间"
    )
    private LocalDateTime transferDate;
    @Schema(
            description = "是否费用审核"
    )
    private Boolean examineFlag;
    @Schema(
            description = "是否跨科患者"
    )
    private Boolean crossDeptFlag;
    @Schema(
            description = "新入院患者标识"
    )
    private Boolean newPatFlag;
    @Schema(
            description = "转科信息"
    )
    private PatIpdChangeDeptOutTo patIpdChangeDeptOutTo;
    @Schema(
            description = "住院患者扩展信息"
    )
    private PatIpdInpatientExtTo patIpdInpatientExtTo;
    @Schema(
            description = "会诊申请单信息"
    )
    private List<CisConsultationApplyAsTo> cisConsultationApplyTos;
    @Schema(
            description = "会诊申请时间(患者之间的排序用)"
    )
    private LocalDateTime cnsltTime;

    public LocalDateTime getCnsltTime() {
        return cnsltTime;
    }

    public void setCnsltTime(LocalDateTime cnsltTime) {
        this.cnsltTime = cnsltTime;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    public String getInpatientCode() {
        return inpatientCode;
    }

    public void setInpatientCode(String inpatientCode) {
        this.inpatientCode = inpatientCode;
    }

    public Integer getInTimes() {
        return inTimes;
    }

    public void setInTimes(Integer inTimes) {
        this.inTimes = inTimes;
    }

    public String getPatCode() {
        return patCode;
    }

    public void setPatCode(String patCode) {
        this.patCode = patCode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public LocalDateTime getBirthDate() {
        return birthDate;
    }

    public void setBirthDate(LocalDateTime birthDate) {
        this.birthDate = birthDate;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getSexName() {
        return sexName;
    }

    public void setSexName(String sexName) {
        this.sexName = sexName;
    }

    public String getAdmissionDeptCode() {
        return admissionDeptCode;
    }

    public void setAdmissionDeptCode(String admissionDeptCode) {
        this.admissionDeptCode = admissionDeptCode;
    }

    public String getAdmissionDeptName() {
        return admissionDeptName;
    }

    public void setAdmissionDeptName(String admissionDeptName) {
        this.admissionDeptName = admissionDeptName;
    }

    public LocalDateTime getInTime() {
        return inTime;
    }

    public void setInTime(LocalDateTime inTime) {
        this.inTime = inTime;
    }

    public String getOutDeptCode() {
        return outDeptCode;
    }

    public void setOutDeptCode(String outDeptCode) {
        this.outDeptCode = outDeptCode;
    }

    public String getOutDeptName() {
        return outDeptName;
    }

    public void setOutDeptName(String outDeptName) {
        this.outDeptName = outDeptName;
    }

    public LocalDateTime getOutTime() {
        return outTime;
    }

    public void setOutTime(LocalDateTime outTime) {
        this.outTime = outTime;
    }

    public String getInDeptCode() {
        return inDeptCode;
    }

    public void setInDeptCode(String inDeptCode) {
        this.inDeptCode = inDeptCode;
    }

    public String getInDeptName() {
        return inDeptName;
    }

    public void setInDeptName(String inDeptName) {
        this.inDeptName = inDeptName;
    }

    public String getBedId() {
        return bedId;
    }

    public void setBedId(String bedId) {
        this.bedId = bedId;
    }

    public String getBedName() {
        return bedName;
    }

    public void setBedName(String bedName) {
        this.bedName = bedName;
    }

    public String getAdmittedDoctor() {
        return admittedDoctor;
    }

    public void setAdmittedDoctor(String admittedDoctor) {
        this.admittedDoctor = admittedDoctor;
    }

    public String getAdmittedDoctorName() {
        return admittedDoctorName;
    }

    public void setAdmittedDoctorName(String admittedDoctorName) {
        this.admittedDoctorName = admittedDoctorName;
    }

    public String getMasterDoctor() {
        return masterDoctor;
    }

    public void setMasterDoctor(String masterDoctor) {
        this.masterDoctor = masterDoctor;
    }

    public String getMasterDoctorName() {
        return masterDoctorName;
    }

    public void setMasterDoctorName(String masterDoctorName) {
        this.masterDoctorName = masterDoctorName;
    }

    public String getDirectorDoctor() {
        return directorDoctor;
    }

    public void setDirectorDoctor(String directorDoctor) {
        this.directorDoctor = directorDoctor;
    }

    public String getDirectorDoctorName() {
        return directorDoctorName;
    }

    public void setDirectorDoctorName(String directorDoctorName) {
        this.directorDoctorName = directorDoctorName;
    }

    public String getMasterNurse() {
        return masterNurse;
    }

    public void setMasterNurse(String masterNurse) {
        this.masterNurse = masterNurse;
    }

    public String getDeptNurseCode() {
        return deptNurseCode;
    }

    public void setDeptNurseCode(String deptNurseCode) {
        this.deptNurseCode = deptNurseCode;
    }

    public String getDeptNurseName() {
        return deptNurseName;
    }

    public void setDeptNurseName(String deptNurseName) {
        this.deptNurseName = deptNurseName;
    }

    public String getMasterNurseName() {
        return masterNurseName;
    }

    public void setMasterNurseName(String masterNurseName) {
        this.masterNurseName = masterNurseName;
    }

    public InpatientStatusEnum getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(InpatientStatusEnum statusCode) {
        this.statusCode = statusCode;
    }

    public boolean isNewbornFlag() {
        return newbornFlag;
    }

    public void setNewbornFlag(boolean newbornFlag) {
        this.newbornFlag = newbornFlag;
    }

    public String getNewbornBirthWeight() {
        return newbornBirthWeight;
    }

    public void setNewbornBirthWeight(String newbornBirthWeight) {
        this.newbornBirthWeight = newbornBirthWeight;
    }

    public String getNewbornInWeight() {
        return newbornInWeight;
    }

    public void setNewbornInWeight(String newbornInWeight) {
        this.newbornInWeight = newbornInWeight;
    }

    public String getNewbornInType() {
        return newbornInType;
    }

    public void setNewbornInType(String newbornInType) {
        this.newbornInType = newbornInType;
    }

    public String getUpdatedStaff() {
        return updatedStaff;
    }

    public void setUpdatedStaff(String updatedStaff) {
        this.updatedStaff = updatedStaff;
    }

    public LocalDateTime getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(LocalDateTime updatedDate) {
        this.updatedDate = updatedDate;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Boolean getRcptFlag() {
        return rcptFlag;
    }

    public void setRcptFlag(Boolean rcptFlag) {
        this.rcptFlag = rcptFlag;
    }

    public Boolean getRcptPayFlag() {
        return rcptPayFlag;
    }

    public void setRcptPayFlag(Boolean rcptPayFlag) {
        this.rcptPayFlag = rcptPayFlag;
    }

    public String getFeeType() {
        return feeType;
    }

    public void setFeeType(String feeType) {
        this.feeType = feeType;
    }

    public String getAge() {
        return age;
    }

    public void setAge(String age) {
        this.age = age;
    }

    public LocalDateTime getTransferDate() {
        return transferDate;
    }

    public void setTransferDate(LocalDateTime transferDate) {
        this.transferDate = transferDate;
    }

    public Boolean getExamineFlag() {
        return examineFlag;
    }

    public void setExamineFlag(Boolean examineFlag) {
        this.examineFlag = examineFlag;
    }

    public Boolean getCrossDeptFlag() {
        return crossDeptFlag;
    }

    public void setCrossDeptFlag(Boolean crossDeptFlag) {
        this.crossDeptFlag = crossDeptFlag;
    }

    public Boolean getNewPatFlag() {
        return newPatFlag;
    }

    public void setNewPatFlag(Boolean newPatFlag) {
        this.newPatFlag = newPatFlag;
    }

    public PatIpdChangeDeptOutTo getPatIpdChangeDeptOutTo() {
        return patIpdChangeDeptOutTo;
    }

    public void setPatIpdChangeDeptOutTo(PatIpdChangeDeptOutTo patIpdChangeDeptOutTo) {
        this.patIpdChangeDeptOutTo = patIpdChangeDeptOutTo;
    }

    public PatIpdInpatientExtTo getPatIpdInpatientExtTo() {
        return patIpdInpatientExtTo;
    }

    public void setPatIpdInpatientExtTo(PatIpdInpatientExtTo patIpdInpatientExtTo) {
        this.patIpdInpatientExtTo = patIpdInpatientExtTo;
    }

    public List<CisConsultationApplyAsTo> getCisConsultationApplyTos() {
        return cisConsultationApplyTos;
    }

    public void setCisConsultationApplyTos(List<CisConsultationApplyAsTo> cisConsultationApplyTos) {
        this.cisConsultationApplyTos = cisConsultationApplyTos;
    }
}
