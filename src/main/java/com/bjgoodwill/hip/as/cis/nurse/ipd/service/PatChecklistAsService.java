package com.bjgoodwill.hip.as.cis.nurse.ipd.service;

import com.bjgoodwill.hip.as.cis.nurse.ipd.to.EconIpdBillDetailAsQto;
import com.bjgoodwill.hip.as.cis.nurse.ipd.to.PatOneDayChecklistAsTo;
import com.bjgoodwill.hip.as.cis.nurse.ipd.to.PatTotalsChecklistAsTo;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/3/14 17:10
 * @ClassName: PatOneDayChecklistService
 */
@Tag(name = "患者费用清单应用服务", description = "患者费用清单应用服务类")
public interface PatChecklistAsService {

    List<PatOneDayChecklistAsTo> getPatOneDayChecklist(EconIpdBillDetailAsQto econIpdBillDetailAsQto);

    PatTotalsChecklistAsTo getPatTotalsChecklist(String visitCode);
}
