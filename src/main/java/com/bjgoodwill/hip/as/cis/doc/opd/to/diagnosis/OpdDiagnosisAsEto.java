package com.bjgoodwill.hip.as.cis.doc.opd.to.diagnosis;

import com.bjgoodwill.hip.ds.cis.opdcpoe.diag.to.CisOpdDiagnoseEto;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2025/5/6 10:25
 */
@Schema(description = "门诊医生站-诊断-编辑")
public class OpdDiagnosisAsEto extends CisOpdDiagnoseEto implements Serializable {

    @Schema(description = "标识")
    private String id;

    @Schema(description = "接诊流水编码")
    private String visitCode;

    @Schema(
            description = "中医证型编码"
    )
    private String tcmSyndromeType;
    @Schema(
            description = "中医证型名称"
    )
    private String tcmSyndromeTypeName;

    @Schema(
            description = "诊断前缀"
    )
    private String prefix;
    @Schema(
            description = "诊断后缀"
    )
    private String suffix;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    public String getTcmSyndromeType() {
        return tcmSyndromeType;
    }

    public void setTcmSyndromeType(String tcmSyndromeType) {
        this.tcmSyndromeType = tcmSyndromeType;
    }

    public String getTcmSyndromeTypeName() {
        return tcmSyndromeTypeName;
    }

    public void setTcmSyndromeTypeName(String tcmSyndromeTypeName) {
        this.tcmSyndromeTypeName = tcmSyndromeTypeName;
    }

    public String getPrefix() {
        return prefix;
    }

    public void setPrefix(String prefix) {
        this.prefix = prefix;
    }

    public String getSuffix() {
        return suffix;
    }

    public void setSuffix(String suffix) {
        this.suffix = suffix;
    }
}
