package com.bjgoodwill.hip.as.cis.doc.ipd.to.temp;

import com.bjgoodwill.hip.ds.cis.medicineitem.serviceItem.to.DgimgApplyTo;
import com.bjgoodwill.hip.ds.term.api.to.DictElementTo;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> liangyuqing
 * @description :
 * @createDate : 2025/1/9 16:32
 */
@Schema(description = "检查项目列表")
public class DgimgItemTempDataSource {

    @Schema(description = "检查服务项目列表")
    private List<DgimgApplyTo> dgigmItemList;

    @Schema(description = "方法集合")
    private List<DictElementTo> methodList;

    @Schema(description = "部位集合")
    private List<DictElementTo> humanOrgansList;

    public DgimgItemTempDataSource() {
        setDgigmItemList(new ArrayList<>());
        setMethodList(new ArrayList<>());
        setHumanOrgansList(new ArrayList<>());
    }

    public List<DgimgApplyTo> getDgigmItemList() {
        return dgigmItemList;
    }

    public void setDgigmItemList(List<DgimgApplyTo> dgigmItemList) {
        this.dgigmItemList = dgigmItemList;
    }

    public List<DictElementTo> getMethodList() {
        return methodList;
    }

    public void setMethodList(List<DictElementTo> methodList) {
        this.methodList = methodList;
    }

    public List<DictElementTo> getHumanOrgansList() {
        return humanOrgansList;
    }

    public void setHumanOrgansList(List<DictElementTo> humanOrgansList) {
        this.humanOrgansList = humanOrgansList;
    }
}
