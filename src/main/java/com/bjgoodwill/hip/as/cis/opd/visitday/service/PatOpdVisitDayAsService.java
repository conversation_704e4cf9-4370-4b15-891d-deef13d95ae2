package com.bjgoodwill.hip.as.cis.opd.visitday.service;

import com.bjgoodwill.hip.as.cis.opd.visitday.to.*;
import com.bjgoodwill.hip.as.cis.opd.visitresource.to.PatVisitResourceAsNto;
import com.bjgoodwill.hip.as.cis.opd.visitresource.to.PatVisitResourceAsTo;
import com.bjgoodwill.hip.ds.org.api.to.WorkGroupTo;
import com.bjgoodwill.hip.ds.pat.schedule.to.PatVisitSourceDayEto;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * Description:
 *
 * <AUTHOR>
 * &#064;date 2025/3/20 下午7:43
 */
@Tag(name = "门诊出诊排班模板维护应用服务接口", description = "门诊出诊排班模板维护应用服务接口")
public interface PatOpdVisitDayAsService {

    /**
     * 根据条件查询出诊排班模板主页面显示数据源
     *
     * @param qto
     * @return List<PatVisitDayShowItemAsTo>
     */
    List<PatVisitDayMainShowAsTo> getMainShowList(@RequestBody @Valid PatVisitDayMainAsQto qto);

    /**
     * 获取无出诊日和停诊的科室号出诊资源
     *
     * @param hospitalAreaCode
     * @return
     */
    List<PatVisitDayResourceShowAsTo> getNoDaysDaptResources(String hospitalAreaCode);

    /**
     * 获取无出诊日和停诊的医生号出诊资源
     *
     * @param hospitalAreaCode
     * @return
     */
    List<PatVisitDayResourceShowAsTo> getNoDaysDoctorResources(String hospitalAreaCode);

    /**
     * 获取维护科室号/医生号出诊页面显示数据源
     *
     * @param qto
     * @return
     */
    List<PatVisitDayResourceShowAsTo> getResourceShowList(@RequestBody @Valid PatVisitDayAsQto qto);

    /**
     * 根据条件查询出诊科室数据源
     *
     * @return
     */
    List<WorkGroupTo> getWorkGroupList(@RequestParam(value = "hospitalAreaCode", required = false) String hospitalAreaCode);

    /**
     * 新增(批量)科室号出诊资源以及出诊日(批量)
     *
     * @param nto
     */
    List<PatVisitDayResourceShowAsTo> createDeptResources(@RequestBody @Valid PatVisitDayDeptResourcesNto nto);

    /**
     * 新增医生号出诊资源以及出诊日(批量)
     *
     * @param nto
     */
    PatVisitDayResourceShowAsTo createDoctorResource(@RequestBody @Valid PatVisitDayDocResourcesNto nto);

    /**
     * 科室号/医生号出诊维护保存
     *
     * @param list
     */
    void saveResourceDays(@RequestBody @Valid List<PatVisitSourceDayEto> list);


}
