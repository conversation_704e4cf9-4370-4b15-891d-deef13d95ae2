package com.bjgoodwill.hip.as.cis.doc.ipd.util.build;

import com.bjgoodwill.hip.as.cis.doc.ipd.enums.CisDocIpdBusinessErrorEnum;
import com.bjgoodwill.hip.as.cis.doc.ipd.util.IpdOrderExecuteOrgUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.CisStatusEnum;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.common.util.HIPBeanUtil;
import com.bjgoodwill.hip.common.util.HIPIDUtil;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyTo;
import com.bjgoodwill.hip.ds.cis.apply.charge.enmus.CisChargeTypeEnum;
import com.bjgoodwill.hip.ds.cis.apply.charge.to.CisApplyChargeNto;
import com.bjgoodwill.hip.ds.cis.apply.charge.to.CisApplyChargeTo;
import com.bjgoodwill.hip.ds.cis.apply.dgimg.to.CisDgimgApplyDetailNto;
import com.bjgoodwill.hip.ds.cis.apply.dgimg.to.CisDgimgApplyNto;
import com.bjgoodwill.hip.ds.cis.apply.dgimg.to.CisDgimgApplyTo;
import com.bjgoodwill.hip.ds.cis.apply.diag.to.ApplyDiagnosisNto;
import com.bjgoodwill.hip.ds.cis.cpoe.order.to.CisIpdOrderNto;
import com.bjgoodwill.hip.ds.cis.medicineitem.price.to.ServiceClinicPriceTo;
import com.bjgoodwill.hip.ds.cis.medicineitem.serviceItem.service.ServiceClinicItemService;
import com.bjgoodwill.hip.ds.cis.medicineitem.serviceItem.to.ServiceClinicItemTo;
import com.bjgoodwill.hip.ds.org.api.service.WorkGroupService;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> liangyuqing
 * @description :
 * @createDate : 2025/3/24 16:21
 */
@Service
public class DgimgOrderBuildUtil {

    @Resource(name = "com.bjgoodwill.hip.ds.cis.medicineitem.serviceItem.service.ServiceClinicItemTangibleService")
    private ServiceClinicItemService serviceClinicItemService;

    @Resource
    private IpdOrderExecuteOrgUtil ipdOrderExecuteOrgUtil;

    @Resource
    private WorkGroupService workGroupService;

    /**
     * 复制粘贴用
     * @param cisIpdOrderNto
     * @param cisBaseApplyTo
     * @return
     */
    public CisDgimgApplyNto buildCisIpdOrderNto(CisIpdOrderNto cisIpdOrderNto, CisBaseApplyTo cisBaseApplyTo) {
        CisDgimgApplyTo cisDgimgApplyTo = (CisDgimgApplyTo) cisBaseApplyTo;
        CisDgimgApplyNto cisDgimgApplyNto = HIPBeanUtil.copy(cisDgimgApplyTo, CisDgimgApplyNto.class);
        cisDgimgApplyNto.setId(cisIpdOrderNto.getApplyCode());
        cisDgimgApplyNto.setOrderID(cisIpdOrderNto.getId());
        cisDgimgApplyNto.setVisitCode(cisIpdOrderNto.getVisitCode());
        cisDgimgApplyNto.setPatMiCode(cisIpdOrderNto.getPatMiCode());
        cisDgimgApplyNto.setDeptNurseCode(cisIpdOrderNto.getDeptNurseCode());
        cisDgimgApplyNto.setDeptNurseName(cisIpdOrderNto.getDeptNurseName());

        List<CisDgimgApplyDetailNto> cisDgimgApplyDetailNtos = HIPBeanUtil.copy(cisDgimgApplyTo.getCisDgimgApplyDetails(), CisDgimgApplyDetailNto.class);

        Map<String, List<CisApplyChargeTo>> chargeToMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(cisDgimgApplyTo.getCisApplyCharges())) {
            chargeToMap = cisDgimgApplyTo.getCisApplyCharges().stream().filter(a -> a.getDetailId() != null)
                    .collect(Collectors.groupingBy(CisApplyChargeTo::getDetailId));
        }

        List<CisApplyChargeNto> chargeNtoList = new ArrayList<>();
        for (CisDgimgApplyDetailNto cisDgimgApplyDetailNto : cisDgimgApplyDetailNtos) {
            String originDetailId = cisDgimgApplyDetailNto.getId();
            cisDgimgApplyDetailNto.setId("DT_" + HIPIDUtil.getNextIdString());
            cisDgimgApplyDetailNto.setVisitCode(cisIpdOrderNto.getVisitCode());
            cisDgimgApplyDetailNto.setCisDgimgApplyId(cisIpdOrderNto.getApplyCode());

            // 查询服务项目、收费项目明细
            ServiceClinicItemTo serviceClinicItemTo = serviceClinicItemService.getServiceClinicItemByCode(cisDgimgApplyDetailNto.getServiceItemCode());
            BusinessAssert.notNull(serviceClinicItemTo, CisDocIpdBusinessErrorEnum.CIS_DOC_IPD_0001, "医嘱项目");
            Map<String, ServiceClinicPriceTo> priceToMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(serviceClinicItemTo.getServiceClinicPrices())) {
                priceToMap = serviceClinicItemTo.getServiceClinicPrices().stream().collect(Collectors.toMap(ServiceClinicPriceTo::getPriceItemCode, Function.identity(), (v1, v2) -> v1));
            }

            if (chargeToMap.size() > 0) {
                List<CisApplyChargeTo> chargeTos = chargeToMap.get(originDetailId);
                for (CisApplyChargeTo chargeTo : chargeTos) {
                    CisApplyChargeNto chargeNto = HIPBeanUtil.copy(chargeTo, CisApplyChargeNto.class);
                    chargeNto.setId("CG_" + HIPIDUtil.getNextIdString());
                    chargeNto.setOrderId(cisIpdOrderNto.getId());
                    chargeNto.setCisBaseApplyId(cisIpdOrderNto.getApplyCode());
                    chargeNto.setVisitCode(cisIpdOrderNto.getVisitCode());
                    chargeNto.setDetailId(cisDgimgApplyDetailNto.getId());
                    chargeNto.setChargeType(CisChargeTypeEnum.DOCT);
                    chargeNto.setStatusCode(CisStatusEnum.NEW);

                    if (priceToMap.size() > 0 && priceToMap.containsKey(chargeNto.getPriceItemCode())) {
                        ServiceClinicPriceTo priceTo = priceToMap.get(chargeNto.getPriceItemCode());
                        chargeNto.setNum(priceTo.getQuantity());
                        chargeNto.setChageAmount(chargeNto.getPrice().multiply(new BigDecimal(chargeNto.getNum() + "")));
                        chargeNto.setSystemItemClass(priceTo.getSystemItemClass());
                        String executeOrgCode = ipdOrderExecuteOrgUtil.buildCiaApplyChargeExecuteOrgCode(priceTo, cisIpdOrderNto.getVisitCode(), cisIpdOrderNto.getExecuteOrgCode());
                        chargeNto.setExecuteOrgCode(executeOrgCode);
                        chargeNto.setExecuteOrgName(workGroupService.getWorkGroup(executeOrgCode).getName());
                    }

                    chargeNtoList.add(chargeNto);
                }

            }
        }

        // 诊断信息
        List<ApplyDiagnosisNto> applyDiagnosisNtos = HIPBeanUtil.copy(cisDgimgApplyTo.getApplyDiagnoses(), ApplyDiagnosisNto.class);
        applyDiagnosisNtos.forEach(o -> {
            o.setId(HIPIDUtil.getNextIdString());
            o.setCisBaseApplyId(cisDgimgApplyNto.getId());
        });

        cisDgimgApplyNto.setDetails(cisDgimgApplyDetailNtos);
        cisDgimgApplyNto.setCisApplyCharges(chargeNtoList);
        return cisDgimgApplyNto;
    }
}
