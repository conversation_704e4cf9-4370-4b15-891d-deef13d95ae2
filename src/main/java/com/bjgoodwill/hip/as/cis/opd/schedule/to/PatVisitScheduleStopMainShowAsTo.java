package com.bjgoodwill.hip.as.cis.opd.schedule.to;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * Description:
 *
 * <AUTHOR>
 * &#064;date 2025/4/7 下午2:08
 */
public class PatVisitScheduleStopMainShowAsTo {
    @Schema(description = "行政科室编码")
    private String admdvsCode;
    @Schema(description = "行政科室名称")
    private String admdvsName;
    @Schema(description = "院区编码")
    private String hospitalAreaCode;
    @Schema(description = "院区名称")
    private String hospitalAreaName;

    @Schema(description = "科室排班分组集合")
    private List<PatVisitScheduleStopMainDeptAsTo> deptToList;

    public String getAdmdvsCode() {
        return admdvsCode;
    }

    public void setAdmdvsCode(String admdvsCode) {
        this.admdvsCode = admdvsCode;
    }

    public String getAdmdvsName() {
        return admdvsName;
    }

    public void setAdmdvsName(String admdvsName) {
        this.admdvsName = admdvsName;
    }

    public String getHospitalAreaCode() {
        return hospitalAreaCode;
    }

    public void setHospitalAreaCode(String hospitalAreaCode) {
        this.hospitalAreaCode = hospitalAreaCode;
    }

    public String getHospitalAreaName() {
        return hospitalAreaName;
    }

    public void setHospitalAreaName(String hospitalAreaName) {
        this.hospitalAreaName = hospitalAreaName;
    }

    public List<PatVisitScheduleStopMainDeptAsTo> getDeptToList() {
        return deptToList;
    }

    public void setDeptToList(List<PatVisitScheduleStopMainDeptAsTo> deptToList) {
        this.deptToList = deptToList;
    }
}
