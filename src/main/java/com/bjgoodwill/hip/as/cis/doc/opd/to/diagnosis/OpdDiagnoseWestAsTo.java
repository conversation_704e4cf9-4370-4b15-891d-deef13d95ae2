package com.bjgoodwill.hip.as.cis.doc.opd.to.diagnosis;

import com.bjgoodwill.hip.ds.base.cis.diagnose.dict.to.DiagnoseWestTo;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR>
 * @Date 2025/5/7 17:41
 */
@Schema(description = "西医疾病诊断-ICD10")
public class OpdDiagnoseWestAsTo extends DiagnoseWestTo {
    @Schema(description = "常用标识")
    private Boolean commonFlag;

    @Schema(description = "常用ID")
    private String commonID;

    public Boolean getCommonFlag() {
        return commonFlag;
    }

    public void setCommonFlag(Boolean commonFlag) {
        this.commonFlag = commonFlag;
    }

    public String getCommonID() {
        return commonID;
    }

    public void setCommonID(String commonID) {
        this.commonID = commonID;
    }
}
