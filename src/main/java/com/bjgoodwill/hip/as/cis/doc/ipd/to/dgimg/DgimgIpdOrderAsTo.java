package com.bjgoodwill.hip.as.cis.doc.ipd.to.dgimg;

import com.bjgoodwill.hip.as.cis.doc.ipd.to.CisOrderExecPlanAsTo;
import com.bjgoodwill.hip.as.cis.doc.ipd.to.operation.ApplyDiagnosisAsNto;
import com.bjgoodwill.hip.business.util.cis.common.enums.CheckTypeEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.CisStatusEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.OrderTypeEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.ds.cis.cpoe.order.to.CisIpdOrderExtTo;
import com.bjgoodwill.hip.ds.econ.ipd.bill.to.EconIpdBillAllTo;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> liangy<PERSON><PERSON>
 * @description :
 * @createDate : 2024/12/17 10:18
 */
@Schema(description = "检查医嘱详细信息To")
public class DgimgIpdOrderAsTo {

    @Schema(description = "医嘱标识")
    private String id;

    @Schema(description = "医嘱序号")
    private Double sortNo;

    @Schema(description = "申请单标识")
    private String applyId;

    @Schema(description = "主索引")
    private String patMiCode;

    @Schema(description = "流水号")
    private String visitCode;

    @Schema(description = "服务项目编码-(处置、嘱托、治疗等医嘱传值)")
    private String serviceItemCode;

    @Schema(description = "服务项目名称-(处置、嘱托、治疗等医嘱传值)")
    private String serviceItemName;

    @Schema(description = "医嘱内容")
    private String orderContent;

    @Schema(description = "医嘱类型(SystemTypeEnum)")
    private String orderClass;

    @Schema(description = "医嘱类型(1:长/2:临)")
    private String orderType;

    @Schema(description = "备注")
    private String reMark;

    @Schema(description = "护理组编码")
    private String deptNurseCode;

    @Schema(description = "护理组名称")
    private String deptNurseName;

    @Schema(description = "补录标识-1补")
    private String repairFlag;

    @Schema(description = "补录时间")
    private LocalDateTime repairTime;

    @Schema(description = "加急标识-1急")
    private String isCanPriorityFlag;

    @Schema(description = "频次")
    private String frequency;

    @Schema(description = "频次名称")
    private String frequencyName;

    @Schema(description = "是否婴儿")
    private String isBaby;

    @Schema(description = "执行科室编码")
    private String executeOrgCode;

    @Schema(description = "执行科室名称")
    private String executeOrgName;

    @Schema(description = "第三方")
    private Boolean thirdFlag;

    @Schema(description = "协定处方")
    private Boolean prescriptionFlag;

    @Schema(description = "用法")
    private String usage;

    @Schema(description = "用法名称")
    private String usageName;

    @Schema(description = "每次剂量")
    private Double dosage;

    @Schema(description = "剂量单位")
    private String dosageUnit;

    @Schema(description = "数量")
    private Double num;

    @Schema(description = "检查注意事项")
    private String precautions;

    @Schema(description = "病历及查体摘要")
    private String medrecordAndExamabstract;

    @Schema(description = "体格及其他检查")
    private String physiqueAndExam;

    @Schema(description = "分类")
    private String dgimgClass;

    @Schema(description = "子分类")
    private String dgimgSubClass;

    @Schema(description = "相关辅检")
    private String auxiliaryInspection;

    @Schema(description = "检查目的")
    private String checkPurpose;

    @Schema(description = "申请单预约标识")
    private String applyBookId;

    @Schema(description = "既往病理检查结果")
    private String previousPathologicalExamin;

    @Schema(description = "设备类型")
    private String deviceType;

    @Schema(description = "是否过敏史")
    private Boolean allergicHistoryFlag;

    @Schema(description = "是否职业病史")
    private Boolean occupationalDiseasesFlag;

    @Schema(description = "临床病史")
    private String clinicalHistory;

    @Schema(description = "是否传染病史")
    private Boolean contagiousDiseaseHistoryFlag;

    @Schema(description = "创建人员")
    private String createdStaff;

    @Schema(description = "创建人员名称")
    private String createdStaffName;

    @Schema(description = "创建时间")
    private LocalDateTime createdDate;

    @Schema(description = "创建医生科室")
    private String createOrgCode;

    @Schema(description = "签发时间/开立时间")
    private LocalDateTime commitDate;

    @Schema(description = "签发人")
    private String submitStaffId;

    @Schema(description = "签发人姓名")
    private String submitStaffName;

    @Schema(description = "校对人")
    private String proofStaff;

    @Schema(description = "校对人姓名")
    private String proofStaffName;

    @Schema(description = "校对时间")
    private LocalDateTime proofDate;

    @Schema(description = "医嘱状态")
    private CisStatusEnum statusCode;

    @Schema(description = "状态编码")
    private String statusCodeCode;

    @Schema(description = "状态名称")
    private String statusCodeName;

    @Schema(description = "特显符合标识 1符合 0不符合 null不是特限项目")
    private Boolean limitConformFlag;

    @Schema(description = "医院编码")
    private String hospitalCode;

    @Schema(description = "版本")
    private Integer version;

    @Schema(description = "作废人")
    private String cancelStaff;
    @Schema(
            description = "作废人姓名"
    )
    private String cancelStaffName;
    @Schema(
            description = "作废时间"
    )
    private LocalDateTime cancelDate;
    @Schema(
            description = "作废原因"
    )
    private String cancelRemark;

    @Schema(
            description = "医嘱开始时间"
    )
    private LocalDateTime effectiveLowDate;

    @Schema(description = "申请单明细To")
    private List<DgimgIpdOrderDetailAsTo> detailAsToList;

    @Schema(description = "收费信息")
    private List<EconIpdBillAllTo> econIpdBillAllTos;

    @Schema(description = "医嘱执行档信息")
    private List<CisOrderExecPlanAsTo> cisOrderExecPlanAsToList;

    @Schema(description = "临床诊断列表")
    private List<ApplyDiagnosisAsNto> applyDiagnosisAsNtos;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Double getSortNo() {
        return sortNo;
    }

    public void setSortNo(Double sortNo) {
        this.sortNo = sortNo;
    }

    public String getApplyId() {
        return applyId;
    }

    public void setApplyId(String applyId) {
        this.applyId = applyId;
    }

    public String getPatMiCode() {
        return patMiCode;
    }

    public void setPatMiCode(String patMiCode) {
        this.patMiCode = patMiCode;
    }

    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    public String getServiceItemCode() {
        return serviceItemCode;
    }

    public void setServiceItemCode(String serviceItemCode) {
        this.serviceItemCode = serviceItemCode;
    }

    public String getServiceItemName() {
        return serviceItemName;
    }

    public void setServiceItemName(String serviceItemName) {
        this.serviceItemName = serviceItemName;
    }

    public String getOrderContent() {
        return orderContent;
    }

    public void setOrderContent(String orderContent) {
        this.orderContent = orderContent;
    }

    public String getOrderClass() {
        return orderClass;
    }

    public void setOrderClass(String orderClass) {
        this.orderClass = orderClass;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public String getReMark() {
        return reMark;
    }

    public void setReMark(String reMark) {
        this.reMark = reMark;
    }

    public String getDeptNurseCode() {
        return deptNurseCode;
    }

    public void setDeptNurseCode(String deptNurseCode) {
        this.deptNurseCode = deptNurseCode;
    }

    public String getDeptNurseName() {
        return deptNurseName;
    }

    public void setDeptNurseName(String deptNurseName) {
        this.deptNurseName = deptNurseName;
    }

    public String getRepairFlag() {
        return repairFlag;
    }

    public void setRepairFlag(String repairFlag) {
        this.repairFlag = repairFlag;
    }

    public LocalDateTime getRepairTime() {
        return repairTime;
    }

    public void setRepairTime(LocalDateTime repairTime) {
        this.repairTime = repairTime;
    }

    public String getIsCanPriorityFlag() {
        return isCanPriorityFlag;
    }

    public void setIsCanPriorityFlag(String isCanPriorityFlag) {
        this.isCanPriorityFlag = isCanPriorityFlag;
    }

    public String getFrequency() {
        return frequency;
    }

    public void setFrequency(String frequency) {
        this.frequency = frequency;
    }

    public String getFrequencyName() {
        return frequencyName;
    }

    public void setFrequencyName(String frequencyName) {
        this.frequencyName = frequencyName;
    }

    public String getIsBaby() {
        return isBaby;
    }

    public void setIsBaby(String isBaby) {
        this.isBaby = isBaby;
    }

    public String getExecuteOrgCode() {
        return executeOrgCode;
    }

    public void setExecuteOrgCode(String executeOrgCode) {
        this.executeOrgCode = executeOrgCode;
    }

    public String getExecuteOrgName() {
        return executeOrgName;
    }

    public void setExecuteOrgName(String executeOrgName) {
        this.executeOrgName = executeOrgName;
    }

    public Boolean getThirdFlag() {
        return thirdFlag;
    }

    public void setThirdFlag(Boolean thirdFlag) {
        this.thirdFlag = thirdFlag;
    }

    public Boolean getPrescriptionFlag() {
        return prescriptionFlag;
    }

    public void setPrescriptionFlag(Boolean prescriptionFlag) {
        this.prescriptionFlag = prescriptionFlag;
    }

    public String getUsage() {
        return usage;
    }

    public void setUsage(String usage) {
        this.usage = usage;
    }

    public String getUsageName() {
        return usageName;
    }

    public void setUsageName(String usageName) {
        this.usageName = usageName;
    }

    public Double getDosage() {
        return dosage;
    }

    public void setDosage(Double dosage) {
        this.dosage = dosage;
    }

    public String getDosageUnit() {
        return dosageUnit;
    }

    public void setDosageUnit(String dosageUnit) {
        this.dosageUnit = dosageUnit;
    }

    public Double getNum() {
        return num;
    }

    public void setNum(Double num) {
        this.num = num;
    }

    public String getPrecautions() {
        return precautions;
    }

    public void setPrecautions(String precautions) {
        this.precautions = precautions;
    }

    public String getMedrecordAndExamabstract() {
        return medrecordAndExamabstract;
    }

    public void setMedrecordAndExamabstract(String medrecordAndExamabstract) {
        this.medrecordAndExamabstract = medrecordAndExamabstract;
    }

    public String getPhysiqueAndExam() {
        return physiqueAndExam;
    }

    public void setPhysiqueAndExam(String physiqueAndExam) {
        this.physiqueAndExam = physiqueAndExam;
    }

    public String getDgimgClass() {
        return dgimgClass;
    }

    public void setDgimgClass(String dgimgClass) {
        this.dgimgClass = dgimgClass;
    }

    public String getDgimgSubClass() {
        return dgimgSubClass;
    }

    public void setDgimgSubClass(String dgimgSubClass) {
        this.dgimgSubClass = dgimgSubClass;
    }

    public String getAuxiliaryInspection() {
        return auxiliaryInspection;
    }

    public void setAuxiliaryInspection(String auxiliaryInspection) {
        this.auxiliaryInspection = auxiliaryInspection;
    }

    public String getCheckPurpose() {
        return checkPurpose;
    }

    public void setCheckPurpose(String checkPurpose) {
        this.checkPurpose = checkPurpose;
    }

    public String getApplyBookId() {
        return applyBookId;
    }

    public void setApplyBookId(String applyBookId) {
        this.applyBookId = applyBookId;
    }

    public String getPreviousPathologicalExamin() {
        return previousPathologicalExamin;
    }

    public void setPreviousPathologicalExamin(String previousPathologicalExamin) {
        this.previousPathologicalExamin = previousPathologicalExamin;
    }

    public String getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(String deviceType) {
        this.deviceType = deviceType;
    }

    public Boolean getAllergicHistoryFlag() {
        return allergicHistoryFlag;
    }

    public void setAllergicHistoryFlag(Boolean allergicHistoryFlag) {
        this.allergicHistoryFlag = allergicHistoryFlag;
    }

    public Boolean getOccupationalDiseasesFlag() {
        return occupationalDiseasesFlag;
    }

    public void setOccupationalDiseasesFlag(Boolean occupationalDiseasesFlag) {
        this.occupationalDiseasesFlag = occupationalDiseasesFlag;
    }

    public String getClinicalHistory() {
        return clinicalHistory;
    }

    public void setClinicalHistory(String clinicalHistory) {
        this.clinicalHistory = clinicalHistory;
    }

    public Boolean getContagiousDiseaseHistoryFlag() {
        return contagiousDiseaseHistoryFlag;
    }

    public void setContagiousDiseaseHistoryFlag(Boolean contagiousDiseaseHistoryFlag) {
        this.contagiousDiseaseHistoryFlag = contagiousDiseaseHistoryFlag;
    }

    public String getCreatedStaff() {
        return createdStaff;
    }

    public void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    public String getCreatedStaffName() {
        return createdStaffName;
    }

    public void setCreatedStaffName(String createdStaffName) {
        this.createdStaffName = createdStaffName;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getCreateOrgCode() {
        return createOrgCode;
    }

    public void setCreateOrgCode(String createOrgCode) {
        this.createOrgCode = createOrgCode;
    }

    public LocalDateTime getCommitDate() {
        return commitDate;
    }

    public void setCommitDate(LocalDateTime commitDate) {
        this.commitDate = commitDate;
    }

    public String getSubmitStaffId() {
        return submitStaffId;
    }

    public void setSubmitStaffId(String submitStaffId) {
        this.submitStaffId = submitStaffId;
    }

    public String getSubmitStaffName() {
        return submitStaffName;
    }

    public void setSubmitStaffName(String submitStaffName) {
        this.submitStaffName = submitStaffName;
    }

    public String getProofStaff() {
        return proofStaff;
    }

    public void setProofStaff(String proofStaff) {
        this.proofStaff = proofStaff;
    }

    public String getProofStaffName() {
        return proofStaffName;
    }

    public void setProofStaffName(String proofStaffName) {
        this.proofStaffName = proofStaffName;
    }

    public LocalDateTime getProofDate() {
        return proofDate;
    }

    public void setProofDate(LocalDateTime proofDate) {
        this.proofDate = proofDate;
    }

    public CisStatusEnum getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(CisStatusEnum statusCode) {
        this.statusCode = statusCode;
    }

    public String getStatusCodeCode() {
        return statusCodeCode;
    }

    public void setStatusCodeCode(String statusCodeCode) {
        this.statusCodeCode = statusCodeCode;
    }

    public String getStatusCodeName() {
        return statusCodeName;
    }

    public void setStatusCodeName(String statusCodeName) {
        this.statusCodeName = statusCodeName;
    }

    public Boolean getLimitConformFlag() {
        return limitConformFlag;
    }

    public void setLimitConformFlag(Boolean limitConformFlag) {
        this.limitConformFlag = limitConformFlag;
    }

    public String getHospitalCode() {
        return hospitalCode;
    }

    public void setHospitalCode(String hospitalCode) {
        this.hospitalCode = hospitalCode;
    }

    public List<DgimgIpdOrderDetailAsTo> getDetailAsToList() {
        return detailAsToList;
    }

    public void setDetailAsToList(List<DgimgIpdOrderDetailAsTo> detailAsToList) {
        this.detailAsToList = detailAsToList;
    }

    public List<EconIpdBillAllTo> getEconIpdBillAllTos() {
        return econIpdBillAllTos;
    }

    public void setEconIpdBillAllTos(List<EconIpdBillAllTo> econIpdBillAllTos) {
        this.econIpdBillAllTos = econIpdBillAllTos;
    }

    public List<CisOrderExecPlanAsTo> getCisOrderExecPlanAsToList() {
        return cisOrderExecPlanAsToList;
    }

    public void setCisOrderExecPlanAsToList(List<CisOrderExecPlanAsTo> cisOrderExecPlanAsToList) {
        this.cisOrderExecPlanAsToList = cisOrderExecPlanAsToList;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getCancelStaff() {
        return cancelStaff;
    }

    public void setCancelStaff(String cancelStaff) {
        this.cancelStaff = cancelStaff;
    }

    public String getCancelStaffName() {
        return cancelStaffName;
    }

    public void setCancelStaffName(String cancelStaffName) {
        this.cancelStaffName = cancelStaffName;
    }

    public LocalDateTime getCancelDate() {
        return cancelDate;
    }

    public void setCancelDate(LocalDateTime cancelDate) {
        this.cancelDate = cancelDate;
    }

    public String getCancelRemark() {
        return cancelRemark;
    }

    public void setCancelRemark(String cancelRemark) {
        this.cancelRemark = cancelRemark;
    }

    public LocalDateTime getEffectiveLowDate() {
        return effectiveLowDate;
    }

    public void setEffectiveLowDate(LocalDateTime effectiveLowDate) {
        this.effectiveLowDate = effectiveLowDate;
    }

    public List<ApplyDiagnosisAsNto> getApplyDiagnosisAsNtos() {
        return applyDiagnosisAsNtos;
    }

    public void setApplyDiagnosisAsNtos(List<ApplyDiagnosisAsNto> applyDiagnosisAsNtos) {
        this.applyDiagnosisAsNtos = applyDiagnosisAsNtos;
    }
}
