package com.bjgoodwill.hip.as.cis.nurse.ipd.to;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Schema(description = "领药单发送入参Qto")
public class DrugReceiptSendAsQto {

    @Schema(description = "患者流水号集合")
    private List<String> visitCodes = new ArrayList<>();

    @Schema(description = "取药科室")
    private String receiveOrg;

    @Schema(description = "截止时间")
    private LocalDateTime execPlanDate;

    @NotBlank(message = "患者流水号不能为空！")
    public List<String> getVisitCodes() {
        return visitCodes;
    }

    @Schema(description = "登录科室")
    private String workGroupCode;

    @Schema(description = "医嘱类型")
    private String orderType;

    public String getWorkGroupCode() {
        return workGroupCode;
    }

    public void setWorkGroupCode(String workGroupCode) {
        this.workGroupCode = workGroupCode;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public void setVisitCodes(List<String> visitCodes) {
        this.visitCodes = visitCodes;
    }

    public String getReceiveOrg() {
        return receiveOrg;
    }

    public void setReceiveOrg(String receiveOrg) {
        this.receiveOrg = receiveOrg;
    }

    @NotBlank(message = "取药时间不能为空！")
    public LocalDateTime getExecPlanDate() {
        return execPlanDate;
    }

    public void setExecPlanDate(LocalDateTime execPlanDate) {
        this.execPlanDate = execPlanDate;
    }
}
