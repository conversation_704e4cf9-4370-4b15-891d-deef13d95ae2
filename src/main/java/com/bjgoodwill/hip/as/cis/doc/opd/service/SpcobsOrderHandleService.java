package com.bjgoodwill.hip.as.cis.doc.opd.service;

import com.bjgoodwill.hip.as.cis.doc.opd.to.doctor.CisApplyChargeAsTo;
import com.bjgoodwill.hip.as.cis.doc.opd.to.spcobs.CisSpcobsApplyAsTo;
import com.bjgoodwill.hip.as.cis.doc.opd.to.spcobs.OpdSpcobsApplyAsTo;
import com.bjgoodwill.hip.as.cis.doc.opd.to.spcobs.SpcobsOrderDto;

import java.util.List;

/**
 * <AUTHOR> liangy<PERSON><PERSON>
 * @description :
 * @createDate : 2025/5/13 10:25
 */
public interface SpcobsOrderHandleService {

    /**
     * 查询检验项目列表
     *
     * @param workGroupCode
     * @param hospitalAreaCode
     * @return
     */
    List<OpdSpcobsApplyAsTo> searchItems(String workGroupCode, String hospitalAreaCode);

    /**
     * 查询常用检验项目列表
     *
     * @param workGroupCode
     * @param hospitalAreaCode
     * @return
     */
    List<OpdSpcobsApplyAsTo> searchCommonItems(String workGroupCode, String hospitalAreaCode);

    /**
     * 获取检验项目明细
     *
     * @param serviceItemCode
     * @return
     */
    List<CisApplyChargeAsTo> getServiceClincPriceList(String serviceItemCode);

    /**
     * 插入或更新检验医嘱
     *
     * @param spcobsOrderDtoList
     */
    List<String> insertOrUpdateSpcobsOrders(List<SpcobsOrderDto> spcobsOrderDtoList);

    /**
     * 插入或更新检验医嘱并提交
     *
     * @param spcobsOrderDtoList
     * @param mergeFlag
     * @param prescriptionVersion
     */
    void insertOrUpdateAndSubmitSpcobsOrder(List<SpcobsOrderDto> spcobsOrderDtoList, boolean mergeFlag, String prescriptionVersion);

    /**
     * 查询检验医嘱信息
     *
     * @param id
     * @return
     */
    SpcobsOrderDto getSpcobsOrder(String id);

    /**
     * 查询New检验医嘱列表
     *
     * @param visitCode
     * @return
     */
    List<CisSpcobsApplyAsTo> getNewSpcobsApply(String visitCode);

    /**
     * 查询已签发未缴费检验医嘱列表
     *
     * @param visitCode
     * @return
     */
    List<CisSpcobsApplyAsTo> getActiveSpcobsApply(String visitCode);

    /**
     * 签发检验医嘱
     *
     * @param cisSpcobsApplyAsTos
     * @param mergeFlag
     * @param visitCode
     * @param prescriptionVersion
     */
    void submitSpcobsOrder(List<CisSpcobsApplyAsTo> cisSpcobsApplyAsTos, boolean mergeFlag, String visitCode, String prescriptionVersion);

    /**
     * 撤回检验医嘱
     *
     * @param orderIdList
     */
    void backSpcobsOrder(List<String> orderIdList);

    /**
     * 删除检验医嘱
     *
     * @param orderIdList
     */
    void deleteSpcobsOrder(List<String> orderIdList);
}
