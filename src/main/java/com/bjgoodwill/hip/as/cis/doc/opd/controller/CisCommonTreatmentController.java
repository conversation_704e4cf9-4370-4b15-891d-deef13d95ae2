package com.bjgoodwill.hip.as.cis.doc.opd.controller;

import com.bjgoodwill.hip.as.cis.doc.opd.enums.CisDocOpdBusinessErrorEnum;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.opdcpoe.treatment.service.CisCommonTreatmentService;
import com.bjgoodwill.hip.ds.cis.opdcpoe.treatment.to.CisCommonTreatmentNto;
import com.bjgoodwill.hip.ds.cis.opdcpoe.treatment.to.CisCommonTreatmentTo;
import com.bjgoodwill.hip.ds.cis.opdcpoe.treatment.to.CisTreatmentSourceTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @program: hip-cis-doc-opd-as
 * @author: xdguo
 * @create: 2025-06-11 09:29
 * @className: CisCommonTreatmentController
 * @description:
 **/
@RestController
@Tag(name = "门诊普通诊疗应用服务", description = "门诊普通诊疗应用服务")
@RequestMapping("/cis/doc/opd/commonTreatment")
public class CisCommonTreatmentController {

    //普通诊疗领域服务
    @Autowired
    private CisCommonTreatmentService cisCommonTreatmentService;

    @Operation(summary = "创建诊疗服务", description = "创建诊疗服务")
    @ApiResponse(description = "创建诊疗服务", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = CisTreatmentSourceTo.class))))
    @PostMapping(value = "/commonTreatment/{visit-code:.+}")
    public CisCommonTreatmentTo createTreatment(@PathVariable("visit-code") String visitCode, @RequestBody CisCommonTreatmentNto cisCommonTreatmentNto) {
        BusinessAssert.notNull(cisCommonTreatmentNto, CisDocOpdBusinessErrorEnum.BUS_CIS_DOC_OPD_0001, "诊疗");
        cisCommonTreatmentNto.setId(visitCode);
        return cisCommonTreatmentService.createCisCommonTreatment(cisCommonTreatmentNto);
    }

}