package com.bjgoodwill.hip.as.cis.nurse.ipd.to.patientExpenseManagement;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;

/**
 * File: OrderChargeRefundFeeAsTo
 * Author: zhangyunchuan
 * Date: 2025/2/27
 * Description:
 */
@Schema(description = "医嘱计费-退费")
public class OrderChargeRefundFeeAsTo {

    @Schema(description = "系统项目分类 PRICE:物价项目 DRUG:药品 MATE:材料")
    private String systemItemClassCode;

    @Schema(description = "数量 药品未发药不允许半退")
    private BigDecimal refundNum;

    @Schema(description = "领药单标识")
    private String drugApplyId;

    @Schema(description = "发药人")
    private String dispenseStaff;

    @Schema(description = "正账单标识")
    private String billId;

    @Schema(description = "正账单项标识")
    private String detailId;

    @Schema(description = "执行科室")
    private String executeOrg;

    public String getSystemItemClassCode() {
        return systemItemClassCode;
    }

    public void setSystemItemClassCode(String systemItemClassCode) {
        this.systemItemClassCode = systemItemClassCode;
    }

    public String getDrugApplyId() {
        return drugApplyId;
    }

    public void setDrugApplyId(String drugApplyId) {
        this.drugApplyId = drugApplyId;
    }

    public String getDispenseStaff() {
        return dispenseStaff;
    }

    public void setDispenseStaff(String dispenseStaff) {
        this.dispenseStaff = dispenseStaff;
    }

    public BigDecimal getRefundNum() {
        return refundNum;
    }

    public void setRefundNum(BigDecimal refundNum) {
        this.refundNum = refundNum;
    }

    public String getBillId() {
        return billId;
    }

    public void setBillId(String billId) {
        this.billId = billId;
    }

    public String getDetailId() {
        return detailId;
    }

    public void setDetailId(String detailId) {
        this.detailId = detailId;
    }

    public String getExecuteOrg() {
        return executeOrg;
    }

    public void setExecuteOrg(String executeOrg) {
        this.executeOrg = executeOrg;
    }
}
