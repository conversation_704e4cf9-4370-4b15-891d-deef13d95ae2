package com.bjgoodwill.hip.as.cis.doc.opd.to.dgimg;

import com.bjgoodwill.hip.ds.cis.apply.medicalhistory.to.CisMedicalHistoryTo;
import com.bjgoodwill.hip.ds.term.api.to.DictElementTo;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @description :
 * @createDate : 2025/5/19 10:30
 */
public class OpdDgimgItemResult {

    @Schema(description = "检查服务项目")
    private List<OpdDgimgServiceItemAsTo> serviceItemAsTos;

    @Schema(description = "方法集合")
    private List<DictElementTo> methodList;

    @Schema(description = "部位集合")
    private List<DictElementTo> humanOrgansList;

    @Schema(description = "患者病史")
    private CisMedicalHistoryTo cisMedicalHistoryTo;

    public List<OpdDgimgServiceItemAsTo> getServiceItemAsTos() {
        return serviceItemAsTos;
    }

    public void setServiceItemAsTos(List<OpdDgimgServiceItemAsTo> serviceItemAsTos) {
        this.serviceItemAsTos = serviceItemAsTos;
    }

    public List<DictElementTo> getMethodList() {
        return methodList;
    }

    public void setMethodList(List<DictElementTo> methodList) {
        this.methodList = methodList;
    }

    public List<DictElementTo> getHumanOrgansList() {
        return humanOrgansList;
    }

    public void setHumanOrgansList(List<DictElementTo> humanOrgansList) {
        this.humanOrgansList = humanOrgansList;
    }

    public CisMedicalHistoryTo getCisMedicalHistoryTo() {
        return cisMedicalHistoryTo;
    }

    public void setCisMedicalHistoryTo(CisMedicalHistoryTo cisMedicalHistoryTo) {
        this.cisMedicalHistoryTo = cisMedicalHistoryTo;
    }
}
