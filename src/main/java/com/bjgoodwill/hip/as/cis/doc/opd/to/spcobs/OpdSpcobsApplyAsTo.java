package com.bjgoodwill.hip.as.cis.doc.opd.to.spcobs;

import com.bjgoodwill.hip.ds.cis.medicineitem.serviceItem.to.SpcobsApplyTo;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @description :
 * @createDate : 2025/5/13 10:21
 */
public class OpdSpcobsApplyAsTo extends SpcobsApplyTo implements Serializable {

    @Schema(description = "收藏")
    private Boolean collectionFlag;

    @Schema(description = "收藏id")
    private String collectionId;

    public String getCollectionId() {
        return collectionId;
    }

    public void setCollectionId(String collectionId) {
        this.collectionId = collectionId;
    }

    public Boolean getCollectionFlag() {
        return collectionFlag;
    }

    public void setCollectionFlag(Boolean collectionFlag) {
        this.collectionFlag = collectionFlag;
    }

}
