package com.bjgoodwill.hip.as.cis.nurse.ipd.to.patientExpenseManagement;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * File: OrderChargingPatInfoAsQto
 * Author: zhangyunchuan
 * Date: 2025/3/27
 * Description:
 */
@Schema(description = "医嘱计费-")
public class OrderChargingPatInfoAsQto {

    @Schema(description = "在院科室")
    private String inDeptCode;

    @Schema(description = "住院号-查询")
    private String inpatientCode;

    @Schema(description = "住院号-过滤")
    private String inpatientCodeFilter;

    @Schema(description = "患者姓名-过滤")
    private String patName;

    public String getInDeptCode() {
        return inDeptCode;
    }

    public void setInDeptCode(String inDeptCode) {
        this.inDeptCode = inDeptCode;
    }

    public String getInpatientCode() {
        return inpatientCode;
    }

    public void setInpatientCode(String inpatientCode) {
        this.inpatientCode = inpatientCode;
    }

    public String getInpatientCodeFilter() {
        return inpatientCodeFilter;
    }

    public void setInpatientCodeFilter(String inpatientCodeFilter) {
        this.inpatientCodeFilter = inpatientCodeFilter;
    }

    public String getPatName() {
        return patName;
    }

    public void setPatName(String patName) {
        this.patName = patName;
    }
}
