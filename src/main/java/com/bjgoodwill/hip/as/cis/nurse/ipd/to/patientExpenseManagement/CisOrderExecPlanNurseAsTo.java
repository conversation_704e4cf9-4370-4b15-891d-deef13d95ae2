package com.bjgoodwill.hip.as.cis.nurse.ipd.to.patientExpenseManagement;

import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.to.CisOrderExecPlanTo;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;

/**
 * File: CisOrderExecPlanNurseAsTo
 * Author: zhangyunchuan
 * Date: 2025/2/24
 * Description:
 */
@Schema(description = "划价确费")
public class CisOrderExecPlanNurseAsTo implements Serializable {

    @Schema(description = "医嘱id")
    private String orderId;

    @Schema(description = "医嘱内容")
    private String orderContent;

    @Schema(description = "医嘱类型")
    private SystemTypeEnum orderClass;

    @Schema(description = "医嘱类型编码")
    private String orderClassCode;

    @Schema(description = "医嘱类型名称")
    private String orderClassName;

    @Schema(description = "医嘱执行档")
    private List<CisOrderExecPlanTo> cisOrderExecPlanToList;

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getOrderContent() {
        return orderContent;
    }

    public void setOrderContent(String orderContent) {
        this.orderContent = orderContent;
    }

    public List<CisOrderExecPlanTo> getCisOrderExecPlanToList() {
        return cisOrderExecPlanToList;
    }

    public void setCisOrderExecPlanToList(List<CisOrderExecPlanTo> cisOrderExecPlanToList) {
        this.cisOrderExecPlanToList = cisOrderExecPlanToList;
    }

    public SystemTypeEnum getOrderClass() {
        return orderClass;
    }

    public void setOrderClass(SystemTypeEnum orderClass) {
        this.orderClass = orderClass;
    }

    public String getOrderClassCode() {
        return orderClassCode;
    }

    public void setOrderClassCode(String orderClassCode) {
        this.orderClassCode = orderClassCode;
    }

    public String getOrderClassName() {
        return orderClassName;
    }

    public void setOrderClassName(String orderClassName) {
        this.orderClassName = orderClassName;
    }
}
