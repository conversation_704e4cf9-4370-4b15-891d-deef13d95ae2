package com.bjgoodwill.hip.as.cis.nurse.ipd.to;

import com.bjgoodwill.hip.ds.pat.in.hospital.inpatient.to.PatIpdInpatientTo;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;

@Schema(name = "护士站医嘱执行患者信息To")
public class NurseExecPatInfoAsTo extends PatIpdInpatientTo {

    @Schema(description = "费别名称")
    private String feeTypeName;

    @Schema(description = "住院诊断")
    private String impDiagnosis;

    @Schema(description = "当前病情")
    private String condition;

    @Schema(description = "当前护理级别 字典CV06.00.220 ")
    private String nursingLevel;

    @Schema(description = "当前护理级别名称")
    private String nursingLevelName;

    @Schema(description = "预交余额")
    private BigDecimal balance;

    @Schema(description = "担保金额")
    private BigDecimal guaranteeAmount;

    @Schema(description = "医嘱执行单信息")
    private List<CisOrderExecAsTo> cisOrderExecAsTos;

    public String getFeeTypeName() {
        return feeTypeName;
    }

    public void setFeeTypeName(String feeTypeName) {
        this.feeTypeName = feeTypeName;
    }

    public String getImpDiagnosis() {
        return impDiagnosis;
    }

    public void setImpDiagnosis(String impDiagnosis) {
        this.impDiagnosis = impDiagnosis;
    }

    public String getCondition() {
        return condition;
    }

    public void setCondition(String condition) {
        this.condition = condition;
    }

    public String getNursingLevel() {
        return nursingLevel;
    }

    public void setNursingLevel(String nursingLevel) {
        this.nursingLevel = nursingLevel;
    }

    public String getNursingLevelName() {
        return nursingLevelName;
    }

    public void setNursingLevelName(String nursingLevelName) {
        this.nursingLevelName = nursingLevelName;
    }

    public BigDecimal getBalance() {
        return balance;
    }

    public void setBalance(BigDecimal balance) {
        this.balance = balance;
    }

    public BigDecimal getGuaranteeAmount() {
        return guaranteeAmount;
    }

    public void setGuaranteeAmount(BigDecimal guaranteeAmount) {
        this.guaranteeAmount = guaranteeAmount;
    }

    public List<CisOrderExecAsTo> getCisOrderExecAsTos() {
        return cisOrderExecAsTos;
    }

    public void setCisOrderExecAsTos(List<CisOrderExecAsTo> cisOrderExecAsTos) {
        this.cisOrderExecAsTos = cisOrderExecAsTos;
    }
}
