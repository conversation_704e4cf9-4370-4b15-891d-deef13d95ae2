package com.bjgoodwill.hip.as.cis.doc.opd.to.dgimg;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR> liangyu<PERSON>
 * @description :
 * @createDate : 2025/5/20 14:29
 */
public class CisDgimgApplyAsTo {

    private String orderID;

    private String id;

    private String serviceItemCode;

    private String serviceItemName;

    private String deviceType;

    private String deviceTypeName;

    private String executorOrgCode;

    private String executorOrgName;

    private LocalDateTime createdDate;

    private BigDecimal chargeAmount;

    @Schema(description = "加急标识")
    private String isCanPriorityFlag;

    public String getOrderID() {
        return orderID;
    }

    public void setOrderID(String orderID) {
        this.orderID = orderID;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getServiceItemCode() {
        return serviceItemCode;
    }

    public void setServiceItemCode(String serviceItemCode) {
        this.serviceItemCode = serviceItemCode;
    }

    public String getServiceItemName() {
        return serviceItemName;
    }

    public void setServiceItemName(String serviceItemName) {
        this.serviceItemName = serviceItemName;
    }

    public String getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(String deviceType) {
        this.deviceType = deviceType;
    }

    public String getDeviceTypeName() {
        return deviceTypeName;
    }

    public void setDeviceTypeName(String deviceTypeName) {
        this.deviceTypeName = deviceTypeName;
    }

    public String getExecutorOrgCode() {
        return executorOrgCode;
    }

    public void setExecutorOrgCode(String executorOrgCode) {
        this.executorOrgCode = executorOrgCode;
    }

    public String getExecutorOrgName() {
        return executorOrgName;
    }

    public void setExecutorOrgName(String executorOrgName) {
        this.executorOrgName = executorOrgName;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getIsCanPriorityFlag() {
        return isCanPriorityFlag;
    }

    public void setIsCanPriorityFlag(String isCanPriorityFlag) {
        this.isCanPriorityFlag = isCanPriorityFlag;
    }

    public BigDecimal getChargeAmount() {
        return chargeAmount;
    }

    public void setChargeAmount(BigDecimal chargeAmount) {
        this.chargeAmount = chargeAmount;
    }
}
