package com.bjgoodwill.hip.as.cis.doc.ipd.to.operation;

import com.bjgoodwill.hip.as.cis.doc.ipd.to.CisApplyChargeAsEto;
import com.bjgoodwill.hip.as.cis.doc.ipd.to.CisIpdDocOrderDetailsAsEto;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> liangy<PERSON><PERSON>
 * @description :
 * @createDate : 2025/2/25 15:28
 */
public class OperationApplyDetailAsEto extends CisIpdDocOrderDetailsAsEto implements Serializable {

    @Schema(description = "手术术式编码,ICD9-CM3编码")
    private String operationCode;
    @Schema(description = "手术术式编码,ICD9-CM3名称")
    private String operationName;
    @Schema(description = "手术级别")
    private String operationLevel;
    @Schema(description = "部位")
    private String humanOrgans;
    @Schema(description = "部位名称")
    private String humanOrgansName;
    @Schema(description = "体位")
    private String decubitus;
    @Schema(description = "体位名称")
    private String decubitusName;

    private List<CisApplyChargeAsEto> cisApplyChargeAsEtos;

    public String getOperationCode() {
        return operationCode;
    }

    public void setOperationCode(String operationCode) {
        this.operationCode = operationCode;
    }

    public String getOperationName() {
        return operationName;
    }

    public void setOperationName(String operationName) {
        this.operationName = operationName;
    }

    public String getOperationLevel() {
        return operationLevel;
    }

    public void setOperationLevel(String operationLevel) {
        this.operationLevel = operationLevel;
    }

    public String getHumanOrgans() {
        return humanOrgans;
    }

    public void setHumanOrgans(String humanOrgans) {
        this.humanOrgans = humanOrgans;
    }

    public String getHumanOrgansName() {
        return humanOrgansName;
    }

    public void setHumanOrgansName(String humanOrgansName) {
        this.humanOrgansName = humanOrgansName;
    }

    public String getDecubitus() {
        return decubitus;
    }

    public void setDecubitus(String decubitus) {
        this.decubitus = decubitus;
    }

    public String getDecubitusName() {
        return decubitusName;
    }

    public void setDecubitusName(String decubitusName) {
        this.decubitusName = decubitusName;
    }

    public List<CisApplyChargeAsEto> getCisApplyChargeAsEtos() {
        return cisApplyChargeAsEtos;
    }

    public void setCisApplyChargeAsEtos(List<CisApplyChargeAsEto> cisApplyChargeAsEtos) {
        this.cisApplyChargeAsEtos = cisApplyChargeAsEtos;
    }
}
