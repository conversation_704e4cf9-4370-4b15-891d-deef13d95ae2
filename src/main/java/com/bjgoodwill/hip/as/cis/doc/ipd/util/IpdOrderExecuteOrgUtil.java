package com.bjgoodwill.hip.as.cis.doc.ipd.util;

import com.bjgoodwill.hip.business.util.cis.common.enums.CisFeeExecEnum;
import com.bjgoodwill.hip.ds.cis.medicineitem.price.to.ServiceClinicPriceTo;
import com.bjgoodwill.hip.ds.pat.in.hospital.inpatient.service.PatIpdInpatientService;
import com.bjgoodwill.hip.ds.pat.in.hospital.inpatient.to.PatIpdInpatientTo;
import com.bjgoodwill.hip.security.util.HIPCurrentOrgUtils;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> liangyuqing
 * @description :
 * @createDate : 2025/3/25 10:00
 */
@Service
public class IpdOrderExecuteOrgUtil {

    @Resource
    private PatIpdInpatientService patIpdInpatientService;

    /**
     * 构造费用信息执行科室
     * @param serviceClinicPriceTo
     * @param visitCode
     * @param executeOrgCode 医嘱执行科室
     * @return
     */
    public String buildCiaApplyChargeExecuteOrgCode(ServiceClinicPriceTo serviceClinicPriceTo, String visitCode, String executeOrgCode) {
        CisFeeExecEnum cisFeeExecEnum = serviceClinicPriceTo.getCisFeeExecEnum();
        if(cisFeeExecEnum != null) {
            if (cisFeeExecEnum.equals(CisFeeExecEnum.IN_NURSE_CODE)) {
                PatIpdInpatientTo patient = patIpdInpatientService.getPatBar(visitCode);
                executeOrgCode = patient.getDeptNurseCode();
            } else if (cisFeeExecEnum.equals(CisFeeExecEnum.CREATE_CODE)) {
                executeOrgCode = HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode();
            }
        }

        return executeOrgCode;
    }

}
