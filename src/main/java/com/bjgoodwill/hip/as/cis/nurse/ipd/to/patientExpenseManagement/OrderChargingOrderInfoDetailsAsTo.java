package com.bjgoodwill.hip.as.cis.nurse.ipd.to.patientExpenseManagement;

import com.bjgoodwill.hip.business.util.cis.common.enums.CisStatusEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.OrderTypeEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

/**
 * File: OrderChargingOrderInfoDetailsAsTo
 * Author: zhangyunchuan
 * Date: 2025/2/26
 * Description:
 */
@Schema(description = "医嘱计费-医嘱计费信息")
public class OrderChargingOrderInfoDetailsAsTo {

    @Schema(description = "医嘱id")
    private String orderId;

    @Schema(description = "医嘱类型")
    private SystemTypeEnum orderClass;

    @Schema(description = "医嘱类型编码")
    private String orderClassCode;

    @Schema(description = "医嘱类型名称")
    private String orderClassName;

    @Schema(description = "医嘱类别 长期:1 临时:2")
    private OrderTypeEnum orderType;

    @Schema(description = "医嘱类别编码")
    private String orderTypeCode;

    @Schema(description = "医嘱类别名称")
    private String orderTypeName;

    @Schema(description = "医嘱内容")
    private String orderContent;

    @Schema(description = "频次")
    private String frequency;

    @Schema(description = "用法")
    private String usage;

    @Schema(description = "医嘱开始时间")
    private LocalDateTime effectiveLowDate;

    @Schema(description = "创建的人员")
    private String createdStaff;

    @Schema(description = "创建的人员姓名")
    private String createdStaffName;

    @Schema(description = "医嘱截止时间")
    private LocalDateTime effectiveHighDate;

    @Schema(description = "停止人")
    private String stopStaff;

    @Schema(description = "停止人姓名")
    private String stopStaffName;

    @Schema(description = "执行科室编码")
    private String executeOrgCode;

    @Schema(description = "执行科室名称")
    private String executeOrgName;

    @Schema(description = "状态")
    private CisStatusEnum statusCode;

    @Schema(description = "状态编码")
    private String statusCodeCode;

    @Schema(description = "状态名称")
    private String statusCodeName;

    @Schema(description = "开方科室")
    private String createOrgCode;

    @Schema(description = "开方科室名称")
    private String createOrgName;

    @Schema(description = "预计执行次数")
    private int executeInTimes;

    @Schema(description = "服务项目编码")
    private String serviceItemCode;

    @Schema(description = "服务项目名称")
    private String serviceItemName;

    public int getExecuteInTimes() {
        return executeInTimes;
    }

    public void setExecuteInTimes(int executeInTimes) {
        this.executeInTimes = executeInTimes;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public SystemTypeEnum getOrderClass() {
        return orderClass;
    }

    public void setOrderClass(SystemTypeEnum orderClass) {
        this.orderClass = orderClass;
    }

    public OrderTypeEnum getOrderType() {
        return orderType;
    }

    public void setOrderType(OrderTypeEnum orderType) {
        this.orderType = orderType;
    }

    public String getOrderContent() {
        return orderContent;
    }

    public void setOrderContent(String orderContent) {
        this.orderContent = orderContent;
    }

    public String getFrequency() {
        return frequency;
    }

    public void setFrequency(String frequency) {
        this.frequency = frequency;
    }

    public String getUsage() {
        return usage;
    }

    public void setUsage(String usage) {
        this.usage = usage;
    }

    public LocalDateTime getEffectiveLowDate() {
        return effectiveLowDate;
    }

    public void setEffectiveLowDate(LocalDateTime effectiveLowDate) {
        this.effectiveLowDate = effectiveLowDate;
    }

    public String getCreatedStaff() {
        return createdStaff;
    }

    public void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    public LocalDateTime getEffectiveHighDate() {
        return effectiveHighDate;
    }

    public void setEffectiveHighDate(LocalDateTime effectiveHighDate) {
        this.effectiveHighDate = effectiveHighDate;
    }

    public String getStopStaff() {
        return stopStaff;
    }

    public void setStopStaff(String stopStaff) {
        this.stopStaff = stopStaff;
    }

    public String getStopStaffName() {
        return stopStaffName;
    }

    public void setStopStaffName(String stopStaffName) {
        this.stopStaffName = stopStaffName;
    }

    public String getCreatedStaffName() {
        return createdStaffName;
    }

    public void setCreatedStaffName(String createdStaffName) {
        this.createdStaffName = createdStaffName;
    }

    public String getExecuteOrgCode() {
        return executeOrgCode;
    }

    public void setExecuteOrgCode(String executeOrgCode) {
        this.executeOrgCode = executeOrgCode;
    }

    public String getExecuteOrgName() {
        return executeOrgName;
    }

    public void setExecuteOrgName(String executeOrgName) {
        this.executeOrgName = executeOrgName;
    }

    public CisStatusEnum getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(CisStatusEnum statusCode) {
        this.statusCode = statusCode;
    }

    public String getCreateOrgCode() {
        return createOrgCode;
    }

    public void setCreateOrgCode(String createOrgCode) {
        this.createOrgCode = createOrgCode;
    }

    public String getCreateOrgName() {
        return createOrgName;
    }

    public void setCreateOrgName(String createOrgName) {
        this.createOrgName = createOrgName;
    }

    public String getOrderClassCode() {
        return orderClassCode;
    }

    public void setOrderClassCode(String orderClassCode) {
        this.orderClassCode = orderClassCode;
    }

    public String getOrderClassName() {
        return orderClassName;
    }

    public void setOrderClassName(String orderClassName) {
        this.orderClassName = orderClassName;
    }

    public String getOrderTypeCode() {
        return orderTypeCode;
    }

    public void setOrderTypeCode(String orderTypeCode) {
        this.orderTypeCode = orderTypeCode;
    }

    public String getOrderTypeName() {
        return orderTypeName;
    }

    public void setOrderTypeName(String orderTypeName) {
        this.orderTypeName = orderTypeName;
    }

    public String getStatusCodeCode() {
        return statusCodeCode;
    }

    public void setStatusCodeCode(String statusCodeCode) {
        this.statusCodeCode = statusCodeCode;
    }

    public String getStatusCodeName() {
        return statusCodeName;
    }

    public void setStatusCodeName(String statusCodeName) {
        this.statusCodeName = statusCodeName;
    }

    public String getServiceItemName() {
        return serviceItemName;
    }

    public void setServiceItemName(String serviceItemName) {
        this.serviceItemName = serviceItemName;
    }

    public String getServiceItemCode() {
        return serviceItemCode;
    }

    public void setServiceItemCode(String serviceItemCode) {
        this.serviceItemCode = serviceItemCode;
    }
}
