package com.bjgoodwill.hip.as.cis.doc.ipd.service.assembler;

import com.bjgoodwill.hip.as.cis.doc.ipd.enums.CisDocIpdBusinessErrorEnum;
import com.bjgoodwill.hip.as.cis.doc.ipd.to.*;
import com.bjgoodwill.hip.as.cis.doc.ipd.to.apply.CisApplyChargeAsNto;
import com.bjgoodwill.hip.as.cis.doc.ipd.to.dgimg.*;
import com.bjgoodwill.hip.as.cis.doc.ipd.to.operation.ApplyDiagnosisAsNto;
import com.bjgoodwill.hip.as.cis.doc.ipd.util.IpdOrderExecuteOrgUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.OrderTypeEnum;
import com.bjgoodwill.hip.business.util.cis.common.enums.VisitTypeEnum;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.common.util.HIPBeanUtil;
import com.bjgoodwill.hip.common.util.HIPIDUtil;
import com.bjgoodwill.hip.ds.cis.apply.apply.to.CisBaseApplyTo;
import com.bjgoodwill.hip.ds.cis.apply.charge.enmus.CisChargeTypeEnum;
import com.bjgoodwill.hip.ds.cis.apply.charge.to.CisApplyChargeEto;
import com.bjgoodwill.hip.ds.cis.apply.charge.to.CisApplyChargeNto;
import com.bjgoodwill.hip.ds.cis.apply.charge.to.CisApplyChargeTo;
import com.bjgoodwill.hip.ds.cis.apply.dgimg.to.*;
import com.bjgoodwill.hip.ds.cis.apply.diag.to.ApplyDiagnosisNto;
import com.bjgoodwill.hip.ds.cis.medicineitem.price.to.ServiceClinicPriceTo;
import com.bjgoodwill.hip.ds.cis.medicineitem.serviceItem.service.ServiceClinicItemService;
import com.bjgoodwill.hip.ds.cis.medicineitem.serviceItem.to.ServiceClinicItemTo;
import com.bjgoodwill.hip.ds.org.api.service.WorkGroupService;
import com.bjgoodwill.hip.security.util.HIPCurrentOrgUtils;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> liangyuqing
 * @description :
 * @createDate : 2024/12/9 9:30
 */
@Service
public class DgimgApplyIssuedAsAssembler {

    @Resource
    private IpdOrderExecuteOrgUtil ipdOrderExecuteOrgUtil;

    @Resource(name = "com.bjgoodwill.hip.ds.cis.medicineitem.serviceItem.service.ServiceClinicItemTangibleService")
    private ServiceClinicItemService serviceClinicItemService;

    @Resource
    private WorkGroupService workGroupService;

    /**
     * 检查医嘱开立
     * 构造医嘱申请单信息
     * @param cisIpdDocOrderAsNto
     * @return
     */
    public CisDgimgApplyNto buildCisDgimgApplyNto(CisIpdDocOrderAsNto cisIpdDocOrderAsNto) {
        CisDgimgApplyNto cisDgimgApplyNto = new CisDgimgApplyNto();

        DgimgIpdOrderAsNto dgimgIpdOrderAsNto = ((DgimgIpdOrderAsNto) cisIpdDocOrderAsNto);
        cisDgimgApplyNto.setPrecautions(dgimgIpdOrderAsNto.getPrecautions());
        cisDgimgApplyNto.setMedrecordAndExamabstract("无");
        cisDgimgApplyNto.setPhysiqueAndExam(dgimgIpdOrderAsNto.getPhysiqueAndExam());
        cisDgimgApplyNto.setDgimgClass(dgimgIpdOrderAsNto.getDgimgClass());
        cisDgimgApplyNto.setDgimgSubClass(dgimgIpdOrderAsNto.getDgimgSubClass());
        cisDgimgApplyNto.setAuxiliaryInspection(dgimgIpdOrderAsNto.getAuxiliaryInspection());
        cisDgimgApplyNto.setCheckPurpose(dgimgIpdOrderAsNto.getCheckPurpose());
        cisDgimgApplyNto.setApplyBookId(dgimgIpdOrderAsNto.getApplyBookId());
        cisDgimgApplyNto.setPreviousPathologicalExamin(dgimgIpdOrderAsNto.getPreviousPathologicalExamin());
        cisDgimgApplyNto.setDeviceType(dgimgIpdOrderAsNto.getDeviceType());
        cisDgimgApplyNto.setAllergicHistoryFlag(dgimgIpdOrderAsNto.getAllergicHistoryFlag());
        cisDgimgApplyNto.setOccupationalDiseasesFlag(dgimgIpdOrderAsNto.getOccupationalDiseasesFlag());
        cisDgimgApplyNto.setClinicalHistory(dgimgIpdOrderAsNto.getClinicalHistory());

        cisDgimgApplyNto.setId(cisIpdDocOrderAsNto.getApplyId());
        cisDgimgApplyNto.setPatMiCode(cisIpdDocOrderAsNto.getPatMiCode());
        cisDgimgApplyNto.setVisitCode(cisIpdDocOrderAsNto.getVisitCode());
        cisDgimgApplyNto.setIsCanPriorityFlag(cisIpdDocOrderAsNto.getIsCanPriorityFlag());
        cisDgimgApplyNto.setVisitType(VisitTypeEnum.IPD);
        cisDgimgApplyNto.setDeptNurseCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
        cisDgimgApplyNto.setDeptNurseName(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupName());
        cisDgimgApplyNto.setOrderID(cisIpdDocOrderAsNto.getId());
        cisDgimgApplyNto.setCreateOrgCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
        cisDgimgApplyNto.setCreateOrgName(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupName());
        cisDgimgApplyNto.setOrderType(OrderTypeEnum.TEMPORARY_ORDER);
        cisDgimgApplyNto.setFrequency("ST");
        cisDgimgApplyNto.setFrequencyName("立即");
        cisDgimgApplyNto.setNum(Double.valueOf(1));
        cisDgimgApplyNto.setExecutorOrgCode(cisIpdDocOrderAsNto.getExecuteOrgCode());
        cisDgimgApplyNto.setExecutorOrgName(cisIpdDocOrderAsNto.getExecuteOrgName());
        cisDgimgApplyNto.setVisitOrgCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
        cisDgimgApplyNto.setVisitOrgName(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupName());
        cisDgimgApplyNto.setReMark(cisIpdDocOrderAsNto.getReMark());

        AtomicReference<String> serviceItemCode = new AtomicReference<>("");
        AtomicReference<String> serviceItemName = new AtomicReference<>("");
        dgimgIpdOrderAsNto.getDgimgIpdOrderDetailAsNtoList().forEach(o -> {
            serviceItemCode.set(serviceItemCode.get() + "," + o.getServiceItemCode());
            serviceItemName.set(serviceItemName.get() + "," + o.getServiceItemName());
        });
        cisDgimgApplyNto.setServiceItemCode(serviceItemCode.get().substring(1, serviceItemCode.get().length()));
        cisDgimgApplyNto.setServiceItemName(serviceItemName.get().substring(1, serviceItemName.get().length()));

        if (CollectionUtils.isNotEmpty(dgimgIpdOrderAsNto.getApplyDiagnosisAsNtos())) {
            List<ApplyDiagnosisNto> applyDiagnosisNtos = this.buildDiagnosisNto(dgimgIpdOrderAsNto.getApplyDiagnosisAsNtos(), cisDgimgApplyNto.getId());
            cisDgimgApplyNto.setApplyDiagnosisNtos(applyDiagnosisNtos);
        }


        return cisDgimgApplyNto;
    }

    /**
     * 检查医嘱开立
     * 构造检查申请单明细
     * @param cisIpdDocOrderAsNto
     * @return
     */
    public List<CisDgimgApplyDetailNto> buildCisDgimgApplyDetailNtoList(CisIpdDocOrderAsNto cisIpdDocOrderAsNto) {
        List<CisDgimgApplyDetailNto> cisDgimgApplyDetailNtoList = new ArrayList<>();

        DgimgIpdOrderAsNto dgimgIpdOrderAsNto = ((DgimgIpdOrderAsNto) cisIpdDocOrderAsNto);
        for (DgimgIpdOrderDetailAsNto nto : dgimgIpdOrderAsNto.getDgimgIpdOrderDetailAsNtoList()) {
            CisDgimgApplyDetailNto cisDgimgApplyDetailNto = new CisDgimgApplyDetailNto();
            cisDgimgApplyDetailNto.setCisDgimgApplyId(cisIpdDocOrderAsNto.getApplyId());
            cisDgimgApplyDetailNto.setDgimgName(nto.getServiceItemName());
            cisDgimgApplyDetailNto.setDgimgCode(nto.getServiceItemCode());
            cisDgimgApplyDetailNto.setHumanSystem(nto.getHumanSystem());
            cisDgimgApplyDetailNto.setHumanOrgans(nto.getHumanOrgans());
            cisDgimgApplyDetailNto.setMethod(nto.getMethod());
            cisDgimgApplyDetailNto.setRange(nto.getRange());
            cisDgimgApplyDetailNto.setDirection(nto.getDirection());
            cisDgimgApplyDetailNto.setLayer(nto.getLayer());
            cisDgimgApplyDetailNto.setOperation(nto.getOperation());

            cisDgimgApplyDetailNto.setId(nto.getApplyDetailId());
            cisDgimgApplyDetailNto.setVisitCode(cisIpdDocOrderAsNto.getVisitCode());
            cisDgimgApplyDetailNto.setUnilateralFlag(nto.getUnilateralFlag());
            cisDgimgApplyDetailNto.setExtTypeCode(nto.getExtCode());


            cisDgimgApplyDetailNtoList.add(cisDgimgApplyDetailNto);
        }
        return cisDgimgApplyDetailNtoList;
    }


    /**
     * 构造检查医嘱医嘱charge列表
     * @param cisIpdDocOrderAsNto
     * @return
     */
    public List<CisApplyChargeNto> buildDgimgApplyChargeNtoList(CisIpdDocOrderAsNto cisIpdDocOrderAsNto) {
        List<CisApplyChargeNto> cisApplyChargeNtoList = new ArrayList<>();
        DgimgIpdOrderAsNto dgimgIpdOrderAsNto = ((DgimgIpdOrderAsNto) cisIpdDocOrderAsNto);
        for (DgimgIpdOrderDetailAsNto detailAsNto : dgimgIpdOrderAsNto.getDgimgIpdOrderDetailAsNtoList()) {
            ServiceClinicItemTo serviceClinicItemTo = serviceClinicItemService.getServiceClinicItemByCode(detailAsNto.getServiceItemCode());
            BusinessAssert.notNull(serviceClinicItemTo, CisDocIpdBusinessErrorEnum.CIS_DOC_IPD_0001, "医嘱项目");
            Map<String, ServiceClinicPriceTo> priceToMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(serviceClinicItemTo.getServiceClinicPrices())) {
                priceToMap = serviceClinicItemTo.getServiceClinicPrices().stream().collect(Collectors.toMap(ServiceClinicPriceTo::getPriceItemCode, Function.identity(), (v1, v2) -> v1));
            }

            for (CisApplyChargeAsNto chargeAsNto : detailAsNto.getCisApplyChargeAsNtoList()) {
                CisApplyChargeNto chargeNto = HIPBeanUtil.copy(chargeAsNto, CisApplyChargeNto.class);
                chargeNto.setOrderId(cisIpdDocOrderAsNto.getId());
                chargeNto.setVisitCode(cisIpdDocOrderAsNto.getVisitCode());
                chargeNto.setExecuteOrgCode(cisIpdDocOrderAsNto.getExecuteOrgCode());
                chargeNto.setExecuteOrgName(cisIpdDocOrderAsNto.getExecuteOrgName());
                chargeNto.setCisBaseApplyId(cisIpdDocOrderAsNto.getApplyId());
                chargeNto.setDetailId(detailAsNto.getApplyDetailId());
                chargeNto.setChageAmount(new BigDecimal(chargeNto.getNum()).multiply(chargeNto.getPrice()));
                chargeNto.setChargeType(CisChargeTypeEnum.DOCT);
                // 赋值费用类别、执行科室
                if (priceToMap.size() > 0 && priceToMap.containsKey(chargeNto.getPriceItemCode())) {
                    ServiceClinicPriceTo priceTo = priceToMap.get(chargeNto.getPriceItemCode());
                    chargeNto.setSystemItemClass(priceTo.getSystemItemClass());
                    String executeOrgCode = ipdOrderExecuteOrgUtil.buildCiaApplyChargeExecuteOrgCode(priceTo, cisIpdDocOrderAsNto.getVisitCode(), cisIpdDocOrderAsNto.getExecuteOrgCode());
                    chargeNto.setExecuteOrgCode(executeOrgCode);
                    chargeNto.setExecuteOrgName(workGroupService.getWorkGroup(executeOrgCode).getName());
                }

                cisApplyChargeNtoList.add(chargeNto);
            }
        }

        return cisApplyChargeNtoList;
    }

    /**
     * 构造检查医嘱临床诊断
     * @param applyDiagnosisAsNtoList
     * @param applyId
     * @return
     */
    public List<ApplyDiagnosisNto> buildDiagnosisNto(List<ApplyDiagnosisAsNto> applyDiagnosisAsNtoList, String applyId) {
        List<ApplyDiagnosisNto> applyDiagnosisNtoList = HIPBeanUtil.copy(applyDiagnosisAsNtoList, ApplyDiagnosisNto.class);
        applyDiagnosisNtoList.forEach(o -> {
            o.setCisBaseApplyId(applyId);
        });
        return applyDiagnosisNtoList;
    }

    /**
     * 医嘱修改
     * 构造检查医嘱修改Eto
     * @param cisIpdDocOrderAsEto
     * @return
     */
    public CisDgimgApplyEto buildCisDgimgApplyEto(CisIpdDocOrderAsEto cisIpdDocOrderAsEto) {
        CisDgimgApplyEto cisDgimgApplyEto = new CisDgimgApplyEto();

        DgimgIpdOrderAsEto dgimgIpdOrderAsEto = ((DgimgIpdOrderAsEto) cisIpdDocOrderAsEto);
        cisDgimgApplyEto.setPrecautions(dgimgIpdOrderAsEto.getPrecautions());
        cisDgimgApplyEto.setMedrecordAndExamabstract("无");
        cisDgimgApplyEto.setDgimgClass(dgimgIpdOrderAsEto.getDgimgClass());
        cisDgimgApplyEto.setDgimgSubClass(dgimgIpdOrderAsEto.getDgimgSubClass());
        cisDgimgApplyEto.setAuxiliaryInspection(dgimgIpdOrderAsEto.getAuxiliaryInspection());
        cisDgimgApplyEto.setCheckPurpose(dgimgIpdOrderAsEto.getCheckPurpose());
        cisDgimgApplyEto.setAllergicHistoryFlag(dgimgIpdOrderAsEto.getAllergicHistoryFlag());
        cisDgimgApplyEto.setOccupationalDiseasesFlag(dgimgIpdOrderAsEto.getOccupationalDiseasesFlag());
        cisDgimgApplyEto.setClinicalHistory(dgimgIpdOrderAsEto.getClinicalHistory());
        cisDgimgApplyEto.setContagiousDiseaseHistoryFlag(dgimgIpdOrderAsEto.getContagiousDiseaseHistoryFlag());
        cisDgimgApplyEto.setPreviousPathologicalExamin(dgimgIpdOrderAsEto.getPreviousPathologicalExamin());

        cisDgimgApplyEto.setReMark(dgimgIpdOrderAsEto.getReMark());
        cisDgimgApplyEto.setIsCanPriorityFlag(dgimgIpdOrderAsEto.getIsCanPriorityFlag());
        cisDgimgApplyEto.setVersion(dgimgIpdOrderAsEto.getVersion());
        cisDgimgApplyEto.setFrequency(dgimgIpdOrderAsEto.getFrequency());
        cisDgimgApplyEto.setFrequencyName(dgimgIpdOrderAsEto.getFrequencyName());
        cisDgimgApplyEto.setStatusCode(cisIpdDocOrderAsEto.getApplyStatusCode());
        cisDgimgApplyEto.setNum(1d);
        if (CollectionUtils.isNotEmpty(dgimgIpdOrderAsEto.getApplyDiagnosisAsNtos())) {
            cisDgimgApplyEto.setApplyDiagnosisNtos(this.buildDiagnosisNto(dgimgIpdOrderAsEto.getApplyDiagnosisAsNtos(), cisIpdDocOrderAsEto.getApplyId()));
        }


        // 费用新增信息
        List<CisApplyChargeNto> cisApplyChargeNtoList = new ArrayList<>();
        // 费用修改信息
        List<CisApplyChargeEto> cisApplyChargeEtoList = new ArrayList<>();

        // 新增的明细信息
        if (CollectionUtils.isNotEmpty(dgimgIpdOrderAsEto.getDgimgIpdOrderDetailAsNtoList())) {
            List<CisDgimgApplyDetailNto> cisDgimgApplyDetailNtoList = new ArrayList<>();
            for (DgimgIpdOrderDetailAsNto nto : dgimgIpdOrderAsEto.getDgimgIpdOrderDetailAsNtoList()) {
                // 查询服务项目
                ServiceClinicItemTo serviceClinicItemTo = serviceClinicItemService.getServiceClinicItemByCode(nto.getServiceItemCode());
                BusinessAssert.notNull(serviceClinicItemTo, CisDocIpdBusinessErrorEnum.CIS_DOC_IPD_0001, "医嘱项目");
                Map<String, ServiceClinicPriceTo> priceToMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(serviceClinicItemTo.getServiceClinicPrices())) {
                    priceToMap = serviceClinicItemTo.getServiceClinicPrices().stream().collect(Collectors.toMap(ServiceClinicPriceTo::getPriceItemCode, Function.identity(), (v1, v2) -> v1));
                }

                CisDgimgApplyDetailNto cisDgimgApplyDetailNto = new CisDgimgApplyDetailNto();

                cisDgimgApplyDetailNto.setCisDgimgApplyId(cisIpdDocOrderAsEto.getApplyId());
                cisDgimgApplyDetailNto.setDgimgName(nto.getServiceItemName());
                cisDgimgApplyDetailNto.setDgimgCode(nto.getServiceItemCode());
                cisDgimgApplyDetailNto.setHumanSystem(nto.getHumanSystem());
                cisDgimgApplyDetailNto.setHumanOrgans(nto.getHumanOrgans());
                cisDgimgApplyDetailNto.setMethod(nto.getMethod());
                cisDgimgApplyDetailNto.setRange(nto.getRange());
                cisDgimgApplyDetailNto.setDirection(nto.getDirection());
                cisDgimgApplyDetailNto.setLayer(nto.getLayer());
                cisDgimgApplyDetailNto.setOperation(nto.getOperation());
                cisDgimgApplyDetailNto.setRequirementPurpose(nto.getRequirementPurpose());
                cisDgimgApplyDetailNto.setId(nto.getApplyDetailId());
                cisDgimgApplyDetailNto.setVisitCode(dgimgIpdOrderAsEto.getVisitCode());
                cisDgimgApplyDetailNto.setUnilateralFlag(nto.getUnilateralFlag());

                for (CisApplyChargeAsNto chargeAsNto : nto.getCisApplyChargeAsNtoList()) {
                    CisApplyChargeNto chargeNto = HIPBeanUtil.copy(chargeAsNto, CisApplyChargeNto.class);
                    chargeNto.setDetailId(cisDgimgApplyDetailNto.getId());

                    chargeNto.setOrderId(dgimgIpdOrderAsEto.getId());
                    chargeNto.setVisitCode(dgimgIpdOrderAsEto.getVisitCode());
                    chargeNto.setExecuteOrgCode(dgimgIpdOrderAsEto.getExecuteOrgCode());
                    chargeNto.setExecuteOrgName(dgimgIpdOrderAsEto.getExecuteOrgName());
                    chargeNto.setCisBaseApplyId(dgimgIpdOrderAsEto.getApplyId());
                    chargeNto.setDetailId(nto.getApplyDetailId());
                    chargeNto.setChageAmount(new BigDecimal(chargeNto.getNum()).multiply(chargeNto.getPrice()));
                    chargeNto.setChargeType(CisChargeTypeEnum.DOCT);

                    // 赋值费用类别、执行科室
                    if (priceToMap.size() > 0 && priceToMap.containsKey(chargeNto.getPriceItemCode())) {
                        ServiceClinicPriceTo priceTo = priceToMap.get(chargeNto.getPriceItemCode());
                        chargeNto.setSystemItemClass(priceTo.getSystemItemClass());
                        String executeOrgCode = ipdOrderExecuteOrgUtil.buildCiaApplyChargeExecuteOrgCode(priceTo, cisIpdDocOrderAsEto.getVisitCode(), cisIpdDocOrderAsEto.getExecuteOrgCode());
                        chargeNto.setExecuteOrgCode(executeOrgCode);
                        chargeNto.setExecuteOrgName(workGroupService.getWorkGroup(executeOrgCode).getName());
                        chargeNto.setSystemItemClass(priceTo.getSystemItemClass());
                    }

                    cisApplyChargeNtoList.add(chargeNto);
                }

                cisDgimgApplyDetailNtoList.add(cisDgimgApplyDetailNto);
            }

            cisDgimgApplyEto.setCisDgimgApplyDetailNtos(cisDgimgApplyDetailNtoList);
            cisDgimgApplyEto.setDetailNtos(cisDgimgApplyDetailNtoList);
            cisDgimgApplyEto.setCisApplyChargeNtos(cisApplyChargeNtoList);
        }

        // 修改的明细信息
        if (CollectionUtils.isNotEmpty(dgimgIpdOrderAsEto.getDgimgIpdOrderDetailAsEtoList())) {
            List<CisDgimgApplyDetailEto> cisDgimgApplyDetailEtoList = new ArrayList<>();
            for (DgimgIpdOrderDetailAsEto eto : dgimgIpdOrderAsEto.getDgimgIpdOrderDetailAsEtoList()) {
                // 查询服务项目
                ServiceClinicItemTo serviceClinicItemTo = serviceClinicItemService.getServiceClinicItemByCode(eto.getServiceItemCode());
                BusinessAssert.notNull(serviceClinicItemTo, CisDocIpdBusinessErrorEnum.CIS_DOC_IPD_0001, "医嘱项目");
                Map<String, ServiceClinicPriceTo> priceToMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(serviceClinicItemTo.getServiceClinicPrices())) {
                    priceToMap = serviceClinicItemTo.getServiceClinicPrices().stream().collect(Collectors.toMap(ServiceClinicPriceTo::getPriceItemCode, Function.identity(), (v1, v2) -> v1));
                }

                CisDgimgApplyDetailEto cisDgimgApplyDetailEto = new CisDgimgApplyDetailEto();

                cisDgimgApplyDetailEto.setId(eto.getApplyDetailId());
                cisDgimgApplyDetailEto.setDgimgCode(eto.getServiceItemCode());
                cisDgimgApplyDetailEto.setDgimgName(eto.getServiceItemName());
                cisDgimgApplyDetailEto.setHumanSystem(eto.getHumanSystem());
                cisDgimgApplyDetailEto.setHumanOrgans(eto.getHumanOrgans());
                cisDgimgApplyDetailEto.setMethod(eto.getMethod());
                cisDgimgApplyDetailEto.setRange(eto.getRange());
                cisDgimgApplyDetailEto.setDirection(eto.getDirection());
                cisDgimgApplyDetailEto.setLayer(eto.getLayer());
                cisDgimgApplyDetailEto.setOperation(eto.getOperation());
                cisDgimgApplyDetailEto.setRequirementPurpose(eto.getRequirementPurpose());
                cisDgimgApplyDetailEto.setUnilateralFlag(eto.getUnilateralFlag());

                for (CisApplyChargeAsEto cisApplyChargeAsEto : eto.getCisApplyChargeAsEtoList()) {
                    CisApplyChargeEto cisApplyChargeEto = HIPBeanUtil.copy(cisApplyChargeAsEto, CisApplyChargeEto.class);

                    cisApplyChargeEto.setPriceItemCode(cisApplyChargeAsEto.getPriceItemCode());
                    cisApplyChargeEto.setPriceItemName(cisApplyChargeAsEto.getPriceItemName());
                    cisApplyChargeEto.setPackageSpec(cisApplyChargeAsEto.getPackageSpec());
                    cisApplyChargeEto.setExecuteOrgCode(dgimgIpdOrderAsEto.getExecuteOrgCode());
                    cisApplyChargeEto.setExecuteOrgName(dgimgIpdOrderAsEto.getExecuteOrgName());
                    cisApplyChargeEto.setPrice(cisApplyChargeAsEto.getPrice());
                    cisApplyChargeEto.setUnit(cisApplyChargeAsEto.getUnit());
                    cisApplyChargeEto.setNum(cisApplyChargeAsEto.getNum());
                    cisApplyChargeEto.setChageAmount(new BigDecimal(cisApplyChargeEto.getNum()).multiply(cisApplyChargeEto.getPrice()));
                    cisApplyChargeEto.setVersion(cisApplyChargeAsEto.getVersion());
                    if (priceToMap.size() > 0 && priceToMap.containsKey(cisApplyChargeAsEto.getPriceItemCode())) {
                        cisApplyChargeEto.setSystemItemClass(priceToMap.get(cisApplyChargeAsEto.getPriceItemCode()).getSystemItemClass());
                    }

                    cisApplyChargeEtoList.add(cisApplyChargeEto);
                }

                cisDgimgApplyDetailEtoList.add(cisDgimgApplyDetailEto);
            }

            cisDgimgApplyEto.setCisDgimgApplyDetailEtos(cisDgimgApplyDetailEtoList);
            cisDgimgApplyEto.setDetailEtos(cisDgimgApplyDetailEtoList);
            cisDgimgApplyEto.setCisApplyChargeEtos(cisApplyChargeEtoList);
        }

        return cisDgimgApplyEto;
    }

    public void buildDgimgOrderAsToByApply(DgimgIpdOrderAsTo orderAsTo, CisDgimgApplyTo cisDgimgApplyTo) {
        orderAsTo.setPrecautions(cisDgimgApplyTo.getPrecautions());
        orderAsTo.setMedrecordAndExamabstract(cisDgimgApplyTo.getMedrecordAndExamabstract());
        orderAsTo.setPhysiqueAndExam(cisDgimgApplyTo.getPhysiqueAndExam());
        orderAsTo.setDgimgClass(cisDgimgApplyTo.getDgimgClass());
        orderAsTo.setDgimgSubClass(cisDgimgApplyTo.getDgimgSubClass());
        orderAsTo.setAuxiliaryInspection(cisDgimgApplyTo.getAuxiliaryInspection());
        orderAsTo.setCheckPurpose(cisDgimgApplyTo.getCheckPurpose());
        orderAsTo.setApplyBookId(cisDgimgApplyTo.getApplyBookId());
        orderAsTo.setPreviousPathologicalExamin(cisDgimgApplyTo.getPreviousPathologicalExamin());
        orderAsTo.setDeviceType(cisDgimgApplyTo.getDeviceType());
        orderAsTo.setAllergicHistoryFlag(cisDgimgApplyTo.getAllergicHistoryFlag());
        orderAsTo.setOccupationalDiseasesFlag(cisDgimgApplyTo.getOccupationalDiseasesFlag());
        orderAsTo.setContagiousDiseaseHistoryFlag(cisDgimgApplyTo.getContagiousDiseaseHistoryFlag());
        orderAsTo.setClinicalHistory(cisDgimgApplyTo.getClinicalHistory());
        orderAsTo.setReMark(cisDgimgApplyTo.getReMark());

        orderAsTo.setApplyId(cisDgimgApplyTo.getId());
        orderAsTo.setIsCanPriorityFlag(cisDgimgApplyTo.getIsCanPriorityFlag());
        orderAsTo.setVersion(cisDgimgApplyTo.getVersion());
    }

    /**
     * 复制粘贴用
     * @param cisIpdDocOrderAsNto
     * @param list
     * @return
     */
    public CisIpdDocOrderAsNto buildCisIpdDocOrderAsNto(CisIpdDocOrderAsNto cisIpdDocOrderAsNto, List<CisBaseApplyTo> list) {
        List<CisDgimgApplyTo> dgimgApplyAsTo = list.stream().filter(CisDgimgApplyTo.class::isInstance)
                .map(CisDgimgApplyTo.class::cast).toList();

        DgimgIpdOrderAsNto dgimgIpdOrderAsNto = HIPBeanUtil.copy(cisIpdDocOrderAsNto, DgimgIpdOrderAsNto.class);

        CisDgimgApplyTo cisDgimgApplyTo = dgimgApplyAsTo.get(0);
        dgimgIpdOrderAsNto.setServiceItemCode(cisDgimgApplyTo.getServiceItemCode());
        dgimgIpdOrderAsNto.setServiceItemName(cisDgimgApplyTo.getServiceItemName());
        dgimgIpdOrderAsNto.setPrecautions(cisDgimgApplyTo.getPrecautions());
        dgimgIpdOrderAsNto.setMedrecordAndExamabstract(cisDgimgApplyTo.getMedrecordAndExamabstract());
        dgimgIpdOrderAsNto.setPhysiqueAndExam(cisDgimgApplyTo.getPhysiqueAndExam());
        dgimgIpdOrderAsNto.setDgimgClass(cisDgimgApplyTo.getDgimgClass());
        dgimgIpdOrderAsNto.setDgimgSubClass(cisDgimgApplyTo.getDgimgSubClass());
        dgimgIpdOrderAsNto.setAuxiliaryInspection(cisDgimgApplyTo.getAuxiliaryInspection());
        dgimgIpdOrderAsNto.setCheckPurpose(cisDgimgApplyTo.getCheckPurpose());
        dgimgIpdOrderAsNto.setPreviousPathologicalExamin(cisDgimgApplyTo.getPreviousPathologicalExamin());
        dgimgIpdOrderAsNto.setDeviceType(cisDgimgApplyTo.getDeviceType());
        dgimgIpdOrderAsNto.setAllergicHistoryFlag(cisDgimgApplyTo.getAllergicHistoryFlag());
        dgimgIpdOrderAsNto.setOccupationalDiseasesFlag(cisDgimgApplyTo.getOccupationalDiseasesFlag());
        dgimgIpdOrderAsNto.setClinicalHistory(cisDgimgApplyTo.getClinicalHistory());
        dgimgIpdOrderAsNto.setContagiousDiseaseHistoryFlag(cisDgimgApplyTo.getContagiousDiseaseHistoryFlag());
        dgimgIpdOrderAsNto.setIsCanPriorityFlag(cisDgimgApplyTo.getIsCanPriorityFlag());


        List<CisApplyChargeTo> cisApplyCharges = dgimgApplyAsTo.get(0).getCisApplyCharges();
        List<CisDgimgApplyDetailTo> cisDgimgApplyDetailTos = cisDgimgApplyTo.getCisDgimgApplyDetails();
        List<DgimgIpdOrderDetailAsNto> cisIpdDocOrderDetailsAsNtoList = new ArrayList<>();
        for (CisDgimgApplyDetailTo cisDgimgApplyDetailTo : cisDgimgApplyDetailTos) {
            DgimgIpdOrderDetailAsNto detailAsNto = new DgimgIpdOrderDetailAsNto();
            String detaiId = "DT_" + HIPIDUtil.getNextIdString();
            detailAsNto.setId(detaiId);
            detailAsNto.setApplyDetailId(detaiId);
            detailAsNto.setServiceItemCode(cisDgimgApplyDetailTo.getDgimgCode());
            detailAsNto.setServiceItemName(cisDgimgApplyDetailTo.getDgimgName());
            detailAsNto.setHumanSystem(cisDgimgApplyDetailTo.getHumanSystem());
            detailAsNto.setHumanOrgans(cisDgimgApplyDetailTo.getHumanOrgans());
            detailAsNto.setMethod(cisDgimgApplyDetailTo.getMethod());
            detailAsNto.setRange(cisDgimgApplyDetailTo.getRange());
            detailAsNto.setDirection(cisDgimgApplyDetailTo.getDirection());
            detailAsNto.setLayer(cisDgimgApplyDetailTo.getLayer());
            detailAsNto.setOperation(cisDgimgApplyDetailTo.getOperation());
            detailAsNto.setRequirementPurpose(cisDgimgApplyDetailTo.getRequirementPurpose());
            detailAsNto.setUnilateralFlag(cisDgimgApplyDetailTo.getUnilateralFlag());

            List<CisApplyChargeTo> currentCharges = cisApplyCharges.stream().filter(charge -> charge.getDetailId().equals(cisDgimgApplyDetailTo.getId())).toList();

            List<CisApplyChargeAsNto> chargeAsNtos = new ArrayList<>();
            for (CisApplyChargeTo cisApplyChargeTo : currentCharges) {
                CisApplyChargeAsNto chargeAsNto = HIPBeanUtil.copy(cisApplyChargeTo, CisApplyChargeAsNto.class);
                chargeAsNto.setId("CG_" + HIPIDUtil.getNextIdString());
                chargeAsNtos.add(chargeAsNto);
            }
            detailAsNto.setCisApplyChargeAsNtoList(chargeAsNtos);

            cisIpdDocOrderDetailsAsNtoList.add(detailAsNto);
        }

        dgimgIpdOrderAsNto.setDgimgIpdOrderDetailAsNtoList(cisIpdDocOrderDetailsAsNtoList);
        cisIpdDocOrderAsNto = dgimgIpdOrderAsNto;
        return cisIpdDocOrderAsNto;
    }

}
