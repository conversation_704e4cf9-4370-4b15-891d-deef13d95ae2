package com.bjgoodwill.hip.as.cis.doc.opd.controller;

import com.bjgoodwill.hip.as.cis.doc.opd.service.SpcobsOrderHandleService;
import com.bjgoodwill.hip.as.cis.doc.opd.to.doctor.CisApplyChargeAsTo;
import com.bjgoodwill.hip.as.cis.doc.opd.to.spcobs.CisSpcobsApplyAsTo;
import com.bjgoodwill.hip.as.cis.doc.opd.to.spcobs.OpdSpcobsApplyAsTo;
import com.bjgoodwill.hip.as.cis.doc.opd.to.spcobs.SpcobsOrderDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR> liangyuqing
 * @description :
 * @createDate : 2025/5/13 9:52
 */
@RestController
@Tag(name = "门诊检验医嘱开立应用服务", description = "检验医嘱开立服务类")
@RequestMapping("/cis/doc/opd/spcobs")
public class SpcobsOrderHandleController {

    @Resource
    private SpcobsOrderHandleService spcobsOrderHandleService;

    @Operation(summary = "检验项目查询", description = "")
    @ApiResponse(description = "", content = @Content(mediaType = "application/json", schema = @Schema(implementation = OpdSpcobsApplyAsTo.class)))
    @GetMapping(value = "/search/{groupCode}/{areaCode}")
    public List<OpdSpcobsApplyAsTo> search(@PathVariable("groupCode") String workGroupCode, @PathVariable("areaCode") String hospitalAreaCode) {
        return spcobsOrderHandleService.searchItems(workGroupCode, hospitalAreaCode);
    }


    @Operation(summary = "常用检验项目查询", description = "")
    @ApiResponse(description = "", content = @Content(mediaType = "application/json", schema = @Schema(implementation = OpdSpcobsApplyAsTo.class)))
    @GetMapping(value = "/search/common/{groupCode}/{areaCode}")
    public List<OpdSpcobsApplyAsTo> searchCommon(@PathVariable("groupCode") String workGroupCode, @PathVariable("areaCode") String hospitalAreaCode) {
        return spcobsOrderHandleService.searchCommonItems(workGroupCode, hospitalAreaCode);
    }


    @Operation(summary = "检验项目明细查询", description = "")
    @ApiResponse(description = "", content = @Content(mediaType = "application/json", schema = @Schema(implementation = CisApplyChargeAsTo.class)))
    @GetMapping(value = "/charges/{code}")
    public List<CisApplyChargeAsTo> getServiceClincPriceList(@PathVariable("code") String serviceItemCode) {
        return spcobsOrderHandleService.getServiceClincPriceList(serviceItemCode);
    }

    @Operation(summary = "新增/修改检验医嘱", description = "新增检验医嘱")
    @ApiResponse(description = "新增/修改检验医嘱", content = @Content(mediaType = "application/json"))
    @PostMapping(value = "/insert/update")
    public void insertOrUpdateSpcobsOrders(@RequestBody @Valid List<SpcobsOrderDto> spcobsOrderDtoList) {
        spcobsOrderHandleService.insertOrUpdateSpcobsOrders(spcobsOrderDtoList);
    }

    @Operation(summary = "保存并签发检验医嘱", description = "新增检验医嘱")
    @ApiResponse(description = "保存并签发检验医嘱", content = @Content(mediaType = "application/json"))
    @PostMapping(value = "/insert/update/submit")
    public void insertOrUpdateAndSubmitSpcobsOrders(@RequestBody @Valid List<SpcobsOrderDto> spcobsOrderDtoList, @RequestParam(value = "mergeFlag") boolean mergeFlag, @RequestParam(value = "version") String prescriptionVersion) {
        spcobsOrderHandleService.insertOrUpdateAndSubmitSpcobsOrder(spcobsOrderDtoList, mergeFlag, prescriptionVersion);
    }

    @Operation(summary = "查询未签发检验申请单列表", description = "查询未签发检验申请单")
    @ApiResponse(description = "查询未签发检验申请单列表", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = CisSpcobsApplyAsTo.class))))
    @GetMapping(value = "/order/new/{visit-code}")
    public List<CisSpcobsApplyAsTo> getNewSpcobsApplyList(@PathVariable("visit-code") String visitCode) {
        return spcobsOrderHandleService.getNewSpcobsApply(visitCode);
    }

    @Operation(summary = "查询已签发未缴费检验申请单列表", description = "查询已签发未缴费检验申请单")
    @ApiResponse(description = "查询已签发未缴费检验申请单列表", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = CisSpcobsApplyAsTo.class))))
    @GetMapping(value = "/order/active/{visit-code}")
    public List<CisSpcobsApplyAsTo> getActiveSpcobsApplyList(@PathVariable("visit-code") String visitCode) {
        return spcobsOrderHandleService.getActiveSpcobsApply(visitCode);
    }

    @Operation(summary = "根据医嘱ID查询检验申请信息", description = "查询检验医嘱")
    @ApiResponse(description = "查询检验医嘱", content = @Content(mediaType = "application/json", schema = @Schema(implementation = SpcobsOrderDto.class)))
    @GetMapping(value = "/order/detail/{id}")
    public SpcobsOrderDto getSpcobsOrder(@PathVariable("id") String id) {
        return spcobsOrderHandleService.getSpcobsOrder(id);
    }

    @Operation(summary = "检验医嘱签发", description = "检验医嘱签发")
    @ApiResponse(description = "检验医嘱签发", content = @Content(mediaType = "application/json"))
    @PostMapping(value = "/submit")
    public void submitSpcobsOrder(@RequestBody List<CisSpcobsApplyAsTo> cisSpcobsApplyAsTos,
                                  @RequestParam(value = "mergeFlag") boolean mergeFlag,
                                  @RequestParam(value = "visitCode") String visitCode,
                                  @RequestParam(value = "version") String prescriptionVersion) {
        spcobsOrderHandleService.submitSpcobsOrder(cisSpcobsApplyAsTos, mergeFlag, visitCode, prescriptionVersion);
    }

    @Operation(summary = "检验医嘱撤回", description = "检验医嘱撤回")
    @ApiResponse(description = "检验医嘱撤回", content = @Content(mediaType = "application/json"))
    @PostMapping(value = "/back")
    public void backSpcobsOrder(@RequestBody List<String> orderIdList) {
        spcobsOrderHandleService.backSpcobsOrder(orderIdList);
    }

    @Operation(summary = "检验医嘱删除", description = "检验医嘱删除")
    @ApiResponse(description = "检验医嘱删除", content = @Content(mediaType = "application/json"))
    @PostMapping(value = "/delete")
    public void deleteSpcobsOrder(@RequestBody List<String> orderIdList) {
        spcobsOrderHandleService.deleteSpcobsOrder(orderIdList);
    }
}
