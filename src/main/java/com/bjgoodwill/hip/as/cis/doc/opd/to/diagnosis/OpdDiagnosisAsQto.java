package com.bjgoodwill.hip.as.cis.doc.opd.to.diagnosis;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2025/5/7 19:54
 */
@Schema(description = "门诊医生站-诊断-查询")
public class OpdDiagnosisAsQto implements Serializable {
    @Schema(description = "主索引")
    private String patMiCode;

    @Schema(description = "中医/西医诊断, 中医:TCM;西医:WMD")
    private String diagnosisClass;

    @Schema(description = "模糊查询")
    private String text;

    public String getPatMiCode() {
        return patMiCode;
    }

    public void setPatMiCode(String patMiCode) {
        this.patMiCode = patMiCode;
    }

    public String getDiagnosisClass() {
        return diagnosisClass;
    }

    public void setDiagnosisClass(String diagnosisClass) {
        this.diagnosisClass = diagnosisClass;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }
}
