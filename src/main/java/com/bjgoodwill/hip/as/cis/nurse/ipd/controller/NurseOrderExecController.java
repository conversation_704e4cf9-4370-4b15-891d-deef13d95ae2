package com.bjgoodwill.hip.as.cis.nurse.ipd.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.bjgoodwill.hip.as.cis.nurse.ipd.enums.CisNurseIpdBusinessErrorEnum;
import com.bjgoodwill.hip.as.cis.nurse.ipd.service.NurseOrderExecAsService;
import com.bjgoodwill.hip.as.cis.nurse.ipd.to.*;
import com.bjgoodwill.hip.business.util.cis.common.CisOrgCommonNto;
import com.bjgoodwill.hip.business.util.cis.common.enums.SkinTypeEnum;
import com.bjgoodwill.hip.common.bean.EnumTo;
import com.bjgoodwill.hip.common.bean.LoginInfo;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.service.CisOrderExecPlanService;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.to.CisOrderExecPlanNurseQto;
import com.bjgoodwill.hip.ds.cis.cpoe.order.service.CisIpdNurseService;
import com.bjgoodwill.hip.ds.drug.goods.enmus.DrugLimitedLevelEnum;
import com.bjgoodwill.hip.ds.org.api.service.StaffService;
import com.bjgoodwill.hip.ds.org.api.to.StaffLocalTo;
import com.bjgoodwill.hip.security.bean.CurrentOrgInfo;
import com.bjgoodwill.hip.security.service.SecurityService;
import com.bjgoodwill.hip.security.util.HIPCurrentOrgUtils;
import com.bjgoodwill.hip.security.util.HIPSecurityUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

@RestController
@SaCheckPermission("cisNurse:nurseWorkstation")
@Tag(name = "医嘱执行应用服务", description = "医嘱执行应用服务类")
@RequestMapping("/cis/nurse/ipd/exec")
public class NurseOrderExecController {

    @Autowired
    private NurseOrderExecAsService nurseOrderExecAsService;

    @Autowired
    private CisIpdNurseService cisIpdNurseService;

    @Autowired
    private CisOrderExecPlanService cisOrderExecPlanService;

    @Autowired
    private SecurityService securityService;

    @Autowired
    private StaffService staffService;

    @Operation(summary = "根据查询条件查询执行单信息", description = "根据查询条件查询执行单信息")
    @ApiResponse(description = "执行单信息", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = NurseExecPatInfoAsTo.class))))
    @PostMapping("/")
    public List<NurseExecPatInfoAsTo> getOrderExecList(@RequestBody CisOrderExecPlanNurseQto qto) {
        CurrentOrgInfo loginInfo = HIPCurrentOrgUtils.getCurrentOrgInfo();
        BusinessAssert.notNull(loginInfo, CisNurseIpdBusinessErrorEnum.CIS_NURSE_IPD_0001, "登录信息");
        qto.setDeptNurseCode(loginInfo.getWorkGroupCode());
        qto.setExecOrgCode(loginInfo.getWorkGroupCode());
        return nurseOrderExecAsService.getOrderExecList(qto);
    }

    @Operation(summary = "批量执行", description = "批量执行")
    @ApiResponse(description = "执行结果信息", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = OrderExecResultAsTo.class))))
    @PostMapping("/execute")
    public List<OrderExecResultAsTo> executeAllOrders(@RequestBody List<NurseOrderExecDto> nurseOrderExecDtoList) {
        return nurseOrderExecAsService.executeAllOrders(nurseOrderExecDtoList);
    }

    @Operation(summary = "取消执行")
    @PutMapping("/cancel")
    void cancelExecute(@RequestBody List<String> execIds) {
        nurseOrderExecAsService.cancelExecuteCisOrderExecPlanBatch(execIds);
    }

    @Operation(summary = "不执行")
    @PutMapping("/noExecute")
    void noExecute(@RequestBody List<String> execIds) {
        CurrentOrgInfo loginInfo = HIPCurrentOrgUtils.getCurrentOrgInfo();
        BusinessAssert.notNull(loginInfo, CisNurseIpdBusinessErrorEnum.CIS_NURSE_IPD_0001, "登录信息");
        CisOrgCommonNto cisOrgCommonNto = new CisOrgCommonNto();
        cisOrgCommonNto.setOrgCode(loginInfo.getWorkGroupCode());
        cisOrgCommonNto.setOrgName(loginInfo.getWorkGroupName());
        cisOrderExecPlanService.noExecuteCisOrderExecPlanBatch(loginInfo.getWorkGroupCode(), execIds,cisOrgCommonNto);
    }

    @Operation(summary = "撤销（重新执行）")
    @PutMapping("/reExecute")
    void reExecute(@RequestBody List<String> execIds) {
        CurrentOrgInfo loginInfo = HIPCurrentOrgUtils.getCurrentOrgInfo();
        BusinessAssert.notNull(loginInfo, CisNurseIpdBusinessErrorEnum.CIS_NURSE_IPD_0001, "登录信息");
        CisOrgCommonNto cisOrgCommonNto = new CisOrgCommonNto();
        cisOrgCommonNto.setOrgCode(loginInfo.getWorkGroupCode());
        cisOrgCommonNto.setOrgName(loginInfo.getWorkGroupName());
        cisOrderExecPlanService.anewExecuteCisOrderExecPlanBatch(loginInfo.getWorkGroupCode(), execIds,cisOrgCommonNto);
    }

    @Operation(summary = "获取皮试结果数据源", description = "获取皮试结果数据源")
    @ApiResponse(description = "返回皮试结果集合", content = @Content(mediaType = "application/json", schema = @Schema(implementation = List.class)))
    @GetMapping(value = "/skinDataSource")
    public List<EnumTo<String>> getSkinDataSource() {
        List<EnumTo<String>> list = new ArrayList<>();
        for (SkinTypeEnum var : SkinTypeEnum.values()){
            EnumTo<String> enumTo = new EnumTo<>();
            enumTo.setCode(var.getCode());
            enumTo.setName(var.getName());
            enumTo.setMnemonicCode(var.name());
            list.add(enumTo);
        }
        return list;
    }

    @Operation(summary = "录入皮试信息")
    @PutMapping("/skinResult")
    void orderSkinResultsInput(@RequestBody CisOrderExecPlanAsEto eto) {
        cisIpdNurseService.orderSkinResultsInput(eto.getOrderId(), eto.getPlanId(), eto);
    }

    @Operation(summary = "获取登录护理组的全部护士信息", description = "获取登录护理组的全部护士信息")
    @ApiResponse(description = "护士信息", content = @Content(mediaType = "application/json", schema = @Schema(implementation = StaffLocalTo.class)))
    @GetMapping("/nurses")
    List<StaffLocalTo> getNurseStaffList(){
        CurrentOrgInfo loginInfo = HIPCurrentOrgUtils.getCurrentOrgInfo();
        BusinessAssert.notNull(loginInfo, CisNurseIpdBusinessErrorEnum.CIS_NURSE_IPD_0001, "登录信息");
        return staffService.getNurseStaffLocalByWorkGroup(loginInfo.getWorkGroupCode());
    }

    @Operation(summary = "校验密码是否正确", description = "校验密码是否正确")
    @PostMapping("/users/passwords/validated")
    public boolean validatePassword(@RequestBody HeadNurseLoginAsTo headNurseLoginAsTo) {
        return securityService.validatePasswordByJobNo(headNurseLoginAsTo.getJobNo(), headNurseLoginAsTo.getPassword());
    }
}
