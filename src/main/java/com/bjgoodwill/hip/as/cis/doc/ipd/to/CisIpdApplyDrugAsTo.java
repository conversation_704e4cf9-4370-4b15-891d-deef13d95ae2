package com.bjgoodwill.hip.as.cis.doc.ipd.to;

import com.bjgoodwill.hip.ds.cis.apply.drug.to.CisDrugApplyDetailTo;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: zhangyunchuan
 * @Date: 2024/11/19 15:29
 * @PROJECT: hip-ac
 */
@Schema(description = "药品类申请单")
public class CisIpdApplyDrugAsTo extends CisIpdApplyAsTo implements Serializable {

    @Schema(description = "用法")
    private String usage;

    @Schema(description = "用法名称")
    private String usageName;

    @Schema(description = "频次")
    private String frequency;

    @Schema(description = "频次名称")
    private String frequencyName;

    @Schema(description = "疗程")
    private String treatmentCourse;

    @Schema(description = "疗程单位")
    private String treatmentCourseUnit;

    @Schema(description = "协定处方")
    private Boolean prescriptionFlag;

    @Schema(description = "领药科室")
    private String receiveOrg;

    @Schema(description = "药品申请单明细")
    private List<CisDrugApplyDetailTo> cisDrugApplyDetails;

    public String getUsage() {
        return usage;
    }

    public void setUsage(String usage) {
        this.usage = usage;
    }

    public String getUsageName() {
        return usageName;
    }

    public void setUsageName(String usageName) {
        this.usageName = usageName;
    }

    public String getFrequency() {
        return frequency;
    }

    public void setFrequency(String frequency) {
        this.frequency = frequency;
    }

    public String getFrequencyName() {
        return frequencyName;
    }

    public void setFrequencyName(String frequencyName) {
        this.frequencyName = frequencyName;
    }

    public String getTreatmentCourse() {
        return treatmentCourse;
    }

    public void setTreatmentCourse(String treatmentCourse) {
        this.treatmentCourse = treatmentCourse;
    }

    public String getTreatmentCourseUnit() {
        return treatmentCourseUnit;
    }

    public void setTreatmentCourseUnit(String treatmentCourseUnit) {
        this.treatmentCourseUnit = treatmentCourseUnit;
    }

    public Boolean getPrescriptionFlag() {
        return prescriptionFlag;
    }

    public void setPrescriptionFlag(Boolean prescriptionFlag) {
        this.prescriptionFlag = prescriptionFlag;
    }

    public String getReceiveOrg() {
        return receiveOrg;
    }

    public void setReceiveOrg(String receiveOrg) {
        this.receiveOrg = receiveOrg;
    }

    public List<CisDrugApplyDetailTo> getCisDrugApplyDetails() {
        return cisDrugApplyDetails;
    }

    public void setCisDrugApplyDetails(List<CisDrugApplyDetailTo> cisDrugApplyDetails) {
        this.cisDrugApplyDetails = cisDrugApplyDetails;
    }
}
