package com.bjgoodwill.hip.as.cis.opd.visitresource.service;

import com.bjgoodwill.hip.as.cis.opd.visitresource.to.*;
import com.bjgoodwill.hip.ds.org.api.to.WorkGroupTo;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * Description:门诊出诊资源应用服务接口
 *
 * <AUTHOR>
 * &#064;date 2025/3/19 下午3:48
 */
@Tag(name = "门诊出诊资源应用服务接口", description = "门诊出诊资源应用服务接口")
public interface PatOpdVisitResourceAsService {

    /**
     * 根据条件查询门诊出诊资源
     *
     * @param qto
     * @return
     */
    List<PatVisitResourceShowAsTo> getList(@RequestBody PatVisitResourceAsQto qto);

    /**
     * 查询出诊资源显示
     *
     * @param qto
     * @return
     */
    List<PatVisitResourceAsTo> getResourceList(@RequestBody PatVisitResourceAsQto qto);

    /**
     * 新增门诊科室号出诊资源
     *
     * @param nto
     */
    PatVisitResourceAsTo createDeptResource(@RequestBody @Valid PatVisitResourceAsNto nto);

    /**
     * 批量新增门诊科室号出诊资源
     *
     * @param nto
     */
    void createDeptResources(@RequestBody @Valid PatVisitResourceListAsNto nto);

    /**
     * 新增门诊医生号出诊资源
     *
     * @param nto
     */
    PatVisitResourceAsTo createDoctorResource(@RequestBody @Valid PatVisitResourceAsNto nto);

    /**
     * 批量更新门诊医生号出诊资源
     *
     * @param etos
     */
    void udpateResources(@RequestBody @Valid List<PatVisitResourceAsEto> etos);

    /**
     * 根据条件查询出诊科室数据源
     *
     * @return
     */
    List<WorkGroupTo> getWorkGroupList(String hospitalAreaCode);
}
