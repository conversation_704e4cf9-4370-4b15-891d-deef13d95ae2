package com.bjgoodwill.hip.as.cis.doc.opd.service.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.bjgoodwill.hip.as.cis.doc.opd.enums.CisDocOpdBusinessErrorEnum;
import com.bjgoodwill.hip.as.cis.doc.opd.service.treatment.diagnose.*;
import com.bjgoodwill.hip.as.cis.doc.opd.service.OpdDiagnosisInputAsService;
import com.bjgoodwill.hip.as.cis.doc.opd.to.diagnosis.*;
import com.bjgoodwill.hip.as.cis.doc.opd.util.OpdDiagnosisUtil;
import com.bjgoodwill.hip.business.util.cis.common.enums.DiagnosisEnum;
import com.bjgoodwill.hip.common.exception.BusinessException;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.common.util.HIPBeanUtil;
import com.bjgoodwill.hip.ds.base.cis.diagnose.dict.service.DiagnoseChineseSymptomService;
import com.bjgoodwill.hip.ds.base.cis.diagnose.dict.to.DiagnoseChineseSymptomTo;
import com.bjgoodwill.hip.ds.cis.cds.diagnose.service.CisDiagnoseDocCommonService;
import com.bjgoodwill.hip.ds.cis.cds.diagnose.to.*;
import com.bjgoodwill.hip.ds.cis.opdcpoe.diag.service.CisOpdChineseDiagnoseService;
import com.bjgoodwill.hip.ds.cis.opdcpoe.diag.service.CisOpdDiagnoseService;
import com.bjgoodwill.hip.ds.cis.opdcpoe.diag.service.CisOpdWestDiagnoseService;
import com.bjgoodwill.hip.ds.cis.opdcpoe.diag.to.*;
import com.bjgoodwill.hip.ds.cis.opdcpoe.emr.service.CisOpdEmrDataService;
import com.bjgoodwill.hip.ds.cis.opdcpoe.emr.to.CisOpdEmrDataEto;
import com.bjgoodwill.hip.ds.cis.opdcpoe.emr.to.CisOpdEmrDataNto;
import com.bjgoodwill.hip.ds.cis.opdcpoe.emr.to.CisOpdEmrDataQto;
import com.bjgoodwill.hip.ds.cis.opdcpoe.emr.to.CisOpdEmrDataTo;
import com.bjgoodwill.hip.ds.org.api.service.WorkGroupService;
import com.bjgoodwill.hip.ds.org.api.to.WorkGroupTo;
import com.bjgoodwill.hip.ds.pat.regist.regist.enmus.PatRegistStasEnum;
import com.bjgoodwill.hip.ds.pat.regist.regist.service.PatRegistService;
import com.bjgoodwill.hip.ds.pat.regist.regist.to.PatRegistTo;
import com.bjgoodwill.hip.ds.pat.regist.regist.to.PatRegistToVisitEto;
import com.bjgoodwill.hip.security.util.HIPSecurityUtils;
import io.micrometer.common.util.StringUtils;
import jakarta.validation.constraints.NotNull;
import org.apache.commons.collections.CollectionUtils;
import org.apache.seata.spring.annotation.GlobalTransactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/4/23 10:04
 */
@Service("com.bjgoodwill.hip.as.cis.doc.opd.service.OpdDiagnosisInputAsService")
public class OpdDiagnosisInputAsServiceImpl implements OpdDiagnosisInputAsService {

    @Autowired
    private CisOpdDiagnoseService cisOpdDiagnoseService;

    @Autowired
    private CisOpdEmrDataService cisOpdEmrDataService;

    @Autowired
    private CisDiagnoseDocCommonService cisDiagnoseDocCommonService;

    @Autowired
    private DiagnoseChineseSymptomService diagnoseChineseSymptomService;

    @Autowired
    private CisOpdChineseDiagnoseService cisOpdChineseDiagnoseService;

    @Autowired
    private CisOpdWestDiagnoseService cisOpdWestDiagnoseService;

    @Autowired
    private PatRegistService patRegistService;

    @Autowired
    private CisChronicTreatmentDiagnoseAsService cisChronicTreatmentDiagnoseAsService;

    @Autowired
    private CisCommonTreatmentDiagnoseAsService cisCommonTreatmentDiagnoseAsService;

    @Autowired
    private CisProcreateTreatmentDiagnoseService cisProcreateTreatmentDiagnoseService;

    @Autowired
    private CisPrenatalCheckTreatmentDiagnoseService cisPrenatalCheckTreatmentDiagnoseService;

    @Autowired
    private CisSingleTreatmentDaignoseAsService cisSingleTreatmentDaignoseAsService;

    @Autowired
    private OpdDiagnosisUtil opdDiagnosisUtil;

    @Autowired
    private WorkGroupService workGroupService;

    private OpdDiagnosisInputAsService getSelf() {
        return SpringUtil.getBean(OpdDiagnosisInputAsService.class);
    }

    @Override
    public EmrDataAndDiagnosesTo getPatEmrDataAndDiagnoses(EmrDataAndDiagnosesQto emrDataAndDiagnosesQto) {
        EmrDataAndDiagnosesTo result = new EmrDataAndDiagnosesTo();
        if (emrDataAndDiagnosesQto.getTreatmentDiagnosisQto() != null) {
            List<OpdDiagnosisAsTo> diagnosisAsTos = getSelf().getAllDiagnosis(emrDataAndDiagnosesQto.getTreatmentDiagnosisQto());
            result.setOpdDiagnosisAsToList(diagnosisAsTos);
        }

        if (emrDataAndDiagnosesQto.getCisOpdEmrDataQto() != null) {
            CisOpdEmrDataTo cisOpdEmrDataTo = getSelf().getPatOpdEmrData(emrDataAndDiagnosesQto.getCisOpdEmrDataQto());
            result.setCisOpdEmrDataTo(cisOpdEmrDataTo);
        }

        return result;
    }

    @Override
    public List<OpdDiagnosisAsTo> getAllDiagnosis(TreatmentDiagnosisQto treatmentDiagnosisQto) {
        List<OpdDiagnosisAsTo> result = new ArrayList<>();
        if ("普通诊疗".equals(treatmentDiagnosisQto.getTreatmentType())) {
            List<OpdDiagnosisAsTo> diagnosisAsTos = cisCommonTreatmentDiagnoseAsService.getPatDiagnosis(treatmentDiagnosisQto);
            if (CollectionUtils.isNotEmpty(diagnosisAsTos)) {
                diagnosisAsTos.forEach(diagnosisAsTo -> {diagnosisAsTo.setTreatmentType("普通诊疗");});
            }
            result.addAll(diagnosisAsTos);
        } else if ("慢病诊疗".equals(treatmentDiagnosisQto.getTreatmentType())) {
            List<OpdDiagnosisAsTo> diagnosisAsTos = cisChronicTreatmentDiagnoseAsService.getPatDiagnosis(treatmentDiagnosisQto);
            if (CollectionUtils.isNotEmpty(diagnosisAsTos)) {
                diagnosisAsTos.forEach(diagnosisAsTo -> {diagnosisAsTo.setTreatmentType("慢病诊疗");});
            }
            result.addAll(diagnosisAsTos);
        } else if ("生育诊疗".equals(treatmentDiagnosisQto.getTreatmentType())) {
            List<OpdDiagnosisAsTo> diagnosisAsTos = cisProcreateTreatmentDiagnoseService.getPatDiagnosis(treatmentDiagnosisQto);
            if (CollectionUtils.isNotEmpty(diagnosisAsTos)) {
                diagnosisAsTos.forEach(diagnosisAsTo -> {diagnosisAsTo.setTreatmentType("生育诊疗");});
            }
            result.addAll(diagnosisAsTos);
        } else if ("产前检查诊疗".equals(treatmentDiagnosisQto.getTreatmentType())) {
            List<OpdDiagnosisAsTo> diagnosisAsTos = cisPrenatalCheckTreatmentDiagnoseService.getPatDiagnosis(treatmentDiagnosisQto);
            if (CollectionUtils.isNotEmpty(diagnosisAsTos)) {
                diagnosisAsTos.forEach(diagnosisAsTo -> {diagnosisAsTo.setTreatmentType("产前检查诊疗");});
            }
            result.addAll(diagnosisAsTos);
        } else if ("单病种诊疗".equals(treatmentDiagnosisQto.getTreatmentType())) {
            List<OpdDiagnosisAsTo> diagnosisAsTos = cisCommonTreatmentDiagnoseAsService.getPatDiagnosis(treatmentDiagnosisQto);
            if (CollectionUtils.isNotEmpty(diagnosisAsTos)) {
                diagnosisAsTos.forEach(diagnosisAsTo -> {diagnosisAsTo.setTreatmentType("单病种诊疗");});
            }
            result.addAll(diagnosisAsTos);
        }

        if (CollectionUtils.isNotEmpty(result)) {
            result = result.stream().sorted(Comparator.comparing(OpdDiagnosisAsTo::getDiagnosisNo)).toList();

            // 诊断科室名称
            List<WorkGroupTo> workGroupTos = workGroupService.getEnableWorkGroup();
            Map<String, WorkGroupTo> workGroupMap = workGroupTos.stream().collect(Collectors.toMap(WorkGroupTo::getId, workGroupTo -> workGroupTo));
            result.forEach(diagnosisAsTo -> {
                diagnosisAsTo.setOrgName(workGroupMap.get(diagnosisAsTo.getOrgCode()).getName());
            });
        }

        return result;
    }

    @Override
    public CisOpdEmrDataTo getPatOpdEmrData(CisOpdEmrDataQto cisOpdEmrDataQto) {

        List<CisOpdEmrDataTo> cisOpdEmrDataToes = cisOpdEmrDataService.getCisOpdEmrDatas(cisOpdEmrDataQto);
        if (CollectionUtils.isNotEmpty(cisOpdEmrDataToes)) {
            return cisOpdEmrDataToes.stream().sorted(Comparator.comparing(CisOpdEmrDataTo::getCreatedDate).reversed()).toList().get(0);
        } else {
            return new CisOpdEmrDataTo();
        }
    }

    @Override
    public CisOpdEmrDataTo createCisOpdEmrData(CisOpdEmrDataAsNto cisOpdEmrDataAsNto) {
        // region查询患者门诊信息，判断是否可以保存
        CisOpdEmrDataQto cisOpdEmrDataQto = new CisOpdEmrDataQto();
        cisOpdEmrDataQto.setVisitCode(cisOpdEmrDataAsNto.getVisitCode());
        cisOpdEmrDataQto.setTreatmentCode(cisOpdEmrDataAsNto.getTreatmentCode());
        List<CisOpdEmrDataTo> cisOpdEmrDataToes = cisOpdEmrDataService.getCisOpdEmrDatas(cisOpdEmrDataQto);
        BusinessAssert.isEmpty(cisOpdEmrDataToes, CisDocOpdBusinessErrorEnum.BUS_CIS_DOC_OPD_0003, "患者已存在门诊信息");
        // endregion

        CisOpdEmrDataNto cisOpdEmrDataNto = HIPBeanUtil.copy(cisOpdEmrDataAsNto, CisOpdEmrDataNto.class);
        return cisOpdEmrDataService.createCisOpdEmrData(cisOpdEmrDataNto);
    }

    @Override
    public void updateCisOpdEmrData(CisOpdEmrDataAsNto cisOpdEmrDataAsNto) {
        CisOpdEmrDataEto cisOpdEmrDataEto = HIPBeanUtil.copy(cisOpdEmrDataAsNto, CisOpdEmrDataEto.class);
        cisOpdEmrDataService.updateCisOpdEmrData(cisOpdEmrDataAsNto.getId(), cisOpdEmrDataEto);
    }


    @GlobalTransactional
    @Override
    public OpdDiagnosisAsTo createCisIpdDiagnose(OpdDiagnosisAsNto opdDiagnosisAsNto) {
        opdDiagnosisAsNto.setOrgCode(opdDiagnosisAsNto.getCurWorkGroupCode());
        OpdDiagnosisAsTo opdDiagnosisAsTo = new OpdDiagnosisAsTo();
        // region 创建诊疗
//        if ("普通诊疗".equals(opdDiagnosisAsNto.getTreatmentType())) {
//            opdDiagnosisAsTo = cisCommonTreatmentDiagnoseAsService.createCisOpdDiagnose(opdDiagnosisAsNto);
//        } else if ("慢病诊疗".equals(opdDiagnosisAsNto.getTreatmentType())) {
//            opdDiagnosisAsTo = cisChronicTreatmentDiagnoseAsService.createCisOpdDiagnose(opdDiagnosisAsNto);
//        } else if ("生育诊疗".equals(opdDiagnosisAsNto.getTreatmentType())) {
//            opdDiagnosisAsTo = cisProcreateTreatmentDiagnoseService.createCisOpdDiagnose(opdDiagnosisAsNto);
//        } else if ("产前检查诊疗".equals(opdDiagnosisAsNto.getTreatmentType())) {
//            opdDiagnosisAsTo = cisPrenatalCheckTreatmentDiagnoseService.createCisOpdDiagnose(opdDiagnosisAsNto);
//        } else if ("单病种诊疗".equals(opdDiagnosisAsNto.getTreatmentType())) {
//            opdDiagnosisAsTo = cisSingleTreatmentDaignoseAsService.createCisOpdDiagnose(opdDiagnosisAsNto);
//        }
        // endregion

        // region修改患者状态
        PatRegistTo patRegistTo = patRegistService.getPatRegistByVisitCode(opdDiagnosisAsNto.getVisitCode());
        if (patRegistTo.getRegistStas().equals(PatRegistStasEnum.已挂号)) {
            PatRegistToVisitEto patRegistToVisitEto = new PatRegistToVisitEto();
            patRegistToVisitEto.setVisitDeptCode(opdDiagnosisAsNto.getCurWorkGroupCode());
            patRegistToVisitEto.setVisitDeptName(opdDiagnosisAsNto.getCurWorkGroupName());
            patRegistToVisitEto.setVersion(patRegistTo.getVersion());
            patRegistService.toVisit(patRegistTo.getId(), patRegistToVisitEto);
        }
        // endregion


        return getIpdDiagnosisAsTo(opdDiagnosisAsTo);
    }

    @Override
    @GlobalTransactional
    public List<OpdDiagnosisAsTo> batchSaveDiagnosesAndEmrData(EmrDataAndDiagnosesNto emrDataAndDiagnosesNto) {

        // region保存诊断信息
        BusinessAssert.notEmpty(emrDataAndDiagnosesNto.getDiagnoses(), CisDocOpdBusinessErrorEnum.BUS_CIS_DOC_OPD_0003, "请选择诊断进行保存");
        emrDataAndDiagnosesNto.getDiagnoses().forEach(opdDiagnosisAsNto -> {
            opdDiagnosisAsNto.setOrgCode(opdDiagnosisAsNto.getCurWorkGroupCode());
        });
        if ("普通诊疗".equals(emrDataAndDiagnosesNto.getDiagnoses().get(0).getTreatmentType())) {
            cisCommonTreatmentDiagnoseAsService.createCisOpdDiagnose(emrDataAndDiagnosesNto.getDiagnoses());
        } else if ("慢病诊疗".equals(emrDataAndDiagnosesNto.getDiagnoses().get(0).getTreatmentType())) {
            cisChronicTreatmentDiagnoseAsService.createCisOpdDiagnose(emrDataAndDiagnosesNto.getDiagnoses());
        } else if ("生育诊疗".equals(emrDataAndDiagnosesNto.getDiagnoses().get(0).getTreatmentType())) {
            cisProcreateTreatmentDiagnoseService.createCisOpdDiagnose(emrDataAndDiagnosesNto.getDiagnoses());
        } else if ("产前检查诊疗".equals(emrDataAndDiagnosesNto.getDiagnoses().get(0).getTreatmentType())) {
            cisPrenatalCheckTreatmentDiagnoseService.createCisOpdDiagnose(emrDataAndDiagnosesNto.getDiagnoses());
        } else if ("单病种诊疗".equals(emrDataAndDiagnosesNto.getDiagnoses().get(0).getTreatmentType())) {
            cisSingleTreatmentDaignoseAsService.createCisOpdDiagnose(emrDataAndDiagnosesNto.getDiagnoses());
        }
        // endregion

        // region保存患者门诊信息
        if (emrDataAndDiagnosesNto.getEmrDataAsNto() != null) {
            emrDataAndDiagnosesNto.getEmrDataAsNto().setTreatmentCode(emrDataAndDiagnosesNto.getDiagnoses().get(0).getTreatmentCode());
            opdDiagnosisUtil.saveOpdEmrData(emrDataAndDiagnosesNto.getEmrDataAsNto());
        }
        // endregion

        // region修改患者就诊状态
        PatRegistTo patRegistTo = patRegistService.getPatRegistByVisitCode(emrDataAndDiagnosesNto.getDiagnoses().get(0).getVisitCode());
        if (patRegistTo.getRegistStas().equals(PatRegistStasEnum.已挂号)) {
            PatRegistToVisitEto patRegistToVisitEto = new PatRegistToVisitEto();
            patRegistToVisitEto.setVisitDeptCode(emrDataAndDiagnosesNto.getDiagnoses().get(0).getCurWorkGroupCode());
            patRegistToVisitEto.setVisitDeptName(emrDataAndDiagnosesNto.getDiagnoses().get(0).getCurWorkGroupName());
            patRegistToVisitEto.setVersion(patRegistTo.getVersion());
            patRegistService.toVisit(patRegistTo.getId(), patRegistToVisitEto);
        }
        // endregion

        // region存为常用诊断信息
        List<OpdDiagnosisAsNto> commonUseDiagnoses = emrDataAndDiagnosesNto.getDiagnoses().stream().filter(a -> a.isCommonUseFlag()).toList();
        if (CollectionUtils.isNotEmpty(commonUseDiagnoses)) {
            opdDiagnosisUtil.saveCommonUseDiagnose(commonUseDiagnoses);
        }
        // endregion

        // 查询该诊疗全部诊断返回
        List<CisOpdDiagnoseTo> cisOpdDiagnoseToList = cisOpdDiagnoseService.getCisOpdDiagnoseByTreatmentCode(
                emrDataAndDiagnosesNto.getDiagnoses().get(0).getTreatmentCode());
        return HIPBeanUtil.copy(cisOpdDiagnoseToList, OpdDiagnosisAsTo.class);
    }

    @NotNull
    private OpdDiagnosisAsTo getIpdDiagnosisAsTo(OpdDiagnosisAsTo cisOpdDiagnoses) {
        cisOpdDiagnoses.setDiagnosisClassCode(cisOpdDiagnoses.getDiagnosisClass().getCode());
        cisOpdDiagnoses.setDiagnosisClassName(cisOpdDiagnoses.getDiagnosisClass().getName());
        return cisOpdDiagnoses;
    }

    @GlobalTransactional
    @Override
    public OpdDiagnosisAsTo createDiagnoseSaveCommon(OpdDiagnosisAsNto opdDiagnosisAsNto) {
        //保存诊断
        OpdDiagnosisAsTo cisIpdDiagnose = getSelf().createCisIpdDiagnose(opdDiagnosisAsNto);

        //保存常用判断
        CisDiagnoseDocCommonQto cisDiagnoseDocCommonQto = new CisDiagnoseDocCommonQto();
        cisDiagnoseDocCommonQto.setDocCode(HIPSecurityUtils.getLoginStaffId());
        cisDiagnoseDocCommonQto.setEnabled(true);
        cisDiagnoseDocCommonQto.setText(opdDiagnosisAsNto.getDiagnosisCode());
        List<CisDiagnoseDocCommonTo> cisDiagnoseDocCommons = cisDiagnoseDocCommonService.getCisDiagnoseDocCommons(cisDiagnoseDocCommonQto);
        if (CollectionUtils.isNotEmpty(cisDiagnoseDocCommons)) {
            List<CisDiagnoseDocCommonTo> list = cisDiagnoseDocCommons.stream().filter(a -> a.getDiagnoseCode().equals(opdDiagnosisAsNto.getDiagnosisCode())).toList();
            if (CollectionUtils.isNotEmpty(list)) {
                //已有相同诊断编码诊断 所以不保存常用医嘱
                return getIpdDiagnosisAsTo(cisIpdDiagnose);
            }
        }
        //保存常用
        CisDiagnoseDocCommonNto cisDiagnoseDocCommonNto = new CisDiagnoseDocCommonNto();
        cisDiagnoseDocCommonNto.setDocCode(HIPSecurityUtils.getLoginStaffId());
        cisDiagnoseDocCommonNto.setDiagnoseCode(opdDiagnosisAsNto.getDiagnosisCode());
        cisDiagnoseDocCommonNto.setDiagnoseName(opdDiagnosisAsNto.getDiagnosisName());
        cisDiagnoseDocCommonNto.setIsFix(false);
        cisDiagnoseDocCommonNto.setPrefix(opdDiagnosisAsNto.getPrefix());
        cisDiagnoseDocCommonNto.setSuffix(opdDiagnosisAsNto.getSuffix());
        cisDiagnoseDocCommonNto.setDiagnosisClass(DiagnosisEnum.valueOf(opdDiagnosisAsNto.getDiagnosisClass()));
        cisDiagnoseDocCommonService.createCisDiagnoseDocCommon(cisDiagnoseDocCommonNto);
        return getIpdDiagnosisAsTo(cisIpdDiagnose);
    }

    @Override
    public void updateDiagnosis(OpdDiagnosisAsEto opdDiagnosisAsEto) {
//        List<CisOpdDiagnoseTo> cisOpdDiagnoseToList = cisOpdDiagnoseService.getCisOpdDiagnoseByVisitCode(opdDiagnosisAsEto.getVisitCode());
//        if (CollectionUtils.isNotEmpty(cisOpdDiagnoseToList)) {
//            //主诊断唯一校验
//            List<CisOpdDiagnoseTo> list = cisOpdDiagnoseToList.stream().filter(a -> a.getChiefFlag() != null && a.getChiefFlag() && a.getDiagnosisClass().getCode().equals(opdDiagnosisAsEto.getDiagnosisClass().getCode())).toList();
//            if (CollectionUtils.isNotEmpty(list) && opdDiagnosisAsEto.getChiefFlag() && !opdDiagnosisAsEto.getId().equals(list.get(0).getId())) {
//                throw new BusinessException("该类型诊断已经存在主诊断,不可修改为主诊断");
//            }
//        }
        if (opdDiagnosisAsEto.getDiagnosisClass() != null &&
                opdDiagnosisAsEto.getDiagnosisClass().getCode().equals(DiagnosisEnum.WMD.getCode())) {
            //门诊西医诊断修改
            CisOpdChineseDiagnoseEto cisOpdChineseDiagnoseEto = HIPBeanUtil.copy(opdDiagnosisAsEto, CisOpdChineseDiagnoseEto.class);
            cisOpdChineseDiagnoseService.updateCisOpdChineseDiagnose(opdDiagnosisAsEto.getId(), cisOpdChineseDiagnoseEto);
        } else if (opdDiagnosisAsEto.getDiagnosisClass() != null &&
                opdDiagnosisAsEto.getDiagnosisClass().getCode().equals(DiagnosisEnum.TCM.getCode())) {
            //门诊中医诊断修改
            CisOpdWestDiagnoseEto cisOpdWestDiagnoseEto = HIPBeanUtil.copy(opdDiagnosisAsEto, CisOpdWestDiagnoseEto.class);
            cisOpdWestDiagnoseService.updateCisOpdWestDiagnose(opdDiagnosisAsEto.getId(), cisOpdWestDiagnoseEto);
        }
    }

    @Override
    public void deleteDiagnosis(OpdDiagnosisAsDto opdDiagnosisAsDto) {
        CisOpdDiagnoseTo cisOpdDiagnoseTo = cisOpdDiagnoseService.getCisOpdDiagnoseById(opdDiagnosisAsDto.getId());
        if (cisOpdDiagnoseTo != null) {
            cisOpdDiagnoseService.deleteCisOpdDiagnoseWithTreatmentCodeByUsed(opdDiagnosisAsDto.getId());
        } else {
            throw new BusinessException("未查询到对应诊断信息");
        }
    }

    @Override
    public List<OpdDiagnoseWestAsTo> getWestDiagnosisObscure(String text, String treatmentType) {
        List<OpdDiagnoseWestAsTo> diagnoseWestAsToList = new ArrayList<>();
        if (treatmentType.equals("普通诊疗")) {
            diagnoseWestAsToList = cisCommonTreatmentDiagnoseAsService.getWestDiagnosisObscure(text);
        } else if (treatmentType.equals("慢病诊疗")) {
            diagnoseWestAsToList = cisChronicTreatmentDiagnoseAsService.getWestDiagnosisObscure(text);
        } else if (treatmentType.equals("生育诊疗")) {
            diagnoseWestAsToList = cisProcreateTreatmentDiagnoseService.getWestDiagnosisObscure(text);
        } else if (treatmentType.equals("产前检查诊疗")) {
            diagnoseWestAsToList = cisPrenatalCheckTreatmentDiagnoseService.getWestDiagnosisObscure(text);
        } else if (treatmentType.equals("单病种诊疗")) {
            diagnoseWestAsToList = cisSingleTreatmentDaignoseAsService.getWestDiagnosisObscure(text);
        }
        return diagnoseWestAsToList;
    }

    @Override
    public List<OpdDiagnoseChineseAsTo> getChineseDiagnosisObscure(String text, String treatmentType) {
        List<OpdDiagnoseChineseAsTo> diagnoseChineseAsToList = new ArrayList<>();
        if (treatmentType.equals("普通诊疗")) {
            diagnoseChineseAsToList = cisCommonTreatmentDiagnoseAsService.getChineseDiagnosisObscure(text);
        } else if (treatmentType.equals("慢病诊疗")) {
            diagnoseChineseAsToList = cisChronicTreatmentDiagnoseAsService.getChineseDiagnosisObscure(text);
        } else if (treatmentType.equals("生育诊疗")) {
            diagnoseChineseAsToList = cisProcreateTreatmentDiagnoseService.getChineseDiagnosisObscure(text);
        } else if (treatmentType.equals("产前检查诊疗")) {
            diagnoseChineseAsToList = cisPrenatalCheckTreatmentDiagnoseService.getChineseDiagnosisObscure(text);
        } else if (treatmentType.equals("单病种诊疗")) {
            diagnoseChineseAsToList = cisSingleTreatmentDaignoseAsService.getChineseDiagnosisObscure(text);
        }
        return diagnoseChineseAsToList;
    }

    @Override
    public List<DiagnoseChineseSymptomTo> getChineseSymptomObscure(String text) {
        List<DiagnoseChineseSymptomTo> diagnoseChineseSymptomsByLike = diagnoseChineseSymptomService.getDiagnoseChineseSymptomsByLike(text, 50);
        diagnoseChineseSymptomsByLike.sort(Comparator.comparingInt(s -> s.getName().indexOf(text)));
        return diagnoseChineseSymptomsByLike;
    }

    @Override
    public List<CisDiagnoseDocCommonTo> getChineseCommonDiagnosis(String text) {
        CisDiagnoseDocCommonQto cisDiagnoseDocCommonQto = new CisDiagnoseDocCommonQto();
        cisDiagnoseDocCommonQto.setDocCode(HIPSecurityUtils.getLoginStaffId());
        cisDiagnoseDocCommonQto.setDiagnosisClass(DiagnosisEnum.TCM);
        cisDiagnoseDocCommonQto.setEnabled(true);
        List<CisDiagnoseDocCommonTo> cisDiagnoseDocCommonTos = getCisDiagnoseDocCommonTos(text, cisDiagnoseDocCommonQto);
        cisDiagnoseDocCommonTos.sort(Comparator.comparingInt(s -> s.getDiagnoseName().indexOf(text)));
        return cisDiagnoseDocCommonTos;
    }

    @Override
    public List<CisDiagnoseDocCommonTo> getWestCommonDiagnosis(String text) {
        CisDiagnoseDocCommonQto cisDiagnoseDocCommonQto = new CisDiagnoseDocCommonQto();
        cisDiagnoseDocCommonQto.setDocCode(HIPSecurityUtils.getLoginStaffId());
        cisDiagnoseDocCommonQto.setDiagnosisClass(DiagnosisEnum.WMD);
        cisDiagnoseDocCommonQto.setEnabled(true);
        List<CisDiagnoseDocCommonTo> cisDiagnoseDocCommonTos = getCisDiagnoseDocCommonTos(text, cisDiagnoseDocCommonQto);
        cisDiagnoseDocCommonTos.sort(Comparator.comparingInt(s -> s.getDiagnoseName().indexOf(text)));
        return cisDiagnoseDocCommonTos;
    }

    @Override
    public void saveCommonDiagnosis(CommonDiagnosisAsNto commonDiagnosisAsNto) {
        //保存常用
        CisDiagnoseDocCommonNto cisDiagnoseDocCommonNto = new CisDiagnoseDocCommonNto();
        cisDiagnoseDocCommonNto.setDocCode(HIPSecurityUtils.getLoginStaffId());
        cisDiagnoseDocCommonNto.setDiagnoseCode(commonDiagnosisAsNto.getDiagnoseCode());
        cisDiagnoseDocCommonNto.setDiagnoseName(commonDiagnosisAsNto.getDiagnoseName());
        cisDiagnoseDocCommonNto.setIsFix(true);
        cisDiagnoseDocCommonNto.setDiagnosisClass(DiagnosisEnum.valueOf(commonDiagnosisAsNto.getDiagnosisClass()));
        cisDiagnoseDocCommonService.createCisDiagnoseDocCommon(cisDiagnoseDocCommonNto);
    }

    @Override
    public void cancelCisDiagnoseDocCommon(String id) {
        cisDiagnoseDocCommonService.cancelCisDiagnoseDocCommon(id);
    }

    @Override
    public void receivePatient(String visitCode, String groupCode, String groupName) {
        PatRegistTo patRegistTo = patRegistService.getPatRegistByVisitCode(visitCode);
        if (patRegistTo.getRegistStas().equals(PatRegistStasEnum.已挂号)) {
            PatRegistToVisitEto patRegistToVisitEto = new PatRegistToVisitEto();
            patRegistToVisitEto.setVisitDeptCode(groupCode);
            patRegistToVisitEto.setVisitDeptName(groupName);
            patRegistToVisitEto.setVersion(patRegistTo.getVersion());
            patRegistService.toVisit(patRegistTo.getId(), patRegistToVisitEto);
        } else if (patRegistTo.getRegistStas().equals(PatRegistStasEnum.已接诊)) {
            BusinessAssert.isTrue(false, CisDocOpdBusinessErrorEnum.BUS_CIS_DOC_OPD_0003, "患者已接诊，无需再次接诊");
        }
    }

    @NotNull
    private List<CisDiagnoseDocCommonTo> getCisDiagnoseDocCommonTos(String text, CisDiagnoseDocCommonQto cisDiagnoseDocCommonQto) {
        List<CisDiagnoseDocCommonTo> cisDiagnoseDocCommons = cisDiagnoseDocCommonService.getCisDiagnoseDocCommons(cisDiagnoseDocCommonQto);
        if (CollectionUtils.isNotEmpty(cisDiagnoseDocCommons)) {
            if (StringUtils.isNotEmpty(text)) {
                List<CisDiagnoseDocCommonTo> list = cisDiagnoseDocCommons.stream().filter(a -> (a.getDiagnoseName().contains(text) || a.getDiagnoseCode().contains(text)) && a.isEnabled()).toList();
                if (CollectionUtils.isNotEmpty(list)) {
                    return list;
                }
            } else {
                return cisDiagnoseDocCommons.stream().filter(CisDiagnoseCommonTo::isEnabled).toList();
            }
        }
        return List.of();
    }

}
