package com.bjgoodwill.hip.as.cis.doc.opd.service;

import com.bjgoodwill.hip.as.cis.doc.opd.to.doctor.CisApplyChargeAsTo;
import com.bjgoodwill.hip.as.cis.doc.opd.to.operation.OperationOrderDto;
import com.bjgoodwill.hip.ds.cis.apply.diag.to.ApplyDiagnosisNto;
import com.bjgoodwill.hip.ds.cis.medicineitem.serviceItem.to.OperationApplyTo;
import com.bjgoodwill.hip.ds.term.api.to.DictElementTo;

import java.util.List;

/**
 * <AUTHOR> lian<PERSON><PERSON><PERSON>
 * @description :
 * @createDate : 2025/5/27 11:05
 */
public interface OperationOrderHandleService {


    /**
     * 查询患者术前诊断
     *
     * @param visitCode
     * @param treatmentCode
     * @return
     */
    List<ApplyDiagnosisNto> getOperationDiagnosis(String visitCode, String treatmentCode);

    /**
     * 查询手术医嘱项目
     *
     * @param text
     * @param workGroupCode
     * @param hospitalAreaCode
     * @return
     */
    List<OperationApplyTo> getOperationItems(String text, String workGroupCode, String hospitalAreaCode);

    /**
     * 新增/修改手术医嘱
     *
     * @param operationOrderDto
     */
    List<String> insertOrUpdateOperationOrder(OperationOrderDto operationOrderDto);

    /**
     * 新增/修改手术医嘱并提交
     *
     * @param operationOrderDto
     */
    void insertOrUpdateOperationOrderAndSubmit(OperationOrderDto operationOrderDto);

    /**
     * 根据麻醉类型，查询对应麻醉方式字典
     *
     * @param typeCode
     * @return
     */
    List<DictElementTo> getAnaesthesiaMode(String typeCode);

    /**
     * 查询手术医嘱信息
     *
     * @param orderId
     * @return
     */
    OperationOrderDto getOperationOrder(String orderId);

    List<CisApplyChargeAsTo> getServiceClincPriceList(String serviceItemCode);

}
