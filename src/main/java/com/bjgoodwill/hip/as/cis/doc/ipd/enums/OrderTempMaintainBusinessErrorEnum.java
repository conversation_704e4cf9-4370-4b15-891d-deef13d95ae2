package com.bjgoodwill.hip.as.cis.doc.ipd.enums;

import com.bjgoodwill.hip.common.exception.BusinessErrorEnum;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @description :
 * @createDate : 2025/1/9 14:34
 */
public enum OrderTempMaintainBusinessErrorEnum implements BusinessErrorEnum {

    ORDER_TEMP_MAINTAIN_0001("父级组套不存在！"),
    ORDER_TEMP_MAINTAIN_0002("该文件夹下存在组套，请先删除组套信息！"),
    ORDER_TEMP_MAINTAIN_0003("组套中存在医嘱信息，请先删除医嘱信息！"),
    ORDER_TEMP_MAINTAIN_0004("[%s]不能为空！"),
    ORDER_TEMP_MAINTAIN_0005("未查询到[%s]，编码[%s]！"),
    ORDER_TEMP_MAINTAIN_0006("%s！");

    private final String message;

    OrderTempMaintainBusinessErrorEnum(String message) {
        this.message = message;
    }

    @Override
    public String getCode() {
        return this.name();
    }

    @Override
    public String getMessage() {
        return message;
    }
}
