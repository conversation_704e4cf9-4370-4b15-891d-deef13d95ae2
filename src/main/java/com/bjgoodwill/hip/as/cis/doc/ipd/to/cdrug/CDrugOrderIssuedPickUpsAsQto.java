package com.bjgoodwill.hip.as.cis.doc.ipd.to.cdrug;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "草药医嘱开立拾取器-查询")
public class CDrugOrderIssuedPickUpsAsQto {

    @Schema(description = "模糊文本")
    private String inPutText;

    @Schema(description = "执行科室")
    private String executeOrg;

    public String getInPutText() {
        return inPutText;
    }

    public void setInPutText(String inPutText) {
        this.inPutText = inPutText;
    }

    public String getExecuteOrg() {
        return executeOrg;
    }

    public void setExecuteOrg(String executeOrg) {
        this.executeOrg = executeOrg;
    }
}
