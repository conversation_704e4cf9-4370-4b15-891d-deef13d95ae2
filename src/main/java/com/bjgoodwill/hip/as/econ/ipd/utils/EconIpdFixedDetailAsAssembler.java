package com.bjgoodwill.hip.as.econ.ipd.utils;

import com.bjgoodwill.hip.as.econ.ipd.to.EconIpdFixedDetailAsTo;
import com.bjgoodwill.hip.ds.econ.fixed.to.EconIpdFixedDetailTo;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public abstract class EconIpdFixedDetailAsAssembler {

    public static List<EconIpdFixedDetailAsTo> toAsTos(List<EconIpdFixedDetailTo> detailToList) {
        return toAsTos(detailToList, false);
    }

    public static List<EconIpdFixedDetailAsTo> toAsTos(List<EconIpdFixedDetailTo> detailToList, boolean withAllParts) {
        if (detailToList == null)
            return null;

        List<EconIpdFixedDetailAsTo> tos = new ArrayList<>();
        for (EconIpdFixedDetailTo detailTo : detailToList)
            tos.add(toAsTo(detailTo, withAllParts));
        return tos;
    }

    public static EconIpdFixedDetailAsTo toAsTo(EconIpdFixedDetailTo detailTo) {
        return toAsTo(detailTo, false);
    }

    /**
     * @generated
     */
    public static EconIpdFixedDetailAsTo toAsTo(EconIpdFixedDetailTo detailTo, boolean withAllParts) {
        if (detailTo == null)
            return null;
        EconIpdFixedDetailAsTo to = new EconIpdFixedDetailAsTo();
        to.setEconIpdFixedId(detailTo.getEconIpdFixedId());
        to.setId(detailTo.getId());
        to.setCode(detailTo.getCode());
        to.setNum(detailTo.getNum());
        to.setLimit(detailTo.getLimit());
        to.setLimitName(detailTo.getLimitName());
        to.setStartUseTime(detailTo.getStartUseTime());
        to.setEndUseTime(detailTo.getEndUseTime());
        to.setCreatedStaff(detailTo.getCreatedStaff());
        to.setCreatedStaffName(detailTo.getCreatedStaffName());
        to.setCreatedDate(detailTo.getCreatedDate());
        to.setUpdatedStaff(detailTo.getUpdatedStaff());
        to.setUpdatedStaffName(detailTo.getUpdatedStaffName());
        to.setUpdatedDate(detailTo.getUpdatedDate());

        if (withAllParts) {
        }
        return to;
    }
}
