package com.bjgoodwill.hip.as.econ.opd.to;

import com.bjgoodwill.hip.business.util.common.to.BaseTo;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;

@Schema(description = "门诊固定费用")
public class EconOpdFixedDetailAsEto extends BaseTo implements Serializable {

    @Serial
    private static final long serialVersionUID = -7930126325574100051L;

    @Schema(description = "数量")
    private Double num;
    @Schema(description = "执行科室编码")
    private String exeDeptCode;
    @Schema(description = "执行科室名称")
    private String exeDeptName;

    @NotNull(message = "数量不能为空！")
    public Double getNum() {
        return num;
    }

    public void setNum(Double num) {
        this.num = num;
    }

//    @NotBlank(message = "执行科室编码不能为空！")
    @Size(max = 32, message = "执行科室编码长度不能超过32个字符！")
    public String getExeDeptCode() {
        return exeDeptCode;
    }

    public void setExeDeptCode(String exeDeptCode) {
        this.exeDeptCode = StringUtils.trimToNull(exeDeptCode);
    }

//    @NotBlank(message = "执行科室名称不能为空！")
    @Size(max = 64, message = "执行科室名称长度不能超过64个字符！")
    public String getExeDeptName() {
        return exeDeptName;
    }

    public void setExeDeptName(String exeDeptName) {
        this.exeDeptName = StringUtils.trimToNull(exeDeptName);
    }
}