package com.bjgoodwill.hip.as.econ.ipd.to.settlement;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.DecimalMin;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: zhangyunchuan
 * @Date: 2025/2/13 14:26
 * @PROJECT: hip-ac
 */
@Schema(description = "住院结算-结算")
public class EconIpdSettleAsNto implements Serializable {

    @Schema(description = "结算标识")
    private boolean rcptFlag;

    @Schema(description = "是否母亲 1是 0否")
    private boolean momFlag;

    @Schema(description = "经济结算-患者信息编码")
    private String patIndexCode;

    @Schema(description = "经济结算-住院流水号")
    private String visitCode;

    @Schema(description = "经济结算-费别")
    private String feeType;

    @Schema(description = "经济结算-应收费用总额")
    private @DecimalMin("0.00") BigDecimal amount;

    @Schema(description = "经济结算-减免类型")
    private String deduType;

    @Schema(description = "经济结算-减免金额")
    private @DecimalMin("0.00") BigDecimal deduAmount;

    @Schema(description = "经济结算-结算费用总额")
    private @DecimalMin("0.00") BigDecimal costs;

    @Schema(description = "经济结算-结算使用预交金金额")
    private @DecimalMin("0.00") BigDecimal prepayAmount;

    @Schema(description = "经济结算-自付金额")
    private @DecimalMin("0.00") BigDecimal paySelfAmount;

    @Schema(description = "经济结算-挂账标识")
    private boolean creditFlag;

    @Schema(description = "经济医保结算-医保类型")
    private String miType;

    @Schema(description = "经济医保结算-基金支付总额")
    private BigDecimal pubAmount;

    @Schema(description = "经济医保结算-医保个账支付")
    private BigDecimal accAmount;

    @Schema(description = "经济医保结算-医院承担金额")
    private BigDecimal hosAmount;

    @Schema(description = "经济医保结算-医保其他金额汇总")
    private BigDecimal otherAmount;

    @Schema(description = "医保结算-医保住院预结算Id")
    private String miIpdPreSetlId;

    @Schema(description = "医保结算-医疗费总额")
    private BigDecimal medfeeSumamt;

    @Schema(description = "就诊凭证类型-结算时读卡读出来")
    private String mdtrtCertType;

    @Schema(description = "就诊凭证编号-结算时读卡读出来")
    private String mdtrtCertCode;

    public String getPatIndexCode() {
        return patIndexCode;
    }

    public void setPatIndexCode(String patIndexCode) {
        this.patIndexCode = patIndexCode;
    }

    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    public String getFeeType() {
        return feeType;
    }

    public void setFeeType(String feeType) {
        this.feeType = feeType;
    }

    public @DecimalMin("0.00") BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(@DecimalMin("0.00") BigDecimal amount) {
        this.amount = amount;
    }

    public String getDeduType() {
        return deduType;
    }

    public void setDeduType(String deduType) {
        this.deduType = deduType;
    }

    public @DecimalMin("0.00") BigDecimal getDeduAmount() {
        return deduAmount;
    }

    public void setDeduAmount(@DecimalMin("0.00") BigDecimal deduAmount) {
        this.deduAmount = deduAmount;
    }

    public @DecimalMin("0.00") BigDecimal getCosts() {
        return costs;
    }

    public void setCosts(@DecimalMin("0.00") BigDecimal costs) {
        this.costs = costs;
    }

    public @DecimalMin("0.00") BigDecimal getPrepayAmount() {
        return prepayAmount;
    }

    public void setPrepayAmount(@DecimalMin("0.00") BigDecimal prepayAmount) {
        this.prepayAmount = prepayAmount;
    }

    public @DecimalMin("0.00") BigDecimal getPaySelfAmount() {
        return paySelfAmount;
    }

    public void setPaySelfAmount(@DecimalMin("0.00") BigDecimal paySelfAmount) {
        this.paySelfAmount = paySelfAmount;
    }

    public boolean isCreditFlag() {
        return creditFlag;
    }

    public void setCreditFlag(boolean creditFlag) {
        this.creditFlag = creditFlag;
    }

    public String getMiType() {
        return miType;
    }

    public void setMiType(String miType) {
        this.miType = miType;
    }

    public BigDecimal getPubAmount() {
        return pubAmount;
    }

    public void setPubAmount(BigDecimal pubAmount) {
        this.pubAmount = pubAmount;
    }

    public BigDecimal getAccAmount() {
        return accAmount;
    }

    public void setAccAmount(BigDecimal accAmount) {
        this.accAmount = accAmount;
    }

    public BigDecimal getHosAmount() {
        return hosAmount;
    }

    public void setHosAmount(BigDecimal hosAmount) {
        this.hosAmount = hosAmount;
    }

    public BigDecimal getOtherAmount() {
        return otherAmount;
    }

    public void setOtherAmount(BigDecimal otherAmount) {
        this.otherAmount = otherAmount;
    }

    public String getMiIpdPreSetlId() {
        return miIpdPreSetlId;
    }

    public void setMiIpdPreSetlId(String miIpdPreSetlId) {
        this.miIpdPreSetlId = miIpdPreSetlId;
    }

    public String getMdtrtCertCode() {
        return mdtrtCertCode;
    }

    public void setMdtrtCertCode(String mdtrtCertCode) {
        this.mdtrtCertCode = mdtrtCertCode;
    }

    public String getMdtrtCertType() {
        return mdtrtCertType;
    }

    public void setMdtrtCertType(String mdtrtCertType) {
        this.mdtrtCertType = mdtrtCertType;
    }

    public BigDecimal getMedfeeSumamt() {
        return medfeeSumamt;
    }

    public void setMedfeeSumamt(BigDecimal medfeeSumamt) {
        this.medfeeSumamt = medfeeSumamt;
    }

    public boolean isMomFlag() {
        return momFlag;
    }

    public void setMomFlag(boolean momFlag) {
        this.momFlag = momFlag;
    }

    public boolean isRcptFlag() {
        return rcptFlag;
    }

    public void setRcptFlag(boolean rcptFlag) {
        this.rcptFlag = rcptFlag;
    }
}
