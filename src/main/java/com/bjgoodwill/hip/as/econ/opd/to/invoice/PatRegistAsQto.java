package com.bjgoodwill.hip.as.econ.opd.to.invoice;

import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @Date 2025/5/19 17:56
 */
@Schema(
        description = "预约挂号结果查询Qto"
)
public class PatRegistAsQto {
    @Schema(description = "患者主索引号")
    private String patCode;

    @Schema(description = "范围开始时间")
    private LocalDate startDate;

    @Schema(description = "范围结束时间")
    private LocalDate endDate;

    public String getPatCode() {
        return patCode;
    }

    public void setPatCode(String patCode) {
        this.patCode = patCode;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }
}
