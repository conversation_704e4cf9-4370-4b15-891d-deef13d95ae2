package com.bjgoodwill.hip.as.econ.invoicemanage.controller;

import com.bjgoodwill.hip.as.econ.invoicemanage.service.EconReceiptMngAsService;
import com.bjgoodwill.hip.as.econ.invoicemanage.to.EconInvoMngAsTo;
import com.bjgoodwill.hip.as.econ.invoicemanage.to.EconInvoiceManageJumpNoAsEto;
import com.bjgoodwill.hip.as.econ.invoicemanage.to.EconReceiptMngAsTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * File: EconReceiptController
 * Author: douzhikai
 * Date: 2025/5/27
 * Description:
 */
@RestController("com.bjgoodwill.hip.as.econ.invoicemanage.controller.EconReceiptController")
@Tag(name = "收据维护应用服务", description = "收据维护应用服务")
@RequestMapping(value = "/common/econ/receipt", produces = "application/json; charset=utf-8")
public class EconReceiptController {

    @Autowired
    private EconReceiptMngAsService econReceiptMngAsService;

    @Operation(summary = "获取登录人已分配的门诊收据组集合")
    @ApiResponse(description = "", content = @Content(mediaType = "application/json", schema = @Schema(implementation = EconReceiptMngAsTo.class)))
    @GetMapping({"/get/staff/opd"})
    List<EconReceiptMngAsTo> getStaffOpdReceipt() {
        return econReceiptMngAsService.getOpdEconReceiptMngsByStaffId();
    }

    @Operation(summary = "获取登录人当前在用门诊收据组")
    @ApiResponse(description = "", content = @Content(mediaType = "application/json", schema = @Schema(implementation = EconReceiptMngAsTo.class)))
    @GetMapping({"/get/staff/opd/using"})
    EconReceiptMngAsTo getStaffOpdUsingReceipt() {
        return econReceiptMngAsService.getOpdUsingEconReceiptMngsByStaffId();
    }

    @Operation(summary = "获取登录人已分配的住院收据组集合")
    @ApiResponse(description = "", content = @Content(mediaType = "application/json", schema = @Schema(implementation = EconReceiptMngAsTo.class)))
    @GetMapping({"/get/staff/ipd"})
    List<EconReceiptMngAsTo> getStaffIpdReceipt() {
        return econReceiptMngAsService.getIpdEconReceiptMngsByStaffId();
    }

    @Operation(summary = "获取登录人当前在用住院收据组")
    @ApiResponse(description = "", content = @Content(mediaType = "application/json", schema = @Schema(implementation = EconReceiptMngAsTo.class)))
    @GetMapping({"/get/staff/ipd/using"})
    EconReceiptMngAsTo getStaffIpdUsingReceipt() {
        return econReceiptMngAsService.getIpdUsingEconReceiptMngsByStaffId();
    }

    @Operation(summary = "启用收据组", description = "根据发票组id启用收据组")
    @ApiResponse(description = "", content = @Content(mediaType = "application/json", schema = @Schema(implementation = EconReceiptMngAsTo.class)))
    @PutMapping({"/enable/id/{id}"})
    EconReceiptMngAsTo enableReceipt(@PathVariable("id") String id) {
        return econReceiptMngAsService.enable(id);
    }

    @Operation(summary = "停用收据组", description = "根据发票组id停用收据组")
    @ApiResponse(description = "", content = @Content(mediaType = "application/json", schema = @Schema(implementation = EconReceiptMngAsTo.class)))
    @PutMapping({"/deactivate/id/{id}"})
    EconReceiptMngAsTo deactivateReceipt(@PathVariable("id") String id) {
        return econReceiptMngAsService.deactivate(id);
    }

    @Operation(summary = "跳号(依次)", description = "根据收据组id跳号")
    @ApiResponse(description = "", content = @Content(mediaType = "application/json", schema = @Schema(implementation = EconReceiptMngAsTo.class)))
    @PostMapping({"/jumpNo/id/{id}"})
    EconReceiptMngAsTo jumpNoReceipt(@PathVariable("id") String id) {
        return econReceiptMngAsService.jumpNo(id);
    }

    @Operation(summary = "P0跳转到指定收据号")
    @ApiResponse(description = "", content = @Content(mediaType = "application/json", schema = @Schema(implementation = EconReceiptMngAsTo.class)))
    @PostMapping("/jump-to/no/id/{id:.+}")
    EconReceiptMngAsTo jumpToNoReceipt(@PathVariable("id") String id,
                                       @RequestBody @Valid EconInvoiceManageJumpNoAsEto eto){
        return econReceiptMngAsService.jumpToNo(id, eto);
    }
}
