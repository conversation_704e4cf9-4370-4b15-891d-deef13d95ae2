package com.bjgoodwill.hip.as.econ.ipd.controller;

import com.bjgoodwill.hip.as.econ.ipd.service.IpdRegistrationAsService;
import com.bjgoodwill.hip.as.econ.ipd.to.*;
import com.bjgoodwill.hip.ds.base.cis.diagnose.dict.service.DiagnoseWestService;
import com.bjgoodwill.hip.ds.base.cis.diagnose.dict.to.DiagnoseWestTo;
import com.bjgoodwill.hip.ds.org.api.to.*;
import com.bjgoodwill.hip.ds.pat.in.hospital.bed.service.PatServiceBedService;
import com.bjgoodwill.hip.ds.term.api.to.DictElementTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Author: zhangyunchuan
 * @Date: 2024/7/30 16:51
 * @PROJECT: econ-management
 */
@Tag(name = "住院登记服务", description = "住院登记应用服务类")
@RestController("com.bjgoodwill.hip.as.econ.ipd.controller.IpdRegistrationAsController")
@RequestMapping(value = "/econ/ipd/registration", produces = "application/json; charset=utf-8")
public class IpdRegistrationAsController {

    @Autowired
    private IpdRegistrationAsService ipdRegistrationAsService;

    @Autowired
    private PatServiceBedService patServiceBedService;

    @Autowired
    private DiagnoseWestService diagnoseWestService;

    @Operation(summary = "西医诊断模糊查询", description = "查询条件:模糊文本")
    @ApiResponse(description = "", content = @Content(mediaType = "application/json", schema = @Schema(implementation = DiagnoseWestTo.class)))
    @GetMapping(value = "/getWestDiagnosis")
    public List<DiagnoseWestTo> getWestDiagnosisObscure(@RequestParam("text") String text) {
        return diagnoseWestService.getDiagnoseWestsByLike(text, 100);
    }

    @Operation(summary = "支付方式", description = "取字典表备注prepay")
    @ApiResponse(description = "返回住院登记支付方式信息", content = @Content(mediaType = "application/json", schema = @Schema(implementation = DictElementTo.class)))
    @PostMapping(value = "/paymentMethodInquiry")
    public List<DictElementTo> ipdRegistrationPaymentMethodInquiry() {
        return ipdRegistrationAsService.paymentMethodInquiry();
    }

    @Operation(summary = "待登记入院", description = "待登记入院信息查询<br/>查询条件：当前开始时间、结束时间<br/>")
    @ApiResponse(description = "返回待登记入院患者信息", content = @Content(mediaType = "application/json", schema = @Schema(implementation = IpdRegistrationAsTo.class)))
    @PostMapping(value = "/treatHospitalized")
    public List<IpdRegistrationAsTo> treatHospitalized(@RequestBody @Valid IpdRegistrationAsQto ipdRegistrationAsQto) {
        return ipdRegistrationAsService.treatHospitalized(ipdRegistrationAsQto);
    }

    @Operation(summary = "已登记入院", description = "已登记入院信息查询<br/>查询条件：当前开始时间、结束时间<br/>")
    @ApiResponse(description = "返回已登记入院患者信息", content = @Content(mediaType = "application/json", schema = @Schema(implementation = IpdRegistrationAsTo.class)))
    @PostMapping(value = "/registeredHospitalized")
    public List<IpdRegistrationAsTo> registeredHospitalized(@RequestBody @Valid IpdRegistrationAsQto ipdRegistrationAsQto) {
        return ipdRegistrationAsService.registeredHospitalized(ipdRegistrationAsQto);
    }

    @Operation(summary = "在院患者", description = "在院患者信息查询<br/>查询条件：当前开始时间、结束时间<br/>")
    @ApiResponse(description = "返回在院患者信息", content = @Content(mediaType = "application/json", schema = @Schema(implementation = IpdRegistrationAsTo.class)))
    @PostMapping(value = "/inHospitalized")
    public List<IpdRegistrationAsTo> inHospitalized(@RequestBody @Valid IpdRegistrationAsQto ipdRegistrationAsQto) {
        return ipdRegistrationAsService.inHospitalized(ipdRegistrationAsQto);
    }

    @Operation(summary = "待登记入院-患者信息检索", description = "待登记入院-患者信息检索<br/>查询条件：查询条件、查询内容")
    @ApiResponse(description = "返回患者列表信息", content = @Content(mediaType = "application/json", schema = @Schema(implementation = PatIndexInfoAsTo.class)))
    @PostMapping(value = "/treatHospitalized/searchPat")
    public List<PatIndexInfoAsTo> treatHospitalizedSearchPat(@RequestBody @Valid IpdRegistrationAsQto ipdRegistrationAsQto) {
        return ipdRegistrationAsService.treatHospitalizedSearchPat(ipdRegistrationAsQto);
    }

    @Operation(summary = "待登记入院-患者预登记", description = "待登记入院-患者预登记<br/>查询条件:主索引")
    @ApiResponse(description = "返回患者预登记信息", content = @Content(mediaType = "application/json", schema = @Schema(implementation = PatIndexInfoAsTo.class)))
    @GetMapping(value = "/patientRegistration/{patMiCode}")
    public PatIndexInfoAsTo patientRegistration(@PathVariable("patMiCode") String patMiCode) {
        return ipdRegistrationAsService.patientRegistration(patMiCode);
    }

    @Operation(summary = "住院登记", description = "住院登记")
    @ApiResponse(description = "住院登记", content = @Content(mediaType = "application/json", schema = @Schema(implementation = NormalPatIpdRegistAsTo.class)))
    @PostMapping(value = "/createPatIpdRegistration")
    public NormalPatIpdRegistAsTo createPatIpdRegistration(@RequestBody @Valid IpdRegistrationAsNto ipdRegistrationAsNto) {
        return ipdRegistrationAsService.createPatIpdRegistration(ipdRegistrationAsNto);
    }

    @Operation(summary = "获取床位", description = "获取某科室下可用床位数，字符串形式，住院登记页面")
    @ApiResponse(description = "获取某科室下可用床位数，字符串形式，住院登记页面", content = @Content(mediaType = "application/json", schema = @Schema(implementation = String.class)))
    @GetMapping(value = "/beds/dept-nurse-code/{inNurseDeptCode}/available/str")
    public String patServiceBedService(@PathVariable("inNurseDeptCode") String inNurseDeptCode) {
        return patServiceBedService.getDeptNurseAvailableBed(inNurseDeptCode);
    }

    @Operation(summary = "取消登记", description = "取消登记<br/>入参:住院登记ID")
    @ApiResponse(description = "取消登记", content = @Content(mediaType = "application/json"))
    @PostMapping(value = "/cacnelRegists/{id}")
    public void cacnelRegists(@PathVariable("id") String id) {
        ipdRegistrationAsService.cacnelRegists(id);
    }

    @Operation(summary = "查询已登记患者信息接口", description = "查询已登记患者信息接口<br/>查询条件:主索引号")
    @ApiResponse(description = "返回已登记患者信息", content = @Content(mediaType = "application/json", schema = @Schema(implementation = StaffLocalTo.class)))
    @PostMapping(value = "/registered/registeredPatInfo")
    public PatIndexInfoAsTo getRegisteredPatInfo(@RequestBody @Valid PatIndexInfoAsTo patIndexInfoAsTo) {
        return ipdRegistrationAsService.getRegisteredPatInfo(patIndexInfoAsTo);
    }

    @Operation(summary = "查询待登记入院、转诊入院数量", description = "查询待登记入院、转诊入院数量")
    @ApiResponse(description = "返回待登记入院、转诊入院数量", content = @Content(mediaType = "application/json", schema = @Schema(implementation = IpdRegistrationAsTo.class)))
    @PostMapping(value = "/pending/selectCount")
    public IpdRegistrationAsTo getPendingCount() {
        return ipdRegistrationAsService.getPendingCount();
    }
}
