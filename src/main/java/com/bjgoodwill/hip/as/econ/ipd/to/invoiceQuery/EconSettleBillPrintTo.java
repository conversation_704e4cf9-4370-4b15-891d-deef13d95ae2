package com.bjgoodwill.hip.as.econ.ipd.to.invoiceQuery;

import com.bjgoodwill.hip.ds.pat.in.hospital.inpatient.to.PatIpdInpatientTo;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> lian<PERSON><PERSON><PERSON>
 * @description :
 * @createDate : 2025/4/8 15:18
 */
@Schema(description = "结算单明细打印To")
public class EconSettleBillPrintTo {

    @Schema(description = "住院患者信息")
    private PatIpdInpatientPrintAsTo patIpdInpatientTo;

    @Schema(description = "结算单明细汇总打印集合")
    private List<BillCollectPrintTo> billCollectPrintToList;

    @Schema(description = "总金额")
    private BigDecimal totalAmount;

    public PatIpdInpatientPrintAsTo getPatIpdInpatientTo() {
        return patIpdInpatientTo;
    }

    public void setPatIpdInpatientTo(PatIpdInpatientPrintAsTo patIpdInpatientTo) {
        this.patIpdInpatientTo = patIpdInpatientTo;
    }

    public List<BillCollectPrintTo> getBillCollectPrintToList() {
        return billCollectPrintToList;
    }

    public void setBillCollectPrintToList(List<BillCollectPrintTo> billCollectPrintToList) {
        this.billCollectPrintToList = billCollectPrintToList;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }
}
