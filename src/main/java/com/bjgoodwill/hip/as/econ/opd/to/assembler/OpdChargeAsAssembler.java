package com.bjgoodwill.hip.as.econ.opd.to.assembler;

import com.bjgoodwill.hip.as.econ.opd.enums.TreatmentClassificationEnum;
import com.bjgoodwill.hip.as.econ.opd.to.opdCharge.OpdPendingPaymentAsTo;
import com.bjgoodwill.hip.as.econ.opd.to.opdCharge.OpdPendingPaymentDetailAsTo;
import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.business.util.econ.enums.SetlStasEnum;
import com.bjgoodwill.hip.business.util.econ.enums.SystemItemClassEnum;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.to.CisOrderExecPlanChargeTo;
import com.bjgoodwill.hip.ds.cis.apply.execPlan.to.CisOrderExecPlanTo;
import com.bjgoodwill.hip.ds.drug.opd.apply.to.DrugOpdApplyDetailTo;
import com.bjgoodwill.hip.ds.econ.opd.bill.bill.enmus.EconOpdOrderClassEnum;
import com.bjgoodwill.hip.ds.econ.opd.bill.order.to.EconOpdOrderDetailTo;
import com.bjgoodwill.hip.ds.econ.opd.bill.order.to.EconOpdOrderTo;
import com.bjgoodwill.hip.security.util.HIPCurrentOrgUtils;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * File: OpdChargeAsAssembler
 * Author: zhangyunchuan
 * Date: 2025/5/26
 * Description:
 */
@Schema(description = "门诊收费汇编")
public class OpdChargeAsAssembler {

    public static OpdPendingPaymentAsTo getOpdPendingPaymentAsTo(CisOrderExecPlanTo cisOrderExecPlanTo) {
        OpdPendingPaymentAsTo opdPendingPaymentAsTo = new OpdPendingPaymentAsTo();
        opdPendingPaymentAsTo.setPatCode(cisOrderExecPlanTo.getPatMiCode());
        opdPendingPaymentAsTo.setVisitCode(cisOrderExecPlanTo.getVisitCode());
        opdPendingPaymentAsTo.setServiceItemCode(cisOrderExecPlanTo.getServiceItemCode());
        opdPendingPaymentAsTo.setServiceItemName(cisOrderExecPlanTo.getServiceItemName());
        if (cisOrderExecPlanTo.getSetlStas() != null) {
            opdPendingPaymentAsTo.setSetlStas(cisOrderExecPlanTo.getSetlStas());
        } else {
            opdPendingPaymentAsTo.setSetlStas(SetlStasEnum.未结算);
        }
        opdPendingPaymentAsTo.setNum(cisOrderExecPlanTo.getNum());
        opdPendingPaymentAsTo.setRxNo(cisOrderExecPlanTo.getPrescriptionID());
        opdPendingPaymentAsTo.setDrordNo(cisOrderExecPlanTo.getOrderId());
        opdPendingPaymentAsTo.setApplyNo(cisOrderExecPlanTo.getCisBaseApplyId());
        opdPendingPaymentAsTo.setExecNo(cisOrderExecPlanTo.getId());
        opdPendingPaymentAsTo.setPrscDr(cisOrderExecPlanTo.getHeldStaff());
        opdPendingPaymentAsTo.setPrscDrName(cisOrderExecPlanTo.getHeldStaffName());
        opdPendingPaymentAsTo.setPrscDrDept(cisOrderExecPlanTo.getCreateOrgCode());
        opdPendingPaymentAsTo.setPrscDrDeptName(cisOrderExecPlanTo.getCreateOrgName());
        opdPendingPaymentAsTo.setPrscDept(cisOrderExecPlanTo.getOrgCode());
        opdPendingPaymentAsTo.setPrscDeptName(cisOrderExecPlanTo.getOrgName());
        opdPendingPaymentAsTo.setOrderClass(EconOpdOrderClassEnum.医疗业务订单);
        //todo opdPendingPaymentAsTo.setGreenChannelFlag();
        opdPendingPaymentAsTo.setReceiveOrg(cisOrderExecPlanTo.getReceiveOrg());
        opdPendingPaymentAsTo.setReceiveOrgName(cisOrderExecPlanTo.getReceiveOrgName());
        opdPendingPaymentAsTo.setExecOrgCode(cisOrderExecPlanTo.getExecOrgCode());
        opdPendingPaymentAsTo.setExecOrgName(cisOrderExecPlanTo.getExecOrgName());
        opdPendingPaymentAsTo.setHospitalAreaCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getHospitalAreaCode());
        opdPendingPaymentAsTo.setHospitalAreaName(HIPCurrentOrgUtils.getCurrentOrgInfo().getHospitalAreaName());
        if (!cisOrderExecPlanTo.getOrderClass().equals(SystemTypeEnum.EDRUG)) {
            opdPendingPaymentAsTo.setDoseNum(String.valueOf(cisOrderExecPlanTo.getNum()));
        }
        opdPendingPaymentAsTo.setExecPlanVersion(cisOrderExecPlanTo.getVersion());
        return opdPendingPaymentAsTo;
    }

    public static OpdPendingPaymentAsTo calculateMedicalPricing(EconOpdOrderTo econOpdOrderTo) {
        OpdPendingPaymentAsTo opdPendingPaymentAsTo = new OpdPendingPaymentAsTo();
        opdPendingPaymentAsTo.setPatCode(econOpdOrderTo.getPatCode());
        opdPendingPaymentAsTo.setVisitCode(econOpdOrderTo.getVisitCode());
        if (econOpdOrderTo.getSetlFlag()) {
            opdPendingPaymentAsTo.setSetlStas(SetlStasEnum.已结算);
        } else {
            opdPendingPaymentAsTo.setSetlStas(SetlStasEnum.未结算);
        }
        opdPendingPaymentAsTo.setNum(1.0);
        opdPendingPaymentAsTo.setRxNo(econOpdOrderTo.getRxNo());
        opdPendingPaymentAsTo.setDrordNo(econOpdOrderTo.getDrordNo());
        opdPendingPaymentAsTo.setApplyNo(econOpdOrderTo.getApplyNo());
        opdPendingPaymentAsTo.setExecNo(econOpdOrderTo.getExecNo());
        opdPendingPaymentAsTo.setPrscDr(econOpdOrderTo.getPrscDr());
        opdPendingPaymentAsTo.setPrscDrName(econOpdOrderTo.getPrscDrName());
        opdPendingPaymentAsTo.setPrscDrDept(econOpdOrderTo.getPrscDrDept());
        opdPendingPaymentAsTo.setPrscDrDeptName(econOpdOrderTo.getPrscDrDeptName());
        opdPendingPaymentAsTo.setPrscDept(econOpdOrderTo.getPrscDept());
        opdPendingPaymentAsTo.setPrscDeptName(econOpdOrderTo.getPrscDeptName());
        opdPendingPaymentAsTo.setOrderClass(econOpdOrderTo.getOrderClass());
        //todo opdPendingPaymentAsTo.setGreenChannelFlag();
        opdPendingPaymentAsTo.setHospitalAreaCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getHospitalAreaCode());
        opdPendingPaymentAsTo.setHospitalAreaName(HIPCurrentOrgUtils.getCurrentOrgInfo().getHospitalAreaName());
        opdPendingPaymentAsTo.setEconOpdOrderId(econOpdOrderTo.getId());
        opdPendingPaymentAsTo.setBillingStatus(true);
        opdPendingPaymentAsTo.setTreatmentClassification(TreatmentClassificationEnum.COMMON);
        opdPendingPaymentAsTo.setTreatmentClassificationCode(TreatmentClassificationEnum.COMMON.getCode());
        opdPendingPaymentAsTo.setTreatmentClassificationName(TreatmentClassificationEnum.COMMON.getName());
        return opdPendingPaymentAsTo;
    }

    public static OpdPendingPaymentDetailAsTo getOpdPendingPaymentDetailAsTo(CisOrderExecPlanChargeTo cisOrderExecPlanChargeTo) {
        OpdPendingPaymentDetailAsTo opdPendingPaymentDetailAsTo = new OpdPendingPaymentDetailAsTo();
        opdPendingPaymentDetailAsTo.setPriceItemCode(cisOrderExecPlanChargeTo.getPriceItemCode());
        opdPendingPaymentDetailAsTo.setPriceItemName(cisOrderExecPlanChargeTo.getPriceItemName());
        opdPendingPaymentDetailAsTo.setPackageSpec(cisOrderExecPlanChargeTo.getPackageSpec());
        opdPendingPaymentDetailAsTo.setPrice(cisOrderExecPlanChargeTo.getPrice());
        opdPendingPaymentDetailAsTo.setUnit(cisOrderExecPlanChargeTo.getUnit());
        opdPendingPaymentDetailAsTo.setUnitName(cisOrderExecPlanChargeTo.getUnitName());
        opdPendingPaymentDetailAsTo.setNum(cisOrderExecPlanChargeTo.getNum());
        opdPendingPaymentDetailAsTo.setChargeAmount(cisOrderExecPlanChargeTo.getChargeAmount());
        opdPendingPaymentDetailAsTo.setExecDelNo(cisOrderExecPlanChargeTo.getId());
        if (cisOrderExecPlanChargeTo.getIsFixed() != null) {
            opdPendingPaymentDetailAsTo.setFixed(cisOrderExecPlanChargeTo.getIsFixed());
        } else {
            opdPendingPaymentDetailAsTo.setFixed(false);
        }
        opdPendingPaymentDetailAsTo.setLimitConformFlag(cisOrderExecPlanChargeTo.getLimitConformFlag());
        opdPendingPaymentDetailAsTo.setExecuteOrgCode(cisOrderExecPlanChargeTo.getExecuteOrgCode());
        opdPendingPaymentDetailAsTo.setExecuteOrgName(cisOrderExecPlanChargeTo.getExecuteOrgName());
        opdPendingPaymentDetailAsTo.setChargeType(cisOrderExecPlanChargeTo.getChargeType());
        opdPendingPaymentDetailAsTo.setSystemItemClass(cisOrderExecPlanChargeTo.getSystemItemClass());
        opdPendingPaymentDetailAsTo.setSystemItemClassCode(cisOrderExecPlanChargeTo.getSystemItemClass().getCode());
        opdPendingPaymentDetailAsTo.setSystemItemClassName(cisOrderExecPlanChargeTo.getSystemItemClass().getName());
        opdPendingPaymentDetailAsTo.setOrderApplyDetailId(cisOrderExecPlanChargeTo.getDetailId());
        return opdPendingPaymentDetailAsTo;
    }

    public static OpdPendingPaymentDetailAsTo getDrugOpdPendingPaymentDetailAsTo(DrugOpdApplyDetailTo drug, CisOrderExecPlanChargeTo cisOrderExecPlanChargeTo) {
        OpdPendingPaymentDetailAsTo opdPendingPaymentDetailAsTo = new OpdPendingPaymentDetailAsTo();
        opdPendingPaymentDetailAsTo.setPriceItemCode(cisOrderExecPlanChargeTo.getPriceItemCode());
        opdPendingPaymentDetailAsTo.setPriceItemName(cisOrderExecPlanChargeTo.getPriceItemName());
        opdPendingPaymentDetailAsTo.setPackageSpec(cisOrderExecPlanChargeTo.getPackageSpec());
        opdPendingPaymentDetailAsTo.setPrice(drug.getSalePrice());
        opdPendingPaymentDetailAsTo.setUnit(cisOrderExecPlanChargeTo.getUnit());
        opdPendingPaymentDetailAsTo.setUnitName(cisOrderExecPlanChargeTo.getUnitName());
        opdPendingPaymentDetailAsTo.setNum(cisOrderExecPlanChargeTo.getNum());
        opdPendingPaymentDetailAsTo.setChargeAmount(cisOrderExecPlanChargeTo.getChargeAmount());
        opdPendingPaymentDetailAsTo.setExecDelNo(cisOrderExecPlanChargeTo.getId());
        if (cisOrderExecPlanChargeTo.getIsFixed() != null) {
            opdPendingPaymentDetailAsTo.setFixed(cisOrderExecPlanChargeTo.getIsFixed());
        } else {
            opdPendingPaymentDetailAsTo.setFixed(false);
        }
        opdPendingPaymentDetailAsTo.setLimitConformFlag(cisOrderExecPlanChargeTo.getLimitConformFlag());
        opdPendingPaymentDetailAsTo.setExecuteOrgCode(cisOrderExecPlanChargeTo.getExecuteOrgCode());
        opdPendingPaymentDetailAsTo.setExecuteOrgName(cisOrderExecPlanChargeTo.getExecuteOrgName());
        opdPendingPaymentDetailAsTo.setChargeType(cisOrderExecPlanChargeTo.getChargeType());
        opdPendingPaymentDetailAsTo.setSystemItemClass(SystemItemClassEnum.DRUG);
        opdPendingPaymentDetailAsTo.setSystemItemClassCode(SystemItemClassEnum.DRUG.getCode());
        opdPendingPaymentDetailAsTo.setSystemItemClassName(SystemItemClassEnum.DRUG.getName());
        opdPendingPaymentDetailAsTo.setDrugOpdApplyDetailId(drug.getId());
        opdPendingPaymentDetailAsTo.setOrderApplyDetailId(drug.getOrderDetailId());
        return opdPendingPaymentDetailAsTo;
    }

    public static OpdPendingPaymentDetailAsTo calculateMedicalPricingDetail(EconOpdOrderDetailTo econOpdOrderDetailTo) {
        OpdPendingPaymentDetailAsTo opdPendingPaymentDetailAsTo = new OpdPendingPaymentDetailAsTo();
        opdPendingPaymentDetailAsTo.setPriceItemCode(econOpdOrderDetailTo.getPriceItemCode());
        opdPendingPaymentDetailAsTo.setPriceItemName(econOpdOrderDetailTo.getPriceItemName());
        opdPendingPaymentDetailAsTo.setPackageSpec(econOpdOrderDetailTo.getPacSpec());
        opdPendingPaymentDetailAsTo.setPrice(econOpdOrderDetailTo.getPrice());
        opdPendingPaymentDetailAsTo.setUnitName(econOpdOrderDetailTo.getPacUnit());
        opdPendingPaymentDetailAsTo.setNum(econOpdOrderDetailTo.getNum());
        opdPendingPaymentDetailAsTo.setChargeAmount(econOpdOrderDetailTo.getReceivableAmt());
        opdPendingPaymentDetailAsTo.setExecDelNo(econOpdOrderDetailTo.getId());
        opdPendingPaymentDetailAsTo.setFixed(econOpdOrderDetailTo.getFixedFlag());
        opdPendingPaymentDetailAsTo.setLimitConformFlag(econOpdOrderDetailTo.getMiApprFlag());
        opdPendingPaymentDetailAsTo.setSystemItemClassCode(econOpdOrderDetailTo.getSystemItemClass());
        opdPendingPaymentDetailAsTo.setSystemItemClassName(econOpdOrderDetailTo.getSystemItemClassName());
        return opdPendingPaymentDetailAsTo;
    }
}
