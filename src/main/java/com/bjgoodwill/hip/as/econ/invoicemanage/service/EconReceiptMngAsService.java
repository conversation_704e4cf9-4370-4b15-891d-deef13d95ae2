package com.bjgoodwill.hip.as.econ.invoicemanage.service;

import com.bjgoodwill.hip.as.econ.invoicemanage.to.EconInvoiceManageJumpNoAsEto;
import com.bjgoodwill.hip.as.econ.invoicemanage.to.EconReceiptMngAsTo;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.util.List;

/**
 * Description:
 *
 * <AUTHOR>
 * &#064;date 2025/5/27 上午9:40
 */
@Tag(name = "收据维护应用服务接口", description = "收据维护应用服务接口")
public interface EconReceiptMngAsService {

    // 门诊收据：获取当前登录人已分配未使用的门诊收据组集合
    List<EconReceiptMngAsTo> getOpdEconReceiptMngsByStaffId();

    // 门诊收据：获取当前登录人使用中的门诊收据组
    EconReceiptMngAsTo getOpdUsingEconReceiptMngsByStaffId();

    // 住院收据：获取当前登录人已分配未使用的住院收据组集合
    List<EconReceiptMngAsTo> getIpdEconReceiptMngsByStaffId();

    // 住院收据：获取当前登录人使用中的住院收据组
    EconReceiptMngAsTo getIpdUsingEconReceiptMngsByStaffId();

    // 停用收据组
    EconReceiptMngAsTo enable(String id);

    // 停用收据组
    EconReceiptMngAsTo deactivate(String id);

    // 跳号(依次)
    EconReceiptMngAsTo jumpNo(String econReceiptMngId);

    // 跳转到指定号
    EconReceiptMngAsTo jumpToNo(String id, EconInvoiceManageJumpNoAsEto eto);
}
