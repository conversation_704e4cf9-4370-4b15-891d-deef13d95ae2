package com.bjgoodwill.hip.as.econ.opd.to.registration.window;

import com.bjgoodwill.hip.ds.econ.opd.bill.order.to.EconOpdOrderDetailTo;
import com.bjgoodwill.hip.ds.mi.opd.setl.to.MiOpdPreSetlTo;
import com.bjgoodwill.hip.ds.pat.regist.regist.to.PatRegistTo;
import com.bjgoodwill.hip.mi.lss.to.MiOpdPreSetlAndFeeAsNto;
import com.bjgoodwill.hip.mi.lss.to.MiOpdRegistAsNto;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * File: RegistrationAsTo
 * Author: zhangyunchuan
 * Date: 2025/5/8
 * Description:
 */
@Schema(description = "挂号")
public class RegistrationAsTo implements Serializable {

    @Schema(description = "挂号标识")
    private String registId;

    @Schema(description = "门诊费用订单标识")
    private String econOpdOrderToId;

    @Schema(description = "主索引号")
    private String patCode;

    @Schema(description = "就诊流水号")
    private String visitCode;

    @Schema(description = "费别")
    private String feeType;

    @Schema(description = "应收")
    private BigDecimal receivableAmount;

    @Schema(description = "实收")
    private BigDecimal actualAmount;

    @Schema(description = "挂号信息")
    private PatRegistTo patRegistTo;

    @Schema(description = "订单明细集合")
    private List<EconOpdOrderDetailTo> econOpdOrderDetails;

    public BigDecimal getReceivableAmount() {
        return receivableAmount;
    }

    public void setReceivableAmount(BigDecimal receivableAmount) {
        this.receivableAmount = receivableAmount;
    }

    public BigDecimal getActualAmount() {
        return actualAmount;
    }

    public void setActualAmount(BigDecimal actualAmount) {
        this.actualAmount = actualAmount;
    }

    public String getEconOpdOrderToId() {
        return econOpdOrderToId;
    }

    public void setEconOpdOrderToId(String econOpdOrderToId) {
        this.econOpdOrderToId = econOpdOrderToId;
    }

    public String getPatCode() {
        return patCode;
    }

    public void setPatCode(String patCode) {
        this.patCode = patCode;
    }

    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    public List<EconOpdOrderDetailTo> getEconOpdOrderDetails() {
        return econOpdOrderDetails;
    }

    public void setEconOpdOrderDetails(List<EconOpdOrderDetailTo> econOpdOrderDetails) {
        this.econOpdOrderDetails = econOpdOrderDetails;
    }

    public String getFeeType() {
        return feeType;
    }

    public void setFeeType(String feeType) {
        this.feeType = feeType;
    }

    public String getRegistId() {
        return registId;
    }

    public void setRegistId(String registId) {
        this.registId = registId;
    }

    public PatRegistTo getPatRegistTo() {
        return patRegistTo;
    }

    public void setPatRegistTo(PatRegistTo patRegistTo) {
        this.patRegistTo = patRegistTo;
    }

}
