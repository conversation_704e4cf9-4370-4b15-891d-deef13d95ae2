package com.bjgoodwill.hip.as.econ.opd.to.registration.window;

import com.bjgoodwill.hip.business.util.econ.enums.SetlPayStasEnum;
import com.bjgoodwill.hip.business.util.econ.enums.SetlStasEnum;
import com.bjgoodwill.hip.ds.pat.regist.regist.enmus.PatRegistStasEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * File: RegisteredPatientsTo
 * Author: zhangyunchuan
 * Date: 2025/5/8
 * Description:
 */
@Schema(description = "已挂患者")
public class RegisteredPatientsAsTo implements Serializable {

    @Schema(description = "挂号操作时间")
    private LocalDateTime createdDate;

    @Schema(description = "流水号")
    private String visitCode;

    @Schema(description = "发票号")
    private String settleNo;

    @Schema(description = "合计金额")
    private BigDecimal amount;

    @Schema(description = "结算状态")
    private SetlStasEnum setlStas;

    @Schema(description = "支付状态")
    private SetlPayStasEnum payFlag;

    @Schema(description = "挂号科室")
    private String appVisitDeptCode;

    @Schema(description = "挂号科室名称")
    private String appVisitDeptName;

    @Schema(description = "挂号类型")
    private String registrationLv;

    @Schema(description = "挂号类型名称")
    private String registrationLvName;

    @Schema(description = "挂号医生编码")
    private String appVisitDocCode;

    @Schema(description = "挂号医生姓名")
    private String appVisitDocName;

    @Schema(description = "挂号人员")
    private String createdStaff;

    @Schema(description = "挂号人员姓名")
    private String createdStaffName;

    @Schema(description = "患者姓名")
    private String name;

    @Schema(description = "性别")
    private String sex;

    @Schema(description = "性别名称")
    private String sexName;

    @Schema(description = "出生日期")
    private LocalDateTime birthDate;

    @Schema(description = "主索引号")
    private String patCode;

    @Schema(description = "接诊医生编码")
    private String visitDocCode;

    @Schema(description = "接诊医生姓名")
    private String visitDocName;

    @Schema(description = "接诊时间")
    private LocalDateTime visitDateTime;

    @Schema(description = "退号人员")
    private String returnStaff;

    @Schema(description = "退号人员姓名")
    private String returnStaffName;

    @Schema(description = "退号时间")
    private LocalDateTime returnDateTime;

    @Schema(description = "挂号预约状态")
    private PatRegistStasEnum registStas;

    @Schema(description = "就诊状态")
    private String visitStatus;

    @Schema(description = "结算单标识(结算单id)")
    private String econOpdSettleId;

    @Schema(description = "原结算单标识(结算单initId)")
    private String initEconOpdSettleId;

    public String getAppVisitDeptCode() {
        return appVisitDeptCode;
    }

    public void setAppVisitDeptCode(String appVisitDeptCode) {
        this.appVisitDeptCode = appVisitDeptCode;
    }

    public String getAppVisitDeptName() {
        return appVisitDeptName;
    }

    public void setAppVisitDeptName(String appVisitDeptName) {
        this.appVisitDeptName = appVisitDeptName;
    }

    public String getAppVisitDocCode() {
        return appVisitDocCode;
    }

    public void setAppVisitDocCode(String appVisitDocCode) {
        this.appVisitDocCode = appVisitDocCode;
    }

    public String getAppVisitDocName() {
        return appVisitDocName;
    }

    public void setAppVisitDocName(String appVisitDocName) {
        this.appVisitDocName = appVisitDocName;
    }

    public LocalDateTime getVisitDateTime() {
        return visitDateTime;
    }

    public void setVisitDateTime(LocalDateTime visitDateTime) {
        this.visitDateTime = visitDateTime;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getSettleNo() {
        return settleNo;
    }

    public void setSettleNo(String settleNo) {
        this.settleNo = settleNo;
    }

    public SetlStasEnum getSetlStas() {
        return setlStas;
    }

    public void setSetlStas(SetlStasEnum setlStas) {
        this.setlStas = setlStas;
    }

    public SetlPayStasEnum getPayFlag() {
        return payFlag;
    }

    public void setPayFlag(SetlPayStasEnum payFlag) {
        this.payFlag = payFlag;
    }

    public String getRegistrationLv() {
        return registrationLv;
    }

    public void setRegistrationLv(String registrationLv) {
        this.registrationLv = registrationLv;
    }

    public String getRegistrationLvName() {
        return registrationLvName;
    }

    public void setRegistrationLvName(String registrationLvName) {
        this.registrationLvName = registrationLvName;
    }

    public String getVisitDocCode() {
        return visitDocCode;
    }

    public void setVisitDocCode(String visitDocCode) {
        this.visitDocCode = visitDocCode;
    }

    public String getVisitDocName() {
        return visitDocName;
    }

    public void setVisitDocName(String visitDocName) {
        this.visitDocName = visitDocName;
    }

    public String getCreatedStaff() {
        return createdStaff;
    }

    public void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    public String getCreatedStaffName() {
        return createdStaffName;
    }

    public void setCreatedStaffName(String createdStaffName) {
        this.createdStaffName = createdStaffName;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getPatCode() {
        return patCode;
    }

    public void setPatCode(String patCode) {
        this.patCode = patCode;
    }

    public String getReturnStaff() {
        return returnStaff;
    }

    public void setReturnStaff(String returnStaff) {
        this.returnStaff = returnStaff;
    }

    public String getReturnStaffName() {
        return returnStaffName;
    }

    public void setReturnStaffName(String returnStaffName) {
        this.returnStaffName = returnStaffName;
    }

    public LocalDateTime getReturnDateTime() {
        return returnDateTime;
    }

    public void setReturnDateTime(LocalDateTime returnDateTime) {
        this.returnDateTime = returnDateTime;
    }

    public PatRegistStasEnum getRegistStas() {
        return registStas;
    }

    public void setRegistStas(PatRegistStasEnum registStas) {
        this.registStas = registStas;
    }

    public LocalDateTime getBirthDate() {
        return birthDate;
    }

    public void setBirthDate(LocalDateTime birthDate) {
        this.birthDate = birthDate;
    }

    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getEconOpdSettleId() {
        return econOpdSettleId;
    }

    public void setEconOpdSettleId(String econOpdSettleId) {
        this.econOpdSettleId = econOpdSettleId;
    }

    public String getInitEconOpdSettleId() {
        return initEconOpdSettleId;
    }

    public void setInitEconOpdSettleId(String initEconOpdSettleId) {
        this.initEconOpdSettleId = initEconOpdSettleId;
    }

    public String getVisitStatus() {
        return visitStatus;
    }

    public void setVisitStatus(String visitStatus) {
        this.visitStatus = visitStatus;
    }

    public String getSexName() {
        return sexName;
    }

    public void setSexName(String sexName) {
        this.sexName = sexName;
    }
}
