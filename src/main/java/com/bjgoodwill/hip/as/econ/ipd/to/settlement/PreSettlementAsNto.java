package com.bjgoodwill.hip.as.econ.ipd.to.settlement;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: zhangyunchuan
 * @Date: 2025/2/13 11:32
 * @PROJECT: hip-ac
 */
@Schema(description = "住院结算-预结算")
public class PreSettlementAsNto implements Serializable {

    @Schema(description = "费别")
    private String feeType;

    @Schema(description = "流水号")
    private String visitCode;

    @Schema(description = "离院方式")
    private String dscgWay;

    @Schema(description = "使用账户 1:使用,0不使用")
    private String acctUsedFlag;

    @Schema(description = "结算方式")
    private String psnSetlway;

    @Schema(description = "读卡方式")
    private String mdtrtCertType;

    @Schema(description = "医疗费总额")
    private BigDecimal settleAmount;

    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    public String getDscgWay() {
        return dscgWay;
    }

    public void setDscgWay(String dscgWay) {
        this.dscgWay = dscgWay;
    }

    public String getPsnSetlway() {
        return psnSetlway;
    }

    public void setPsnSetlway(String psnSetlway) {
        this.psnSetlway = psnSetlway;
    }

    public String getMdtrtCertType() {
        return mdtrtCertType;
    }

    public void setMdtrtCertType(String mdtrtCertType) {
        this.mdtrtCertType = mdtrtCertType;
    }

    public BigDecimal getSettleAmount() {
        return settleAmount;
    }

    public void setSettleAmount(BigDecimal settleAmount) {
        this.settleAmount = settleAmount;
    }

    public String getAcctUsedFlag() {
        return acctUsedFlag;
    }

    public void setAcctUsedFlag(String acctUsedFlag) {
        this.acctUsedFlag = acctUsedFlag;
    }

    public String getFeeType() {
        return feeType;
    }

    public void setFeeType(String feeType) {
        this.feeType = feeType;
    }
}
