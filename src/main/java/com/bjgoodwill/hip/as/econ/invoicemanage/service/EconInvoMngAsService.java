package com.bjgoodwill.hip.as.econ.invoicemanage.service;

import com.bjgoodwill.hip.as.econ.invoicemanage.to.EconInvoMngAsTo;
import com.bjgoodwill.hip.as.econ.invoicemanage.to.EconInvoiceManageJumpNoAsEto;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.util.List;

/**
 * Description:
 *
 * <AUTHOR>
 * &#064;date 2025/5/27 上午9:40
 */
@Tag(name = "发票维护应用服务接口", description = "发票维护应用服务接口")
public interface EconInvoMngAsService {

    // 门诊发票：获取当前登录人已分配未使用的门诊发票组集合
    List<EconInvoMngAsTo> getOpdEconInvoMngsByStaffId();

    // 门诊发票：获取当前登录人使用中的门诊发票组
    EconInvoMngAsTo getOpdUsingEconInvoMngsByStaffId();

    // 住院发票：获取当前登录人已分配未使用的住院发票组集合
    List<EconInvoMngAsTo> getIpdEconInvoMngsByStaffId();

    // 住院发票：获取当前登录人使用中的住院发票组
    EconInvoMngAsTo getIpdUsingEconInvoMngsByStaffId();

    // 启用发票组
    EconInvoMngAsTo enable(String id);

    // 停用发票组
    EconInvoMngAsTo deactivate(String id);

    // 跳号(依次)
    EconInvoMngAsTo jumpNo(String econInvoMngId);

    // 跳转到指定号
    EconInvoMngAsTo jumpToNo(String id, EconInvoiceManageJumpNoAsEto eto);
}
