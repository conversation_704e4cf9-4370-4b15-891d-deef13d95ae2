package com.bjgoodwill.hip.as.econ.ipd.to.invoiceQuery;

import com.bjgoodwill.hip.ds.pat.in.hospital.inpatient.to.PatIpdInpatientTo;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @description :
 * @createDate : 2025/4/9 14:34
 */
public class InvoicePatientShowTo extends PatIpdInpatientTo {

    @Schema(description = "电话")
    private String tel;

    @Schema(description = "证件号")
    private String cardCode;

    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    public String getCardCode() {
        return cardCode;
    }

    public void setCardCode(String cardCode) {
        this.cardCode = cardCode;
    }
}
