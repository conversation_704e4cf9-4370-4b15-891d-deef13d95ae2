package com.bjgoodwill.hip.as.econ.opd.service.impl;

import com.bjgoodwill.hip.as.econ.opd.enums.EconOpdBusinessErrorEnum;
import com.bjgoodwill.hip.as.econ.opd.service.EconOpdPricingAsService;
import com.bjgoodwill.hip.as.econ.opd.to.pricing.EconOpdOrderPricingAsTo;
import com.bjgoodwill.hip.as.econ.opd.to.pricing.EconOpdPricingAsNto;
import com.bjgoodwill.hip.as.econ.opd.to.registration.window.EstablishMedicalRecordAsNto;
import com.bjgoodwill.hip.as.econ.opd.to.registration.window.PatientInquiryAsQto;
import com.bjgoodwill.hip.common.exception.BusinessException;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.common.util.HIPBeanUtil;
import com.bjgoodwill.hip.common.util.HIPIDUtil;
import com.bjgoodwill.hip.ds.cis.opdcpoe.treatment.service.CisCommonTreatmentService;
import com.bjgoodwill.hip.ds.cis.opdcpoe.treatment.to.CisCommonTreatmentNto;
import com.bjgoodwill.hip.ds.cis.opdcpoe.treatment.to.CisCommonTreatmentTo;
import com.bjgoodwill.hip.ds.econ.opd.bill.bill.enmus.EconOpdOrderClassEnum;
import com.bjgoodwill.hip.ds.econ.opd.bill.order.service.EconOpdOrderService;
import com.bjgoodwill.hip.ds.econ.opd.bill.order.to.*;
import com.bjgoodwill.hip.ds.org.api.service.StaffService;
import com.bjgoodwill.hip.ds.org.api.service.WorkGroupService;
import com.bjgoodwill.hip.ds.org.api.to.*;
import com.bjgoodwill.hip.ds.param.api.service.ParameterService;
import com.bjgoodwill.hip.ds.param.api.to.ParameterTo;
import com.bjgoodwill.hip.ds.pat.index.service.PatIndexService;
import com.bjgoodwill.hip.ds.pat.index.to.*;
import com.bjgoodwill.hip.ds.pat.regist.fee.service.PatOpdFeeService;
import com.bjgoodwill.hip.ds.pat.regist.fee.to.PatOpdFeeQto;
import com.bjgoodwill.hip.ds.pat.regist.fee.to.PatOpdFeeTo;
import com.bjgoodwill.hip.ds.pat.regist.regist.enmus.PatRegistStasEnum;
import com.bjgoodwill.hip.ds.pat.regist.regist.enmus.PatRegistWayEnum;
import com.bjgoodwill.hip.ds.pat.regist.regist.service.PatRegistService;
import com.bjgoodwill.hip.ds.pat.regist.regist.to.PatRegistQto;
import com.bjgoodwill.hip.ds.pat.regist.regist.to.PatRegistSystemResourceNto;
import com.bjgoodwill.hip.ds.pat.regist.regist.to.PatRegistTo;
import com.bjgoodwill.hip.enums.DictParameterEnum;
import com.bjgoodwill.hip.security.bean.CurrentOrgInfo;
import com.bjgoodwill.hip.security.util.HIPCurrentOrgUtils;
import com.bjgoodwill.hip.security.util.HIPSecurityUtils;
import io.micrometer.common.util.StringUtils;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.apache.seata.spring.annotation.GlobalTransactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Period;
import java.util.ArrayList;
import java.util.List;

/**
 * File: EconOpdPricingAsServiceImpl
 * Author: zhangyunchuan
 * Date: 2025/6/30
 * Description:
 */
@Service("com.bjgoodwill.hip.as.econ.opd.service.EconOpdPricingAsService")
public class EconOpdPricingAsServiceImpl implements EconOpdPricingAsService {

    @Autowired
    private PatRegistService patRegistService;

    @Autowired
    private PatIndexService patIndexService;

    @Resource
    private ParameterService parameterService;

    @Autowired
    private EconOpdOrderService econOpdOrderService;

    @Autowired
    private PatOpdFeeService patOpdFeeService;

    @Autowired
    private CisCommonTreatmentService cisCommonTreatmentService;

    @Autowired
    private StaffService staffService;

    @Autowired
    private WorkGroupService workGroupService;

    //权重系数数组
    private static final int[] WEIGHT = {7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2};

    //校验码对应表
    private static final char[] CHECK_CODES = {'1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'};

    @Override
    public List<WorkGroupTo> getWorkGroupList() {
        CurrentOrgInfo currentOrgInfo = HIPCurrentOrgUtils.getCurrentOrgInfo();
        String hospitalAreaCode = currentOrgInfo.getHospitalAreaCode();
        //查询门诊出诊工作组
        WorkGroupOutpatientDeptQto outpatientDeptQto = new WorkGroupOutpatientDeptQto();
        outpatientDeptQto.setHospitalAreaCode(hospitalAreaCode);
        List<WorkGroupOutpatientDeptTo> outpatientDept = workGroupService.getOutpatientDept(outpatientDeptQto);

        //查询急诊出诊工作组
        WorkGroupEmergencyDeptQto emergencyDeptQto = new WorkGroupEmergencyDeptQto();
        emergencyDeptQto.setHospitalAreaCode(hospitalAreaCode);
        List<WorkGroupEmergencyDeptTo> emergencyDept = workGroupService.getEmergencyDept(emergencyDeptQto);

        List<WorkGroupTo> workGroupTos = new ArrayList<>();
        for (WorkGroupOutpatientDeptTo workGroupOutpatientDeptTo : outpatientDept) {
            WorkGroupTo workGroupTo = new WorkGroupTo();
            workGroupTo.setId(workGroupOutpatientDeptTo.getId());
            workGroupTo.setName(workGroupOutpatientDeptTo.getName());
            workGroupTo.setHospitalAreaCode(workGroupOutpatientDeptTo.getHospitalAreaCode());
            workGroupTo.setHospitalAreaName(workGroupOutpatientDeptTo.getHospitalAreaName());
            workGroupTo.setDeptCode(workGroupOutpatientDeptTo.getDeptCode());
            workGroupTo.setInputPy(workGroupOutpatientDeptTo.getInputPy());
            workGroupTo.setWbCode(workGroupOutpatientDeptTo.getWbCode());
            workGroupTo.setDeptName(workGroupOutpatientDeptTo.getDeptName());
            workGroupTos.add(workGroupTo);
        }

        for (WorkGroupEmergencyDeptTo workGroupEmergencyDeptTo : emergencyDept) {
            WorkGroupTo workGroupTo = new WorkGroupTo();
            workGroupTo.setId(workGroupEmergencyDeptTo.getId());
            workGroupTo.setName(workGroupEmergencyDeptTo.getName());
            workGroupTo.setHospitalAreaCode(workGroupEmergencyDeptTo.getHospitalAreaCode());
            workGroupTo.setHospitalAreaName(workGroupEmergencyDeptTo.getHospitalAreaName());
            workGroupTo.setDeptCode(workGroupEmergencyDeptTo.getDeptCode());
            workGroupTo.setInputPy(workGroupEmergencyDeptTo.getInputPy());
            workGroupTo.setWbCode(workGroupEmergencyDeptTo.getWbCode());
            workGroupTo.setDeptName(workGroupEmergencyDeptTo.getDeptName());
            workGroupTos.add(workGroupTo);
        }
        return workGroupTos;
    }

    @Override
    public PatIndexTo pricingEstablishMedicalRecord(EstablishMedicalRecordAsNto establishMedicalRecordNto) {
        //验证患者信息是否重复
        PatCardQto qto = new PatCardQto();
        qto.setType(establishMedicalRecordNto.getCardType());
        qto.setCode(establishMedicalRecordNto.getCardCode());
        List<PatIndexTo> patIndexes = patIndexService.getPatIndexListByCard(qto);
        if (CollectionUtils.isNotEmpty(patIndexes)) {
            throw new BusinessException(EconOpdBusinessErrorEnum.ECON_OPD_0003, "患者已经存在档案信息");
        }
        String indexCode = patIndexService.getIndexCode();
        BusinessAssert.notNull(indexCode, EconOpdBusinessErrorEnum.ECON_OPD_0001, "患者主索引");
        PatIndexNto patIndexNto = HIPBeanUtil.copy(establishMedicalRecordNto, PatIndexNto.class);
        patIndexNto.setCode(indexCode);
        patIndexNto.setThreeNoPersionnelFlag(false);
        if (StringUtils.isNotEmpty(establishMedicalRecordNto.getStaffFlag())) {
            if (establishMedicalRecordNto.getStaffFlag().equals("1")) {
                patIndexNto.setStaffFlag(true);
            } else {
                patIndexNto.setStaffFlag(false);
            }
        } else {
            patIndexNto.setStaffFlag(false);
        }
        if (Period.between(establishMedicalRecordNto.getBirthDate().toLocalDate(), LocalDate.now()).getYears() < 16) {
            BusinessAssert.notNull(patIndexNto.getGuardianName(), EconOpdBusinessErrorEnum.ECON_OPD_0002, "监护人名称");
            BusinessAssert.notNull(patIndexNto.getGuardianType(), EconOpdBusinessErrorEnum.ECON_OPD_0002, "监护人类型");
            BusinessAssert.notNull(patIndexNto.getGuardianTel(), EconOpdBusinessErrorEnum.ECON_OPD_0002, "监护人联系电话");
        }
        //生成主索引信息
        PatIndexTo patIndex = patIndexService.createPatIndex(patIndexNto);
        if (patIndex != null) {
            //生成身份证号卡表信息
            PatCardNto idCardNto = new PatCardNto();
            idCardNto.setId(HIPIDUtil.getNextIdString());
            idCardNto.setType(establishMedicalRecordNto.getCardType());
            idCardNto.setCode(establishMedicalRecordNto.getCardCode());
            patIndexService.createPatCard(indexCode, idCardNto);
            //生成就诊卡号卡表信息
            if (StringUtils.isNotBlank(establishMedicalRecordNto.getMedicalCardNumber())) {
                PatCardQto patCardQto = new PatCardQto();
                patCardQto.setType("MED_ID");
                patCardQto.setCode(establishMedicalRecordNto.getMedicalCardNumber());
                List<PatCardTo> patCards = patIndexService.getPatCards(patCardQto);
                if (CollectionUtils.isEmpty(patCards)) {
                    PatCardNto medCardNto = new PatCardNto();
                    medCardNto.setId(HIPIDUtil.getNextIdString());
                    medCardNto.setType("MED_ID");
                    medCardNto.setCode(establishMedicalRecordNto.getMedicalCardNumber());
                    patIndexService.createPatCard(indexCode, medCardNto);
                }
            }
        } else {
            throw new BusinessException(EconOpdBusinessErrorEnum.ECON_OPD_0003, "生成主索引信息失败");
        }
        return patIndex;
    }

    @Override
    public List<PatIndexTo> patientInformationQuery(PatientInquiryAsQto patientInquiryQto) {
        List<PatIndexTo> patIndexToList = new ArrayList<>();
        if (StringUtils.isNotEmpty(patientInquiryQto.getSearchCriteria()) && StringUtils.isNotEmpty(patientInquiryQto.getSearchKeyword())) {
            if (patientInquiryQto.getSearchCriteria().equals("1")) {
                PatCardQto qto = new PatCardQto();
                qto.setType("1");
                qto.setCode(patientInquiryQto.getSearchKeyword());
                List<PatIndexTo> patIndexes = patIndexService.getPatIndexListByCard(qto);
                if (CollectionUtils.isNotEmpty(patIndexes)) {
                    patIndexToList.addAll(patIndexes);
                }
            } else if (patientInquiryQto.getSearchCriteria().equals("tel")) {
                PatIndexQto patIndexQto = new PatIndexQto();
                patIndexQto.setTel(patientInquiryQto.getSearchKeyword());
                List<PatIndexTo> patIndexes = patIndexService.getPatIndexes(patIndexQto);
                if (CollectionUtils.isNotEmpty(patIndexes)) {
                    patIndexToList.addAll(patIndexes);
                }
            } else if (patientInquiryQto.getSearchCriteria().equals("name")) {
                PatIndexQto patIndexQto = new PatIndexQto();
                patIndexQto.setName(patientInquiryQto.getSearchKeyword());
                List<PatIndexTo> patIndexes = patIndexService.getPatIndexes(patIndexQto);
                if (CollectionUtils.isNotEmpty(patIndexes)) {
                    patIndexToList.addAll(patIndexes);
                }
            } else if (patientInquiryQto.getSearchCriteria().equals("medCard")) {
                PatCardQto qto = new PatCardQto();
                qto.setType("MED_ID");
                qto.setCode(patientInquiryQto.getSearchKeyword());
                List<PatIndexTo> patIndexes = patIndexService.getPatIndexListByCard(qto);
                if (CollectionUtils.isNotEmpty(patIndexes)) {
                    patIndexToList.addAll(patIndexes);
                }
            }
        } else if (StringUtils.isNotEmpty(patientInquiryQto.getAutoSearchValue())) {
            if (isValidIdCard(patientInquiryQto.getAutoSearchValue())) {
                PatCardQto qto = new PatCardQto();
                qto.setType("1");
                qto.setCode(patientInquiryQto.getAutoSearchValue());
                qto.setEnabled(true);
                List<PatIndexTo> patIndexes = patIndexService.getPatIndexListByCard(qto);
                if (CollectionUtils.isNotEmpty(patIndexes)) {
                    patIndexToList.addAll(patIndexes);
                }
            } else if (isValidPhoneNumber(patientInquiryQto.getAutoSearchValue())) {
                PatIndexQto patIndexQto = new PatIndexQto();
                patIndexQto.setTel(patientInquiryQto.getAutoSearchValue());
                List<PatIndexTo> patIndexes = patIndexService.getPatIndexes(patIndexQto);
                if (CollectionUtils.isNotEmpty(patIndexes)) {
                    patIndexToList.addAll(patIndexes);
                }
            } else if (isValidChinese(patientInquiryQto.getAutoSearchValue())) {
                PatIndexQto patIndexQto = new PatIndexQto();
                patIndexQto.setName(patientInquiryQto.getAutoSearchValue());
                List<PatIndexTo> patIndexes = patIndexService.getPatIndexes(patIndexQto);
                if (CollectionUtils.isNotEmpty(patIndexes)) {
                    patIndexToList.addAll(patIndexes);
                }
            } else {
                PatCardQto qto = new PatCardQto();
                qto.setType("MED_ID");
                qto.setCode(patientInquiryQto.getAutoSearchValue());
                qto.setEnabled(true);
                List<PatIndexTo> patIndexes = patIndexService.getPatIndexListByCard(qto);
                if (CollectionUtils.isNotEmpty(patIndexes)) {
                    patIndexToList.addAll(patIndexes);
                }
            }
        } else {
            throw new BusinessException(EconOpdBusinessErrorEnum.ECON_OPD_0002, "查询条件");
        }
        return patIndexToList;
    }

    @Override
    public List<PatRegistTo> registrationInquiry(String patCode) {
        List<PatRegistTo> patRegistToList = new ArrayList<>();
        PatRegistQto patRegistQto = new PatRegistQto();
        patRegistQto.setPatCode(patCode);
        List<PatRegistStasEnum> registStasList = new ArrayList<>();
        registStasList.add(PatRegistStasEnum.已接诊);
        registStasList.add(PatRegistStasEnum.已挂号);
        patRegistQto.setRegistStasList(registStasList);
        patRegistQto.setMiType("08");
        List<PatRegistTo> registToList = patRegistService.getPatRegists(patRegistQto);
        if (CollectionUtils.isNotEmpty(registToList)) {
            ParameterTo<String> econRegistrationValidityPeriod = parameterService.getStringParameter(DictParameterEnum.EconRegistrationValidityPeriod.getCode());
            if (econRegistrationValidityPeriod != null) {
                for (PatRegistTo patRegistTo : registToList) {
                    if (patRegistTo.getCreatedDate().plusDays(Long.parseLong(econRegistrationValidityPeriod.getValue())).isAfter(LocalDateTime.now())) {
                        patRegistToList.add(patRegistTo);
                    }
                }
            } else {
                throw new BusinessException("");
            }
        }
        return patRegistToList;
    }

    @Override
    @GlobalTransactional
    public PatRegistTo systemRegistration(String patCode) {
        PatIndexTo patIndexById = patIndexService.getPatIndexById(patCode);
        if (patIndexById != null) {
            PatRegistSystemResourceNto patRegistrationNto = new PatRegistSystemResourceNto();
            PatOpdFeeQto qto = new PatOpdFeeQto();
            qto.setMedicalPayMethod("08");
            List<PatOpdFeeTo> patOpdFees = patOpdFeeService.getPatOpdFees(qto);
            if (CollectionUtils.isNotEmpty(patOpdFees)) {
                if (patOpdFees.size() == 1) {
                    patRegistrationNto.setHisMiType(patOpdFees.get(0).getId());
                } else {
                    throw new BusinessException("查询出多条门诊费别信息，请联系管理员");
                }
            } else {
                throw new BusinessException("未查询出门诊费别信息，请联系管理员");
            }
            patRegistrationNto.setRegistWay(PatRegistWayEnum.划价免费号);
            patRegistrationNto.setPatCode(patCode);
            patRegistrationNto.setName(patIndexById.getName());
            patRegistrationNto.setAppVisitDate(LocalDateTime.now());
            patRegistrationNto.setAppVisitDeptCode("999000999");
            patRegistrationNto.setAppVisitDeptName("便民门诊");
            patRegistrationNto.setAppVisitDocCode(HIPSecurityUtils.getLoginInfo().getStaffId());
            patRegistrationNto.setAppVisitDocName(HIPSecurityUtils.getLoginInfo().getName());
            patRegistrationNto.setRegistrationLv("99");
            patRegistrationNto.setHospitalAreaCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getHospitalAreaCode());
            patRegistrationNto.setHospitalAreaName(HIPCurrentOrgUtils.getCurrentOrgInfo().getHospitalAreaName());
            PatRegistTo patRegistTo = patRegistService.registSystemResource(patRegistrationNto);
            CisCommonTreatmentNto cisCommonTreatmentNto = new CisCommonTreatmentNto();
            cisCommonTreatmentNto.setId(HIPIDUtil.getNextIdString());
            cisCommonTreatmentNto.setPatMiCode(patCode);
            cisCommonTreatmentNto.setVisitCode(patRegistTo.getVisitCode());
            cisCommonTreatmentNto.setOrgCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
            cisCommonTreatmentNto.setDocCode(HIPSecurityUtils.getLoginUserId());
            cisCommonTreatmentNto.setFareCode(patOpdFees.get(0).getId());
            cisCommonTreatmentNto.setFareName(patOpdFees.get(0).getName());
            cisCommonTreatmentNto.setTreatmentTypeCode(patOpdFees.get(0).getTreatmentType());
            cisCommonTreatmentNto.setTreatmentTypeName(patOpdFees.get(0).getTreatmentTypeName());
            CisCommonTreatmentTo cisCommonTreatment = cisCommonTreatmentService.createCisCommonTreatment(cisCommonTreatmentNto);
            return patRegistTo;
        } else {
            throw new BusinessException("未查询到患者信息!");
        }
    }

    @Override
    public List<EconOpdOrderPricingAsTo> pricedDataQuery(String visitCode) {
        List<EconOpdOrderPricingAsTo> econOpdOrderPricingAsToList = new ArrayList<>();
        EconOpdOrderQto econOpdOrderQto = new EconOpdOrderQto();
        econOpdOrderQto.setVisitCode(visitCode);
        econOpdOrderQto.setOrderClass(EconOpdOrderClassEnum.门诊划价订单);
        econOpdOrderQto.setSetlFlag(false);
        econOpdOrderQto.setPayFlag(false);
        List<EconOpdOrderTo> econOpdOrders = econOpdOrderService.getEconOpdOrders(econOpdOrderQto);
        if (CollectionUtils.isNotEmpty(econOpdOrders)) {
            for (EconOpdOrderTo econOpdOrderTo : econOpdOrders) {
                EconOpdOrderPricingAsTo econOpdOrderPricingAsTo = new EconOpdOrderPricingAsTo();
                econOpdOrderPricingAsTo.setId(econOpdOrderTo.getId());
                econOpdOrderPricingAsTo.setPatCode(econOpdOrderTo.getPatCode());
                econOpdOrderPricingAsTo.setVisitCode(econOpdOrderTo.getVisitCode());
                econOpdOrderPricingAsTo.setPrscDeptName(econOpdOrderTo.getPrscDeptName());
                econOpdOrderPricingAsTo.setPrscDrName(econOpdOrderTo.getPrscDrName());
                econOpdOrderPricingAsTo.setExecDeptName(econOpdOrderTo.getExecDeptName());
                for (EconOpdOrderDetailTo econOpdOrderDetailTo : econOpdOrderTo.getEconOpdOrderDetails()) {
                    econOpdOrderPricingAsTo.setPriceItemCode(econOpdOrderDetailTo.getPriceItemCode());
                    econOpdOrderPricingAsTo.setPriceItemName(econOpdOrderDetailTo.getPriceItemName());
                    econOpdOrderPricingAsTo.setPacSpec(econOpdOrderDetailTo.getPacSpec());
                    econOpdOrderPricingAsTo.setPacUnit(econOpdOrderDetailTo.getPacUnit());
                    econOpdOrderPricingAsTo.setPacCnt(econOpdOrderDetailTo.getPacCnt());
                    econOpdOrderPricingAsTo.setPacPrice(econOpdOrderDetailTo.getPacPrice());
                    econOpdOrderPricingAsTo.setNum(econOpdOrderDetailTo.getNum());
                    econOpdOrderPricingAsTo.setReceiptsAmt(econOpdOrderDetailTo.getReceiptsAmt());
                    econOpdOrderPricingAsToList.add(econOpdOrderPricingAsTo);
                }
            }
        }
        return econOpdOrderPricingAsToList;
    }

    @Override
    public void deleteEconOpdOrder(List<String> ids) {
        econOpdOrderService.deleteEconOpdOrders(ids);
    }

    @Override
    public void priceQuotation(List<EconOpdPricingAsNto> econOpdPricingAsNtoList) {
        List<EconOpdOrderChargeNto> econOpdOrderChargeNtoList = new ArrayList<>();
        for (EconOpdPricingAsNto econOpdPricingAsNto : econOpdPricingAsNtoList) {
            EconOpdOrderChargeNto econOpdOrderChargeNto = new EconOpdOrderChargeNto();
            econOpdOrderChargeNto.setPatCode(econOpdPricingAsNto.getPatCode());
            econOpdOrderChargeNto.setVisitCode(econOpdPricingAsNto.getVisitCode());
            econOpdOrderChargeNto.setPrscDr(econOpdPricingAsNto.getPrscDr());
            econOpdOrderChargeNto.setPrscDrName(econOpdPricingAsNto.getPrscDrName());
            StaffLocalTo staffLocal = staffService.getStaffLocal(econOpdPricingAsNto.getPrscDr());
            if (staffLocal != null) {
                econOpdOrderChargeNto.setPrscDrDept(staffLocal.getAppointmentDept());
                econOpdOrderChargeNto.setPrscDrDeptName(staffLocal.getAppointmentDeptName());
            } else {
                econOpdOrderChargeNto.setPrscDrDept(econOpdPricingAsNto.getPrscDept());
                econOpdOrderChargeNto.setPrscDrDeptName(econOpdPricingAsNto.getPrscDeptName());
            }
            econOpdOrderChargeNto.setPrscDept(econOpdPricingAsNto.getPrscDept());
            econOpdOrderChargeNto.setPrscDeptName(econOpdPricingAsNto.getPrscDeptName());
            econOpdOrderChargeNto.setExecDept(econOpdPricingAsNto.getExecDept());
            econOpdOrderChargeNto.setExecDeptName(econOpdPricingAsNto.getExecDeptName());
            econOpdOrderChargeNto.setDoseNum(String.valueOf(econOpdPricingAsNto.getNum()));
            econOpdOrderChargeNto.setOrderClass(EconOpdOrderClassEnum.门诊划价订单);
            econOpdOrderChargeNto.setHospitalAreaCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getHospitalAreaCode());
            econOpdOrderChargeNto.setHospitalAreaName(HIPCurrentOrgUtils.getCurrentOrgInfo().getHospitalAreaName());
            List<EconOpdOrderDetailChargeNto> detailNtoList = new ArrayList<>();
            EconOpdOrderDetailChargeNto econOpdOrderDetailChargeNto = new EconOpdOrderDetailChargeNto();
            econOpdOrderDetailChargeNto.setId(HIPIDUtil.getNextIdString());
            econOpdOrderDetailChargeNto.setServiceItemCode(econOpdPricingAsNto.getPriceItemCode());
            econOpdOrderDetailChargeNto.setServiceItemName(econOpdPricingAsNto.getPriceItemName());
            econOpdOrderDetailChargeNto.setPriceItemCode(econOpdPricingAsNto.getPriceItemCode());
            econOpdOrderDetailChargeNto.setPriceItemName(econOpdPricingAsNto.getPriceItemName());
            econOpdOrderDetailChargeNto.setHighvalMcsNo(econOpdPricingAsNto.getHighvalMcsNo());
            econOpdOrderDetailChargeNto.setSystemItemClass(econOpdPricingAsNto.getSystemItemClass());
            econOpdOrderDetailChargeNto.setFixedFlag(econOpdPricingAsNto.getFixedFlag());
            econOpdOrderDetailChargeNto.setPackagePrice(econOpdPricingAsNto.getPackagePrice());
            econOpdOrderDetailChargeNto.setNum(econOpdPricingAsNto.getNum());
            econOpdOrderDetailChargeNto.setMiApprFlag(true);
            detailNtoList.add(econOpdOrderDetailChargeNto);
            econOpdOrderChargeNto.setDetailNtoList(detailNtoList);
            econOpdOrderChargeNtoList.add(econOpdOrderChargeNto);
        }
        econOpdOrderService.createEconOpdOrders(econOpdOrderChargeNtoList);
    }

    public static boolean isValidIdCard(String idNumber) {
        // 基础格式验证
        if (idNumber == null || !idNumber.matches("^\\d{17}[\\dXx]$")) {
            return false;
        }
        // 转换大写处理最后位的X
        idNumber = idNumber.toUpperCase();
        // 校验码验证
        int sum = 0;
        for (int i = 0; i < 17; i++) {
            sum += (idNumber.charAt(i) - '0') * WEIGHT[i];
        }
        char checkCode = CHECK_CODES[sum % 11];
        return checkCode == idNumber.charAt(17);
    }

    public static boolean isValidPhoneNumber(String phone) {
        //判断1开头的十一位数字
        return phone != null && phone.matches("^1[0-9]\\d{9}$");
    }

    public static boolean isValidChinese(String input) {
        return input != null && input.matches("^[\u4E00-\u9FA5]+$");
    }
}

