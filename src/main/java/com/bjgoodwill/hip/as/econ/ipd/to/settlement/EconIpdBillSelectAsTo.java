package com.bjgoodwill.hip.as.econ.ipd.to.settlement;

import com.bjgoodwill.hip.ds.econ.ipd.bill.to.EconIpdBillAllTo;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.List;

/**
 * File: EconIpdBillSelectAsTo
 * Author: zhangyunchuan
 * Date: 2025/3/26
 * Description:
 */
public class EconIpdBillSelectAsTo {

    @Schema(description = "收费时间")
    private String chargingDate;

    @Schema(description = "实收总金额")
    private BigDecimal costsSum;

    @Schema(description = "应收总金额")
    private BigDecimal amountSum;

    @Schema(description = "已结算金额")
    private BigDecimal settleAmountSum;

    @Schema(description = "未结算金额")
    private BigDecimal unSettleAmountSum;

    @Schema(description = "账单明细")
    private List<EconIpdBillAllTo> econIpdBillAllToList;

    public BigDecimal getCostsSum() {
        return costsSum;
    }

    public void setCostsSum(BigDecimal costsSum) {
        this.costsSum = costsSum;
    }

    public BigDecimal getAmountSum() {
        return amountSum;
    }

    public void setAmountSum(BigDecimal amountSum) {
        this.amountSum = amountSum;
    }

    public BigDecimal getSettleAmountSum() {
        return settleAmountSum;
    }

    public void setSettleAmountSum(BigDecimal settleAmountSum) {
        this.settleAmountSum = settleAmountSum;
    }

    public BigDecimal getUnSettleAmountSum() {
        return unSettleAmountSum;
    }

    public void setUnSettleAmountSum(BigDecimal unSettleAmountSum) {
        this.unSettleAmountSum = unSettleAmountSum;
    }

    public List<EconIpdBillAllTo> getEconIpdBillAllToList() {
        return econIpdBillAllToList;
    }

    public void setEconIpdBillAllToList(List<EconIpdBillAllTo> econIpdBillAllToList) {
        this.econIpdBillAllToList = econIpdBillAllToList;
    }

    public String getChargingDate() {
        return chargingDate;
    }

    public void setChargingDate(String chargingDate) {
        this.chargingDate = chargingDate;
    }
}
