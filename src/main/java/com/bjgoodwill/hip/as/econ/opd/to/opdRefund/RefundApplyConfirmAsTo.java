package com.bjgoodwill.hip.as.econ.opd.to.opdRefund;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * File: RefundApplyConfirmAsTo
 * Author: zhangyunchuan
 * Date: 2025/6/9
 * Description:
 */
@Schema(description = "申请退费项目")
public class RefundApplyConfirmAsTo implements Serializable {

    @Schema(description = "订单号")
    private String econOrderId;

    @Schema(description = "医嘱号")
    private String orderId;

    @Schema(description = "执行/取药科室编码")
    private String execDept;

    @Schema(description = "执行/取药科室名称")
    private String execDeptName;

    @Schema(description = "项目名称")
    private String itemName;

    @Schema(description = "项目编码")
    private String itemCode;

    @Schema(description = "包装规格")
    private String pacSpec;

    @Schema(description = "单价")
    private BigDecimal price;

    @Schema(description = "单位")
    private String unit;

    @Schema(description = "单位名称")
    private String unitName;

    @Schema(description = "退费数量")
    private Double num;

    @Schema(description = "退费单位")
    private String chargeUnit;

    @Schema(description = "退费单位名称")
    private String chargeUnitName;

    @Schema(description = "待退金额")
    private BigDecimal receiptsAmt;

    @Schema(description = "说明")
    private String content;

    @Schema(description = "退费申请人")
    private String reApplyStaff;

    @Schema(description = "退费申请人姓名")
    private String reApplyStaffName;

    @Schema(description = "退费申请时间")
    private LocalDateTime reApplyDate;

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getExecDept() {
        return execDept;
    }

    public void setExecDept(String execDept) {
        this.execDept = execDept;
    }

    public String getExecDeptName() {
        return execDeptName;
    }

    public void setExecDeptName(String execDeptName) {
        this.execDeptName = execDeptName;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getPacSpec() {
        return pacSpec;
    }

    public void setPacSpec(String pacSpec) {
        this.pacSpec = pacSpec;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public Double getNum() {
        return num;
    }

    public void setNum(Double num) {
        this.num = num;
    }

    public String getChargeUnit() {
        return chargeUnit;
    }

    public void setChargeUnit(String chargeUnit) {
        this.chargeUnit = chargeUnit;
    }

    public String getChargeUnitName() {
        return chargeUnitName;
    }

    public void setChargeUnitName(String chargeUnitName) {
        this.chargeUnitName = chargeUnitName;
    }

    public BigDecimal getReceiptsAmt() {
        return receiptsAmt;
    }

    public void setReceiptsAmt(BigDecimal receiptsAmt) {
        this.receiptsAmt = receiptsAmt;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getReApplyStaff() {
        return reApplyStaff;
    }

    public void setReApplyStaff(String reApplyStaff) {
        this.reApplyStaff = reApplyStaff;
    }

    public String getReApplyStaffName() {
        return reApplyStaffName;
    }

    public void setReApplyStaffName(String reApplyStaffName) {
        this.reApplyStaffName = reApplyStaffName;
    }

    public LocalDateTime getReApplyDate() {
        return reApplyDate;
    }

    public void setReApplyDate(LocalDateTime reApplyDate) {
        this.reApplyDate = reApplyDate;
    }

    public String getEconOrderId() {
        return econOrderId;
    }

    public void setEconOrderId(String econOrderId) {
        this.econOrderId = econOrderId;
    }
}
