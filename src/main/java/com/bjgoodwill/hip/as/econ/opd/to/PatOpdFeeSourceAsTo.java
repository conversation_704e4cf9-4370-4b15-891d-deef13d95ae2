package com.bjgoodwill.hip.as.econ.opd.to;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

@Schema(description = "门诊费别下拉数据源")
public class PatOpdFeeSourceAsTo implements Serializable {

    @Schema(description = "编码")
    private String code;
    @Schema(description = "名称")
    private String name;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}