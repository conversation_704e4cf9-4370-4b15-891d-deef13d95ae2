package com.bjgoodwill.hip.as.econ.ipd.to.settlement;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * File: AmountCalculationAsTo
 * Author: zhangyunchuan
 * Date: 2025/3/19
 * Description:
 */
@Schema(description = "住院结算-结算金额计算(页面左下角)")
public class AmountCalculationAsTo implements Serializable {

    @Schema(description = "状态(收:1, 退:0)")
    private String flag;

    @Schema(description = "收/退 金额")
    private BigDecimal amount;

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }
}
