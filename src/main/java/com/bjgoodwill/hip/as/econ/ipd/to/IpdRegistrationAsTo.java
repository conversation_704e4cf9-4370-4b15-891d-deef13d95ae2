package com.bjgoodwill.hip.as.econ.ipd.to;

import com.bjgoodwill.hip.ds.pat.apply.ipdApply.to.PatApplyDiagnosisTo;
import com.bjgoodwill.hip.ds.pat.in.hospital.regist.diagnosis.to.PatIpdRegistDiagnosisTo;
import com.bjgoodwill.hip.ds.pat.in.hospital.regist.enmus.PatIpdRegistStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: zhangyunchuan
 * @Date: 2024/7/30 17:09
 * @PROJECT: econ-management
 */
@Schema(description = "住院登记")
public class IpdRegistrationAsTo implements Serializable {

    @Serial
    private static final long serialVersionUID = -3453497327505208098L;

    @Schema(description = "标识")
    private String id;

    @Schema(description = "主索引")
    private String patMiCode;

    @Schema(description = "流水号")
    private String visitCode;

    @Schema(description = "姓名")
    private String name;

    @Schema(description = "性别 字典AdministrativeGender")
    private String sex;

    @Schema(description = "卡类型 字典IdentifierName")
    private String cardType;

    @Schema(description = "卡号")
    private String cardCode;

    @Schema(description = "生日")
    private LocalDate birthDate;

    @Schema(description = "电话")
    private String tel;

    @Schema(description = "费别 字典ChargeType")
    private String feeType;

    @Schema(description = "发起申请医生")
    private String applyDoc;

    @Schema(description = "发起申请科室")
    private String applyDeptCode;

    @Schema(description = "入院来源 字典PatientSource")
    private String patientClass;

    @Schema(description = "入院途径 字典InpWay")
    private String inRoute;

    @Schema(description = "申请的护士工作组")
    private String nurseDeptCode;

    @Schema(description = "申请的护士工作组名称")
    private String nurseDeptName;

    @Schema(description = "申请的住院科室")
    private String orgCode;

    @Schema(description = "申请的住院科室名称")
    private String orgName;

    @Schema(description = "医院编码")
    private String hospitalCode;

    @Schema(description = "申请医院编码")
    private String applyHospitalCode;

    @Schema(description = "外院申请")
    private Boolean isOtherHospital;

    @Schema(description = "状态")
    private String statusCode;

    @Schema(description = "创建的人员")
    private String createdStaff;

    @Schema(description = "创建的人员姓名")
    private String createdStaffName;

    @Schema(description = "创建时间:申请时间")
    private LocalDateTime createdDate;

    @Schema(description = "登记时间")
    private LocalDateTime registDate;

    @Schema(description = "最后修改人员")
    private String updatedStaff;

    @Schema(description = "最后修改的人员姓名")
    private String updatedStaffName;

    @Schema(description = "最后修改时间")
    private LocalDateTime updatedDate;

    @Schema(description = "审批人")
    private String approvalStaff;

    @Schema(description = "审批日期")
    private LocalDateTime approvalDate;

    @Schema(description = "患者病情 字典PatAdmCondition")
    private String patAdmCondition;

    @Schema(description = "逻辑删除标记")
    private boolean deleted;

    @Schema(description = "入院科室")
    private String inDeptCode;

    @Schema(description = "入院科室名称")
    private String inDeptName;

    @Schema(description = "入院护理组编码")
    private String inNurseDeptCode;

    @Schema(description = "入院护理组名称")
    private String inNurseDeptName;

    @Schema(description = "医疗类型（字典：1住院2日间手术）")
    private String medicalType;

    @Schema(description = "入院日期")
    private LocalDateTime inDate;

    @Schema(description = "治疗类别（字典：CF06.00.958）")
    private String treatmentType;

    @Schema(description = "来源机构")
    private String sourceAgency;

    @Schema(description = "申请入院医生")
    private String applyDoctorCode;

    @Schema(description = "申请入院医生名称")
    private String applyDoctorName;

    @Schema(description = "入院申请ID")
    private String applyId;

    @Schema(description = "接诊状态")
    private PatIpdRegistStatusEnum receiveStatus;

    @Schema(description = "作废人员")
    private String cancelStaff;

    @Schema(description = "作废人员姓名")
    private String cancelStaffName;

    @Schema(description = "作废时间")
    private LocalDateTime cancelDate;

    @Schema(description = "已启用")
    private boolean enabled;

    @Schema(description = "版本")
    private Integer version;

    @Schema(description = "住院号")
    private String inpatientNo;

    @Schema(description = "性别名称")
    private String sexName;

    @Schema(description = "年龄")
    private String age;

    @Schema(description = "住院次")
    private Integer inTimes;

    @Schema(description = "出院科室")
    private String outDeptCode;

    @Schema(description = "出院科室名称")
    private String outDeptName;

    @Schema(description = "出院时间")
    private LocalDateTime outTime;

    @Schema(description = "住院登记有效日期")
    private LocalDateTime effectiveDate;

    @Schema(description = "住院登记类型")
    private String registType;

    @Schema(description = "当前护理组")
    private String deptNurseCode;

    @Schema(description = "当前护理组名称")
    private String deptNurseName;

    @Schema(description = "当前床位")
    private String bedId;

    @Schema(description = "当前床位名称")
    private String bedName;

    @Schema(description = "当前住院医师")
    private String admittedDoctor;

    @Schema(description = "当前住院医师名称")
    private String admittedDoctorName;

    @Schema(description = "当前主治医师")
    private String masterDoctor;

    @Schema(description = "当前主治医师名称")
    private String masterDoctorName;

    @Schema(description = "当前主任医师")
    private String directorDoctor;

    @Schema(description = "当前主任医师名称")
    private String directorDoctorName;

    @Schema(description = "当前责任护士")
    private String masterNurse;

    @Schema(description = "当前责任护士名称")
    private String masterNurseName;

    @Schema(description = "当前护理级别 字典CV06.00.220 ")
    private String nursingLevel;

    @Schema(description = "当前病情")
    private String condition;

    @Schema(description = "当前病情名称")
    private String conditionName;

    @Schema(description = "当前病情开始时间")
    private LocalDateTime conditionTime;

    @Schema(description = "是否新生儿 1是 0否")
    private boolean newbornFlag;

    @Schema(description = "新生儿出生体重")
    private String newbornBirthWeight;

    @Schema(description = "新生儿入院体重")
    private String newbornInWeight;

    @Schema(description = "新生儿入院类型（字典）  新生儿入院类型1正常新生儿2早产儿3有疾病新生儿4非无菌分娩9其它 ")
    private String newbornInType;

    @Schema(description = "是否已结算")
    private Boolean rcptFlag;

    @Schema(description = "是否支付")
    private Boolean rcptPayFlag;

    @Schema(description = "住院登记收住诊断列表")
    private List<PatIpdRegistDiagnosisTo> patIpdRegistDiagnosisToList;

    @Schema(description = "入院申请诊断列表")
    private List<PatApplyDiagnosisTo> patApplyDiagnosiss;

    @Schema(description = "待登记入院数量")
    private Integer treatCount;

    @Schema(description = "转诊入院数量")
    private Integer referralCount;

    @Schema(description = "总数量")
    private long total;

    public String getInDeptCode() {
        return inDeptCode;
    }

    public void setInDeptCode(String inDeptCode) {
        this.inDeptCode = inDeptCode;
    }

    public String getInDeptName() {
        return inDeptName;
    }

    public void setInDeptName(String inDeptName) {
        this.inDeptName = inDeptName;
    }

    public String getInNurseDeptCode() {
        return inNurseDeptCode;
    }

    public void setInNurseDeptCode(String inNurseDeptCode) {
        this.inNurseDeptCode = inNurseDeptCode;
    }

    public String getInNurseDeptName() {
        return inNurseDeptName;
    }

    public void setInNurseDeptName(String inNurseDeptName) {
        this.inNurseDeptName = inNurseDeptName;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPatMiCode() {
        return patMiCode;
    }

    public void setPatMiCode(String patMiCode) {
        this.patMiCode = patMiCode;
    }

    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public String getCardCode() {
        return cardCode;
    }

    public void setCardCode(String cardCode) {
        this.cardCode = cardCode;
    }

    public LocalDate getBirthDate() {
        return birthDate;
    }

    public void setBirthDate(LocalDate birthDate) {
        this.birthDate = birthDate;
    }

    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    public String getFeeType() {
        return feeType;
    }

    public void setFeeType(String feeType) {
        this.feeType = feeType;
    }

    public String getApplyDoc() {
        return applyDoc;
    }

    public void setApplyDoc(String applyDoc) {
        this.applyDoc = applyDoc;
    }

    public String getApplyDeptCode() {
        return applyDeptCode;
    }

    public void setApplyDeptCode(String applyDeptCode) {
        this.applyDeptCode = applyDeptCode;
    }

    public String getPatientClass() {
        return patientClass;
    }

    public void setPatientClass(String patientClass) {
        this.patientClass = patientClass;
    }

    public String getInRoute() {
        return inRoute;
    }

    public void setInRoute(String inRoute) {
        this.inRoute = inRoute;
    }

    public String getNurseDeptCode() {
        return nurseDeptCode;
    }

    public void setNurseDeptCode(String nurseDeptCode) {
        this.nurseDeptCode = nurseDeptCode;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getHospitalCode() {
        return hospitalCode;
    }

    public void setHospitalCode(String hospitalCode) {
        this.hospitalCode = hospitalCode;
    }

    public String getApplyHospitalCode() {
        return applyHospitalCode;
    }

    public void setApplyHospitalCode(String applyHospitalCode) {
        this.applyHospitalCode = applyHospitalCode;
    }

    public Boolean getOtherHospital() {
        return isOtherHospital;
    }

    public void setIsOtherHospital(Boolean otherHospital) {
        isOtherHospital = otherHospital;
    }

    public String getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(String statusCode) {
        this.statusCode = statusCode;
    }

    public String getCreatedStaff() {
        return createdStaff;
    }

    public void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getUpdatedStaff() {
        return updatedStaff;
    }

    public void setUpdatedStaff(String updatedStaff) {
        this.updatedStaff = updatedStaff;
    }

    public LocalDateTime getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(LocalDateTime updatedDate) {
        this.updatedDate = updatedDate;
    }

    public String getApprovalStaff() {
        return approvalStaff;
    }

    public void setApprovalStaff(String approvalStaff) {
        this.approvalStaff = approvalStaff;
    }

    public LocalDateTime getApprovalDate() {
        return approvalDate;
    }

    public void setApprovalDate(LocalDateTime approvalDate) {
        this.approvalDate = approvalDate;
    }

    public String getPatAdmCondition() {
        return patAdmCondition;
    }

    public void setPatAdmCondition(String patAdmCondition) {
        this.patAdmCondition = patAdmCondition;
    }

    public boolean isDeleted() {
        return deleted;
    }

    public void setDeleted(boolean deleted) {
        this.deleted = deleted;
    }

    public List<PatApplyDiagnosisTo> getPatApplyDiagnosiss() {
        return patApplyDiagnosiss;
    }

    public void setPatApplyDiagnosiss(List<PatApplyDiagnosisTo> patApplyDiagnosiss) {
        this.patApplyDiagnosiss = patApplyDiagnosiss;
    }

    public LocalDateTime getRegistDate() {
        return registDate;
    }

    public void setRegistDate(LocalDateTime registDate) {
        this.registDate = registDate;
    }

    public void setOtherHospital(Boolean otherHospital) {
        isOtherHospital = otherHospital;
    }

    public String getCreatedStaffName() {
        return createdStaffName;
    }

    public void setCreatedStaffName(String createdStaffName) {
        this.createdStaffName = createdStaffName;
    }

    public String getUpdatedStaffName() {
        return updatedStaffName;
    }

    public void setUpdatedStaffName(String updatedStaffName) {
        this.updatedStaffName = updatedStaffName;
    }

    public String getMedicalType() {
        return medicalType;
    }

    public void setMedicalType(String medicalType) {
        this.medicalType = medicalType;
    }

    public LocalDateTime getInDate() {
        return inDate;
    }

    public void setInDate(LocalDateTime inDate) {
        this.inDate = inDate;
    }

    public String getTreatmentType() {
        return treatmentType;
    }

    public void setTreatmentType(String treatmentType) {
        this.treatmentType = treatmentType;
    }

    public String getSourceAgency() {
        return sourceAgency;
    }

    public void setSourceAgency(String sourceAgency) {
        this.sourceAgency = sourceAgency;
    }

    public String getApplyDoctorCode() {
        return applyDoctorCode;
    }

    public void setApplyDoctorCode(String applyDoctorCode) {
        this.applyDoctorCode = applyDoctorCode;
    }

    public String getApplyDoctorName() {
        return applyDoctorName;
    }

    public void setApplyDoctorName(String applyDoctorName) {
        this.applyDoctorName = applyDoctorName;
    }

    public String getApplyId() {
        return applyId;
    }

    public void setApplyId(String applyId) {
        this.applyId = applyId;
    }

    public String getCancelStaff() {
        return cancelStaff;
    }

    public void setCancelStaff(String cancelStaff) {
        this.cancelStaff = cancelStaff;
    }

    public String getCancelStaffName() {
        return cancelStaffName;
    }

    public void setCancelStaffName(String cancelStaffName) {
        this.cancelStaffName = cancelStaffName;
    }

    public LocalDateTime getCancelDate() {
        return cancelDate;
    }

    public void setCancelDate(LocalDateTime cancelDate) {
        this.cancelDate = cancelDate;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getInpatientNo() {
        return inpatientNo;
    }

    public void setInpatientNo(String inpatientNo) {
        this.inpatientNo = inpatientNo;
    }

    public String getSexName() {
        return sexName;
    }

    public void setSexName(String sexName) {
        this.sexName = sexName;
    }

    public String getAge() {
        return age;
    }

    public void setAge(String age) {
        this.age = age;
    }

    public Integer getInTimes() {
        return inTimes;
    }

    public void setInTimes(Integer inTimes) {
        this.inTimes = inTimes;
    }

    public LocalDateTime getEffectiveDate() {
        return effectiveDate;
    }

    public void setEffectiveDate(LocalDateTime effectiveDate) {
        this.effectiveDate = effectiveDate;
    }

    public List<PatIpdRegistDiagnosisTo> getPatIpdRegistDiagnosisToList() {
        return patIpdRegistDiagnosisToList;
    }

    public void setPatIpdRegistDiagnosisToList(List<PatIpdRegistDiagnosisTo> patIpdRegistDiagnosisToList) {
        this.patIpdRegistDiagnosisToList = patIpdRegistDiagnosisToList;
    }

    public String getRegistType() {
        return registType;
    }

    public void setRegistType(String registType) {
        this.registType = registType;
    }

    public String getOutDeptCode() {
        return outDeptCode;
    }

    public void setOutDeptCode(String outDeptCode) {
        this.outDeptCode = outDeptCode;
    }

    public String getOutDeptName() {
        return outDeptName;
    }

    public void setOutDeptName(String outDeptName) {
        this.outDeptName = outDeptName;
    }

    public LocalDateTime getOutTime() {
        return outTime;
    }

    public void setOutTime(LocalDateTime outTime) {
        this.outTime = outTime;
    }

    public String getDeptNurseCode() {
        return deptNurseCode;
    }

    public void setDeptNurseCode(String deptNurseCode) {
        this.deptNurseCode = deptNurseCode;
    }

    public String getDeptNurseName() {
        return deptNurseName;
    }

    public void setDeptNurseName(String deptNurseName) {
        this.deptNurseName = deptNurseName;
    }

    public String getBedId() {
        return bedId;
    }

    public void setBedId(String bedId) {
        this.bedId = bedId;
    }

    public String getBedName() {
        return bedName;
    }

    public void setBedName(String bedName) {
        this.bedName = bedName;
    }

    public String getAdmittedDoctor() {
        return admittedDoctor;
    }

    public void setAdmittedDoctor(String admittedDoctor) {
        this.admittedDoctor = admittedDoctor;
    }

    public String getAdmittedDoctorName() {
        return admittedDoctorName;
    }

    public void setAdmittedDoctorName(String admittedDoctorName) {
        this.admittedDoctorName = admittedDoctorName;
    }

    public String getMasterDoctor() {
        return masterDoctor;
    }

    public void setMasterDoctor(String masterDoctor) {
        this.masterDoctor = masterDoctor;
    }

    public String getMasterDoctorName() {
        return masterDoctorName;
    }

    public void setMasterDoctorName(String masterDoctorName) {
        this.masterDoctorName = masterDoctorName;
    }

    public String getDirectorDoctor() {
        return directorDoctor;
    }

    public void setDirectorDoctor(String directorDoctor) {
        this.directorDoctor = directorDoctor;
    }

    public String getDirectorDoctorName() {
        return directorDoctorName;
    }

    public void setDirectorDoctorName(String directorDoctorName) {
        this.directorDoctorName = directorDoctorName;
    }

    public String getMasterNurse() {
        return masterNurse;
    }

    public void setMasterNurse(String masterNurse) {
        this.masterNurse = masterNurse;
    }

    public String getMasterNurseName() {
        return masterNurseName;
    }

    public void setMasterNurseName(String masterNurseName) {
        this.masterNurseName = masterNurseName;
    }

    public String getNursingLevel() {
        return nursingLevel;
    }

    public void setNursingLevel(String nursingLevel) {
        this.nursingLevel = nursingLevel;
    }

    public String getCondition() {
        return condition;
    }

    public void setCondition(String condition) {
        this.condition = condition;
    }

    public String getConditionName() {
        return conditionName;
    }

    public void setConditionName(String conditionName) {
        this.conditionName = conditionName;
    }

    public LocalDateTime getConditionTime() {
        return conditionTime;
    }

    public void setConditionTime(LocalDateTime conditionTime) {
        this.conditionTime = conditionTime;
    }

    public boolean isNewbornFlag() {
        return newbornFlag;
    }

    public void setNewbornFlag(boolean newbornFlag) {
        this.newbornFlag = newbornFlag;
    }

    public String getNewbornBirthWeight() {
        return newbornBirthWeight;
    }

    public void setNewbornBirthWeight(String newbornBirthWeight) {
        this.newbornBirthWeight = newbornBirthWeight;
    }

    public String getNewbornInWeight() {
        return newbornInWeight;
    }

    public void setNewbornInWeight(String newbornInWeight) {
        this.newbornInWeight = newbornInWeight;
    }

    public String getNewbornInType() {
        return newbornInType;
    }

    public void setNewbornInType(String newbornInType) {
        this.newbornInType = newbornInType;
    }

    public Boolean getRcptFlag() {
        return rcptFlag;
    }

    public void setRcptFlag(Boolean rcptFlag) {
        this.rcptFlag = rcptFlag;
    }

    public Boolean getRcptPayFlag() {
        return rcptPayFlag;
    }

    public void setRcptPayFlag(Boolean rcptPayFlag) {
        this.rcptPayFlag = rcptPayFlag;
    }

    public Integer getReferralCount() {
        return referralCount;
    }

    public void setReferralCount(Integer referralCount) {
        this.referralCount = referralCount;
    }

    public Integer getTreatCount() {
        return treatCount;
    }

    public void setTreatCount(Integer treatCount) {
        this.treatCount = treatCount;
    }

    public PatIpdRegistStatusEnum getReceiveStatus() {
        return receiveStatus;
    }

    public void setReceiveStatus(PatIpdRegistStatusEnum receiveStatus) {
        this.receiveStatus = receiveStatus;
    }

    public String getNurseDeptName() {
        return nurseDeptName;
    }

    public void setNurseDeptName(String nurseDeptName) {
        this.nurseDeptName = nurseDeptName;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public long getTotal() {
        return total;
    }

    public void setTotal(long total) {
        this.total = total;
    }
}
