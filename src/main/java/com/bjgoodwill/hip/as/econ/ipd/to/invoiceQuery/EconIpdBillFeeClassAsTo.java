package com.bjgoodwill.hip.as.econ.ipd.to.invoiceQuery;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;

/**
 * <AUTHOR> lian<PERSON><PERSON><PERSON>
 * @description :
 * @createDate : 2025/4/7 14:27
 */
public class EconIpdBillFeeClassAsTo {

    @Schema(description = "账单明细Id")
    private String billDetailId;

    @Schema(description = "费用类别名称")
    private String feeClassName;

    @Schema(description = "编码")
    private String code;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "规格")
    private String spec;

    @Schema(description = "单价")
    private BigDecimal price;

    @Schema(description = "单位")
    private String unitName;

    @Schema(description = "数量")
    private BigDecimal num;

    @Schema(description = "计价金额")
    private BigDecimal chargeAmount;

    @Schema(description = "应付金额")
    private BigDecimal amount;

    @Schema(description = "医保类别")
    private String miClassName;

    @Schema(description = "自付比例")
    private Double selfpayProp;

    @Schema(description = "先行自付金额")
    private BigDecimal preselfpayAmt;

    public String getBillDetailId() {
        return billDetailId;
    }

    public void setBillDetailId(String billDetailId) {
        this.billDetailId = billDetailId;
    }

    public String getFeeClassName() {
        return feeClassName;
    }

    public void setFeeClassName(String feeClassName) {
        this.feeClassName = feeClassName;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSpec() {
        return spec;
    }

    public void setSpec(String spec) {
        this.spec = spec;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public BigDecimal getNum() {
        return num;
    }

    public void setNum(BigDecimal num) {
        this.num = num;
    }

    public BigDecimal getChargeAmount() {
        return chargeAmount;
    }

    public void setChargeAmount(BigDecimal chargeAmount) {
        this.chargeAmount = chargeAmount;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getMiClassName() {
        return miClassName;
    }

    public void setMiClassName(String miClassName) {
        this.miClassName = miClassName;
    }

    public Double getSelfpayProp() {
        return selfpayProp;
    }

    public void setSelfpayProp(Double selfpayProp) {
        this.selfpayProp = selfpayProp;
    }

    public BigDecimal getPreselfpayAmt() {
        return preselfpayAmt;
    }

    public void setPreselfpayAmt(BigDecimal preselfpayAmt) {
        this.preselfpayAmt = preselfpayAmt;
    }
}
