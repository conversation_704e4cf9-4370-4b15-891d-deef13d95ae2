package com.bjgoodwill.hip.as.econ.ipd.service.impl;

import com.bjgoodwill.hip.as.econ.ipd.service.EconIpdCancelSettlementAsService;
import com.bjgoodwill.hip.as.econ.ipd.to.EconIpdSettlePayAsTo;
import com.bjgoodwill.hip.as.econ.ipd.to.settlement.*;
import com.bjgoodwill.hip.common.exception.BusinessException;
import com.bjgoodwill.hip.common.util.HIPBeanUtil;
import com.bjgoodwill.hip.ds.econ.invoice.service.EconRecMngService;
import com.bjgoodwill.hip.ds.econ.ipd.bill.amount.service.EconIpdAmountService;
import com.bjgoodwill.hip.ds.econ.ipd.bill.amount.to.EconIpdAmountTo;
import com.bjgoodwill.hip.ds.econ.ipd.bill.settle.service.EconIpdSelfSettleService;
import com.bjgoodwill.hip.ds.econ.ipd.bill.settle.service.EconIpdSettleService;
import com.bjgoodwill.hip.ds.econ.ipd.bill.settle.to.*;
import com.bjgoodwill.hip.ds.param.api.service.ParameterService;
import com.bjgoodwill.hip.ds.param.api.to.ParameterTo;
import com.bjgoodwill.hip.ds.pat.in.hospital.inpatient.enmus.InpatientStatusEnum;
import com.bjgoodwill.hip.ds.pat.in.hospital.inpatient.service.PatIpdInpatientService;
import com.bjgoodwill.hip.ds.pat.in.hospital.inpatient.to.PatIpdInpatientQto;
import com.bjgoodwill.hip.ds.pat.in.hospital.inpatient.to.PatIpdInpatientTo;
import com.bjgoodwill.hip.ds.term.api.service.DictElementService;
import com.bjgoodwill.hip.ds.term.api.to.DictElementTo;
import com.bjgoodwill.hip.enums.DictParameterEnum;
import com.bjgoodwill.hip.security.bean.CurrentOrgInfo;
import com.bjgoodwill.hip.security.util.HIPCurrentOrgUtils;
import com.bjgoodwill.hip.security.util.HIPSecurityUtils;
import io.micrometer.common.util.StringUtils;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.apache.seata.spring.annotation.GlobalTransactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * File: EconIpdCancelSettlementAsServiceImpl
 * Author: zhangyunchuan
 * Date: 2025/3/4
 * Description:
 */
@Service("com.bjgoodwill.hip.as.econ.ipd.service.EconIpdCancelSettlementAsService")
public class EconIpdCancelSettlementAsServiceImpl implements EconIpdCancelSettlementAsService {

    @Autowired
    private PatIpdInpatientService patIpdInpatientService;

    @Autowired
    private EconIpdSettleService econIpdSettleService;

    @Autowired
    private EconIpdAmountService econIpdAmountService;

    @Resource
    private ParameterService parameterService;

    @Autowired
    private DictElementService dictElementService;

    @Autowired
    private EconIpdSelfSettleService econIpdSelfSettleService;

    @Autowired
    private EconRecMngService econRecMngService;

    @Override
    public List<DictElementTo> paymentMethodInquiry() {
        List<DictElementTo> dictElementToList = new ArrayList<>();
        List<DictElementTo> customDictElement = dictElementService.getCustomDictElement("PayWay");
        if (CollectionUtils.isNotEmpty(customDictElement)) {
            dictElementToList = customDictElement.stream().filter(a -> StringUtils.isNotEmpty(a.getScopeOfApplication()) && a.getScopeOfApplication().contains("ipd")).toList();
        }
        return dictElementToList;
    }

    @Override
    public List<PatSettlementInfoAsTo> getPatSettlementInfo(EconSettlePatInfoAsQTo econSettlePatInfoAsQTo) {
        //费别字典
        List<DictElementTo> chargeTypeList = dictElementService.getCustomDictElement("ChargeType");
        List<PatSettlementInfoAsTo> settleInfos = new ArrayList<>();
        PatIpdInpatientQto patIpdInpatientQto = new PatIpdInpatientQto();
        String visitCode = "";
        if (econSettlePatInfoAsQTo.getInpatientCode() != null) {
            patIpdInpatientQto.setInpatientCode(econSettlePatInfoAsQTo.getInpatientCode());
            patIpdInpatientQto.setStatus(InpatientStatusEnum.DISCHARGED);
            patIpdInpatientQto.setRcptPayFlag(true);
            List<PatIpdInpatientTo> patIpdInpatientToList = patIpdInpatientService.getPatIpdInpatientCommonQuery(patIpdInpatientQto);
            if (CollectionUtils.isNotEmpty(patIpdInpatientToList)) {
                if (patIpdInpatientToList.size() > 1) {
                    patIpdInpatientToList.stream().sorted(Comparator.comparing(PatIpdInpatientTo::getOutTime).reversed()).toList();
                }
                visitCode = patIpdInpatientToList.get(0).getVisitCode();
            }
        }
        EconIpdSettleQto econIpdSettleQto = new EconIpdSettleQto();
        if (StringUtils.isEmpty(visitCode) && StringUtils.isEmpty(econSettlePatInfoAsQTo.getInvoiceNo())) {
            throw new BusinessException("取消结算-未查询到患者有效数据");
        }
        if (StringUtils.isNotEmpty(visitCode)) {
            econIpdSettleQto.setVisitCode(visitCode);
        }
        if (StringUtils.isNotEmpty(econSettlePatInfoAsQTo.getInvoiceNo())) {
            econIpdSettleQto.setSettleNo(econSettlePatInfoAsQTo.getInvoiceNo());
        }
        econIpdSettleQto.setEnabled(true);
        econIpdSettleQto.setPayFlag(true);
        List<EconIpdSettleTo> econIpdSettles = econIpdSettleService.getEconIpdSettles(econIpdSettleQto);
        if (CollectionUtils.isNotEmpty(econIpdSettles)) {
            EconIpdSettleTo econIpdSettleTo = econIpdSettles.get(0);
            //判断母婴结算流程
            ParameterTo<String> econRefundFeeMode = parameterService.getStringParameter(DictParameterEnum.EconMomAndChildSettlementMethod.getCode());
            if (econRefundFeeMode != null) {
                if (econRefundFeeMode.getValue().equals("together")) {
                    //查询母婴患者信息
                    List<PatIpdInpatientTo> patIpdInpatientToList = patIpdInpatientService.getPatIpdNewBornInpatientByVisitCode(econIpdSettleTo.getVisitCode());
                    if (CollectionUtils.isNotEmpty(patIpdInpatientToList)) {
                        //校验查询条件
                        List<PatIpdInpatientTo> list = patIpdInpatientToList.stream().filter(a -> !a.isNewbornFlag()).toList();
                        if (CollectionUtils.isNotEmpty(list)) {
                            PatIpdInpatientTo patIpdInpatientTo = list.get(0);
                            EconIpdSettleQto qto = new EconIpdSettleQto();
                            qto.setVisitCode(patIpdInpatientTo.getVisitCode());
                            econIpdSettleQto.setEnabled(true);
                            econIpdSettleQto.setPayFlag(true);
                            //母亲结算信息
                            List<EconIpdSettleTo> econIpdSettlesList = econIpdSettleService.getEconIpdSettles(qto);
                            if (CollectionUtils.isNotEmpty(econIpdSettlesList)) {
                                EconIpdSettleTo econIpdSettleTo1 = econIpdSettlesList.get(0);
                                if (StringUtils.isNotEmpty(econSettlePatInfoAsQTo.getInpatientCode())) {
                                    if (!patIpdInpatientTo.getInpatientCode().equals(econSettlePatInfoAsQTo.getInpatientCode())) {
                                        throw new BusinessException("当前流程为母婴分开结算，需要先取消结算母亲的费用信息\n是否直接进行取消母亲[发票号:" + econIpdSettleTo1.getSettleNo() + "]结算");
                                    }
                                }
                                if (StringUtils.isNotEmpty(econSettlePatInfoAsQTo.getInvoiceNo())) {
                                    if (!econIpdSettleTo1.getSettleNo().equals(econSettlePatInfoAsQTo.getInvoiceNo())) {
                                        throw new BusinessException("当前流程为母婴分开结算，需要先取消结算母亲的费用信息\n是否直接进行取消母亲[发票号:" + econIpdSettleTo1.getSettleNo() + "]结算");
                                    }
                                }
                            }
                        }
                        //有母婴患者信息
                        for (PatIpdInpatientTo patIpdInpatientTo : patIpdInpatientToList) {
                            EconIpdSettleQto qto = new EconIpdSettleQto();
                            qto.setVisitCode(patIpdInpatientTo.getVisitCode());
                            econIpdSettleQto.setEnabled(true);
                            econIpdSettleQto.setPayFlag(true);
                            List<EconIpdSettleTo> econIpdSettlesList = econIpdSettleService.getEconIpdSettles(qto);
                            if (CollectionUtils.isNotEmpty(econIpdSettlesList)) {
                                econIpdSettlesList.forEach(econIpdSettle -> {
                                    PatSettlementInfoAsTo settleInfo = HIPBeanUtil.copy(econIpdSettle, PatSettlementInfoAsTo.class);
                                    settleInfo.setName(patIpdInpatientTo.getName());
                                    settleInfo.setInpatientCode(patIpdInpatientTo.getInpatientCode());
                                    settleInfo.setSettleTypeCode(settleInfo.getSettleType().getCode());
                                    settleInfo.setSettleTypeName(settleInfo.getSettleType().getName());
                                    settleInfo.setSettleId(econIpdSettle.getId());
                                    if (CollectionUtils.isNotEmpty(chargeTypeList) && StringUtils.isNotEmpty(patIpdInpatientTo.getFeeType())) {
                                        List<DictElementTo> dictElementToList = chargeTypeList.stream().filter(a -> a.getElementCode().equals(patIpdInpatientTo.getFeeType())).toList();
                                        if (CollectionUtils.isNotEmpty(dictElementToList)) {
                                            DictElementTo dictElementTo = dictElementToList.get(0);
                                            settleInfo.setFeeTypeName(dictElementTo.getElementName());
                                        }
                                        settleInfo.setFeeType(patIpdInpatientTo.getFeeType());
                                    }
                                    settleInfos.add(settleInfo);
                                });
                            }
                        }
                        return settleInfos;
                    } else {
                        //没有母婴患者信息
                        return getPatSettlementInfoAsTos(settleInfos, econIpdSettles, econIpdSettleTo, chargeTypeList);
                    }
                } else if (econRefundFeeMode.getValue().equals("separate")) {
                    return getPatSettlementInfoAsTos(settleInfos, econIpdSettles, econIpdSettleTo, chargeTypeList);
                }
            } else {
                throw new BusinessException("取消结算-请先维护母婴结算方式参数[EconMomAndChildSettlementMethod]");
            }
        }
        return settleInfos;
    }

    @Override
    public EconIpdCancelSettlementAsTo getPatInpatientEconInfo(String settleId) {
        EconIpdCancelSettlementAsTo econIpdCancelSettlementAsTo = new EconIpdCancelSettlementAsTo();
        EconIpdSettleQto econIpdSettleQto = new EconIpdSettleQto();
        econIpdSettleQto.setRefundId(settleId);
        econIpdSettleQto.setPayFlag(true);
        econIpdSettleQto.setEnabled(true);
        List<EconIpdSettleTo> econIpdSettleToList = econIpdSettleService.getEconIpdSettles(econIpdSettleQto);
        if (CollectionUtils.isNotEmpty(econIpdSettleToList)) {
            List<BpubTypeAsTo> bpubTypeAsList = new ArrayList<>();
            //结算减免方式
            List<DictElementTo> bpubTypeList = dictElementService.getCustomDictElement("BpubType");
            PatIpdInpatientTo patIpdInpatientByVisitCode = patIpdInpatientService.getPatIpdInpatientByVisitCode(econIpdSettleToList.get(0).getVisitCode());
            //查询缴费方式
            if (patIpdInpatientByVisitCode.getFeeType().equals("08")) {
                BpubTypeAsTo bpubTypeAsTo = new BpubTypeAsTo();
                if (StringUtils.isNotEmpty(econIpdSettleToList.get(0).getDeduType())) {
                    bpubTypeAsTo.setBpubType(econIpdSettleToList.get(0).getDeduType());
                    List<DictElementTo> dictElementToList = bpubTypeList.stream().filter(a -> a.getElementCode().equals(econIpdSettleToList.get(0).getDeduType())).toList();
                    if (CollectionUtils.isNotEmpty(dictElementToList)) {
                        DictElementTo dictElementTo = dictElementToList.get(0);
                        bpubTypeAsTo.setBpubTypeName(dictElementTo.getElementName());
                    }
                    bpubTypeAsTo.setBpubTypeAmount(econIpdSettleToList.get(0).getDeduAmount());
                    bpubTypeAsList.add(bpubTypeAsTo);
                }
            } else {
                //todo 医保
                econIpdSettleToList.get(0).getDeduType();
                econIpdSettleToList.get(0).getDeduAmount();
            }
            econIpdCancelSettlementAsTo.setBpubTypeList(bpubTypeAsList);
            //住院余额
            EconIpdAmountTo econIpdAmountById = econIpdAmountService.getEconIpdAmountByVisitCode(econIpdSettleToList.get(0).getVisitCode());
            econIpdCancelSettlementAsTo.setTotalAmount(econIpdAmountById.getTotalAmount());
            econIpdCancelSettlementAsTo.setPrepayAmount(econIpdAmountById.getPrepayAmount());
            //查询已支付信息
            List<EconIpdSettlePayTo> settlePays = econIpdSettleService.getSettlePays(settleId, true);
            List<EconIpdSettlePayAsTo> econIpdSettlePayAsToList = HIPBeanUtil.copy(settlePays, EconIpdSettlePayAsTo.class);
            econIpdSettlePayAsToList.forEach(econIpdSettlePayAsTo -> {
                econIpdSettlePayAsTo.setTransactionTypeCode(econIpdSettlePayAsTo.getTransactionType().getCode());
                econIpdSettlePayAsTo.setTransactionTypeName(econIpdSettlePayAsTo.getTransactionType().getName());
            });
            econIpdCancelSettlementAsTo.setEconIpdSettlePayAsToList(econIpdSettlePayAsToList);
            List<EconIpdSettlePayTo> list = settlePays.stream().filter(a -> !a.isRefundFlag()).toList();
            if (CollectionUtils.isNotEmpty(list)) {
                BigDecimal payAmount = list.stream().map(EconIpdSettlePayTo::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                econIpdCancelSettlementAsTo.setPayAmount(payAmount);
            }
        }
        return econIpdCancelSettlementAsTo;
    }

    @Override
    @GlobalTransactional
    public EconIpdSettleTo cancelSettle(EconIpdSettleCancelAsTo econIpdSettleCancelAsTo) {
        CurrentOrgInfo loginInfo = HIPCurrentOrgUtils.getCurrentOrgInfo();
        long usingIpdRecCountByStaffId = econRecMngService.getUsingIpdRecCountByStaffId(HIPSecurityUtils.getLoginStaffId());
        int size = econIpdSettleCancelAsTo.getEconCancelSettlePrepayAsTo().size();
        if (BigDecimal.valueOf(usingIpdRecCountByStaffId).subtract(BigDecimal.valueOf(size)).compareTo(BigDecimal.ZERO) < 0) {
            throw new BusinessException("当前剩余发票数量不足");
        }
        if (econIpdSettleCancelAsTo.getFeeType().equals("08")) {
            EconIpdSettleCancelTo cancelTo = new EconIpdSettleCancelTo();
            cancelTo.setSettleId(econIpdSettleCancelAsTo.getSettleId());
            if (CollectionUtils.isNotEmpty(econIpdSettleCancelAsTo.getEconCancelSettlePrepayAsTo())) {
                List<String> list = econIpdSettleCancelAsTo.getEconCancelSettlePrepayAsTo().stream().map(EconCancelSettlePrepayAsTo::getId).toList();
                cancelTo.setSettlePayToPrepayIdList(list);
            }
            if (StringUtils.isNotEmpty(econIpdSettleCancelAsTo.getSettleNo())) {
                if (econIpdSettleCancelAsTo.getInvoiceType().equals("1")) {
                    //todo 电子发票
                    EconIpdSettleEleInvoiceNto econIpdSettleEleInvoiceNto = new EconIpdSettleEleInvoiceNto();
                } else if (econIpdSettleCancelAsTo.getInvoiceType().equals("2")) {
                    //纸质发票
                    EconIpdSettlePlainInvoiceNto econIpdSettlePlainInvoiceNto = new EconIpdSettlePlainInvoiceNto();
                    econIpdSettlePlainInvoiceNto.setAmount(econIpdSettleCancelAsTo.getAmount());
                    econIpdSettlePlainInvoiceNto.setInvoiceNo(econIpdSettleCancelAsTo.getSettleNo());
                }
            }
            cancelTo.setCurWorkGroupCode(loginInfo.getWorkGroupCode());
            cancelTo.setCurWorkGroupName(loginInfo.getWorkGroupName());
            cancelTo.setBranchHospitalCode(loginInfo.getHospitalAreaCode());
            cancelTo.setBranchHospitalName(loginInfo.getHospitalAreaName());
            cancelTo.setCurLoginStaff(HIPSecurityUtils.getLoginStaffId());
            cancelTo.setCurLoginStaffName(HIPSecurityUtils.getLoginUserName());
            EconIpdSelfSettleTo econIpdSelfSettleTo = econIpdSelfSettleService.cancelSettle(cancelTo);
            patIpdInpatientService.changeRcptAndRcptPayFlag(econIpdSettleCancelAsTo.getVisitCode(), false, false);
            return econIpdSelfSettleTo;
        } else {
            //todo 医保
        }
        return null;
    }

    private List<PatSettlementInfoAsTo> getPatSettlementInfoAsTos(List<PatSettlementInfoAsTo> settleInfos, List<EconIpdSettleTo> econIpdSettles, EconIpdSettleTo econIpdSettleTo, List<DictElementTo> chargeTypeList) {
        PatIpdInpatientTo patIpdInpatientByVisitCode = patIpdInpatientService.getPatIpdInpatientByVisitCode(econIpdSettleTo.getVisitCode());
        if (patIpdInpatientByVisitCode != null) {
            econIpdSettles.forEach(econIpdSettle -> {
                PatSettlementInfoAsTo settleInfo = HIPBeanUtil.copy(econIpdSettle, PatSettlementInfoAsTo.class);
                settleInfo.setName(patIpdInpatientByVisitCode.getName());
                settleInfo.setInpatientCode(patIpdInpatientByVisitCode.getInpatientCode());
                settleInfo.setSettleTypeCode(settleInfo.getSettleType().getCode());
                settleInfo.setSettleTypeName(settleInfo.getSettleType().getName());
                settleInfo.setSettleId(econIpdSettle.getId());
                if (CollectionUtils.isNotEmpty(chargeTypeList) && StringUtils.isNotEmpty(patIpdInpatientByVisitCode.getFeeType())) {
                    List<DictElementTo> dictElementToList = chargeTypeList.stream().filter(a -> a.getElementCode().equals(patIpdInpatientByVisitCode.getFeeType())).toList();
                    if (CollectionUtils.isNotEmpty(dictElementToList)) {
                        DictElementTo dictElementTo = dictElementToList.get(0);
                        settleInfo.setFeeTypeName(dictElementTo.getElementName());
                    }
                    settleInfo.setFeeType(patIpdInpatientByVisitCode.getFeeType());
                }
                settleInfos.add(settleInfo);
            });
        }
        return settleInfos;
    }

}
