package com.bjgoodwill.hip.as.view.controller;

import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.ds.cis.cdr.order.service.CisCdrOrderService;
import com.bjgoodwill.hip.ds.cis.cdr.order.to.CisCdrOrderQto;
import com.bjgoodwill.hip.ds.cis.cdr.order.to.CisCdrOrderTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "患者视图门诊医嘱服务", description = "患者视图门诊医嘱服务")
@RestController
@RequestMapping("/common/patient/view/opdOrders")
public class OrderOpdController {
    @Autowired
    private CisCdrOrderService cisCdrOrderService;

    @Operation(summary = "根据流水号查询患者门诊药品数据")
    @ApiResponse(description = "返回门诊医嘱数据", content = @Content(mediaType = "application/json"))
    @GetMapping(value = "/order/drug/{visitCode:.+}")
    public List<CisCdrOrderTo> getDrugOrderOpdList(@PathVariable("visitCode") String visitCode) {
        return getOrderOpdList(visitCode, null).stream()
                .filter(order ->
                        SystemTypeEnum.EDRUG.equals(order.getOrderClass())
                                || SystemTypeEnum.CDRUG.equals(order.getOrderClass())).toList();
    }

    @Operation(summary = "根据流水号查询患者门诊医嘱数据")
    @ApiResponse(description = "返回门诊医嘱数据", content = @Content(mediaType = "application/json"))
    @GetMapping(value = "/order/{visitCode:.+}")
    public List<CisCdrOrderTo> getOrderOpdListByVisitCode(@PathVariable("visitCode") String visitCode) {
        return getOrderOpdList(visitCode, null);
    }

    @Operation(summary = "根据流水号查询患者门诊检查医嘱数据")
    @ApiResponse(description = "返回门诊检查医嘱数据", content = @Content(mediaType = "application/json"))
    @GetMapping(value = "/order/dgimg/{visitCode:.+}")
    public List<CisCdrOrderTo> getDgimgOrderOpdList(@PathVariable("visitCode") String visitCode) {
        return getOrderOpdList(visitCode, SystemTypeEnum.DGIMG);
    }

    @Operation(summary = "根据流水号查询患者门诊检验医嘱数据")
    @ApiResponse(description = "返回门诊检验医嘱数据", content = @Content(mediaType = "application/json"))
    @GetMapping(value = "/order/spcobs/{visitCode:.+}")
    public List<CisCdrOrderTo> getSpcobsOrderOpdList(@PathVariable("visitCode") String visitCode) {
        return getOrderOpdList(visitCode, SystemTypeEnum.SPCOBS);
    }

    @Operation(summary = "根据流水号、医嘱类型查询患者门诊医嘱数据")
    @ApiResponse(description = "返回门诊医嘱数据", content = @Content(mediaType = "application/json"))
    @GetMapping(value = "/order/list")
    public List<CisCdrOrderTo> getOrderOpdList(@RequestParam("visitCode") String visitCode, @RequestParam(value = "orderClass", required = false) SystemTypeEnum orderClass) {
        CisCdrOrderQto orderOpdQto = new CisCdrOrderQto();
        orderOpdQto.setVisitCode(visitCode);
        orderOpdQto.setOrderClass(orderClass);
        return cisCdrOrderService.getCisCdrOrders(orderOpdQto);
    }
}
