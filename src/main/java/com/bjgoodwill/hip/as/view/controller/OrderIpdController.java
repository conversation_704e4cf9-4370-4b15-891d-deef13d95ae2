package com.bjgoodwill.hip.as.view.controller;

import com.bjgoodwill.hip.as.view.to.OrderIpdAsTo;
import com.bjgoodwill.hip.business.util.cis.common.enums.SystemTypeEnum;
import com.bjgoodwill.hip.common.util.HIPBeanUtil;
import com.bjgoodwill.hip.ds.cis.cdr.order.service.CisCdrOrderService;
import com.bjgoodwill.hip.ds.cis.cdr.order.to.CisCdrOrderQto;
import com.bjgoodwill.hip.ds.cis.cdr.order.to.CisCdrOrderTo;
import com.bjgoodwill.hip.ds.cis.cdr.plan.service.CisCdrOrderPlanRecordService;
import com.bjgoodwill.hip.ds.cis.cdr.plan.to.CisCdrOrderPlanRecordQto;
import com.bjgoodwill.hip.ds.cis.cdr.plan.to.CisCdrOrderPlanRecordTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Tag(name = "患者视图住院医嘱服务", description = "患者视图住院医嘱服务")
@RestController
@RequestMapping("/common/patient/view/ipdOrders")
public class OrderIpdController {
    @Autowired
    private CisCdrOrderPlanRecordService cisCdrOrderPlanRecordService;
    @Autowired
    private CisCdrOrderService cisCdrOrderService;

    @Operation(summary = "根据流水号查询患者药品数据")
    @ApiResponse(description = "返回医嘱数据", content = @Content(mediaType = "application/json"))
    @GetMapping(value = "/order/drug/{visitCode:.+}")
    public List<CisCdrOrderTo> getDrugOrderOpdList(@PathVariable("visitCode") String visitCode) {
        return getOrderIpdList(visitCode, null).stream()
                .filter(order ->
                        SystemTypeEnum.EDRUG.equals(order.getOrderClass())
                                || SystemTypeEnum.CDRUG.equals(order.getOrderClass())).toList();
    }

    @Operation(summary = "根据流水号查询患者医嘱数据")
    @ApiResponse(description = "返回医嘱数据", content = @Content(mediaType = "application/json"))
    @GetMapping(value = "/order/{visitCode:.+}")
    public List<CisCdrOrderTo> getOrderIpdListByVisitCode(@PathVariable("visitCode") String visitCode) {
        return getOrderIpdList(visitCode, null);
    }

    @Operation(summary = "根据流水号查询患者检查医嘱数据")
    @ApiResponse(description = "返回检查医嘱数据", content = @Content(mediaType = "application/json"))
    @GetMapping(value = "/order/dgimg/{visitCode:.+}")
    public List<CisCdrOrderTo> getDgimgOrderOpdList(@PathVariable("visitCode") String visitCode) {
        return getOrderIpdList(visitCode, SystemTypeEnum.DGIMG);
    }

    @Operation(summary = "根据流水号查询患者检验医嘱数据")
    @ApiResponse(description = "返回检验医嘱数据", content = @Content(mediaType = "application/json"))
    @GetMapping(value = "/order/spcobs/{visitCode:.+}")
    public List<CisCdrOrderTo> getSpcobsOrderOpdList(@PathVariable("visitCode") String visitCode) {
        return getOrderIpdList(visitCode, SystemTypeEnum.SPCOBS);
    }

    @Operation(summary = "根据流水号查询患者住院手术相关医嘱数据")
    @ApiResponse(description = "返回住院医嘱数据", content = @Content(mediaType = "application/json"))
    @GetMapping(value = "/order/operation/{visitCode:.+}")
    public List<CisCdrOrderTo> getOperationOrderIpdList(@PathVariable("visitCode") String visitCode) {
        return getOrderIpdList(visitCode, SystemTypeEnum.OPERATIONAPPLY);
    }

    @Operation(summary = "根据流水号查询患者住院治疗医嘱数据")
    @ApiResponse(description = "返回住院医嘱数据", content = @Content(mediaType = "application/json"))
    @GetMapping(value = "/order/management/{visitCode:.+}")
    public List<CisCdrOrderTo> getManagementOrderIpdList(@PathVariable("visitCode") String visitCode) {
        return getOrderIpdList(visitCode, SystemTypeEnum.MANAGEMENT);
    }

    @Operation(summary = "根据流水号、医嘱类型查询患者住院医嘱数据")
    @ApiResponse(description = "返回住院医嘱数据", content = @Content(mediaType = "application/json"))
    @GetMapping(value = "/order/visitCode/list")
    public List<CisCdrOrderTo> getOrderIpdList(@RequestParam("visitCode") String visitCode, @RequestParam(value = "orderClass", required = false) SystemTypeEnum orderClass) {
        CisCdrOrderQto orderIpdQto = new CisCdrOrderQto();
        orderIpdQto.setVisitCode(visitCode);
        orderIpdQto.setOrderClass(orderClass);
        return cisCdrOrderService.getCisCdrOrders(orderIpdQto);
    }

    @Operation(summary = "查询患者一定时间内的执行记录")
    @ApiResponse(description = "返回住院医嘱数据", content = @Content(mediaType = "application/json"))
    @GetMapping(value = "/order/visitCode/date")
    public List<OrderIpdAsTo> getIpdOrderByDate(@RequestParam("visitCode") String visitCode,
                                                @RequestParam("startDate") LocalDateTime startDate,
                                                @RequestParam("endDate") LocalDateTime endDate) {
        // 根据就诊编码获取住院医嘱记录，并过滤出在指定日期范围内的执行记录
        List<CisCdrOrderPlanRecordTo> items = getIpdOrderPlanByVisitCode(visitCode).stream()
                .filter(p -> p.getExecuteDate().isAfter(startDate) && p.getExecuteDate().isBefore(endDate))
                .toList();
        // 如果没有找到任何执行记录，返回空列表
        if (CollectionUtils.isEmpty(items)) {
            return Collections.emptyList();
        }
        // 查询参数
        CisCdrOrderQto cisCdrOrderQto = new CisCdrOrderQto();
        cisCdrOrderQto.setVisitCode(visitCode);
        // 查询与医嘱 ID 匹配的住院医嘱详情
        List<String> orderIds = items.stream().map(CisCdrOrderPlanRecordTo::getOrderId).distinct().toList();
        List<CisCdrOrderTo> ipdOrders = cisCdrOrderService.getCisCdrOrders(cisCdrOrderQto).stream()
                .filter(p -> orderIds.contains(p.getId()))
                .toList();
        // 将执行记录按医嘱 ID 分组
        Map<String, List<CisCdrOrderPlanRecordTo>> map = items.stream()
                .collect(Collectors.groupingBy(CisCdrOrderPlanRecordTo::getOrderId));

        return ipdOrders.stream()
                .map(p -> {
                    OrderIpdAsTo orderIpdAsTo = HIPBeanUtil.copy(p, OrderIpdAsTo.class);
                    orderIpdAsTo.setCisCdrOrderPlanRecordToList(map.getOrDefault(p.getId(), Collections.emptyList()));
                    return orderIpdAsTo;
                })
                .collect(Collectors.toList());
    }

    /**
     * 根据就诊码获取门诊订单计划记录
     *
     * @param visitCode 就诊码，用于查询对应的订单计划记录
     * @return List<CisCdrOrderPlanRecordTo> 返回与就诊码关联的订单计划记录列表
     */
    private List<CisCdrOrderPlanRecordTo> getIpdOrderPlanByVisitCode(String visitCode) {
        // 创建一个查询传输对象并设置就诊码
        CisCdrOrderPlanRecordQto cisCdrOrderPlanRecordQto = new CisCdrOrderPlanRecordQto();
        cisCdrOrderPlanRecordQto.setVisitCode(visitCode);
        return cisCdrOrderPlanRecordService.getCisCdrOrderPlanRecords(cisCdrOrderPlanRecordQto);
    }

    @Operation(summary = "查询患者医嘱执行结果")
    @ApiResponse(description = "返回住院医嘱执行数据", content = @Content(mediaType = "application/json"))
    @GetMapping(value = "/order/visitCode/orderPlan/orderIds")
    public List<CisCdrOrderPlanRecordTo> getOrderExcelRecordInOrderIds(@RequestParam("visitCode") String visitCode, @RequestParam("orderIds") List<String> orderIds) {
        return getIpdOrderPlanByVisitCode(visitCode).stream().filter(p -> orderIds.contains(p.getOrderId())).toList();
    }

    @Operation(summary = "患者用药趋势")
    @ApiResponse(description = "返回住院医嘱数据", content = @Content(mediaType = "application/json"))
    @GetMapping(value = "/order/visitCode/orderPlan/drugCode")
    public List<CisCdrOrderPlanRecordTo> getDrugTrend(@RequestParam("visitCode") String visitCode, @RequestParam("drugCode") String drugCode) {
        return getIpdOrderPlanByVisitCode(visitCode)
                .stream().filter(p ->
                        SystemTypeEnum.EDRUG.equals(p.getOrderClass())
                                || SystemTypeEnum.CDRUG.equals(p.getOrderClass()))
                .filter(p -> drugCode.equals(p.getDrugCode())).toList();
    }
}
