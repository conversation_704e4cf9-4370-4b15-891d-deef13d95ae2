package com.bjgoodwill.hip.as.view.controller;

import com.bjgoodwill.hip.ds.cis.cdr.critical.service.CriticalValueReportService;
import com.bjgoodwill.hip.ds.cis.cdr.critical.to.CriticalValueReportQto;
import com.bjgoodwill.hip.ds.cis.cdr.critical.to.CriticalValueReportTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Tag(name = "患者视图危急值管理服务", description = "患者视图危急值管理服务")
@RestController
@RequestMapping("/common/patient/view/criticalValueReports")
public class CriticalValueReportsController {
    @Autowired
    private CriticalValueReportService criticalValueReportService;

    /**
     * 根据流水号查询危急值
     * 
     * @param visitCode 流水号
     * @return 危急值报告列表
     */
    @Operation(summary = "根据流水号查询危急值")
    @ApiResponse(description = "返回集合", content = @Content(mediaType = "application/json", schema = @Schema(implementation = CriticalValueReportTo.class)))
    @GetMapping(value = "/{visitCode:.+}")
    public List<CriticalValueReportTo> getCriticalValueReportList(@PathVariable("visitCode") String visitCode) {
        CriticalValueReportQto criticalValueReportQto = new CriticalValueReportQto();
        criticalValueReportQto.setVisitCode(visitCode);
        return criticalValueReportService.getCriticalValueReports(criticalValueReportQto);
    }
}
