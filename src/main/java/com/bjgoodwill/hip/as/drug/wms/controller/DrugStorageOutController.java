package com.bjgoodwill.hip.as.drug.wms.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.bjgoodwill.hip.as.drug.wms.enums.DrugWmsBusinessErrorEnum;
import com.bjgoodwill.hip.as.drug.wms.service.DrugPharmacyApplyAsService;
import com.bjgoodwill.hip.as.drug.wms.to.DrugPharmacyApplyDetailAsTo;
import com.bjgoodwill.hip.as.drug.wms.to.DrugStorageOutAllAsTo;
import com.bjgoodwill.hip.as.drug.wms.to.DrugStorageOutAsTo;
import com.bjgoodwill.hip.as.drug.wms.to.DrugStorageOutDetailAsTo;
import com.bjgoodwill.hip.common.bean.EnumTo;
import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.common.util.HIPBeanUtil;
import com.bjgoodwill.hip.ds.drug.goods.to.DrugGoodsTo;
import com.bjgoodwill.hip.ds.drug.goods.to.DrugGoodsWestTo;
import com.bjgoodwill.hip.ds.drug.inout.pharmacy.apply.service.DrugPharmacyApplyService;
import com.bjgoodwill.hip.ds.drug.inout.pharmacy.apply.to.DrugPharmacyApplyNto;
import com.bjgoodwill.hip.ds.drug.inout.pharmacy.apply.to.DrugPharmacyApplyTo;
import com.bjgoodwill.hip.ds.drug.inout.pharmacy.out.enmus.DrugRejectReasonEnum;
import com.bjgoodwill.hip.ds.drug.inout.stocksetting.enmus.DrugChangeDirectionEnum;
import com.bjgoodwill.hip.ds.drug.inout.stocksetting.enmus.DrugDirectionTypeEnum;
import com.bjgoodwill.hip.ds.drug.inout.stocksetting.enmus.DrugSettingTypeEnum;
import com.bjgoodwill.hip.ds.drug.inout.stocksetting.enmus.DrugStockSettingStatusEnum;
import com.bjgoodwill.hip.ds.drug.inout.stocksetting.service.DrugStockSettingService;
import com.bjgoodwill.hip.ds.drug.inout.stocksetting.to.DrugStockSettingQto;
import com.bjgoodwill.hip.ds.drug.inout.stocksetting.to.DrugStockSettingTo;
import com.bjgoodwill.hip.ds.drug.inout.storage.out.service.DrugStorageOutService;
import com.bjgoodwill.hip.ds.drug.inout.storage.out.to.*;
import com.bjgoodwill.hip.ds.drug.stock.stock.service.DrugStockService;
import com.bjgoodwill.hip.ds.drug.stock.stock.to.DrugStockQto;
import com.bjgoodwill.hip.ds.drug.stock.stock.to.DrugStockTo;
import com.bjgoodwill.hip.ds.org.api.service.WorkGroupService;
import com.bjgoodwill.hip.ds.org.api.to.WorkGroupPharmacyTo;
import com.bjgoodwill.hip.ds.org.api.to.WorkGroupTo;
import com.bjgoodwill.hip.security.bean.CurrentOrgInfo;
import com.bjgoodwill.hip.security.util.HIPCurrentOrgUtils;
import com.bjgoodwill.hip.to.CardResultSetTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

@RestController("com.bjgoodwill.hip.as.drug.wms.controller.DrugStorageOutController")
@SaCheckPermission("drugWms:storageOut")
@Tag(name = "药库出退库管理服务", description = "药库出退库服务类")
@RequestMapping("/drug/wms/storageOut")
public class DrugStorageOutController {

    @Autowired
    private DrugStorageOutService drugStorageOutService;

    @Autowired
    private WorkGroupService workGroupService;

    @Autowired
    private DrugStockSettingService drugStockSettingService;

    @Autowired
    private DrugPharmacyApplyService drugPharmacyApplyService;

    @Autowired
    private DrugStockService drugStockService;

    @Autowired
    private DrugPharmacyApplyAsService drugPharmacyApplyAsService;

    @Operation(summary = "根据查询条件查询出退库信息", description = "根据查询条件查询出退库信息")
    @ApiResponse(description = "返回出退库信息集合", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = DrugStorageOutTo.class))))
    @GetMapping(value = "/")
    public List<DrugStorageOutTo> getDrugStorageOuts(@ParameterObject @SpringQueryMap DrugStorageOutQto drugStorageOutQto) {
        CurrentOrgInfo loginInfo = HIPCurrentOrgUtils.getCurrentOrgInfo();
        BusinessAssert.notNull(loginInfo, DrugWmsBusinessErrorEnum.BUS_DRUG_WMS_0006, "登录信息");
        drugStorageOutQto.setStorageCode(loginInfo.getWorkGroupCode());
        drugStorageOutQto.setStorageName(loginInfo.getWorkGroupName());
        List<DrugStorageOutTo> drugStorageOutTos = drugStorageOutService.getDrugStorageOuts(drugStorageOutQto);
        if (!CollectionUtils.isEmpty(drugStorageOutTos)) {
            drugStorageOutTos = drugStorageOutTos.stream().sorted(Comparator.comparing(DrugStorageOutTo::getOutDate, Comparator.nullsFirst(Comparator.reverseOrder())).thenComparing(DrugStorageOutTo::getCreatedDate, Comparator.nullsFirst(Comparator.reverseOrder()))).collect(Collectors.toList());
        }
        return drugStorageOutTos;
    }

    @Operation(summary = "根据查询条件查询出退库信息-分页查询", description = "根据查询条件查询出退库信息-分页查询")
    @ApiResponse(description = "返回出退库信息集合", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = DrugStorageOutTo.class))))
    @GetMapping(value = "/pages")
    public GridResultSet<DrugStorageOutTo> getDrugStorageOutPage(@ParameterObject @SpringQueryMap DrugStorageOutQto drugStorageOutQto) {
        CurrentOrgInfo loginInfo = HIPCurrentOrgUtils.getCurrentOrgInfo();
        BusinessAssert.notNull(loginInfo, DrugWmsBusinessErrorEnum.BUS_DRUG_WMS_0006, "登录信息");
        drugStorageOutQto.setStorageCode(loginInfo.getWorkGroupCode());
        drugStorageOutQto.setStorageName(loginInfo.getWorkGroupName());
        return drugStorageOutService.getDrugStorageOutPage(drugStorageOutQto);
    }

    @Operation(summary = "根据条件查询药库历史出库单", description = "新单页面用 ")
    @ApiResponse(description = "返回出库明细信息", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = DrugStorageOutTo.class))))
    @PostMapping("/historyOutRecords")
    public GridResultSet<DrugStorageOutAllTo> getDrugStorageOutAllPage(@RequestBody DrugStorageOutQto drugStorageOutQto) {
        CurrentOrgInfo loginInfo = HIPCurrentOrgUtils.getCurrentOrgInfo();
        BusinessAssert.notNull(loginInfo, DrugWmsBusinessErrorEnum.BUS_DRUG_WMS_0006, "登录信息");
        drugStorageOutQto.setStorageCode(loginInfo.getWorkGroupCode());
        return drugStorageOutService.getDrugStorageOutAllPage(drugStorageOutQto);
    }

    @Operation(summary = "历史出库记录添加方式", description = "新单页面用 ")
    @ApiResponse(description = "返回出库明细信息", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = DrugStorageOutAllTo.class))))
    @PostMapping("/addWay")
    public List<DrugStorageOutAllAsTo> historyOutRecordsAddWay(@RequestBody List<DrugStorageOutAllTo> drugStorageOutAllTos, @RequestParam("addWay") String addWay) {
        CurrentOrgInfo loginInfo = HIPCurrentOrgUtils.getCurrentOrgInfo();
        BusinessAssert.notNull(loginInfo, DrugWmsBusinessErrorEnum.BUS_DRUG_WMS_0006, "登录信息");
        List<DrugStorageOutAllAsTo> drugStorageOutAllAsToList = new ArrayList<>();
        if (CollectionUtils.isEmpty(drugStorageOutAllTos)) {
            return drugStorageOutAllAsToList;
        }
        Map<String, List<DrugStorageOutAllTo>> map = new HashMap<>();
        if ("batchNo".equals(addWay)) {
            map = drugStorageOutAllTos.stream().collect(Collectors.groupingBy(p -> p.getDrugGoodsCode() + p.getBatchNo()));
        } else if ("batchNumNo".equals(addWay)) {
            map = drugStorageOutAllTos.stream().collect(Collectors.groupingBy(p -> p.getDrugGoodsCode() + p.getBatchNumNo()));
        } else {
            map = drugStorageOutAllTos.stream().collect(Collectors.groupingBy(DrugStorageOutAllTo::getDrugGoodsCode));
        }
        map.forEach((key, value) -> {
            drugStorageOutAllAsToList.add(HIPBeanUtil.copy(value.get(0), DrugStorageOutAllAsTo.class));
        });

        //跟大萌、晓雷、文青开会确认之后，轮循调用查询库存信息
        drugStorageOutAllAsToList.forEach(v -> {
            DrugStockTo drugStockTo = drugStockService.getDrugStockQuantity(loginInfo.getWorkGroupCode(), v.getDrugGoodsCode(), v.getBatchNo(), v.getBatchNumNo(), v.getSalePrice(), v.getPurchasePrice());
            v.setCurrentQuantity(drugStockTo.getQuantity());
            v.setCurrentQuantityMax(drugStockTo.getQuantityMax());
            v.setCurrentQuantityMin(drugStockTo.getQuantityMin());
        });
        return drugStorageOutAllAsToList;
    }

    @Operation(summary = "根据masterId对药库出退库明细进行查询。", description = "根据masterId对药库出退库明细进行查询。")
    @ApiResponse(description = "返回出退库明细信息集合", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = DrugStorageOutDetailTo.class))))
    @GetMapping(value = "/{masterId}/drugStorageOutDetails")
    public List<DrugStorageOutDetailTo> getDrugStorageOutDetails(@PathVariable("masterId") String masterId) {
        return drugStorageOutService.getDrugStorageOutDetails(masterId);
    }

    @Operation(summary = "根据主单ID查询主单以及明细数据。", description = "根据主单ID查询主单以及明细数据。")
    @ApiResponse(description = "返回主单和明细信息", content = @Content(mediaType = "application/json", schema = @Schema(implementation = DrugStorageOutTo.class)))
    @GetMapping(value = "/{id}/drugStorageOut")
    public DrugStorageOutAsTo getDrugStorageOutById(@PathVariable("id") String id) {
        CurrentOrgInfo loginInfo = HIPCurrentOrgUtils.getCurrentOrgInfo();
        BusinessAssert.notNull(loginInfo, DrugWmsBusinessErrorEnum.BUS_DRUG_WMS_0006, "登录信息");
        DrugStorageOutTo drugStorageOutTo = drugStorageOutService.getDrugStorageOutById(id);
        DrugStorageOutAsTo drugStorageOutAsTo = new DrugStorageOutAsTo();
        List<DrugStorageOutDetailAsTo> drugStorageOutDetailAsTos = new ArrayList<>();
        if (drugStorageOutTo != null && drugStorageOutTo.getDrugStorageOutDetails() != null) {
            drugStorageOutAsTo = HIPBeanUtil.copy(drugStorageOutTo, DrugStorageOutAsTo.class);
            drugStorageOutTo.getDrugStorageOutDetails().forEach(p -> {
                DrugStorageOutDetailAsTo drugStorageOutDetailAsTo = HIPBeanUtil.copy(p, DrugStorageOutDetailAsTo.class);
                DrugStockTo drugStockTo = drugStockService.getDrugStockQuantity(loginInfo.getWorkGroupCode(), p.getDrugGoodsCode(), p.getBatchNo(), p.getBatchNumNo(), p.getSalePrice(), p.getPurchasePrice());
                drugStorageOutDetailAsTo.setCurrentQuantity(drugStockTo.getQuantity());
                drugStorageOutDetailAsTo.setCurrentQuantityMax(drugStockTo.getQuantityMax());
                drugStorageOutDetailAsTo.setCurrentQuantityMin(drugStockTo.getQuantityMin());
                if (p.getDrugGoodsTo() != null) {
                    DrugGoodsTo drugGoodsTo = p.getDrugGoodsTo();
                    drugStorageOutDetailAsTo.setDrugGoodsName(drugGoodsTo.getDrugGoodsName());
                    drugStorageOutDetailAsTo.setMinUnitValue(drugGoodsTo.getMinUnitValue());
                    drugStorageOutDetailAsTo.setPackageUnitValue(drugGoodsTo.getPackageUnitValue());
                    drugStorageOutDetailAsTo.setDrugSpec(drugGoodsTo.getDrugSpec());
                    drugStorageOutDetailAsTo.setDrugFormValue(drugGoodsTo.getDrugFormValue());
                    drugStorageOutDetailAsTo.setToxiProperty(drugGoodsTo.getToxiProperty());
                    drugStorageOutDetailAsTo.setToxiPropertyName(drugGoodsTo.getToxiPropertyName());
                    drugStorageOutDetailAsTo.setInsuranceCode(drugGoodsTo.getInsuranceCode());
                    if (p.getDrugGoodsTo() instanceof DrugGoodsWestTo drugGoodsWestTo) {
                        drugStorageOutDetailAsTo.setApprovalDoc(drugGoodsWestTo.getApprovalDoc());
                        ;
                    }
                }
                drugStorageOutDetailAsTos.add(drugStorageOutDetailAsTo);
            });
            drugStorageOutAsTo.setDrugStorageOutDetailAsTos(drugStorageOutDetailAsTos);
        }
        return drugStorageOutAsTo;
    }

    @Operation(summary = "根据主单ID查询主单以及明细数据（打印用）。", description = "根据主单ID查询主单以及明细数据。")
    @ApiResponse(description = "返回主单和明细信息", content = @Content(mediaType = "application/json", schema = @Schema(implementation = DrugStorageOutTo.class)))
    @GetMapping(value = "/{id}/drugStorageOutForPrint")
    public DrugStorageOutAsTo getDrugStorageOutForPrintById(@PathVariable("id") String id) {
        CurrentOrgInfo loginInfo = HIPCurrentOrgUtils.getCurrentOrgInfo();
        BusinessAssert.notNull(loginInfo, DrugWmsBusinessErrorEnum.BUS_DRUG_WMS_0006, "登录信息");
        DrugStorageOutTo drugStorageOutTo = drugStorageOutService.getDrugStorageOutById(id);
        DrugStorageOutAsTo drugStorageOutAsTo = new DrugStorageOutAsTo();
        List<DrugStorageOutDetailAsTo> drugStorageOutDetailAsTos = new ArrayList<>();
        if (drugStorageOutTo != null && drugStorageOutTo.getDrugStorageOutDetails() != null) {
            drugStorageOutAsTo = HIPBeanUtil.copy(drugStorageOutTo, DrugStorageOutAsTo.class);
            drugStorageOutTo.getDrugStorageOutDetails().forEach(p -> {
                DrugStorageOutDetailAsTo drugStorageOutDetailAsTo = HIPBeanUtil.copy(p, DrugStorageOutDetailAsTo.class);
                //取当前库房库存
                DrugStockTo drugStockTo = drugStockService.getDrugStockQuantity(loginInfo.getWorkGroupCode(), p.getDrugGoodsCode(), p.getBatchNo(), p.getBatchNumNo(), p.getSalePrice(), p.getPurchasePrice());
                drugStorageOutDetailAsTo.setCurrentQuantity(drugStockTo.getQuantity());
                drugStorageOutDetailAsTo.setCurrentQuantityMax(drugStockTo.getQuantityMax());
                drugStorageOutDetailAsTo.setCurrentQuantityMin(drugStockTo.getQuantityMin());
                if (p.getDrugGoodsTo() != null) {
                    DrugGoodsTo drugGoodsTo = p.getDrugGoodsTo();
                    drugStorageOutDetailAsTo.setDrugGoodsName(drugGoodsTo.getDrugGoodsName());
                    drugStorageOutDetailAsTo.setMinUnitValue(drugGoodsTo.getMinUnitValue());
                    drugStorageOutDetailAsTo.setPackageUnitValue(drugGoodsTo.getPackageUnitValue());
                    drugStorageOutDetailAsTo.setDrugSpec(drugGoodsTo.getDrugSpec());
                }
                drugStorageOutDetailAsTos.add(drugStorageOutDetailAsTo);
            });
            drugStorageOutAsTo.setDrugStorageOutDetailAsTos(drugStorageOutDetailAsTos);
        }
        return drugStorageOutAsTo;
    }

    @Operation(summary = "根据masterId对药库出退库明细进行查询-分页查询", description = "根据masterId对药库出退库明细进行查询-分页查询")
    @ApiResponse(description = "返回出退库明细信息集合", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = DrugStorageOutDetailTo.class))))
    @GetMapping(value = "/{masterId}/drugStorageOutDetails/pages")
    public GridResultSet<DrugStorageOutDetailTo> getDrugStorageOutDetailPage(@PathVariable("masterId") String masterId, @ParameterObject @SpringQueryMap DrugStorageOutDetailQto drugStorageOutDetailQto) {
        return drugStorageOutService.getDrugStorageOutDetailPage(masterId, drugStorageOutDetailQto);
    }

    @Operation(summary = "根据出库单出库、出退库", description = "新单页面用")
    @PostMapping("/outStock")
    public void outStockDrugStorageOut(@RequestBody DrugStorageOutNto drugStorageOutNto) {
        CurrentOrgInfo loginInfo = HIPCurrentOrgUtils.getCurrentOrgInfo();
        BusinessAssert.notNull(loginInfo, DrugWmsBusinessErrorEnum.BUS_DRUG_WMS_0006, "登录信息");
        drugStorageOutNto.setStorageCode(loginInfo.getWorkGroupCode());
        drugStorageOutNto.setStorageName(loginInfo.getWorkGroupName());
        DrugStockSettingQto drugStockSettingQto = new DrugStockSettingQto();
        drugStockSettingQto.setStorageCode(loginInfo.getWorkGroupCode());
        drugStockSettingQto.setSettingCode(drugStorageOutNto.getOutType());
        List<DrugStockSettingTo> drugStockSettingTos = drugStockSettingService.getDrugStockSettings(drugStockSettingQto);
        BusinessAssert.notEmpty(drugStockSettingTos, DrugWmsBusinessErrorEnum.BUS_DRUG_WMS_0006, "出库方式");
        if (DrugChangeDirectionEnum.减少.getCode().equals(drugStockSettingTos.get(0).getChangeDirection())) {
            drugStorageOutNto.setInoutType("OUT");
            drugStorageOutService.outStockDrugStorageOut(drugStorageOutNto);
        } else if (DrugChangeDirectionEnum.增加.getCode().equals(drugStockSettingTos.get(0).getChangeDirection())) {
            drugStorageOutNto.setInoutType("IN");
            drugStorageOutService.refundStock(drugStorageOutNto);
        }
    }

    @Operation(summary = "根据masterId出库", description = "根据masterId出库")
    @PostMapping("/{masterId}/outStock")
    public void outStockDrugStorageOutById(@PathVariable("masterId") String masterId) {
        drugStorageOutService.outStockById(masterId);
    }

    @Operation(summary = "出库退库-根据主单Id出库退库", description = "出库退库-根据主单Id出库退库")
    @PostMapping("/{id}/refund")
    public void refundStockById(@PathVariable("id") String id) {
        drugStorageOutService.refundStockById(id);
    }

    @Operation(summary = "根据出库主单ID整单出库退库", description = "根据出库主单ID整单出库退库")
    @PostMapping("/{id}/outStockRefund")
    public void outStockRefundById(@PathVariable("id") String masterId) {
        drugStorageOutService.outStockRefundById(masterId);
    }

    @Operation(summary = "请领出库", description = "请领出库")
    @PostMapping("/applyToOutStock")
    public void applyToOutStock(@RequestBody DrugStorageOutNto drugStorageOutNto) {
        CurrentOrgInfo loginInfo = HIPCurrentOrgUtils.getCurrentOrgInfo();
        BusinessAssert.notNull(loginInfo, DrugWmsBusinessErrorEnum.BUS_DRUG_WMS_0006, "登录信息");
        drugStorageOutNto.setStorageCode(loginInfo.getWorkGroupCode());
        drugStorageOutNto.setStorageName(loginInfo.getWorkGroupName());
        drugStorageOutService.applyToOutStock(drugStorageOutNto);
    }

    @Operation(summary = "接收退药", description = "接收退药")
    @PostMapping("/applyToRefundStock")
    public void applyToRefundStock(@RequestBody DrugStorageOutNto drugStorageOutNto) {
        CurrentOrgInfo loginInfo = HIPCurrentOrgUtils.getCurrentOrgInfo();
        BusinessAssert.notNull(loginInfo, DrugWmsBusinessErrorEnum.BUS_DRUG_WMS_0006, "登录信息");
        drugStorageOutNto.setStorageCode(loginInfo.getWorkGroupCode());
        drugStorageOutNto.setStorageName(loginInfo.getWorkGroupName());
        drugStorageOutService.applyToRefundStock(drugStorageOutNto);
    }

    @Operation(summary = "创建药库出退库", description = "创建药库出退库")
    @ApiResponse(description = "返回出库信息", content = @Content(mediaType = "application/json", schema = @Schema(implementation = DrugStorageOutTo.class)))
    @PostMapping("/createDrugStorageOuts")
    public DrugStorageOutTo createDrugStorageOut(@RequestBody DrugStorageOutNto drugStorageOutNto) {
        CurrentOrgInfo loginInfo = HIPCurrentOrgUtils.getCurrentOrgInfo();
        BusinessAssert.notNull(loginInfo, DrugWmsBusinessErrorEnum.BUS_DRUG_WMS_0006, "登录信息");
        drugStorageOutNto.setStorageCode(loginInfo.getWorkGroupCode());
        drugStorageOutNto.setStorageName(loginInfo.getWorkGroupName());
        return drugStorageOutService.createDrugStorageOut(drugStorageOutNto);
    }

    @Operation(summary = "暂存药库出退库", description = "暂存药库出退库")
    @ApiResponse(description = "返回出库信息", content = @Content(mediaType = "application/json", schema = @Schema(implementation = DrugStorageOutTo.class)))
    @PostMapping("/tempDrugStorageOuts")
    public DrugStorageOutTo tempDrugStorageOut(@RequestBody DrugStorageOutNto drugStorageOutNto) {
        CurrentOrgInfo loginInfo = HIPCurrentOrgUtils.getCurrentOrgInfo();
        BusinessAssert.notNull(loginInfo, DrugWmsBusinessErrorEnum.BUS_DRUG_WMS_0006, "登录信息");
        drugStorageOutNto.setStorageCode(loginInfo.getWorkGroupCode());
        drugStorageOutNto.setStorageName(loginInfo.getWorkGroupName());
        return drugStorageOutService.tempDrugStorageOut(drugStorageOutNto);
    }

    @Operation(summary = "修改药库出退库", description = "修改药库出退库")
    @ApiResponse(description = "返回出库信息", content = @Content(mediaType = "application/json", schema = @Schema(implementation = DrugStorageOutTo.class)))
    @PostMapping("/updateDrugStorageOuts")
    public DrugStorageOutTo updateDrugStorageOut(@RequestBody DrugStorageOutNto drugStorageOutNto) {
        return drugStorageOutService.updateDrugStorageOutAndDetail(drugStorageOutNto);
    }

    @Operation(summary = "根据唯一标识删除药库出退库", description = "根据唯一标识删除药库出退库")
    @DeleteMapping("/{id}")
    public void deleteDrugStorageOut(@PathVariable("id") String id) {
        drugStorageOutService.deleteDrugStorageOut(id);
    }

    @Operation(summary = "获取药库出库去向单位数据源", description = "获取药库出库去向单位数据源")
    @ApiResponse(description = "返回去向单位集合", content = @Content(mediaType = "application/json", schema = @Schema(implementation = List.class)))
    @GetMapping(value = "/outDrugStorages")
    public List<WorkGroupTo> getDrugStorages() {
        List<WorkGroupTo> workGroupTos = new ArrayList<>();
        List<WorkGroupPharmacyTo> pharmacyToList = workGroupService.getEnablePharmacy();
        if (!CollectionUtils.isEmpty(pharmacyToList)) {
            workGroupTos.addAll(pharmacyToList);
        }
        return workGroupTos;
    }

    @Operation(summary = "获取药库出退库类型数据源", description = "获取药库出退库类型数据源")
    @ApiResponse(description = "返回出库类型集合", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = DrugStockSettingTo.class))))
    @GetMapping(value = "/stockOutSettingAll")
    public List<DrugStockSettingTo> getStockOutSettingForQuery() {
        CurrentOrgInfo loginInfo = HIPCurrentOrgUtils.getCurrentOrgInfo();
        BusinessAssert.notNull(loginInfo, DrugWmsBusinessErrorEnum.BUS_DRUG_WMS_0006, "登录信息");
        DrugStockSettingQto stockSettingQto = new DrugStockSettingQto();
        stockSettingQto.setStorageCode(loginInfo.getWorkGroupCode());
        stockSettingQto.setDirection(DrugDirectionTypeEnum.出库.getCode());
        stockSettingQto.setSettingType(DrugSettingTypeEnum.通用.getCode());
        stockSettingQto.setStatusCode(DrugStockSettingStatusEnum.有效.getCode());
        return drugStockSettingService.getDrugStockSettings(stockSettingQto);
    }

    @Operation(summary = "获取药库直接出库类型数据源", description = "获取药库直接出库类型数据源")
    @ApiResponse(description = "返回出库类型集合", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = DrugStockSettingTo.class))))
    @GetMapping(value = "/stockOutSetting")
    public List<DrugStockSettingTo> getStockOutSetting() {
        CurrentOrgInfo loginInfo = HIPCurrentOrgUtils.getCurrentOrgInfo();
        BusinessAssert.notNull(loginInfo, DrugWmsBusinessErrorEnum.BUS_DRUG_WMS_0006, "登录信息");
        DrugStockSettingQto stockSettingQto = new DrugStockSettingQto();
        stockSettingQto.setStorageCode(loginInfo.getWorkGroupCode());
        stockSettingQto.setDirection(DrugDirectionTypeEnum.出库.getCode());
        stockSettingQto.setChangeDirection(DrugChangeDirectionEnum.减少.getCode());
        stockSettingQto.setSettingType(DrugSettingTypeEnum.通用.getCode());
        stockSettingQto.setStatusCode(DrugStockSettingStatusEnum.有效.getCode());
        return drugStockSettingService.getDrugStockSettings(stockSettingQto);
    }

    @Operation(summary = "获取药库接收退药出库方式类型数据源", description = "获取药库接收退药出库方式类型数据源")
    @ApiResponse(description = "返回出库类型集合", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = DrugStockSettingTo.class))))
    @GetMapping(value = "/refundStockOutSetting")
    public List<DrugStockSettingTo> getRefundStockOutSetting() {
        CurrentOrgInfo loginInfo = HIPCurrentOrgUtils.getCurrentOrgInfo();
        BusinessAssert.notNull(loginInfo, DrugWmsBusinessErrorEnum.BUS_DRUG_WMS_0006, "登录信息");
        DrugStockSettingQto stockSettingQto = new DrugStockSettingQto();
        stockSettingQto.setStorageCode(loginInfo.getWorkGroupCode());
        stockSettingQto.setDirection(DrugDirectionTypeEnum.出库.getCode());
        stockSettingQto.setChangeDirection(DrugChangeDirectionEnum.增加.getCode());
        stockSettingQto.setSettingType(DrugSettingTypeEnum.通用.getCode());
        stockSettingQto.setStatusCode(DrugStockSettingStatusEnum.有效.getCode());
        return drugStockSettingService.getDrugStockSettings(stockSettingQto);
    }

    @Operation(summary = "根据查询条件查询主界面申请单（请领出库）", description = "根据查询条件查询主界面申请单（请领出库）")
    @ApiResponse(description = "返回药房请领申请集合", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = DrugPharmacyApplyTo.class))))
    @GetMapping(value = "/pharmacyApplies")
    public List<CardResultSetTo<DrugPharmacyApplyTo>> getDrugPharmacyApplies(@RequestParam("applyStorageCode") String applyStorageCode) {
        CurrentOrgInfo loginInfo = HIPCurrentOrgUtils.getCurrentOrgInfo();
        BusinessAssert.notNull(loginInfo, DrugWmsBusinessErrorEnum.BUS_DRUG_WMS_0006, "登录信息");
        return drugPharmacyApplyAsService.getDrugPharmacyAppliesForExport(loginInfo.getWorkGroupCode(), applyStorageCode);
    }

    @Operation(summary = "根据查询条件查询主界面申请单（接收退药）", description = "根据查询条件查询主界面申请单（接收退药）")
    @ApiResponse(description = "返回药房请领申请集合", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = DrugPharmacyApplyTo.class))))
    @GetMapping(value = "/pharmacyRefunds")
    public List<CardResultSetTo<DrugPharmacyApplyTo>> getDrugPharmacyRefunds(@RequestParam("applyStorageCode") String applyStorageCode) {
        CurrentOrgInfo loginInfo = HIPCurrentOrgUtils.getCurrentOrgInfo();
        BusinessAssert.notNull(loginInfo, DrugWmsBusinessErrorEnum.BUS_DRUG_WMS_0006, "登录信息");
        return drugPharmacyApplyAsService.getDrugPharmacyRefundsForExport(loginInfo.getWorkGroupCode(), applyStorageCode);
    }

    @Operation(summary = "根据申请单信息查询请领科室、当前科室库存信息", description = "根据申请单信息查询请领科室、当前科室库存信息")
    @ApiResponse(description = "返回药房请领申请集合（包含库存信息）", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = DrugPharmacyApplyDetailAsTo.class))))
    @PostMapping(value = "/appliesWithStockQuantity")
    public List<DrugPharmacyApplyDetailAsTo> getPharmacyAppliesWithStockQuantity(@RequestBody DrugPharmacyApplyTo drugPharmacyApplyTo) {
        return drugPharmacyApplyAsService.getPharmacyAppliesWithStockQuantity(drugPharmacyApplyTo);
    }

    @Operation(summary = "药库出库根据查询条件对药品库存按库房编码和药品编码汇总后进行分页查询", description = "药库出库根据查询条件对药品库存按库房编码和药品编码汇总后进行分页查询")
    @ApiResponse(description = "返回药品库存集合", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = DrugStockQto.class))))
    @GetMapping(value = "/drugStocks/drugOutPages")
    public GridResultSet<DrugStockTo> getDrugStockGroupDrugCodeOutPage(@ParameterObject @SpringQueryMap DrugStockQto drugStockQto) {
        CurrentOrgInfo loginInfo = HIPCurrentOrgUtils.getCurrentOrgInfo();
        BusinessAssert.notNull(loginInfo, DrugWmsBusinessErrorEnum.BUS_DRUG_WMS_0006, "登录信息");
        drugStockQto.setStorageCode(loginInfo.getWorkGroupCode());
        drugStockQto.setStorageName(loginInfo.getWorkGroupName());
        return drugStockService.getDrugStockGroupDrugCodeOutPage(drugStockQto);
    }

    @Operation(summary = "药库出库根据查询条件对药品库存按库房编码和药品编码和批号汇总后进行分页查询", description = "药库出库根据查询条件对药品库存按库房编码和药品编码和批号汇总后进行分页查询")
    @ApiResponse(description = "返回药品库存集合", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = DrugStockQto.class))))
    @GetMapping(value = "/drugStocks/batchNoPages")
    public GridResultSet<DrugStockTo> getDrugStockGroupBatchNoPage(@ParameterObject @SpringQueryMap DrugStockQto drugStockQto) {
        CurrentOrgInfo loginInfo = HIPCurrentOrgUtils.getCurrentOrgInfo();
        BusinessAssert.notNull(loginInfo, DrugWmsBusinessErrorEnum.BUS_DRUG_WMS_0006, "登录信息");
        drugStockQto.setStorageCode(loginInfo.getWorkGroupCode());
        drugStockQto.setStorageName(loginInfo.getWorkGroupName());
        return drugStockService.getDrugStockGroupBatchNoPage(drugStockQto);
    }

    @Operation(summary = "根据查询条件对药库出退库表查询总金额", description = "根据查询条件对药库出退库表查询总金额")
    @ApiResponse(description = "出库总金额", content = @Content(mediaType = "application/json", schema = @Schema(implementation = DrugStorageOutTo.class)))
    @GetMapping(value = "/sumAmount")
    public DrugStorageOutTo getSumAmount(@ParameterObject @SpringQueryMap DrugStorageOutQto drugStorageOutQto) {
        CurrentOrgInfo loginInfo = HIPCurrentOrgUtils.getCurrentOrgInfo();
        BusinessAssert.notNull(loginInfo, DrugWmsBusinessErrorEnum.BUS_DRUG_WMS_0006, "登录信息");
        drugStorageOutQto.setStorageCode(loginInfo.getWorkGroupCode());
        drugStorageOutQto.setStorageName(loginInfo.getWorkGroupName());
        return drugStorageOutService.getSumAmount(drugStorageOutQto);
    }

    @Operation(summary = "根据申请单ID整单驳回申请单和申请单明细", description = "根据申请单ID整单驳回申请单和申请单明细")
    @PostMapping(value = "/reject")
    public void updateDrugPharmacyAppliesStatusById(@RequestBody List<DrugPharmacyApplyNto> drugPharmacyApplyNtos) {
        drugPharmacyApplyService.updateDrugPharmacyAppliesStatusById(drugPharmacyApplyNtos);
    }

    @Operation(summary = "驳回原因数据源", description = "驳回原因数据源")
    @ApiResponse(description = "返回驳回原因集合", content = @Content(mediaType = "application/json", schema = @Schema(implementation = List.class)))
    @GetMapping(value = "/rejectReasons")
    public List<EnumTo<String>> getDrugRejectReason() {
        return DrugRejectReasonEnum.getList();
    }
}
