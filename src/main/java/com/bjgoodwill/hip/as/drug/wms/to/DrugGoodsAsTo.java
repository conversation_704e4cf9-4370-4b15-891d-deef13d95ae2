package com.bjgoodwill.hip.as.drug.wms.to;

import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@JsonTypeInfo(use = JsonTypeInfo.Id.MINIMAL_CLASS, include = JsonTypeInfo.As.PROPERTY, property = "minimal_class")
@Schema(description = "药品信息")
public class DrugGoodsAsTo implements Serializable {

    @Serial
    private static final long serialVersionUID = 8489705758619654244L;

    @Schema(description = "药品编码")
    private String drugGoodsCode;

    @Schema(description = "药品名称")
    private String drugGoodsName;

    @Schema(description = "商品名称")
    private String drugProductName;

    @Schema(description = "药品别名")
    private String drugCommonName;

    @Schema(description = "拼音码")
    private String inputPy;

    @Schema(description = "五笔码")
    private String inputWb;

    @Schema(description = "助记码")
    private String mnemonicCode;

    @Schema(description = "基本剂量")
    private Double dosageBase;

    @Schema(description = "剂量单位")
    private String dosageUnit;

    @Schema(description = "剂量单位名称")
    private String dosageUnitValue;

    @Schema(description = "最小单位")
    private String minUnit;

    @Schema(description = "最小单位名称")
    private String minUnitValue;

    @Schema(description = "包装数")
    private Integer packageNum;

    @Schema(description = "包装单位")
    private String packageUnit;

    @Schema(description = "包装单位名称")
    private String packageUnitValue;

    @Schema(description = "规格")
    private String drugSpec;

    @Schema(description = "购入价")
    private BigDecimal purchasePrice;

    @Schema(description = "零售价")
    private BigDecimal salePrice;

    @Schema(description = "加价方式")
    private String premiumType;

    @Schema(description = "药品类别")
    private String drugType;

    @Schema(description = "药品类别名称")
    private String drugTypeValue;

    @Schema(description = "药理归类")
    private String actionType;

    @Schema(description = "剂型")
    private String drugForm;

    @Schema(description = "剂型名称")
    private String drugFormValue;

    @Schema(description = "生产厂家")
    private String manufactureFirm;

    @Schema(description = "生产厂家名称")
    private String manufactureFirmValue;

    @Schema(description = "储藏条件")
    private String storageCondition;

    @Schema(description = "包装包材")
    private String packMaterial;

    @Schema(description = "医保甲乙丙类")
    private String insuranceType;

    @Schema(description = "医保编码")
    private String insuranceCode;

    @Schema(description = "医保名称")
    private String insuranceName;

    @Schema(description = "医保说明")
    private String insuranceProperty;

    @Schema(description = "药监局码")
    private String mpaCode;

    @Schema(description = "医院原药品编码")
    private String oldGoodsCode;

    @Schema(description = "费用类别")
    private String priceItemType;

    @Schema(description = "病案费用类型")
    private String mrFeeClass;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "医院编码")
    private String hospitalCode;

    @Schema(description = "状态")
    private String statusCode;

    @Schema(description = "创建人")
    private String createdStaff;

    @Schema(description = "创建时间")
    private LocalDateTime createdDate;

    @Schema(description = "最后修改的人员")
    private String updatedStaff;

    @Schema(description = "最后修改的时间")
    private LocalDateTime updatedDate;

    public String getDrugGoodsCode() {
        return drugGoodsCode;
    }

    public void setDrugGoodsCode(String drugGoodsCode) {
        this.drugGoodsCode = drugGoodsCode;
    }

    public String getDrugGoodsName() {
        return drugGoodsName;
    }

    public void setDrugGoodsName(String drugGoodsName) {
        this.drugGoodsName = drugGoodsName;
    }

    public String getDrugProductName() {
        return drugProductName;
    }

    public void setDrugProductName(String drugProductName) {
        this.drugProductName = drugProductName;
    }

    public String getDrugCommonName() {
        return drugCommonName;
    }

    public void setDrugCommonName(String drugCommonName) {
        this.drugCommonName = drugCommonName;
    }

    public String getInputPy() {
        return inputPy;
    }

    public void setInputPy(String inputPy) {
        this.inputPy = inputPy;
    }

    public String getInputWb() {
        return inputWb;
    }

    public void setInputWb(String inputWb) {
        this.inputWb = inputWb;
    }

    public String getMnemonicCode() {
        return mnemonicCode;
    }

    public void setMnemonicCode(String mnemonicCode) {
        this.mnemonicCode = mnemonicCode;
    }

    public Double getDosageBase() {
        return dosageBase;
    }

    public void setDosageBase(Double dosageBase) {
        this.dosageBase = dosageBase;
    }

    public String getDosageUnit() {
        return dosageUnit;
    }

    public void setDosageUnit(String dosageUnit) {
        this.dosageUnit = dosageUnit;
    }

    public String getDosageUnitValue() {
        return dosageUnitValue;
    }

    public void setDosageUnitValue(String dosageUnitValue) {
        this.dosageUnitValue = dosageUnitValue;
    }

    public String getMinUnit() {
        return minUnit;
    }

    public void setMinUnit(String minUnit) {
        this.minUnit = minUnit;
    }

    public String getMinUnitValue() {
        return minUnitValue;
    }

    public void setMinUnitValue(String minUnitValue) {
        this.minUnitValue = minUnitValue;
    }

    public Integer getPackageNum() {
        return packageNum;
    }

    public void setPackageNum(Integer packageNum) {
        this.packageNum = packageNum;
    }

    public String getPackageUnit() {
        return packageUnit;
    }

    public void setPackageUnit(String packageUnit) {
        this.packageUnit = packageUnit;
    }

    public String getPackageUnitValue() {
        return packageUnitValue;
    }

    public void setPackageUnitValue(String packageUnitValue) {
        this.packageUnitValue = packageUnitValue;
    }

    public String getDrugSpec() {
        return drugSpec;
    }

    public void setDrugSpec(String drugSpec) {
        this.drugSpec = drugSpec;
    }

    public BigDecimal getPurchasePrice() {
        return purchasePrice;
    }

    public void setPurchasePrice(BigDecimal purchasePrice) {
        this.purchasePrice = purchasePrice;
    }

    public BigDecimal getSalePrice() {
        return salePrice;
    }

    public void setSalePrice(BigDecimal salePrice) {
        this.salePrice = salePrice;
    }

    public String getPremiumType() {
        return premiumType;
    }

    public void setPremiumType(String premiumType) {
        this.premiumType = premiumType;
    }

    public String getDrugType() {
        return drugType;
    }

    public void setDrugType(String drugType) {
        this.drugType = drugType;
    }

    public String getDrugTypeValue() {
        return drugTypeValue;
    }

    public void setDrugTypeValue(String drugTypeValue) {
        this.drugTypeValue = drugTypeValue;
    }

    public String getActionType() {
        return actionType;
    }

    public void setActionType(String actionType) {
        this.actionType = actionType;
    }

    public String getDrugForm() {
        return drugForm;
    }

    public void setDrugForm(String drugForm) {
        this.drugForm = drugForm;
    }

    public String getDrugFormValue() {
        return drugFormValue;
    }

    public void setDrugFormValue(String drugFormValue) {
        this.drugFormValue = drugFormValue;
    }

    public String getManufactureFirm() {
        return manufactureFirm;
    }

    public void setManufactureFirm(String manufactureFirm) {
        this.manufactureFirm = manufactureFirm;
    }

    public String getManufactureFirmValue() {
        return manufactureFirmValue;
    }

    public void setManufactureFirmValue(String manufactureFirmValue) {
        this.manufactureFirmValue = manufactureFirmValue;
    }

    public String getStorageCondition() {
        return storageCondition;
    }

    public void setStorageCondition(String storageCondition) {
        this.storageCondition = storageCondition;
    }

    public String getPackMaterial() {
        return packMaterial;
    }

    public void setPackMaterial(String packMaterial) {
        this.packMaterial = packMaterial;
    }

    public String getInsuranceType() {
        return insuranceType;
    }

    public void setInsuranceType(String insuranceType) {
        this.insuranceType = insuranceType;
    }

    public String getInsuranceCode() {
        return insuranceCode;
    }

    public void setInsuranceCode(String insuranceCode) {
        this.insuranceCode = insuranceCode;
    }

    public String getInsuranceName() {
        return insuranceName;
    }

    public void setInsuranceName(String insuranceName) {
        this.insuranceName = insuranceName;
    }

    public String getInsuranceProperty() {
        return insuranceProperty;
    }

    public void setInsuranceProperty(String insuranceProperty) {
        this.insuranceProperty = insuranceProperty;
    }

    public String getMpaCode() {
        return mpaCode;
    }

    public void setMpaCode(String mpaCode) {
        this.mpaCode = mpaCode;
    }

    public String getOldGoodsCode() {
        return oldGoodsCode;
    }

    public void setOldGoodsCode(String oldGoodsCode) {
        this.oldGoodsCode = oldGoodsCode;
    }

    public String getPriceItemType() {
        return priceItemType;
    }

    public void setPriceItemType(String priceItemType) {
        this.priceItemType = priceItemType;
    }

    public String getMrFeeClass() {
        return mrFeeClass;
    }

    public void setMrFeeClass(String mrFeeClass) {
        this.mrFeeClass = mrFeeClass;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getHospitalCode() {
        return hospitalCode;
    }

    public void setHospitalCode(String hospitalCode) {
        this.hospitalCode = hospitalCode;
    }

    public String getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(String statusCode) {
        this.statusCode = statusCode;
    }

    public String getCreatedStaff() {
        return createdStaff;
    }

    public void setCreatedStaff(String createdStaff) {
        this.createdStaff = createdStaff;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getUpdatedStaff() {
        return updatedStaff;
    }

    public void setUpdatedStaff(String updatedStaff) {
        this.updatedStaff = updatedStaff;
    }

    public LocalDateTime getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(LocalDateTime updatedDate) {
        this.updatedDate = updatedDate;
    }

}
