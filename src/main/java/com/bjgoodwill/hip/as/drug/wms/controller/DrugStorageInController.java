package com.bjgoodwill.hip.as.drug.wms.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.bjgoodwill.hip.as.drug.wms.enums.DrugWmsBusinessErrorEnum;
import com.bjgoodwill.hip.as.drug.wms.service.DrugStorageInAsService;
import com.bjgoodwill.hip.as.drug.wms.to.DrugPurchasePlanAsTo;
import com.bjgoodwill.hip.as.drug.wms.to.DrugStorageInAsNto;
import com.bjgoodwill.hip.as.drug.wms.to.DrugStorageInAsTo;
import com.bjgoodwill.hip.common.bean.EnumTo;
import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.common.util.LocalDateUtil;
import com.bjgoodwill.hip.ds.drug.goods.service.DrugGoodsService;
import com.bjgoodwill.hip.ds.drug.goods.service.DrugPriceMarkupService;
import com.bjgoodwill.hip.ds.drug.goods.to.DrugGoodsSimpleTo;
import com.bjgoodwill.hip.ds.drug.inout.enmus.DrugInOutTypeEnum;
import com.bjgoodwill.hip.ds.drug.inout.stocksetting.enmus.DrugChangeDirectionEnum;
import com.bjgoodwill.hip.ds.drug.inout.stocksetting.enmus.DrugDirectionTypeEnum;
import com.bjgoodwill.hip.ds.drug.inout.stocksetting.enmus.DrugSettingTypeEnum;
import com.bjgoodwill.hip.ds.drug.inout.stocksetting.enmus.DrugStockSettingStatusEnum;
import com.bjgoodwill.hip.ds.drug.inout.stocksetting.service.DrugStockSettingService;
import com.bjgoodwill.hip.ds.drug.inout.stocksetting.to.DrugStockSettingQto;
import com.bjgoodwill.hip.ds.drug.inout.stocksetting.to.DrugStockSettingTo;
import com.bjgoodwill.hip.ds.drug.inout.storage.in.enmus.DrugInStatusEnum;
import com.bjgoodwill.hip.ds.drug.inout.storage.in.service.DrugStorageInService;
import com.bjgoodwill.hip.ds.drug.inout.storage.in.to.*;
import com.bjgoodwill.hip.ds.drug.pms.purchaseplan.service.DrugPurchasePlanService;
import com.bjgoodwill.hip.ds.drug.pms.purchaseplan.to.DrugPurchasePlanQto;
import com.bjgoodwill.hip.ds.drug.pms.purchaseplan.to.DrugPurchasePlanTo;
import com.bjgoodwill.hip.ds.drug.stock.stock.service.DrugStockService;
import com.bjgoodwill.hip.ds.drug.stock.stock.to.DrugStockTo;
import com.bjgoodwill.hip.ds.drug.stock.supplier.service.SupplierService;
import com.bjgoodwill.hip.ds.drug.stock.supplier.to.SupplierQto;
import com.bjgoodwill.hip.ds.drug.stock.supplier.to.SupplierTo;
import com.bjgoodwill.hip.ds.org.api.service.WorkGroupService;
import com.bjgoodwill.hip.ds.org.api.to.WorkGroupPharmacyTo;
import com.bjgoodwill.hip.ds.org.api.to.WorkGroupTo;
import com.bjgoodwill.hip.ds.param.api.service.ParameterService;
import com.bjgoodwill.hip.ds.param.api.to.ParameterTo;
import com.bjgoodwill.hip.enums.DictParameterEnum;
import com.bjgoodwill.hip.security.bean.CurrentOrgInfo;
import com.bjgoodwill.hip.security.util.HIPCurrentOrgUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@RestController("com.bjgoodwill.hip.as.drug.wms.controller.DrugStorageInController")
@SaCheckPermission("drugWms:storageIn")
@Tag(name = "药库入退库管理服务", description = "药库入退库服务类")
@RequestMapping("/drug/wms/storageIn")
public class DrugStorageInController {

    @Autowired
    private DrugStorageInService drugStorageInService;

    @Autowired
    private DrugStorageInAsService drugStorageInAsService;

    @Autowired
    private DrugStockSettingService drugStockSettingService;

    @Autowired
    private WorkGroupService workGroupService;

    @Autowired
    private ParameterService parameterService;

    @Autowired
    private DrugPurchasePlanService drugPurchasePlanService;

    @Autowired
    private SupplierService supplierService;

    @Autowired
    private DrugPriceMarkupService drugPriceMarkupService;

    @Autowired
    private DrugStockService drugStockService;

    @Autowired
    private DrugGoodsService drugGoodsService;

    @Operation(summary = "根据查询条件查询入退库信息-分页查询", description = "根据查询条件查询入退库信息-分页查询")
    @ApiResponse(description = "返回入退库信息集合", content = @Content(mediaType = "application/json", schema = @Schema(implementation = DrugStorageInTo.class)))
    @PostMapping(value = "/pages")
    public GridResultSet<DrugStorageInTo> getDrugStorageInPage(@RequestBody DrugStorageInQto drugStorageInQto) {
        CurrentOrgInfo loginInfo = HIPCurrentOrgUtils.getCurrentOrgInfo();
        BusinessAssert.notNull(loginInfo, DrugWmsBusinessErrorEnum.BUS_DRUG_WMS_0006, "登录信息");
        drugStorageInQto.setStorageCode(loginInfo.getWorkGroupCode());
        //查询时间不可超过3个月
        boolean flag = LocalDateUtil.betweenMonthsExceeding(drugStorageInQto.getInDateBegin(), drugStorageInQto.getInDateEnd(), 3);
        BusinessAssert.isTrue(!flag, DrugWmsBusinessErrorEnum.BUS_DRUG_WMS_0002, 3);

        ParameterTo<String> parameter = parameterService.getStringParameter(DictParameterEnum.DrugIsOpenApproval.getCode());
        boolean isOpenApproval = !(parameter != null && parameter.getValue().equals("0"));
        if (isOpenApproval) {
            drugStorageInQto.setSortBy("submitDate");
        } else {
            drugStorageInQto.setSortBy("inDate");
        }
        drugStorageInQto.setSortOrder("desc");
        return drugStorageInService.getDrugStorageInPage(drugStorageInQto);
    }

    @Operation(summary = "根据查询条件查询药库入退库表查询总金额。", description = "根据查询条件查询药库入退库表查询总金额")
    @ApiResponse(description = "返回入退库表查询总金额", content = @Content(mediaType = "application/json", schema = @Schema(implementation = DrugStorageInTo.class)))
    @PostMapping(value = "/sumAmount")
    public DrugStorageInTo getDrugStorageInSumAmount(@RequestBody DrugStorageInQto drugStorageInQto) {
        CurrentOrgInfo loginInfo = HIPCurrentOrgUtils.getCurrentOrgInfo();
        BusinessAssert.notNull(loginInfo, DrugWmsBusinessErrorEnum.BUS_DRUG_WMS_0006, "登录信息");
        drugStorageInQto.setStorageCode(loginInfo.getWorkGroupCode());
        drugStorageInQto.setStorageName(loginInfo.getWorkGroupName());
        //查询时间不可超过3个月
        boolean flag = LocalDateUtil.betweenMonthsExceeding(drugStorageInQto.getInDateBegin(), drugStorageInQto.getInDateEnd(), 3);
        BusinessAssert.isTrue(!flag, DrugWmsBusinessErrorEnum.BUS_DRUG_WMS_0002, 3);
        return drugStorageInService.getSumAmount(drugStorageInQto);
    }

    @Operation(summary = "根据主单ID查询入退库明细信息-分页查询", description = "根据主单ID查询入退库明细信息-分页查询")
    @ApiResponse(description = "返回入退库明细信息集合", content = @Content(mediaType = "application/json", schema = @Schema(implementation = DrugStorageInDetailTo.class)))
    @GetMapping(value = "/{masterId}/detail/Pages")
    public GridResultSet<DrugStorageInDetailTo> getDrugStorageInDetailPage(@PathVariable("masterId") String masterId) {
        return drugStorageInService.getDrugStorageInDetailPage(masterId);
    }

    @Operation(summary = "根据主单ID获取入库主单和明细-编辑入库单", description = "根据主单ID获取入库主单和明细-编辑入库单")
    @ApiResponse(description = "返回主单和明细集合", content = @Content(mediaType = "application/json", schema = @Schema(implementation = DrugStorageInTo.class)))
    @GetMapping(value = "/{masterId}")
    public DrugStorageInTo getDrugStorageInById(@PathVariable("masterId") String masterId) {
        return drugStorageInAsService.getDrugStorageInById(masterId);
    }

    @Operation(summary = "暂存入库信息", description = "暂存入库药品信息")
    @ApiResponse(description = "返回入库信息", content = @Content(mediaType = "application/json", schema = @Schema(implementation = DrugStorageInTo.class)))
    @PostMapping("/tempIn")
    public DrugStorageInTo tempDrugStorageIn(@RequestBody DrugStorageInNto drugStorageInNto) {
        CurrentOrgInfo loginInfo = HIPCurrentOrgUtils.getCurrentOrgInfo();
        BusinessAssert.notNull(loginInfo, DrugWmsBusinessErrorEnum.BUS_DRUG_WMS_0006, "登录信息");
        drugStorageInNto.setStorageCode(loginInfo.getWorkGroupCode());
        drugStorageInNto.setStorageName(loginInfo.getWorkGroupName());
        drugStorageInNto.setInoutType(DrugInOutTypeEnum.入.getCode());
        DrugStorageInTo drugStorageInTo = drugStorageInService.tempDrugStorageIns(drugStorageInNto);
        return drugStorageInTo != null ? drugStorageInService.getDrugStorageInById(drugStorageInTo.getId()) : null;
    }

    @Operation(summary = "暂存退库信息", description = "暂存退库药品信息")
    @ApiResponse(description = "返回退库信息", content = @Content(mediaType = "application/json", schema = @Schema(implementation = DrugStorageInTo.class)))
    @PostMapping("/tempOut")
    public DrugStorageInTo tempDrugStorageOut(@RequestBody DrugStorageInNto drugStorageInNto) {
        CurrentOrgInfo loginInfo = HIPCurrentOrgUtils.getCurrentOrgInfo();
        BusinessAssert.notNull(loginInfo, DrugWmsBusinessErrorEnum.BUS_DRUG_WMS_0006, "登录信息");
        drugStorageInNto.setStorageCode(loginInfo.getWorkGroupCode());
        drugStorageInNto.setStorageName(loginInfo.getWorkGroupName());
        drugStorageInNto.setInoutType(DrugInOutTypeEnum.出.getCode());
        DrugStorageInTo drugStorageInTo = drugStorageInService.tempDrugStorageIns(drugStorageInNto);
        return drugStorageInTo != null ? drugStorageInService.getDrugStorageInById(drugStorageInTo.getId()) : null;
    }

    @Operation(summary = "暂存提交入库信息", description = "暂存提交入库信息")
    @ApiResponse(description = "返回入库信息", content = @Content(mediaType = "application/json", schema = @Schema(implementation = DrugStorageInTo.class)))
    @PostMapping("/tempAndCommitIn")
    public DrugStorageInTo tempAndCommitDrugStorageIn(@RequestBody DrugStorageInNto drugStorageInNto) {
        CurrentOrgInfo loginInfo = HIPCurrentOrgUtils.getCurrentOrgInfo();
        BusinessAssert.notNull(loginInfo, DrugWmsBusinessErrorEnum.BUS_DRUG_WMS_0006, "登录信息");
        drugStorageInNto.setStorageCode(loginInfo.getWorkGroupCode());
        drugStorageInNto.setStorageName(loginInfo.getWorkGroupName());
        drugStorageInNto.setInoutType(DrugInOutTypeEnum.入.getCode());
        DrugStorageInTo drugStorageInTo = drugStorageInService.tempDrugStorageIns(drugStorageInNto);
        if (drugStorageInTo != null) {
            drugStorageInTo = drugStorageInService.getDrugStorageInById(drugStorageInTo.getId());
            drugStorageInService.submitDrugStorageIn(drugStorageInTo.getId(), drugStorageInTo.getVersion());
        }
        return drugStorageInService.getDrugStorageInById(drugStorageInTo.getId());
    }

    @Operation(summary = "暂存提交退库信息", description = "暂存提交退库信息")
    @ApiResponse(description = "返回入库信息", content = @Content(mediaType = "application/json", schema = @Schema(implementation = DrugStorageInTo.class)))
    @PostMapping("/tempAndCommitOut")
    public DrugStorageInTo tempAndCommitDrugStorageOut(@RequestBody DrugStorageInNto drugStorageInNto) {
        CurrentOrgInfo loginInfo = HIPCurrentOrgUtils.getCurrentOrgInfo();
        BusinessAssert.notNull(loginInfo, DrugWmsBusinessErrorEnum.BUS_DRUG_WMS_0006, "登录信息");
        drugStorageInNto.setStorageCode(loginInfo.getWorkGroupCode());
        drugStorageInNto.setStorageName(loginInfo.getWorkGroupName());
        drugStorageInNto.setInoutType(DrugInOutTypeEnum.出.getCode());
        DrugStorageInTo drugStorageInTo = drugStorageInService.tempDrugStorageIns(drugStorageInNto);
        if (drugStorageInTo != null) {
            drugStorageInTo = drugStorageInService.getDrugStorageInById(drugStorageInTo.getId());
            drugStorageInService.submitDrugStorageIn(drugStorageInTo.getId(), drugStorageInTo.getVersion());
        }
        return drugStorageInService.getDrugStorageInById(drugStorageInTo.getId());
    }

    @Operation(summary = "提交入库信息--仅审核流程调用", description = "提交入库信息")
    @ApiResponse(description = "返回入库信息", content = @Content(mediaType = "application/json", schema = @Schema(implementation = DrugStorageInAsTo.class)))
    @PutMapping("/submit")
    public void submitDrugStorageIn(@RequestParam("id") String id, @RequestParam("version") Integer version) {
        drugStorageInService.submitDrugStorageIn(id, version);
    }

    @Operation(summary = "审核入库信息--仅审核流程调用", description = "审核入库信息")
    @ApiResponse(description = "返回入库信息", content = @Content(mediaType = "application/json", schema = @Schema(implementation = DrugStorageInAsTo.class)))
    @PutMapping("/reviewer")
    public void reviewerDrugStorageIn(@RequestParam("id") String id, @RequestParam("version") Integer version) {
        drugStorageInService.reviewerDrugStorageIn(id, version);
    }

    @Operation(summary = "确认入库上账", description = "确认入库上账")
    @PutMapping("/inStock")
    public void inStockDrugStorageIn(@RequestBody DrugStorageInAsNto drugStorageInAsNto) {
        CurrentOrgInfo loginInfo = HIPCurrentOrgUtils.getCurrentOrgInfo();
        BusinessAssert.notNull(loginInfo, DrugWmsBusinessErrorEnum.BUS_DRUG_WMS_0006, "登录信息");
        drugStorageInAsNto.setStorageCode(loginInfo.getWorkGroupCode());
        drugStorageInAsNto.setStorageName(loginInfo.getWorkGroupName());
        drugStorageInAsService.inStockDrugStorageIn(drugStorageInAsNto);
    }

    @Operation(summary = "确认退库上账", description = "确认退库上账")
    @PutMapping("/refundStock")
    public void refundStockDrugStorageIn(@RequestBody DrugStorageInNto drugStorageInNto) {
        CurrentOrgInfo loginInfo = HIPCurrentOrgUtils.getCurrentOrgInfo();
        BusinessAssert.notNull(loginInfo, DrugWmsBusinessErrorEnum.BUS_DRUG_WMS_0006, "登录信息");
        drugStorageInNto.setStorageCode(loginInfo.getWorkGroupCode());
        drugStorageInNto.setStorageName(loginInfo.getWorkGroupName());
        drugStorageInAsService.refundStockDrugStorageIn(drugStorageInNto);
    }

    @Operation(summary = "补发票", description = "补发票")
    @PutMapping("/invoice")
    public void updateDrugStorageInDetailInvoice(@RequestBody DrugStorageInInvoiceEto drugStorageInInvoiceEto) {
        drugStorageInService.updateDrugStorageInDetailInvoice(drugStorageInInvoiceEto);
    }

    @Operation(summary = "根据唯一标识删除药库入退库。")
    @DeleteMapping("/{id}")
    void deleteDrugStorageIn(@PathVariable("id") String id, @RequestParam("version") Integer version) {
        drugStorageInService.deleteDrugStorageIn(id, version);
    }

    @Operation(summary = "根据唯一标识删除药库入退库明细。")
    @DeleteMapping("/detail/{id}")
    void deleteDrugStorageInDetail(@PathVariable("id") String id, @RequestParam("version") Integer version) {
        drugStorageInService.deleteDrugStorageInDetail(id, version);
    }

    @Operation(summary = "获取药库入库类型数据源", description = "获取药库入库类型数据源")
    @ApiResponse(description = "返回入库类型集合", content = @Content(mediaType = "application/json", schema = @Schema(implementation = DrugStockSettingTo.class)))
    @GetMapping(value = "/stockInSetting")
    public List<DrugStockSettingTo> getStockInSetting() {
        CurrentOrgInfo loginInfo = HIPCurrentOrgUtils.getCurrentOrgInfo();
        BusinessAssert.notNull(loginInfo, DrugWmsBusinessErrorEnum.BUS_DRUG_WMS_0006, "登录信息");
        DrugStockSettingQto stockSettingQto = new DrugStockSettingQto();
        stockSettingQto.setStorageCode(loginInfo.getWorkGroupCode());
        stockSettingQto.setDirection(DrugDirectionTypeEnum.入库.getCode());
        stockSettingQto.setChangeDirection(DrugChangeDirectionEnum.增加.getCode());
        stockSettingQto.setSettingType(DrugSettingTypeEnum.通用.getCode());
        stockSettingQto.setStatusCode(DrugStockSettingStatusEnum.有效.getCode());
        List<DrugStockSettingTo> stockSettingTos = drugStockSettingService.getDrugStockSettings(stockSettingQto);
        return stockSettingTos;
    }

    @Operation(summary = "获取药库退库类型数据源", description = "获取药库退库类型数据源")
    @ApiResponse(description = "返回退库类型集合", content = @Content(mediaType = "application/json", schema = @Schema(implementation = DrugStockSettingTo.class)))
    @GetMapping(value = "/stockRefundSetting")
    public List<DrugStockSettingTo> getStockRefundSetting() {
        CurrentOrgInfo loginInfo = HIPCurrentOrgUtils.getCurrentOrgInfo();
        BusinessAssert.notNull(loginInfo, DrugWmsBusinessErrorEnum.BUS_DRUG_WMS_0006, "登录信息");
        DrugStockSettingQto stockSettingQto = new DrugStockSettingQto();
        stockSettingQto.setStorageCode(loginInfo.getWorkGroupCode());
        stockSettingQto.setDirection(DrugDirectionTypeEnum.入库.getCode());
        stockSettingQto.setChangeDirection(DrugChangeDirectionEnum.减少.getCode());
        stockSettingQto.setSettingType(DrugSettingTypeEnum.通用.getCode());
        stockSettingQto.setStatusCode(DrugStockSettingStatusEnum.有效.getCode());
        List<DrugStockSettingTo> stockSettingTos = drugStockSettingService.getDrugStockSettings(stockSettingQto);
        return stockSettingTos;
    }

    @Operation(summary = "获取药库直接出库类型数据源", description = "获取药库直接出库类型数据源")
    @ApiResponse(description = "返回出库类型集合", content = @Content(mediaType = "application/json", schema = @Schema(implementation = DrugStockSettingTo.class)))
    @GetMapping(value = "/stockOutSetting")
    public List<DrugStockSettingTo> getStockOutSetting() {
        CurrentOrgInfo loginInfo = HIPCurrentOrgUtils.getCurrentOrgInfo();
        BusinessAssert.notNull(loginInfo, DrugWmsBusinessErrorEnum.BUS_DRUG_WMS_0006, "登录信息");
        DrugStockSettingQto stockSettingQto = new DrugStockSettingQto();
        stockSettingQto.setStorageCode(loginInfo.getWorkGroupCode());
        stockSettingQto.setDirection(DrugDirectionTypeEnum.出库.getCode());
        stockSettingQto.setChangeDirection(DrugChangeDirectionEnum.减少.getCode());
        stockSettingQto.setSettingType(DrugSettingTypeEnum.通用.getCode());
        stockSettingQto.setStatusCode(DrugStockSettingStatusEnum.有效.getCode());
        List<DrugStockSettingTo> stockSettingTos = drugStockSettingService.getDrugStockSettings(stockSettingQto);
        return stockSettingTos;
    }

    @Operation(summary = "获取药库入库状态数据源", description = "获取药库入库状态数据源")
    @ApiResponse(description = "返回入库状态集合", content = @Content(mediaType = "application/json", schema = @Schema(implementation = List.class)))
    @GetMapping(value = "/status")
    public List<EnumTo<String>> getDrugInStatus() {
        ParameterTo<String> parameter = parameterService.getStringParameter(DictParameterEnum.DrugIsOpenApproval.getCode());
        boolean isOpenApproval = !(parameter != null && parameter.getValue().equals("0"));
        List<EnumTo<String>> list = new ArrayList<>();
        for (DrugInStatusEnum var : DrugInStatusEnum.values()) {
            if (!isOpenApproval && (var.name().equals("已审核") || var.name().equals("已提交"))) {
                continue;
            }
            EnumTo<String> enumTo = new EnumTo<>();
            enumTo.setCode(var.getCode());
            enumTo.setName(var.name());
            list.add(enumTo);
        }
        return list;
    }

    @Operation(summary = "获取药库入库去向单位数据源", description = "获取药库入库去向单位数据源")
    @ApiResponse(description = "返回去向单位集合", content = @Content(mediaType = "application/json", schema = @Schema(implementation = List.class)))
    @GetMapping(value = "/getDrugStorages")
    public List<WorkGroupTo> getDrugStorages(@RequestParam("dirUnit") String dirUnit) {
        BusinessAssert.notNull(dirUnit, DrugWmsBusinessErrorEnum.BUS_DRUG_WMS_0001, "去向单位");
        List<WorkGroupTo> workGroupTos = new ArrayList<>();
        if (dirUnit.equals("pharmacy")) {
            List<WorkGroupPharmacyTo> pharmacyToList = workGroupService.getEnablePharmacy();
            if (!CollectionUtils.isEmpty(pharmacyToList)) {
                workGroupTos.addAll(pharmacyToList);
            }
        } else {
            workGroupTos = workGroupService.getEnableWorkGroup();
        }

        return workGroupTos;
    }

    @Operation(summary = "获取是否开启提交审核流程参数", description = "获取是否省略提交审核流程参数")
    @ApiResponse(description = "返回参数值", content = @Content(mediaType = "application/json", schema = @Schema(implementation = String.class)))
    @GetMapping(value = "/parameter")
    public Boolean getParameterValue() {
        ParameterTo<String> parameter = parameterService.getStringParameter(DictParameterEnum.DrugIsOpenApproval.getCode());
        return !(parameter != null && parameter.getValue().equals("0"));
    }

    @Operation(summary = "获取药品采购计划数据", description = "获取药品采购计划数据")
    @ApiResponse(description = "返回参数值", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = DrugPurchasePlanTo.class))))
    @PostMapping(value = "/purchasePlans")
    public List<DrugPurchasePlanAsTo> getDrugPurchasePlans(@RequestBody DrugPurchasePlanQto drugPurchasePlanQto) {
        return drugStorageInAsService.getDrugPurchasePlansByMoreConditions(drugPurchasePlanQto);
    }

    @Operation(summary = "获取供货商数据源", description = "获取供货商数据源")
    @ApiResponse(description = "返回供货商集合")
    @GetMapping(value = "/dataSource")
    public List<SupplierTo> getSupplierDataSource() {
        return supplierService.getSuppliers(new SupplierQto());
    }

    @Operation(summary = "获取供货商数据源-分页查询", description = "获取供货商数据源-分页查询")
    @ApiResponse(description = "返回供货商集合")
    @GetMapping(value = "/dataSourceByPage")
    public GridResultSet<SupplierTo> getSupplierDataSourcePage() {
        return supplierService.getSupplierPage(new SupplierQto());
    }

    @Operation(summary = "根据药品类型和金额，自动计算加价后金额。", description = "根据药品类型和金额，自动计算加价后金额。")
    @ApiResponse(description = "药品金额")
    @GetMapping(value = "/markUp")
    public BigDecimal getMarkupAmount(@RequestParam("drugType") String drugType, @RequestParam("price") BigDecimal price) {
        return drugPriceMarkupService.getMarkupAmount(drugType, price);
    }

    @Operation(summary = "根据多参数获取药品库存数。", description = "根据多参数获取药品库存数")
    @ApiResponse(description = "药品库存数量")
    @GetMapping(value = "/drugStock")
    public DrugStockTo getDrugStockNum(@RequestParam("drugGoodsCode") String drugGoodsCode,
                                       @RequestParam(name = "batchNo", required = false) String batchNo, @RequestParam(name = "batchNumNo", required = false) String batchNumNo,
                                       @RequestParam(name = "salePrice", required = false) BigDecimal salePrice, @RequestParam(name = "purchasePrice", required = false) BigDecimal purchasePrice) {
        CurrentOrgInfo loginInfo = HIPCurrentOrgUtils.getCurrentOrgInfo();
        BusinessAssert.notNull(loginInfo, DrugWmsBusinessErrorEnum.BUS_DRUG_WMS_0006, "登录信息");
        return drugStockService.getDrugStockQuantity(loginInfo.getWorkGroupCode(), drugGoodsCode, batchNo, batchNumNo, salePrice, purchasePrice);
    }

    @Operation(summary = "查询主要字段的全部药品字典信息", description = "查询主要字段的全部药品字典信息")
    @ApiResponse(description = "返回药品字典", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = DrugGoodsSimpleTo.class))))
    @GetMapping(value = "/simpleDrugs")
    public List<DrugGoodsSimpleTo> getALLDrugGoodsSimple() {
        return drugGoodsService.getALLDrugGoodsSimple();
    }
}
