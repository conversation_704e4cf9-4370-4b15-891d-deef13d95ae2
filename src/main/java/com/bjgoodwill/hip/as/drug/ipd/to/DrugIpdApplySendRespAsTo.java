package com.bjgoodwill.hip.as.drug.ipd.to;

import com.bjgoodwill.hip.ds.drug.ipd.apply.to.DrugIpdApplyTo;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/3/5 9:05
 * @ClassName: DrugIpdApplySendRespAsTo
 * @Description: 住院发药弹窗显示
 */
@Schema(description = "住院发药弹窗显示To")
public class DrugIpdApplySendRespAsTo {

    @Schema(description = "状态(1:弹窗；0:不弹窗)")
    private String statusCode;

    private List<DrugIpdApplySendVerifyAsTo> drugIpdApplySendVerifyAsTos;

    private List<DrugIpdApplyTo> successResultList;

    public String getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(String statusCode) {
        this.statusCode = statusCode;
    }

    public List<DrugIpdApplySendVerifyAsTo> getDrugIpdApplySendVerifyAsTos() {
        return drugIpdApplySendVerifyAsTos;
    }

    public void setDrugIpdApplySendVerifyAsTos(List<DrugIpdApplySendVerifyAsTo> drugIpdApplySendVerifyAsTos) {
        this.drugIpdApplySendVerifyAsTos = drugIpdApplySendVerifyAsTos;
    }

    public List<DrugIpdApplyTo> getSuccessResultList() {
        return successResultList;
    }

    public void setSuccessResultList(List<DrugIpdApplyTo> successResultList) {
        this.successResultList = successResultList;
    }
}
