package com.bjgoodwill.hip.as.drug.ipd.enums;

import com.bjgoodwill.hip.common.exception.BusinessErrorEnum;

public enum DrugIpdBusinessErrorEnum implements BusinessErrorEnum {

    BUS_DRUG_IPD_0000("数据已被修改,请刷新重试！"),
    BUS_DRUG_IPD_0001("参数[%s]不可为空！"),
    BUS_DRUG_IPD_0002("查询时间区间不可超过[%s]个月！"),
    BUS_DRUG_IPD_0003("查询时间区间不可超过[%s]天！"),
    BUS_DRUG_IPD_0004("明细不能为空！"),
    BUS_DRUG_IPD_0005("参数[%s]未维护！"),
    BUS_DRUG_IPD_0006("[%s]获取失败！"),
    BUS_DRUG_IPD_9999("[%s]"),;

    private final String message;

    DrugIpdBusinessErrorEnum(String message) {
        this.message = message;
    }

    @Override
    public String getCode() {
        return this.name();
    }

    @Override
    public String getMessage() {
        return message;
    }
}
