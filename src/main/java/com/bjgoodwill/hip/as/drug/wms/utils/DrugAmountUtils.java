package com.bjgoodwill.hip.as.drug.wms.utils;

import cn.hutool.core.util.NumberUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class DrugAmountUtils {

    /**
     * 根据大包装数量、小包装数量、包装数计算小包装数量
     *
     * @param quantityPackage
     * @param quantityMin
     * @param packageNum
     * @return
     */
    public static BigDecimal getQunility(BigDecimal quantityPackage, BigDecimal quantityMin, Integer packageNum) {

        //计算总数量，大包装数量*包装数+小包装数量
        BigDecimal quantity = NumberUtil.add(NumberUtil.mul(quantityPackage, packageNum), quantityMin);

        return quantity;
    }

    /**
     * 根据小包装数量计算大包装数
     *
     * @param quantity
     * @param packageNum
     * @return
     */
    public static BigDecimal getPackage(BigDecimal quantity, Integer packageNum) {
        //计算大包装数量
        BigDecimal quantityPackage = NumberUtil.div(quantity, packageNum, 0, RoundingMode.DOWN);
       return quantityPackage;
    }

    /**
     * 根据小包装数量计算大包装数后剩余小包装数
     *
     * @param quantity
     * @param packageNum
     * @return
     */
    public static BigDecimal getMin(BigDecimal quantity, Integer packageNum) {
        //计算小包装数量
        BigDecimal quantityMin = quantity.remainder(BigDecimal.valueOf(packageNum));
        return quantityMin;
    }

    /**
     * 根据小包装数量计算大包装数
     *
     * @param quantity
     * @param packageNum
     * @return
     */
    public static BigDecimal getMax(BigDecimal quantity, Integer packageNum) {
        if (quantity == null) {
            return BigDecimal.ZERO;
        }
        //计算大包装数量
        BigDecimal quantityPackage = NumberUtil.div(quantity, packageNum, 0, RoundingMode.DOWN);
        return quantityPackage;
    }

    /**
     * 根据小包装数量、包装数、单价计算总金额
     *
     * @param quantity
     * @param packageNum
     * @param price
     * @return
     */
    public static BigDecimal getPriceAmount(BigDecimal quantity, Integer packageNum,BigDecimal price) {
        //计算金额
        BigDecimal priceAmount = NumberUtil.div(NumberUtil.mul(quantity, price), packageNum, 4);
        return priceAmount;
    }

    /**
     * 拼装数量和单位
     * @param quantity
     * @param maxUnit
     * @param minUnit
     * @return
     */
    public static String getQuantityUnitByMin(BigDecimal quantity,Integer packageNum, String maxUnit ,String minUnit){
        String str = "";
        BigDecimal quantityMax = getPackage(quantity,packageNum);
        BigDecimal quantityMin = getMin(quantity,packageNum);
        if(quantityMax.compareTo(BigDecimal.ZERO) != 0){
            str+=quantityMax+maxUnit;
        }
        if(quantityMin.compareTo(BigDecimal.ZERO) != 0){
            str+=quantityMin+minUnit;
        }
        return str;
    }

}
