package com.bjgoodwill.hip.as.drug.wms.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSONObject;
import com.bjgoodwill.hip.as.drug.wms.enums.DrugWmsBusinessErrorEnum;
import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.drug.goods.service.DrugGoodsService;
import com.bjgoodwill.hip.ds.drug.goods.to.DrugGoodsQto;
import com.bjgoodwill.hip.ds.drug.goods.to.DrugGoodsSimpleTo;
import com.bjgoodwill.hip.ds.drug.goods.to.DrugGoodsTo;
import com.bjgoodwill.hip.ds.drug.stock.stockplace.service.DrugStockPlaceService;
import com.bjgoodwill.hip.ds.drug.stock.stockplace.to.*;
import com.bjgoodwill.hip.security.bean.CurrentOrgInfo;
import com.bjgoodwill.hip.security.util.HIPCurrentOrgUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Tag(name = "药品库存定义维护应用服务", description = "药品库存定义维护应用服务类")
@RestController("com.bjgoodwill.hip.as.drug.wms.controller.DrugStockPlaceController")
//@SaCheckPermission("drugWms:stockPlace")
@RequestMapping(value = "/drug/wms/stockPlace")
public class DrugStockPlaceController {

    @Autowired
    private DrugStockPlaceService drugStockPlaceService;
    @Autowired
    private DrugGoodsService drugGoodsService;
    @Autowired
    private ThreadPoolTaskExecutor executor;

    //左扣掉右侧重复返回左侧
    @Operation(summary = "根据查询条件对左侧药品信息查询", description = "根据查询条件对左侧药品信息查询")
    @ApiResponse(description = "根据查询条件对左侧药品信息查询")
    @GetMapping("/drugGoods")
    public List<DrugGoodsSimpleTo> getDrugGoodses(@ParameterObject @SpringQueryMap DrugGoodsQto drugGoodsQto) {

        // 获取登录信息
        long loginInfoStartTime = System.currentTimeMillis();
        CurrentOrgInfo loginInfo = HIPCurrentOrgUtils.getCurrentOrgInfo();
        BusinessAssert.notNull(loginInfo, DrugWmsBusinessErrorEnum.BUS_DRUG_WMS_0006, "登录信息");
        long loginInfoEndTime = System.currentTimeMillis();
        // 异步获取左侧药品信息
        CompletableFuture<List<DrugGoodsSimpleTo>> leftFuture = CompletableFuture.supplyAsync(() -> {
            long leftStartTime = System.currentTimeMillis();
            List<DrugGoodsSimpleTo> result = drugGoodsService.getALLDrugGoodsSimple();
            long leftEndTime = System.currentTimeMillis();
            return result;
        }, executor);

        // 异步获取右侧药品库存定义
        CompletableFuture<List<DrugStockPlaceTo>> rightFuture = CompletableFuture.supplyAsync(() -> {
            long rightStartTime = System.currentTimeMillis();
            List<DrugStockPlaceTo> result = drugStockPlaceService.getDrugStockPlacesByStorageCodeSimple(loginInfo.getWorkGroupCode());
            long rightEndTime = System.currentTimeMillis();
            return result;
        }, executor);

        // 合并结果并进行过滤
        CompletableFuture<List<DrugGoodsSimpleTo>> filteredFuture = leftFuture.thenCombine(rightFuture, (left, right) -> {
            if (CollectionUtil.isEmpty(right) || CollectionUtil.isEmpty(left)) {
                return left;
            }

            // 构建右侧药品编码集合
            long setStartTime = System.currentTimeMillis();
            Set<String> rightDrugGoodsCodes = new HashSet<>();
            right.forEach(drugStockPlace -> rightDrugGoodsCodes.add(drugStockPlace.getDrugGoodsCode()));
            long setEndTime = System.currentTimeMillis();

            // 过滤左侧药品信息
            long filterStartTime = System.currentTimeMillis();
            List<DrugGoodsSimpleTo> filteredResult = left.parallelStream()
                    .filter(drugGoods -> !rightDrugGoodsCodes.contains(drugGoods.getDrugGoodsCode()))
                    .collect(Collectors.toList());
            //保留小数点后两位
            filteredResult.parallelStream().forEach(drugGoods -> {
                if (drugGoods.getSalePrice() != null) {
                    drugGoods.setSalePrice(drugGoods.getSalePrice().setScale(2, RoundingMode.HALF_UP));
                }

            });
            long filterEndTime = System.currentTimeMillis();

            return filteredResult;
        });

        // 等待过滤任务完成
        List<DrugGoodsSimpleTo> finalResult = filteredFuture.join();

        long endTime = System.currentTimeMillis();

        return finalResult;
    }

    private Double formatDouble(double value) {
        return BigDecimal.valueOf(value).setScale(2, RoundingMode.HALF_UP).doubleValue();
    }

    @Operation(summary = "根据库房编码对右侧药品库存定义进行模糊分页查询", description = "根据库房编码对药品库存定义进行模糊分页查询")
    @ApiResponse(description = "返回分页库存定义集合")
    @PostMapping(value = "/queryByStorageCodePage")
    public GridResultSet<DrugStockPlaceTo> getDrugStockPlacesByStorageCodePage(@RequestBody DrugStockPlaceQto drugStockPlaceQto) {
        CurrentOrgInfo loginInfo = HIPCurrentOrgUtils.getCurrentOrgInfo();
        Object o = JSONObject.toJSONString(drugStockPlaceQto);
        GridResultSet<DrugStockPlaceTo> resultSet = drugStockPlaceService.getDrugStockPlacesByStorageCodePage(loginInfo.getWorkGroupCode(), drugStockPlaceQto);
        return resultSet;
    }

    @Operation(summary = "根据查询条件对药品信息进行分页查询", description = "根据查询条件对药品信息进行分页查询")
    @ApiResponse(description = "根据查询条件对药品信息进行分页查询")
    @PostMapping("/drugGoods/pages")
    public GridResultSet<DrugGoodsTo> getDrugGoodsPage(@RequestBody DrugGoodsQto drugGoodsQto) {
        Object o = JSONObject.toJSONString(drugGoodsQto);
        GridResultSet<DrugGoodsTo> resultSet = drugGoodsService.getDrugGoodsPage(drugGoodsQto);
        return resultSet;
    }

    @Operation(summary = "创建药品库存定义", description = "创建药品库存定义")
    @ApiResponse(description = "新增药品库存定义")
    @PostMapping("")
    public DrugStockPlaceTo createStockPlace(@RequestBody DrugStockPlaceNto drugStockPlaceNto) {
        CurrentOrgInfo loginInfo = HIPCurrentOrgUtils.getCurrentOrgInfo();
        BusinessAssert.notNull(loginInfo, DrugWmsBusinessErrorEnum.BUS_DRUG_WMS_0006, "登录信息");
        drugStockPlaceNto.setStorageCode(loginInfo.getWorkGroupCode());
        return drugStockPlaceService.createDrugStockPlace(drugStockPlaceNto);
    }

    @Operation(summary = "根据库房编码对药品库存定义进行查询", description = "根据库房编码对药品库存定义进行查询")
    @ApiResponse(description = "根据库房编码对药品库存定义进行查询")
    @GetMapping("/getByStorageCode")
    public List<DrugStockPlaceSimpleTo> getDrugStockPlacesByStorageCode() {
        CurrentOrgInfo loginInfo = HIPCurrentOrgUtils.getCurrentOrgInfo();
        BusinessAssert.notNull(loginInfo, DrugWmsBusinessErrorEnum.BUS_DRUG_WMS_0006, "登录信息");
        List<DrugStockPlaceSimpleTo> result = drugStockPlaceService.getDrugStockPlacesByStorageCodeSimpleNew(loginInfo.getWorkGroupCode());
        return result;
    }


    @Operation(summary = "根据唯一标识修改药品库存定义", description = "根据唯一标识修改药品库存定义")
    @ApiResponse(description = "返回修改药品库存定义")
    @PutMapping("/{code}")
    public boolean updateDrugStockPlace(@PathVariable("code") String id, @RequestBody DrugStockPlaceEto drugStockPlaceEto) {
        CurrentOrgInfo loginInfo = HIPCurrentOrgUtils.getCurrentOrgInfo();
        BusinessAssert.notNull(loginInfo, DrugWmsBusinessErrorEnum.BUS_DRUG_WMS_0006, "登录信息");
        drugStockPlaceEto.setStorageCode(loginInfo.getWorkGroupCode());
        drugStockPlaceService.updatePartDrugStockPlace(id, drugStockPlaceEto.getMaxQuantity(), drugStockPlaceEto.getMinQuantity(), drugStockPlaceEto.getStockPlaceID() != null ? drugStockPlaceEto.getStockPlaceID() : "", drugStockPlaceEto.getStockPlaceName() != null ? drugStockPlaceEto.getStockPlaceName() : "", drugStockPlaceEto.getVersion());
        return true;
    }

    @Operation(summary = "根据标识删除库存定义", description = "根据标识删除库存定义")
    @DeleteMapping("/{code}")
    public boolean deleteDrugStockPlace(@PathVariable("code") String id) {
        drugStockPlaceService.deleteDrugStockPlace(id);
        return true;
    }

    @Operation(summary = "根据唯一标识返回药品库存定义", description = "根据唯一标识返回药品库存定义")
    @ApiResponse(description = "返回库存定义")
    @GetMapping("/{code}")
    public DrugStockPlaceTo getDrugStockPlaceById(@PathVariable("code") String id) {
        return drugStockPlaceService.getDrugStockPlaceById(id);
    }

    @Operation(summary = "根据查询条件对药品库存定义进行查询", description = "根据查询条件对药品库存定义进行查询")
    @ApiResponse(description = "返回库存定义集合")
    @PostMapping(value = "/query")
    public List<DrugStockPlaceTo> getDrugStockPlaces(@RequestBody DrugStockPlaceQto drugStockPlaceQto) {
        return drugStockPlaceService.getDrugStockPlaces(drugStockPlaceQto);
    }

    @Operation(summary = "根据查询条件对药品库存定义进行分页查询", description = "根据查询条件对药品库存定义进行分页查询")
    @ApiResponse(description = "返回分页库存定义集合")
    @PostMapping(value = "/queryByPage")
    public GridResultSet<DrugStockPlaceTo> getDrugStockPlacePage(@RequestBody DrugStockPlaceQto drugStockPlaceQto) {
        return drugStockPlaceService.getDrugStockPlacePage(drugStockPlaceQto);
    }


}