package com.bjgoodwill.hip.as.drug.wms.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.bjgoodwill.hip.as.drug.wms.service.DrugInventoryAsService;
import com.bjgoodwill.hip.as.drug.wms.to.*;
import com.bjgoodwill.hip.common.util.HIPBeanUtil;
import com.bjgoodwill.hip.ds.drug.inout.inventory.service.DrugInventoryService;
import com.bjgoodwill.hip.ds.drug.inout.inventory.to.DrugInventoryDetailTo;
import com.bjgoodwill.hip.ds.drug.inout.inventory.to.DrugInventoryTo;
import com.bjgoodwill.hip.excel.util.ExcelUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Author: zhangyunchuan
 * @Date: 2024/7/24 16:12
 * @PROJECT: 药品盘点
 */
@Tag(name = "药品盘点应用服务", description = "药品盘点应用服务类")
@RestController("com.bjgoodwill.hip.as.drug.wms.controller.DrugInventoryController")
@SaCheckPermission("drugWms:inventory")
@RequestMapping(value = "/drug/wms/inventory", produces = "application/json; charset=utf-8")
public class DrugInventoryController {

    @Autowired
    private DrugInventoryAsService drugInventoryAsService;

    @Autowired
    private DrugInventoryService drugInventoryService;

    @Operation(summary = "药品盘点单初始化", description = "进入页面时调用初始化接口传入当前登录科室,会返回待结存的盘点单" +
            "<br/>此时金额使用盈亏购入总额(purchaseAmount)")
    @ApiResponse(description = "药品盘点单初始化", content = @Content(mediaType = "application/json", schema = @Schema(implementation = DrugInventoryTo.class)))
    @GetMapping("/drugInventoryInitialize")
    public DrugInventoryTo drugInventoryInitialize() {
        return drugInventoryAsService.drugInventoryInitialize();
    }

    @Operation(summary = "创建药品盘点", description = "创建药品盘点单需要以下入参：标识id(前端生成)、库房编码、库房名称、生成盘点类型,其中生成盘点类型共有三种状态：非零库存盘点:1、零库存盘点:2、异动盘点:3")
    @ApiResponse(description = "返回药品信息", content = @Content(mediaType = "application/json", schema = @Schema(implementation = DrugInventoryAsTo.class)))
    @PostMapping("/createDrugInventory")
    public DrugInventoryAsTo createDrugInventory(@RequestBody @Valid DrugInventoryAsNto drugInventoryAsNto) {
        return drugInventoryAsService.createDrugInventory(drugInventoryAsNto);
    }

    @Operation(summary = "全盘", description = "点击全盘按钮后需要将药品盘点单id传给后端进行数据处理")
    @ApiResponse(description = "返回该盘点单实时数据", content = @Content(mediaType = "application/json", schema = @Schema(implementation = DrugInventoryTo.class)))
    @PutMapping("/drugInventories/updateAll/{id:.+}")
    public DrugInventoryTo updateDrugInventoryAll(@PathVariable("id") String id) {
        return drugInventoryAsService.updateDrugInventoryAll(id);
    }

    @Operation(summary = "取消盘点", description = "取消盘点需要将药品盘点单id传给后端进行数据处理")
    @ApiResponse(description = "无数据返回", content = @Content(mediaType = "application/json"))
    @PutMapping("/drugInventories/cancel/{id:.+}")
    public void deleteDrugInventory(@PathVariable("id") String id) {
        drugInventoryService.deleteDrugInventory(id);
    }

    @Operation(summary = "修改实际盘点数", description = "焦点离开当前药品行后请求后台实时数据,数据包括盈亏数、发药数、实时库存" +
            "<br/>请求数据：当前药品标识id、版本号version、大包装数、小包装数、当前药品盈亏金额、盈亏总金额" +
            "<br/>注：大包装数和小包装数不可同时为空、其中哪一个为空哪一个传0" +
            "<br/>此时金额使用盈亏购入总额")
    @ApiResponse(description = "返回该药品实时数据", content = @Content(mediaType = "application/json", schema = @Schema(implementation = DrugInventoryExtendAsTo.class)))
    @PostMapping("/updateDrugInventoryDetail")
    public DrugInventoryExtendAsTo updateDrugInventoryDetail(@RequestBody @Valid DrugInventoryDetailAsEto drugInventoryDetailAsEto) {
        return drugInventoryAsService.updateDrugInventoryDetail(drugInventoryDetailAsEto);
    }

    @Operation(summary = "刷新发药数", description = "根据药品盘点单id刷新整张药品盘点单数据")
    @ApiResponse(description = "返回该盘点单实时数据", content = @Content(mediaType = "application/json", schema = @Schema(implementation = DrugInventoryTo.class)))
    @GetMapping("/refreshStock/{id}")
    public DrugInventoryTo updateRefreshStock(@PathVariable("id") String id) {
        return drugInventoryAsService.updateRefreshStock(id);
    }

    @Operation(summary = "结存", description = "结存整张盘点单需要盘点单标识id以及版本号version;若结存期间发生数据变化则需要手动点击刷新按钮,修改数据后重新进行结存")
    @ApiResponse(description = "返回该盘点单实时数据", content = @Content(mediaType = "application/json"))
    @PostMapping("/drugInventoryConfirm")
    public void drugInventoryConfirm(@RequestBody @Valid DrugInventoryAsEto drugInventoryAsEto) {
        drugInventoryAsService.drugInventoryConfirm(drugInventoryAsEto);
    }

    @Operation(summary = "查询历史盘点单集合", description = "根据当前登录科室编码查询历史盘点单集合,查询历史盘点单单条明细数据<br/>历史药品盘点单盘点人和盘点时间取结存人和结存时间")
    @ApiResponse(description = "返回历史盘点单集合", content = @Content(mediaType = "application/json", schema = @Schema(implementation = DrugInventoryTo.class)))
    @GetMapping("/historyInventories")
    public List<DrugInventoryTo> getHistoryInventories() {
        return drugInventoryAsService.getHistoryInventories();
    }

    @Operation(summary = "查询历史盘点单明细数据", description = "根据明细标识id,查询历史盘点单单条明细数据<br/>历史药品盘点单盘点人和盘点时间取结存人和结存时间")
    @ApiResponse(description = "返回历史盘点单明细数据", content = @Content(mediaType = "application/json", schema = @Schema(implementation = DrugInventoryTo.class)))
    @GetMapping("/historyInventory/{id}")
    public DrugInventoryTo getHistoryInventory(@PathVariable("id") String id) {
        return drugInventoryAsService.getDrugInventoryById(id);
    }

    @Operation(summary = "历史导出", description = "导出到Excel表格<br/>入参需要ID进行查询历史盘点单药品明细")
    @ApiResponse(description = "导出到Excel表格", content = @Content(mediaType = "application/json"))
    @PostMapping(value = "/exportHistory")
    public void exportHistory(@RequestBody @Valid DrugInventoryAsQto drugInventoryAsQto, HttpServletResponse response) {
        //获取导出数据
        DrugInventoryTo drugInventoryTo = drugInventoryService.getDrugInventoryById(drugInventoryAsQto.getId());
        //调用导出方法
        if (CollectionUtils.isNotEmpty(drugInventoryTo.getDrugInventoryDetails())) {
            List<DrugInventoryDetailTo> drugInventoryDetails = drugInventoryTo.getDrugInventoryDetails();
            ExcelUtil.exportExcel(response, HIPBeanUtil.copy(drugInventoryDetails, DrugInventoryExpTo.class), "药品盘点", DrugInventoryExpTo.class);
        }
    }

    @Operation(summary = "导出", description = "导出到Excel表格<br/>入参需要当前页面药品明细")
    @ApiResponse(description = "导出到Excel表格", content = @Content(mediaType = "application/json"))
    @PostMapping(value = "/export")
    public void export(@RequestBody @Valid DrugInventoryAsTo drugInventoryAsTo, HttpServletResponse response) {
        //调用导出方法
        if (CollectionUtils.isNotEmpty(drugInventoryAsTo.getDrugInventoryDetails())) {
            List<DrugInventoryDetailAsTo> drugInventoryDetails = drugInventoryAsTo.getDrugInventoryDetails();
            ExcelUtil.exportExcel(response, HIPBeanUtil.copy(drugInventoryDetails, DrugInventoryExpTo.class), "药品盘点", DrugInventoryExpTo.class);
        }
    }

}
