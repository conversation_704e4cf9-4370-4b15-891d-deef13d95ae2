package com.bjgoodwill.hip.as.drug.wms.to;

import com.bjgoodwill.hip.ds.drug.goods.to.DrugGoodsOtherTo;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 药品其他信息应用层To
 */
@Schema(description = "药品其他信息")
public class DrugGoodsOtherAsTo extends DrugGoodsOtherTo {

    @Schema(description = "药品图片URL")
    private String drugImageUrl;

    public String getDrugImageUrl() {
        return drugImageUrl;
    }

    public void setDrugImageUrl(String drugImageUrl) {
        this.drugImageUrl = drugImageUrl;
    }
}
