package com.bjgoodwill.hip.as.drug.wms.controller;

import com.bjgoodwill.hip.as.drug.wms.enums.DrugWmsBusinessErrorEnum;
import com.bjgoodwill.hip.as.drug.wms.service.PurchasePlanApplyAsService;
import com.bjgoodwill.hip.as.drug.wms.to.DrugPurchaseIntentionAsNto;
import com.bjgoodwill.hip.as.drug.wms.to.DrugPurchaseIntentionAsQto;
import com.bjgoodwill.hip.as.drug.wms.to.DrugPurchaseIntentionAsTo;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.common.util.HIPBeanUtil;
import com.bjgoodwill.hip.excel.util.ExcelUtil;
import com.bjgoodwill.hip.security.bean.CurrentOrgInfo;
import com.bjgoodwill.hip.security.util.HIPCurrentOrgUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Tag(name = "采购计划申请", description = "采购计划申请")
@RestController("com.bjgoodwill.hip.as.drug.wms.controller.PurchasePlanApplyController")
@RequestMapping(value = "/drug/wms/purchasePlan")
public class PurchasePlanApplyController {

    @Autowired
    private PurchasePlanApplyAsService purchasePlanApplyAsService;

    @Operation(summary = "采购计划申请信息查询", description = "采购计划申请信息查询")
    @ApiResponse(description = "返回采购计划申请信息", content = @Content(mediaType = "application/json", schema = @Schema(implementation = DrugPurchaseIntentionAsTo.class)))
    @PostMapping(value = "/getPurchasePlanApplyList")
    public List<DrugPurchaseIntentionAsTo> getPurchasePlanApplyList(@RequestBody @Valid DrugPurchaseIntentionAsQto drugPurchaseIntentionAsQto) {
        CurrentOrgInfo loginInfo = HIPCurrentOrgUtils.getCurrentOrgInfo();
        BusinessAssert.notNull(loginInfo, DrugWmsBusinessErrorEnum.BUS_DRUG_WMS_0006, "登录信息");
        drugPurchaseIntentionAsQto.setStorageCode(loginInfo.getWorkGroupCode());
        return purchasePlanApplyAsService.getPurchasePlanApplyList(drugPurchaseIntentionAsQto);
    }

    @Operation(summary = "新增采购计划申请信息", description = "新增采购计划申请信息")
    @ApiResponse(description = "返回采购计划申请信息", content = @Content(mediaType = "application/json", schema = @Schema(implementation = DrugPurchaseIntentionAsTo.class)))
    @PostMapping("/newPurchasePlanApplyList")
    public void newPurchasePlanApplyList(@RequestBody @Valid List<DrugPurchaseIntentionAsNto> drugPurchaseIntentionAsNtoList) {
        CurrentOrgInfo loginInfo = HIPCurrentOrgUtils.getCurrentOrgInfo();
        BusinessAssert.notNull(loginInfo, DrugWmsBusinessErrorEnum.BUS_DRUG_WMS_0006, "登录信息");
        drugPurchaseIntentionAsNtoList.stream().forEach(a -> {
            a.setApplyStorageCode(loginInfo.getWorkGroupCode());
            a.setApplyStorageName(loginInfo.getWorkGroupName());
        });
        purchasePlanApplyAsService.newPurchasePlanApplyList(drugPurchaseIntentionAsNtoList);
    }

    @Operation(summary = "采购计划申请选择药品信息", description = "采购计划申请选择药品信息")
    @ApiResponse(description = "返回采购计划申请选择药品信息", content = @Content(mediaType = "application/json", schema = @Schema(implementation = DrugPurchaseIntentionAsTo.class)))
    @PostMapping(value = "/getPurchaseSelectedDrug")
    public List<DrugPurchaseIntentionAsTo> getPurchaseSelectedDrug(@RequestBody @Valid DrugPurchaseIntentionAsQto drugPurchaseIntentionAsQto) {
        CurrentOrgInfo loginInfo = HIPCurrentOrgUtils.getCurrentOrgInfo();
        BusinessAssert.notNull(loginInfo, DrugWmsBusinessErrorEnum.BUS_DRUG_WMS_0006, "登录信息");
        drugPurchaseIntentionAsQto.setStorageCode(loginInfo.getWorkGroupCode());
        return purchasePlanApplyAsService.getPurchaseSelectedDrug(drugPurchaseIntentionAsQto);
    }


    @Operation(summary = "导出", description = "导出到Excel表格")
    @ApiResponse(description = "导出到Excel表格", content = @Content(mediaType = "application/json"))
    @PostMapping(value = "/export")
    public void export(@RequestBody @Valid List<DrugPurchaseIntentionAsTo> resultMainDTOList, HttpServletResponse response) {

        //调用导出方法
        if (CollectionUtils.isNotEmpty(resultMainDTOList)) {
            ExcelUtil.exportExcel(response, HIPBeanUtil.copy(resultMainDTOList, DrugPurchaseIntentionAsTo.class), "已计划", DrugPurchaseIntentionAsTo.class);
        }
    }
}
