package com.bjgoodwill.hip.as.drug.wms.service.impl;

import com.bjgoodwill.hip.as.drug.wms.service.DrugStorageInAsService;
import com.bjgoodwill.hip.as.drug.wms.service.assembler.DrugStorageInAsAssembler;
import com.bjgoodwill.hip.as.drug.wms.to.DrugPurchasePlanAsTo;
import com.bjgoodwill.hip.as.drug.wms.to.DrugStorageInAsNto;
import com.bjgoodwill.hip.as.drug.wms.utils.DrugAmountUtils;
import com.bjgoodwill.hip.common.exception.BusinessException;
import com.bjgoodwill.hip.common.util.HIPBeanUtil;
import com.bjgoodwill.hip.ds.drug.goods.to.DrugGoodsHerbsTo;
import com.bjgoodwill.hip.ds.drug.goods.to.DrugGoodsWestTo;
import com.bjgoodwill.hip.ds.drug.inout.enmus.DrugInOutTypeEnum;
import com.bjgoodwill.hip.ds.drug.inout.storage.in.service.DrugStorageInService;
import com.bjgoodwill.hip.ds.drug.inout.storage.in.to.DrugStorageInNto;
import com.bjgoodwill.hip.ds.drug.inout.storage.in.to.DrugStorageInTo;
import com.bjgoodwill.hip.ds.drug.pms.purchaseplan.service.DrugPurchasePlanService;
import com.bjgoodwill.hip.ds.drug.pms.purchaseplan.to.DrugPurchasePlanQto;
import com.bjgoodwill.hip.ds.drug.pms.purchaseplan.to.DrugPurchasePlanTo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

@Service("com.bjgoodwill.hip.as.drug.wms.service.DrugStorageInAsService")
public class DrugStorageInAsServiceImpl implements DrugStorageInAsService {

    @Autowired
    private DrugStorageInService drugStorageInService;

    @Autowired
    private DrugPurchasePlanService drugPurchasePlanService;

    @Override
    public DrugStorageInTo getDrugStorageInById(String masterId) {
        return drugStorageInService.getDrugStorageInById(masterId);
    }

    @Override
    public DrugStorageInTo tempDrugStorageIn(DrugStorageInNto drugStorageInNto) {
        Assert.notNull(drugStorageInNto, "参数drugStorageInNto不能为空！");

        DrugStorageInTo drugStorageInTo = new DrugStorageInTo();
        if (StringUtils.isBlank(drugStorageInNto.getId())) {
            drugStorageInTo = drugStorageInService.createDrugStorageIn(drugStorageInNto);
        } else {
            drugStorageInTo = drugStorageInService.updateDrugStorageInAndDetail(drugStorageInNto);
        }
        return drugStorageInService.getDrugStorageInById(drugStorageInTo.getId());
    }

    @Override
    public void inStockDrugStorageIn(DrugStorageInAsNto drugStorageInAsNto) {
        Assert.notNull(drugStorageInAsNto, "参数drugStorageInAsNto不能为空！");

        DrugStorageInNto drugStorageInNto = DrugStorageInAsAssembler.asNtoToNto(drugStorageInAsNto, true);
        drugStorageInNto.setInoutType(DrugInOutTypeEnum.入.getCode());
        //勾选了入库直接出库
        if (drugStorageInAsNto.getImmediateOut() != null && drugStorageInAsNto.getImmediateOut()) {
            //出库方式和去向单位必填
            if (drugStorageInAsNto.getOutType() == null || drugStorageInAsNto.getReceiveOrg() == null) {
                throw new BusinessException("勾选了直接出库，请填写出库类型和去向单位！");
            }
            //药库入库且直接出库
            drugStorageInService.inAndOutStockDrugStorageIn(drugStorageInNto,
                    drugStorageInAsNto.getOutType(), drugStorageInAsNto.getOutTypeName(), drugStorageInAsNto.getReceiveOrg(), drugStorageInAsNto.getReceiveOrgName());
        } else {
            //药库入库
            drugStorageInService.inStockDrugStorageIn(drugStorageInNto);
        }
    }

    @Override
    public void refundStockDrugStorageIn(DrugStorageInNto drugStorageInNto) {
        Assert.notNull(drugStorageInNto, "参数drugStorageInAsNto不能为空！");
        //药库退库
        drugStorageInNto.setInoutType(DrugInOutTypeEnum.出.getCode());
        drugStorageInService.refundStockDrugStorageIn(drugStorageInNto);

    }

    @Override
    public List<DrugPurchasePlanAsTo> getDrugPurchasePlansByMoreConditions(DrugPurchasePlanQto drugPurchasePlanQto) {
        List<DrugPurchasePlanAsTo> drugPurchasePlanAsTos = new ArrayList<>();
        List<DrugPurchasePlanTo> drugPurchasePlanTos = drugPurchasePlanService.getDrugPurchasePlansByMoreConditions(drugPurchasePlanQto);
        if (!CollectionUtils.isEmpty(drugPurchasePlanTos)) {
            drugPurchasePlanTos.forEach(p -> {
                if (!CollectionUtils.isEmpty(p.getDrugPurchasePlanDetails())) {
                    p.getDrugPurchasePlanDetails().forEach(d -> {
                        DrugPurchasePlanAsTo drugPurchasePlanAsTo = HIPBeanUtil.copy(d, DrugPurchasePlanAsTo.class);
                        if (d.getDrugGoodsTo() instanceof DrugGoodsWestTo) {
                            DrugGoodsWestTo drugGoodsWestTo = HIPBeanUtil.copy(d.getDrugGoodsTo(), DrugGoodsWestTo.class);
                            drugPurchasePlanAsTo.setDrugGoodsWestTo(drugGoodsWestTo);
                            drugPurchasePlanAsTo.setDrugSpec(drugGoodsWestTo.getDrugSpec());
                            drugPurchasePlanAsTo.setSalePrice(drugGoodsWestTo.getSalePrice());
                            drugPurchasePlanAsTo.setDrugPurchasePrice(drugGoodsWestTo.getPurchasePrice());
                            drugPurchasePlanAsTo.setInsuranceCode(drugGoodsWestTo.getInsuranceCode());
                            drugPurchasePlanAsTo.setApprovalDoc(drugGoodsWestTo.getApprovalDoc());
                            drugPurchasePlanAsTo.setManufactureFirm(drugGoodsWestTo.getManufactureFirm());
                            drugPurchasePlanAsTo.setManufactureFirmValue(drugGoodsWestTo.getManufactureFirmValue());
                            drugPurchasePlanAsTo.setMinUnit(drugGoodsWestTo.getMinUnit());
                            drugPurchasePlanAsTo.setMinUnitValue(drugGoodsWestTo.getMinUnitValue());
                            drugPurchasePlanAsTo.setPackageUnit(drugGoodsWestTo.getPackageUnit());
                            drugPurchasePlanAsTo.setPackageUnitValue(drugGoodsWestTo.getPackageUnitValue());
                            drugPurchasePlanAsTo.setPackageNum(drugGoodsWestTo.getPackageNum());
                            drugPurchasePlanAsTo.setDrugType(drugGoodsWestTo.getDrugType());
                            if (d.getPlanNum() != null) {
                                drugPurchasePlanAsTo.setPurchaseAmount(DrugAmountUtils.getPriceAmount(d.getPlanNum(), drugGoodsWestTo.getPackageNum(), drugGoodsWestTo.getPurchasePrice()));
                                drugPurchasePlanAsTo.setSaleAmount(DrugAmountUtils.getPriceAmount(d.getPlanNum(), drugGoodsWestTo.getPackageNum(), drugGoodsWestTo.getSalePrice()));
                            }

                        } else if (d.getDrugGoodsTo() instanceof DrugGoodsHerbsTo) {
                            DrugGoodsHerbsTo drugGoodsHerbsTo = HIPBeanUtil.copy(d.getDrugGoodsTo(), DrugGoodsHerbsTo.class);
                            drugPurchasePlanAsTo.setDrugGoodsHerbsTo(drugGoodsHerbsTo);

                            drugPurchasePlanAsTo.setDrugSpec(drugGoodsHerbsTo.getDrugSpec());
                            drugPurchasePlanAsTo.setSalePrice(drugGoodsHerbsTo.getSalePrice());
                            drugPurchasePlanAsTo.setDrugPurchasePrice(drugGoodsHerbsTo.getPurchasePrice());
                            drugPurchasePlanAsTo.setInsuranceCode(drugGoodsHerbsTo.getInsuranceCode());
                            drugPurchasePlanAsTo.setManufactureFirm(drugGoodsHerbsTo.getManufactureFirm());
                            drugPurchasePlanAsTo.setManufactureFirmValue(drugGoodsHerbsTo.getManufactureFirmValue());
                            drugPurchasePlanAsTo.setMinUnit(drugGoodsHerbsTo.getMinUnit());
                            drugPurchasePlanAsTo.setMinUnitValue(drugGoodsHerbsTo.getMinUnitValue());
                            drugPurchasePlanAsTo.setPackageUnit(drugGoodsHerbsTo.getPackageUnit());
                            drugPurchasePlanAsTo.setPackageUnitValue(drugGoodsHerbsTo.getPackageUnitValue());
                            drugPurchasePlanAsTo.setPackageNum(drugGoodsHerbsTo.getPackageNum());
                            drugPurchasePlanAsTo.setDrugType(drugGoodsHerbsTo.getDrugType());
                            if (d.getPlanNum() != null) {
                                drugPurchasePlanAsTo.setPurchaseAmount(DrugAmountUtils.getPriceAmount(d.getPlanNum(), drugGoodsHerbsTo.getPackageNum(), drugGoodsHerbsTo.getPurchasePrice()));
                                drugPurchasePlanAsTo.setSaleAmount(DrugAmountUtils.getPriceAmount(d.getPlanNum(), drugGoodsHerbsTo.getPackageNum(), drugGoodsHerbsTo.getSalePrice()));
                            }
                        }
                        drugPurchasePlanAsTo.setPlanDate(p.getPlanDate());
                        drugPurchasePlanAsTo.setPlanStorageCode(p.getPlanStorageCode());
                        drugPurchasePlanAsTo.setPlanStorageName(p.getPlanStorageName());
                        drugPurchasePlanAsTo.setSupplier(p.getSupplier());
                        drugPurchasePlanAsTo.setSupplierName(p.getSupplierName());
                        drugPurchasePlanAsTo.setCreatedDate(p.getCreatedDate());
                        drugPurchasePlanAsTo.setCreatedStaff(p.getCreatedStaff());
                        drugPurchasePlanAsTo.setCreatedStaffName(p.getCreatedStaffName());
                        drugPurchasePlanAsTos.add(drugPurchasePlanAsTo);
                    });
                }
            });
        }
        return drugPurchasePlanAsTos;
    }
}
