package com.bjgoodwill.hip.as.drug.opd.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.bjgoodwill.hip.as.drug.opd.enums.DrugOpdBusinessErrorEnum;
import com.bjgoodwill.hip.as.drug.opd.enums.DrugOpdCardTypeEnum;
import com.bjgoodwill.hip.as.drug.opd.service.DrugOpdRefundAsService;
import com.bjgoodwill.hip.as.drug.opd.to.*;
import com.bjgoodwill.hip.common.bean.EnumTo;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.drug.opd.apply.enmus.DrugOpdDispenseStatusEnum;
import com.bjgoodwill.hip.ds.drug.opd.apply.enmus.DrugOpdRefundStatusEnum;
import com.bjgoodwill.hip.ds.drug.opd.apply.service.DrugOpdApplyService;
import com.bjgoodwill.hip.ds.drug.opd.apply.service.DrugOpdRefundService;
import com.bjgoodwill.hip.ds.drug.opd.apply.to.DrugOpdDispenseTo;
import com.bjgoodwill.hip.ds.drug.opd.apply.to.DrugOpdInoutTo;
import com.bjgoodwill.hip.ds.drug.opd.apply.to.DrugOpdRefundDto;
import com.bjgoodwill.hip.ds.drug.opd.window.service.DrugOpdWindowDispensingService;
import com.bjgoodwill.hip.ds.drug.opd.window.to.DrugOpdWindowDispensingTo;
import com.bjgoodwill.hip.ds.drug.opd.window.to.DrugOpdWindowStaffQto;
import com.bjgoodwill.hip.ds.drug.opd.window.to.DrugOpdWindowStaffTo;
import com.bjgoodwill.hip.ds.org.api.service.StaffService;
import com.bjgoodwill.hip.ds.org.api.to.StaffTo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;


/**
 * 门诊退药Controller
 *
 * <AUTHOR> 2025.5.6
 */
@RestController("com.bjgoodwill.hip.as.drug.opd.controller.DrugOpdRefundController")
@SaCheckPermission("drugOpd:dispense")
@RequestMapping("/drug/opd/refund")
@Tag(name = "门诊退药应用服务", description = "门诊退药应用服务")
public class DrugOpdRefundController {

    @Autowired
    private StaffService staffService;

    @Autowired
    private DrugOpdRefundAsService drugOpdRefundAsService;

    @Operation(summary = "卡类型", description = "卡类型")
    @ApiResponse(description = "返回卡类型", content = @Content(mediaType = "application/json", schema = @Schema(implementation = List.class)))
    @GetMapping(value = "/getCardType")
    public List<EnumTo<String>> getCardType() {
        return DrugOpdCardTypeEnum.getList();
    }

    @Operation(summary = "获取退药状态", description = "获取退药状态")
    @ApiResponse(description = "获取退药状态", content = @Content(mediaType = "application/json", schema = @Schema(implementation = List.class)))
    @GetMapping(value = "/drugRefundStatus")
    public List<EnumTo<String>> drugRefundStatus() {
        return DrugOpdRefundStatusEnum.getList();
    }

    @Operation(summary = "查询退药人。")
    @GetMapping("/refund/staff")
    @ApiResponse(description = "查询退药人", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = DrugOpdWindowDispensingTo.class))))
    public List<DrugOpdWindowStaffTo> getWindowStaff() {
        List<DrugOpdWindowStaffTo> staffTos = new ArrayList<>();
        List<StaffTo> staffLocalTos = staffService.getStaffLocalByPharmacy();
        if (!CollectionUtils.isEmpty(staffLocalTos)) {
            for (StaffTo staffLocalTo : staffLocalTos) {
                DrugOpdWindowStaffTo staffTo = new DrugOpdWindowStaffTo();
                staffTo.setStaffId(staffLocalTo.getId());
                staffTo.setStaffName(staffLocalTo.getName());
                staffTos.add(staffTo);
            }
        }
        return staffTos;
    }

    @Operation(summary = "门诊退药患者查询。")
    @PostMapping({"/getDispensePat"})
    List<DrugOpdPatRefundAsTo> getDrugOpdDispensePat(@RequestBody @Valid DrugOpdPatRefundAsQto drugOpdPatRefundAsQto) {
        return drugOpdRefundAsService.getDrugOpdRefundPat(drugOpdPatRefundAsQto);
    }

    @Operation(summary = "门诊退药患者处方药品明细查询")
    @PostMapping({"/getDispenseDetail"})
    DrugOpdDispenseAsTo getDrugOpdDispenseDetail(@RequestBody @Valid DrugOpdPatRefundAsQto drugOpdPatRefundAsQto) {
        BusinessAssert.hasText(drugOpdPatRefundAsQto.getPatMiCode(), DrugOpdBusinessErrorEnum.BUS_DRUG_OPD_0001, "主索引");
        if (DrugOpdRefundStatusEnum.已退药.equals(drugOpdPatRefundAsQto.getRefundStatus())) {
            BusinessAssert.notNull(drugOpdPatRefundAsQto.getRefundDrugCode(), DrugOpdBusinessErrorEnum.BUS_DRUG_OPD_0001, "退药号不可为空");
        }
        return drugOpdRefundAsService.getDrugOpdRefundDetail(drugOpdPatRefundAsQto);
    }

    @Operation(summary = "门诊退药确认")
    @PostMapping("/refund/pass")
    List<DrugOpdInoutTo> refundPass(@RequestBody @Valid DrugOpdRefundDto drugOpdRefundDto) {
        return drugOpdRefundAsService.refundApprove(drugOpdRefundDto);
    }

    @Operation(summary = "门诊退药驳回")
    @PostMapping("/refund/reject")
    void refundReject(@RequestBody @Valid DrugOpdRefundDto drugOpdRefundDto) {
        drugOpdRefundAsService.refundReject(drugOpdRefundDto);
    }

    @Operation(summary = "门诊退药撤销")
    @PostMapping("/refund/cancel/{refundDrugCode}")
    void refundCancel(@PathVariable("refundDrugCode") String refundDrugCode,
                      @RequestBody @Valid DrugOpdRefundDto drugOpdRefundDto) {
        drugOpdRefundAsService.refundCancel(refundDrugCode,drugOpdRefundDto);
    }

}
