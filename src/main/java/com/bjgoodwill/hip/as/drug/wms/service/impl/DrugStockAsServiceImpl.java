package com.bjgoodwill.hip.as.drug.wms.service.impl;

import com.bjgoodwill.hip.as.drug.wms.service.DrugStockAsService;
import com.bjgoodwill.hip.as.drug.wms.service.assembler.DrugStockAsAssembler;
import com.bjgoodwill.hip.as.drug.wms.to.DrugStockAmountAsTo;
import com.bjgoodwill.hip.as.drug.wms.to.DrugStockAsQto;
import com.bjgoodwill.hip.as.drug.wms.to.DrugStockAsTo;
import com.bjgoodwill.hip.as.drug.wms.utils.DrugAmountUtils;
import com.bjgoodwill.hip.business.util.drug.to.DrugInoutQto;
import com.bjgoodwill.hip.business.util.drug.to.DrugInoutTo;
import com.bjgoodwill.hip.ds.drug.goods.enmus.DrugSplitPackageEnum;
import com.bjgoodwill.hip.ds.drug.inout.query.service.DrugInoutQueryService;
import com.bjgoodwill.hip.ds.drug.stock.stock.service.DrugStockService;
import com.bjgoodwill.hip.ds.drug.stock.stock.to.DrugGoodsStockTo;
import com.bjgoodwill.hip.ds.drug.stock.stock.to.DrugStockQto;
import com.bjgoodwill.hip.ds.drug.stock.stock.to.DrugStockTo;
import com.bjgoodwill.hip.ds.org.api.type.WorkGroupType;
import com.bjgoodwill.hip.security.bean.CurrentOrgInfo;
import com.bjgoodwill.hip.security.util.HIPCurrentOrgUtils;
import io.micrometer.common.util.StringUtils;
import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Service("com.bjgoodwill.hip.as.drug.wms.service.DrugStockAsService")
public class DrugStockAsServiceImpl implements DrugStockAsService {

    @Autowired
    private DrugStockService drugStockService;

    @Autowired
    private DrugInoutQueryService drugInoutQueryService;

    /**
     * 根据查询条件获取库存信息
     */
    @Override
    public DrugStockAmountAsTo getDrugStockInfo(DrugStockAsQto drugStockAsQto) {
        DrugStockAmountAsTo drugStockAmountAsTo = new DrugStockAmountAsTo();
        //库存总金额查询
        DrugStockTo stockAmount = drugStockService.getStockAmount(drugStockAsQto.getStorageCode());
        if (stockAmount != null) {
            //购入金额合计
            if (stockAmount.getPurchaseAmount() != null) {
                drugStockAmountAsTo.setPurchaseTotalAmount(stockAmount.getPurchaseAmount().setScale(2, RoundingMode.DOWN));
            }
            //零售总额
            if (stockAmount.getSaleAmount() != null) {
                drugStockAmountAsTo.setSaleTotalPrice(stockAmount.getSaleAmount().setScale(2, RoundingMode.DOWN));
            }
        }
        DrugStockQto drugStockQto = getDrugStockQto(drugStockAsQto);
        //药库库存页面查询-汇总
        List<DrugGoodsStockTo> drugGoodsStockToList = drugStockService.findAllDrugGoodsStock(drugStockQto);
        List<DrugStockAsTo> drugStockAsToList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(drugGoodsStockToList)) {
            for (DrugGoodsStockTo drugGoodsStockTo : drugGoodsStockToList) {
                DrugStockAsTo drugStockAsTo = getDrugStockAsTo(drugGoodsStockTo);
                drugStockAsToList.add(drugStockAsTo);
            }
        }
        if (CollectionUtils.isNotEmpty(drugStockAsToList)) {
            //筛选购入总额
            BigDecimal siftPurchaseTotalAmount = drugStockAsToList.stream().map(DrugStockAsTo::getPurchaseTotalAmountNum).reduce(BigDecimal.ZERO, BigDecimal::add);
            drugStockAmountAsTo.setSiftPurchaseTotalAmount(siftPurchaseTotalAmount);
            //筛选零售总额
            BigDecimal siftSaleTotalPrice = drugStockAsToList.stream().map(DrugStockAsTo::getSaleTotalPriceNum).reduce(BigDecimal.ZERO, BigDecimal::add);
            drugStockAmountAsTo.setSiftSaleTotalPrice(siftSaleTotalPrice);
            //药品库存列表
            drugStockAmountAsTo.setDrugStockAsToList(drugStockAsToList);
        }
        return drugStockAmountAsTo;
    }

    private static @NotNull DrugStockQto getDrugStockQto(DrugStockAsQto drugStockAsQto) {
        DrugStockQto drugStockQto = new DrugStockQto();
        //可供状态
        if (drugStockAsQto.getAvailableStatus() != null) {
            int availableStatus = drugStockAsQto.getAvailableStatus().intValue();
            if (availableStatus == 1) {
                drugStockQto.setMissingLabel(true);
            } else if (availableStatus == 2) {
                drugStockQto.setMissingLabel(false);
            }
        }
        //库存数量
        if (drugStockAsQto.getQuantity() != null) {
            int quantity = drugStockAsQto.getQuantity().intValue();
            if (quantity == 1) {
                drugStockQto.setStockZero("1");
            } else if (quantity == 2) {
                drugStockQto.setStockZero("2");
            }
        }
        //药品编码
        if (StringUtils.isNotEmpty(drugStockAsQto.getDrugGoodsCode())) {
            drugStockQto.setText(drugStockAsQto.getDrugGoodsCode());
        }
        //登录科室
        drugStockQto.setStorageCode(drugStockAsQto.getStorageCode());
        //剂型
        if (StringUtils.isNotEmpty(drugStockAsQto.getDrugForm())) {
            drugStockQto.setDrugForm(drugStockAsQto.getDrugForm());
        }
        //毒麻分类
        if (StringUtils.isNotEmpty(drugStockAsQto.getPoisonousHemp())) {
            drugStockQto.setToxiProperty(drugStockAsQto.getPoisonousHemp());
        }
        //类别
        if (StringUtils.isNotEmpty(drugStockAsQto.getCategory())) {
            drugStockQto.setDrugType(drugStockAsQto.getCategory());
        }
        //药理归类
        if (StringUtils.isNotEmpty(drugStockAsQto.getPharmacological())) {
            drugStockQto.setActionType(drugStockAsQto.getPharmacological());
        }
        return drugStockQto;
    }

    private static @NotNull DrugStockAsTo getDrugStockAsTo(DrugGoodsStockTo drugGoodsStockTo) {
        DrugStockAsTo drugStockAsTo = new DrugStockAsTo();
        //药品编码
        drugStockAsTo.setDrugGoodsCode(drugGoodsStockTo.getDrugGoodsCode());
        //药品名称
        drugStockAsTo.setDrugGoodsName(drugGoodsStockTo.getDrugGoodsName());
        //货位名称
        drugStockAsTo.setStoragePlaceName(drugGoodsStockTo.getStockPlaceName());
        //规格
        drugStockAsTo.setDrugSpec(drugGoodsStockTo.getDrugSpec());
        //剂型
        drugStockAsTo.setDrugForm(drugGoodsStockTo.getDrugFormValue());
        //库存数量
        if (drugGoodsStockTo.getQuantityMin().compareTo(BigDecimal.ZERO) == 0) {
            drugStockAsTo.setQuantityStr(drugGoodsStockTo.getQuantityMax() + drugGoodsStockTo.getPackageUnitValue());
        } else if (drugGoodsStockTo.getQuantityMin().compareTo(BigDecimal.ZERO) > 0) {
            drugStockAsTo.setQuantityStr(drugGoodsStockTo.getQuantityMax() + drugGoodsStockTo.getPackageUnitValue() + drugGoodsStockTo.getQuantityMin() + drugGoodsStockTo.getMinUnitValue());
        }
        //最小数量
        drugStockAsTo.setQuantityMinStr(drugGoodsStockTo.getQuantity() + drugGoodsStockTo.getMinUnitValue());
        //预扣数量
        if (drugGoodsStockTo.getWithholdMin().compareTo(BigDecimal.ZERO) == 0) {
            drugStockAsTo.setWithhold(drugGoodsStockTo.getWithholdMax() + drugGoodsStockTo.getPackageUnitValue());
        } else if (drugGoodsStockTo.getWithholdMin().compareTo(BigDecimal.ZERO) > 0) {
            drugStockAsTo.setWithhold(drugGoodsStockTo.getWithholdMax() + drugGoodsStockTo.getPackageUnitValue() + drugGoodsStockTo.getWithholdMin() + drugGoodsStockTo.getMinUnitValue());
        }
        //可用库存数量
        if (drugGoodsStockTo.getUsableQuantityMin().compareTo(BigDecimal.ZERO) == 0) {
            drugStockAsTo.setAvailableQuantity(drugGoodsStockTo.getUsableQuantityMax() + drugGoodsStockTo.getPackageUnitValue());
        } else if (drugGoodsStockTo.getUsableQuantityMin().compareTo(BigDecimal.ZERO) > 0) {
            drugStockAsTo.setAvailableQuantity(drugGoodsStockTo.getUsableQuantityMax() + drugGoodsStockTo.getPackageUnitValue() + drugGoodsStockTo.getUsableQuantityMin() + drugGoodsStockTo.getMinUnitValue());
        }
        //包装数
        drugStockAsTo.setPackageNum(drugGoodsStockTo.getPackageNum());
        //拆包
        drugStockAsTo.setUnpacking(drugGoodsStockTo.getSplitPackageFlag());
        //拆包名称
        drugStockAsTo.setUnpackingName(DrugSplitPackageEnum.getName(drugGoodsStockTo.getSplitPackageFlag()));
        //零售金额
        drugStockAsTo.setSalePrice(drugGoodsStockTo.getSalePrice().setScale(4, RoundingMode.DOWN).toString());
        //购入金额
        drugStockAsTo.setPurchaseAmount(drugGoodsStockTo.getPurchasePrice().setScale(4, RoundingMode.DOWN).toString());
        //零售总额
        drugStockAsTo.setSaleTotalPrice(drugGoodsStockTo.getSaleAmount().setScale(2, RoundingMode.DOWN).toString());
        //购入总额
        drugStockAsTo.setPurchaseTotalAmount(drugGoodsStockTo.getPurchaseAmount().setScale(2, RoundingMode.DOWN).toString());
        //零售总额
        drugStockAsTo.setSaleTotalPriceNum(drugGoodsStockTo.getSaleAmount().setScale(2, RoundingMode.DOWN));
        //购入总额
        drugStockAsTo.setPurchaseTotalAmountNum(drugGoodsStockTo.getPurchaseAmount().setScale(2, RoundingMode.DOWN));
        //可供
        if (drugGoodsStockTo.getMissingLabel()) {
            drugStockAsTo.setAvailability("可供");
        } else {
            drugStockAsTo.setAvailability("不可供");
        }
        //效期
        drugStockAsTo.setEffectiveDate(drugGoodsStockTo.getEffectiveDate());
        //最后使用时间
        drugStockAsTo.setLastUseDate(drugGoodsStockTo.getUpdatedDate());
        //生产厂家
        drugStockAsTo.setManufactureFirmValue(drugGoodsStockTo.getManufactureName());
        //供货商
        drugStockAsTo.setSupplierName(drugGoodsStockTo.getSupplierName());
        //药品类型
        drugStockAsTo.setDrugType(drugGoodsStockTo.getDrugType());
        //追溯码
        drugStockAsTo.setDrugTraceCode(drugGoodsStockTo.getDrugTraceCode());
        //颜色判断
        if (drugGoodsStockTo.getEffectiveDate() != null) {
            //有效期近6个月
            if (isEffectiveDate(drugGoodsStockTo.getEffectiveDate(), 6L)) {
                drugStockAsTo.setTextColor("#2C91E9");
            }
            //有效期近三个月
            if (isEffectiveDate(drugGoodsStockTo.getEffectiveDate(), 3L)) {
                drugStockAsTo.setTextColor("#F42A2A");
            }
            //过期
            if (!drugGoodsStockTo.getEffectiveDate().isAfter(LocalDate.now())) {
                drugStockAsTo.setTextColor("#9A0DBC");
            }
        }
        if (drugGoodsStockTo.getQuantity() != null) {
            //高于库存上限
            if (drugGoodsStockTo.getMaxQuantity() != null) {
                if (drugGoodsStockTo.getQuantity().compareTo(BigDecimal.valueOf(drugGoodsStockTo.getMaxQuantity())) > 0) {
                    drugStockAsTo.setColor("#FOEFFF");
                }
            }
            //低于库存下线
            if (drugGoodsStockTo.getMinQuantity() != null) {
                if (drugGoodsStockTo.getQuantity().compareTo(BigDecimal.valueOf(drugGoodsStockTo.getMaxQuantity())) < 0) {
                    drugStockAsTo.setColor("#ECFFF5");
                }
            }
        }
        //停供-灰色
        if (!drugGoodsStockTo.getMissingLabel()) {
            drugStockAsTo.setTextColor("#999999");
            drugStockAsTo.setColor(null);
        }
        return drugStockAsTo;
    }

    /**
     * 查询药品库存分布
     */
    @Override
    public DrugStockAmountAsTo getDrugStockDistribution(String drugGoodsCode) {
        Assert.notNull(drugGoodsCode, "药品编码不能为空！");
        List<DrugStockAsTo> drugStockAsToList = new ArrayList<>();
        List<DrugStockTo> drugGoodsStockHospitalList = drugStockService.getDrugGoodsStockHospital(drugGoodsCode);
        for (DrugStockTo drugGoodsStockHospital : drugGoodsStockHospitalList) {
            DrugStockAsTo drugStockAsTo = generate(drugGoodsStockHospital);
            drugStockAsToList.add(drugStockAsTo);
        }
        DrugStockAmountAsTo drugStockAmountAsTo = new DrugStockAmountAsTo();
        drugStockAmountAsTo.setDrugStockAsToList(drugStockAsToList);
        //零售总额合计
        BigDecimal salePriceTotal = drugGoodsStockHospitalList.stream().map(DrugStockTo::getSaleAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        drugStockAmountAsTo.setSalePriceTotal(salePriceTotal);
        //购入金额合计
        BigDecimal purchaseTotalAmount = drugGoodsStockHospitalList.stream().map(DrugStockTo::getPurchaseAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        drugStockAmountAsTo.setPurchaseTotalAmount(purchaseTotalAmount);
        //大包装
        BigDecimal quantityMax = drugGoodsStockHospitalList.stream().map(DrugStockTo::getQuantityMax).reduce(BigDecimal.ZERO, BigDecimal::add);
        //小包装
        BigDecimal quantityMin = drugGoodsStockHospitalList.stream().map(DrugStockTo::getQuantityMin).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (quantityMin.compareTo(BigDecimal.ZERO) > 0) {
            drugStockAmountAsTo.setTotalInventory(quantityMax + drugGoodsStockHospitalList.get(0).getPackageUnitValue() + quantityMin + drugGoodsStockHospitalList.get(0).getMinUnitValue());
        } else {
            drugStockAmountAsTo.setTotalInventory(quantityMax + drugGoodsStockHospitalList.get(0).getPackageUnitValue());
        }
        return drugStockAmountAsTo;
    }

    /**
     * 查询药品出入库记录
     */
    @Override
    public List<DrugStockAsTo> getDrugInOutRecords(String storageCode, String drugGoodsCode, LocalDateTime startDate, LocalDateTime endDate) {
        Assert.notNull(storageCode, "当前登录科室不能为空！");
        Assert.notNull(drugGoodsCode, "药品编码不能为空！");
        Assert.notNull(startDate, "开始时间不能为空！");
        Assert.notNull(endDate, "结束时间不能为空！");
        List<DrugStockAsTo> drugStockAsToList = new ArrayList<>();
        //药库查药库出入库、药库盘点、药库特殊出入库、盘点出入库
        DrugInoutQto drugInoutQto = new DrugInoutQto();
        drugInoutQto.setStorageCode(storageCode);
        drugInoutQto.setDrugGoodsCode(drugGoodsCode);
        drugInoutQto.setBeginDate(startDate);
        drugInoutQto.setEndDate(endDate);
        List<DrugInoutTo> storageInList = drugInoutQueryService.getStorageIn(drugInoutQto);
        for (DrugInoutTo storageIn : storageInList) {
            DrugStockAsTo drugStockAsTo = DrugStockAsAssembler.toTo(storageIn);
            drugStockAsToList.add(drugStockAsTo);
        }
        List<DrugInoutTo> drugInoutTosList = drugInoutQueryService.queryStorageOut(drugInoutQto);
        for (DrugInoutTo drugInoutTos : drugInoutTosList) {
            DrugStockAsTo drugStockAsTo = DrugStockAsAssembler.toTo(drugInoutTos);
            drugStockAsToList.add(drugStockAsTo);
        }
        List<DrugInoutTo> pharmacyInList = drugInoutQueryService.getPharmacyIn(drugInoutQto);
        for (DrugInoutTo pharmacyIn : pharmacyInList) {
            DrugStockAsTo drugStockAsTo = DrugStockAsAssembler.toTo(pharmacyIn);
            drugStockAsToList.add(drugStockAsTo);
        }
        List<DrugInoutTo> pharmacyOutList = drugInoutQueryService.getPharmacyOut(drugInoutQto);
        for (DrugInoutTo pharmacyOut : pharmacyOutList) {
            DrugStockAsTo drugStockAsTo = DrugStockAsAssembler.toTo(pharmacyOut);
            drugStockAsToList.add(drugStockAsTo);
        }
        List<DrugInoutTo> specialInoutList = drugInoutQueryService.getSpecialInout(drugInoutQto);
        for (DrugInoutTo specialInout : specialInoutList) {
            DrugStockAsTo drugStockAsTo = DrugStockAsAssembler.toTo(specialInout);
            drugStockAsToList.add(drugStockAsTo);
        }
        List<DrugInoutTo> inventoryInoutList = drugInoutQueryService.getInventoryInout(drugInoutQto);
        for (DrugInoutTo inventoryInout : inventoryInoutList) {
            DrugStockAsTo drugStockAsTo = DrugStockAsAssembler.toTo(inventoryInout);
            drugStockAsToList.add(drugStockAsTo);
        }
        //判断当前登录科室是药库还是药房
        CurrentOrgInfo loginInfo = HIPCurrentOrgUtils.getCurrentOrgInfo();
        //药房查药房出入库、药库盘点、药库特殊出入库、盘点出入库、门诊发药、住院发药
        //药房查询相较药库增加门诊发药、住院发药的查询
        if (loginInfo != null) {
            String workGroupType = loginInfo.getWorkGroupType();
            //判断药房药库
            if (StringUtils.isNotEmpty(workGroupType) && workGroupType.equals(WorkGroupType.WORK_GROUP_PHARMACY)) {
                //TODO 门诊发药
                //TODO 住院发药
            }
        }
        return drugStockAsToList;
    }

    /**
     * 编辑药品货位号
     */
    @Override
    public void getDrugLocationNo(String storageCode, String drugGoodsCode, String locationNo) {
        Assert.notNull(storageCode, "当前登录科室不能为空！");
        Assert.notNull(locationNo, "货位号不能为空！");
        Assert.notNull(drugGoodsCode, "药品编码不能为空！");
        drugStockService.updateDrugStockPlaceName(storageCode, drugGoodsCode, locationNo);
    }

    /**
     * 编辑药品是否可供
     */
    @Override
    public void getDrugAvailability(String storageCode, String drugGoodsCode, Boolean missingLabel) {
        Assert.notNull(storageCode, "当前登录科室不能为空！");
        Assert.notNull(drugGoodsCode, "药品编码不能为空！");
        Assert.notNull(missingLabel, "可供标识不能为空！");
        if (missingLabel) {
            drugStockService.updateDrugStockEnable(storageCode, drugGoodsCode);
        } else {
            drugStockService.updateDrugStockDisable(storageCode, drugGoodsCode);
        }
    }

    /**
     * 编辑药品明细是否可供
     */
    @Override
    public void getDrugDetailAvailability(String drugGoodsDetailId, Boolean missingLabel) {
        Assert.notNull(drugGoodsDetailId, "药品明细ID不能为空！");
        Assert.notNull(missingLabel, "可供标识不能为空！");
        if (missingLabel) {
            drugStockService.updateDrugStockEnable(drugGoodsDetailId);
        } else {
            drugStockService.updateDrugStockDisable(drugGoodsDetailId);
        }
    }

    /**
     * 查询药品批次信息
     */
    @Override
    public DrugStockAmountAsTo getDrugBatchInfo(String drugGoodsCode, String storageCode) {
        Assert.notNull(storageCode, "登录科室不能为空！");
        Assert.notNull(drugGoodsCode, "药品编码不能为空！");
        DrugStockAmountAsTo drugStockAmountAsTo = new DrugStockAmountAsTo();
        List<DrugStockTo> drugGoodsStockDetailList = drugStockService.getDrugGoodsStockDetail(storageCode, drugGoodsCode);
        if (CollectionUtils.isNotEmpty(drugGoodsStockDetailList)) {
            //购入金额合计
            BigDecimal purchaseTotalAmountReduce = drugGoodsStockDetailList.stream().map(DrugStockTo::getPurchaseAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            drugStockAmountAsTo.setPurchaseTotalAmount(purchaseTotalAmountReduce.setScale(2, RoundingMode.DOWN));
            //零售总额合计
            BigDecimal saleTotalPriceReduce = drugGoodsStockDetailList.stream().map(DrugStockTo::getSaleAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            drugStockAmountAsTo.setSalePriceTotal(saleTotalPriceReduce.setScale(2, RoundingMode.DOWN));
            //大包装数
            BigDecimal quantityMax = drugGoodsStockDetailList.stream().map(DrugStockTo::getQuantityMax).reduce(BigDecimal.ZERO, BigDecimal::add);
            //小包装数
            BigDecimal quantityMin = drugGoodsStockDetailList.stream().map(DrugStockTo::getQuantityMin).reduce(BigDecimal.ZERO, BigDecimal::add);
            //根据小包装数量计算大包装数
            BigDecimal aPackage = DrugAmountUtils.getPackage(quantityMin, drugGoodsStockDetailList.get(0).getPackageNum());
            quantityMax = quantityMax.add(aPackage);
            //根据小包装数量计算大包装数后剩余小包装数
            quantityMin = DrugAmountUtils.getMin(quantityMin, drugGoodsStockDetailList.get(0).getPackageNum());
            //库存总量
            if (quantityMin.intValue() > 0) {
                drugStockAmountAsTo.setTotalInventory(quantityMax + drugGoodsStockDetailList.get(0).getPackageUnitValue() + quantityMin + drugGoodsStockDetailList.get(0).getMinUnitValue());
            } else {
                drugStockAmountAsTo.setTotalInventory(quantityMax + drugGoodsStockDetailList.get(0).getPackageUnitValue());
            }
            List<DrugStockAsTo> drugStockAsToList = new ArrayList<>();
            for (DrugStockTo drugGoodsStockDetail : drugGoodsStockDetailList) {
                DrugStockAsTo drugStockAsTo = getDrugStockAsTo(drugGoodsStockDetail);
                drugStockAsToList.add(drugStockAsTo);
            }
            if (CollectionUtils.isNotEmpty(drugStockAsToList)) {
                drugStockAmountAsTo.setDrugStockAsToList(drugStockAsToList);
            }
        }
        return drugStockAmountAsTo;
    }

    @Override
    public List<DrugStockAsTo> export(DrugStockAsQto drugStockAsQto) {
        DrugStockQto drugStockQto = getDrugStockQto(drugStockAsQto);
        //药库库存页面查询-汇总
        List<DrugGoodsStockTo> drugGoodsStockToList = drugStockService.findAllDrugGoodsStock(drugStockQto);
        List<DrugStockAsTo> drugStockAsToList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(drugGoodsStockToList)) {
            for (DrugGoodsStockTo drugGoodsStockTo : drugGoodsStockToList) {
                DrugStockAsTo drugStockAsTo = getDrugStockAsTo(drugGoodsStockTo);
                drugStockAsToList.add(drugStockAsTo);
            }
        }
        return drugStockAsToList;
    }

    private static @NotNull DrugStockAsTo getDrugStockAsTo(DrugStockTo drugGoodsStockDetail) {
        DrugStockAsTo drugStockAsTo = new DrugStockAsTo();
        //库房名称
        drugStockAsTo.setStorageName(drugGoodsStockDetail.getStorageName());
        //药品编码
        drugStockAsTo.setDrugGoodsCode(drugGoodsStockDetail.getDrugGoodsCode());
        //药品名称
        drugStockAsTo.setDrugGoodsName(drugGoodsStockDetail.getDrugGoodsName());
        //库存数量
        if (drugGoodsStockDetail.getQuantityMin() != null && drugGoodsStockDetail.getQuantityMin().intValue() > 0) {
            drugStockAsTo.setQuantityStr(drugGoodsStockDetail.getQuantityMax() + drugGoodsStockDetail.getPackageUnitValue() + drugGoodsStockDetail.getQuantityMin() + drugGoodsStockDetail.getMinUnitValue());
        } else {
            drugStockAsTo.setQuantityStr(drugGoodsStockDetail.getQuantityMax() + drugGoodsStockDetail.getPackageUnitValue());
        }
        //零售金额
        drugStockAsTo.setSalePrice(drugGoodsStockDetail.getSalePrice().setScale(4, RoundingMode.DOWN).toString());
        //零售总额
        drugStockAsTo.setSaleTotalPrice(drugGoodsStockDetail.getSaleAmount().setScale(2, RoundingMode.DOWN).toString());
        //购入金额
        drugStockAsTo.setPurchaseAmount(drugGoodsStockDetail.getPurchasePrice().setScale(4, RoundingMode.DOWN).toString());
        //购入总额
        drugStockAsTo.setPurchaseTotalAmount(drugGoodsStockDetail.getPurchaseAmount().setScale(2, RoundingMode.DOWN).toString());
        //批号
        drugStockAsTo.setBatchNo(drugGoodsStockDetail.getBatchNo());
        //批次号(入库次)
        drugStockAsTo.setBatchNumNo(drugGoodsStockDetail.getBatchNumNo());
        //有效期
        drugStockAsTo.setEffectiveDate(drugGoodsStockDetail.getEffectiveDate());
        //供应商
        drugStockAsTo.setSupplierName(drugGoodsStockDetail.getSupplierName());
        //药品明细ID
        drugStockAsTo.setDrugGoodsDetailId(drugGoodsStockDetail.getId());
        //是否可供
        drugStockAsTo.setMissingLabel(drugGoodsStockDetail.getMissingLabel());
        //停供-灰色
        if (!drugGoodsStockDetail.getMissingLabel()) {
            drugStockAsTo.setTextColor("#999999");
            drugStockAsTo.setColor(null);
        }
        return drugStockAsTo;
    }

    public DrugStockAsTo generate(DrugStockTo drugStockTo) {
        if (drugStockTo == null) {
            return null;
        }
        DrugStockAsTo drugStockAsTo = new DrugStockAsTo();
        //库房编码
        drugStockAsTo.setStorageCode(drugStockTo.getStorageCode());
        //库房名称
        drugStockAsTo.setStorageName(drugStockTo.getStorageName());
        //药品编码
        drugStockAsTo.setDrugGoodsCode(drugStockTo.getDrugGoodsCode());
        //药品名称
        drugStockAsTo.setDrugGoodsName(drugStockTo.getDrugGoodsName());
        //批号
        drugStockAsTo.setBatchNo(drugStockTo.getBatchNo());
        //入库次(批次号)
        drugStockAsTo.setBatchNumNo(drugStockTo.getBatchNumNo());
        //购入金额
        drugStockAsTo.setPurchaseAmount(drugStockTo.getPurchasePrice().setScale(4, RoundingMode.DOWN).toString());
        //购入总额
        drugStockAsTo.setPurchaseTotalAmount(drugStockTo.getPurchaseAmount().setScale(2, RoundingMode.DOWN).toString());
        //零售金额
        drugStockAsTo.setSalePrice(drugStockTo.getSalePrice().setScale(4, RoundingMode.DOWN).toString());
        //零售总额
        drugStockAsTo.setSaleTotalPrice(drugStockTo.getSaleAmount().setScale(2, RoundingMode.DOWN).toString());
        //有效期
        drugStockAsTo.setEffectiveDate(drugStockTo.getEffectiveDate());
        //库存数量
        if (drugStockTo.getQuantityMin() != null && drugStockTo.getQuantityMin().intValue() > 0) {
            drugStockAsTo.setQuantityStr(drugStockTo.getQuantityMax() + drugStockTo.getPackageUnitValue() + drugStockTo.getQuantityMin() + drugStockTo.getMinUnitValue());
        } else {
            drugStockAsTo.setQuantityStr(drugStockTo.getQuantityMax() + drugStockTo.getPackageUnitValue());
        }
        //大包装数量
        drugStockAsTo.setQuantityMax(drugStockTo.getQuantityMax());
        //小包装数量
        drugStockAsTo.setQuantityMin(drugStockTo.getQuantityMin());
        //供应商名称
        drugStockAsTo.setSupplierName(drugStockTo.getSupplierName());
        //可供
        if (drugStockTo.getMissingLabel()) {
            drugStockAsTo.setAvailability("可供");
        } else {
            drugStockAsTo.setAvailability("不可供");
        }
        return drugStockAsTo;
    }

    public static boolean isEffectiveDate(LocalDate effectiveDate, Long month) {
        LocalDate now = LocalDate.now();
        LocalDate plusTime = now.plusMonths(month);
        return effectiveDate.isAfter(now) && effectiveDate.isBefore(plusTime);
    }

}
