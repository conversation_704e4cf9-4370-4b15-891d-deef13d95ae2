package com.bjgoodwill.hip.as.drug.wms.to;

import com.bjgoodwill.hip.excel.annotation.ExcelImp;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.time.LocalDateTime;

@Schema(name = "manufactureImpTo", description = "生产厂家导入")
public class ManufactureImpTo implements Serializable {
    @ExcelImp(
            name = "生产厂商编码"
    )
    private String manufactureCode;
    @ExcelImp(
            name = "生产厂商名称"
    )
    private String manufactureName;

    @ExcelImp(
            name = "生产厂商联系人"
    )
    private String relationEmp;

    @ExcelImp(
            name = "生产厂商简称"
    )
    private String summary;

    @ExcelImp(
            name = "生产厂商名称拼音码"
    )
    private String inputPY;
    @ExcelImp(
            name = "备注"
    )
    private String remark;


    public String getManufactureCode() {
        return manufactureCode;
    }

    public void setManufactureCode(String manufactureCode) {
        this.manufactureCode = manufactureCode;
    }

    public String getManufactureName() {
        return manufactureName;
    }

    public void setManufactureName(String manufactureName) {
        this.manufactureName = manufactureName;
    }

    public String getRelationEmp() {
        return relationEmp;
    }

    public void setRelationEmp(String relationEmp) {
        this.relationEmp = relationEmp;
    }


    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }


    public String getInputPY() {
        return inputPY;
    }

    public void setInputPY(String inputPY) {
        this.inputPY = inputPY;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
