package com.bjgoodwill.hip.as.drug.wms.service.impl;

import com.bjgoodwill.hip.as.drug.wms.enums.DrugWmsBusinessErrorEnum;
import com.bjgoodwill.hip.as.drug.wms.service.PurchasePlanApplyAsService;
import com.bjgoodwill.hip.as.drug.wms.to.DrugPurchaseIntentionAsNto;
import com.bjgoodwill.hip.as.drug.wms.to.DrugPurchaseIntentionAsQto;
import com.bjgoodwill.hip.as.drug.wms.to.DrugPurchaseIntentionAsTo;
import com.bjgoodwill.hip.common.bean.GridResultSet;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.drug.goods.service.DrugGoodsWestService;
import com.bjgoodwill.hip.ds.drug.goods.to.DrugGoodsWestQto;
import com.bjgoodwill.hip.ds.drug.goods.to.DrugGoodsWestTo;
import com.bjgoodwill.hip.ds.drug.inout.pharmacy.out.service.DrugPharmacyOutService;
import com.bjgoodwill.hip.ds.drug.inout.pharmacy.out.to.DrugPharmacyOutDetailTo;
import com.bjgoodwill.hip.ds.drug.inout.pharmacy.out.to.DrugPharmacyOutTo;
import com.bjgoodwill.hip.ds.drug.pms.purchaseintention.service.DrugPurchaseIntentionService;
import com.bjgoodwill.hip.ds.drug.pms.purchaseintention.to.DrugPurchaseIntentionNto;
import com.bjgoodwill.hip.ds.drug.pms.purchaseintention.to.DrugPurchaseIntentionQto;
import com.bjgoodwill.hip.ds.drug.pms.purchaseintention.to.DrugPurchaseIntentionTo;
import com.bjgoodwill.hip.ds.drug.stock.stock.service.DrugStockService;
import com.bjgoodwill.hip.ds.drug.stock.stock.to.DrugStockTo;
import com.bjgoodwill.hip.ds.drug.stock.stockplace.service.DrugStockPlaceService;
import com.bjgoodwill.hip.ds.drug.stock.stockplace.to.DrugStockPlaceTo;
import com.bjgoodwill.hip.security.bean.CurrentOrgInfo;
import com.bjgoodwill.hip.security.util.HIPCurrentOrgUtils;
import io.micrometer.common.util.StringUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service("com.bjgoodwill.hip.as.drug.wms.service.PurchasePlanApplyAsService")
public class PurchasePlanApplyAsServiceImpl implements PurchasePlanApplyAsService {

    @Autowired
    private DrugPurchaseIntentionService drugPurchaseIntentionService;

    @Autowired
    private DrugGoodsWestService drugGoodsWestService;

    @Autowired
    private DrugStockPlaceService drugStockPlaceService;

    @Autowired
    private DrugPharmacyOutService drugPharmacyOutService;

    @Autowired
    private DrugStockService drugStockService;

    /**
     * 采购计划申请获取申请信息
     */
    @Override
    public List<DrugPurchaseIntentionAsTo> getPurchasePlanApplyList(DrugPurchaseIntentionAsQto drugPurchaseIntentionAsQto) {
        CurrentOrgInfo loginInfo = HIPCurrentOrgUtils.getCurrentOrgInfo();
        BusinessAssert.notNull(loginInfo, DrugWmsBusinessErrorEnum.BUS_DRUG_WMS_0006, "登录信息");
        BusinessAssert.notNull(loginInfo.getWorkGroupCode(), DrugWmsBusinessErrorEnum.BUS_DRUG_WMS_0006, "登录科室");
        List<DrugPurchaseIntentionAsTo> drugPurchaseIntentionAsToList = new ArrayList<>();
        DrugPurchaseIntentionQto drugPurchaseIntentionQto = new DrugPurchaseIntentionQto();
        drugPurchaseIntentionQto.setCreatedDate(LocalDateTime.now());
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        //申请开始时间
        if (StringUtils.isEmpty(drugPurchaseIntentionAsQto.getStatsStartStr())) {
            Assert.notNull(drugPurchaseIntentionAsQto.getStatsStartStr(), "申请开始时间不能为空！");
        } else {
            drugPurchaseIntentionQto.setBeginDate(LocalDateTime.parse(drugPurchaseIntentionAsQto.getStatsStartStr(), fmt));
        }
        //申请结束时间
        if (StringUtils.isEmpty(drugPurchaseIntentionAsQto.getStatsEndStr())) {
            Assert.notNull(drugPurchaseIntentionAsQto.getStatsEndStr(), "申请结束时间不能为空！");
        } else {
            drugPurchaseIntentionQto.setEndDate(LocalDateTime.parse(drugPurchaseIntentionAsQto.getStatsEndStr(), fmt));
        }
        //登陆科室
        drugPurchaseIntentionQto.setApplyStorageCode(loginInfo.getWorkGroupCode());
        //申请科室
        if (StringUtils.isNotEmpty(drugPurchaseIntentionAsQto.getApplyStorageCode())) {
            drugPurchaseIntentionQto.setStorageCode(drugPurchaseIntentionAsQto.getApplyStorageCode());
        }
        //意向药品查询界面根据主查询条件对药品采购意向进行分页查询
        GridResultSet<DrugPurchaseIntentionTo> drugPurchaseIntentionsByMainQtoPage = drugPurchaseIntentionService.getDrugPurchaseIntentionsByMainQtoPage(drugPurchaseIntentionQto);
        List<DrugPurchaseIntentionTo> resultList = drugPurchaseIntentionsByMainQtoPage.getResult();
        if (CollectionUtils.isNotEmpty(resultList)) {
            for (DrugPurchaseIntentionTo result : resultList) {
                DrugPurchaseIntentionAsTo drugPurchaseIntentionAsTo = new DrugPurchaseIntentionAsTo();
                //药品编码
                drugPurchaseIntentionAsTo.setDrugGoodsCode(result.getDrugGoodsCode());
                //药品名称
                drugPurchaseIntentionAsTo.setDrugGoodsName(result.getDrugGoodsName());
                //计划数量(意向数量)
                drugPurchaseIntentionAsTo.setInventoryQuantity(result.getIntentionNum());
                //申请人
                drugPurchaseIntentionAsTo.setCreatedStaffName(result.getCreatedStaffName());
                //申请人编码
                drugPurchaseIntentionAsTo.setCreatedStaff(result.getCreatedStaff());
                //申请时间
                drugPurchaseIntentionAsTo.setApplyDate(result.getCreatedDate());
                if (result.getDrugGoodsTo() != null) {
                    //库存数量
                    BigDecimal drugStockHistory = drugStockService.getDrugStockHistory(drugPurchaseIntentionAsQto.getStorageCode(), result.getDrugGoodsCode());
                    if (drugStockHistory != null) {
                        BigDecimal divide = drugStockHistory.divide(BigDecimal.valueOf(result.getDrugGoodsTo().getPackageNum()));
                        drugPurchaseIntentionAsTo.setRepertoryQuantity(divide);
                    } else {
                        drugPurchaseIntentionAsTo.setRepertoryQuantity(BigDecimal.valueOf(0));
                    }
                    //规格
                    drugPurchaseIntentionAsTo.setSpecification(result.getDrugGoodsTo().getDrugSpec());
                    //毒理属性
                    if (StringUtils.isNotEmpty(result.getDrugGoodsTo().getToxiProperty())) {
                        String toxiPropertyName = result.getDrugGoodsTo().getToxiPropertyName();
                        if (StringUtils.isNotEmpty(toxiPropertyName)) {
                            drugPurchaseIntentionAsTo.setToxicologyProperty(toxiPropertyName);
                        } else {
                            drugPurchaseIntentionAsTo.setToxicologyProperty("普通药品");
                        }
                    } else {
                        drugPurchaseIntentionAsTo.setToxicologyProperty("普通药品");
                    }
                    //单位
                    drugPurchaseIntentionAsTo.setUnit(result.getDrugGoodsTo().getPackageUnitValue());
                    //规格
                    drugPurchaseIntentionAsTo.setSpecification(result.getDrugGoodsTo().getDrugSpec());
                    //助记码
                    drugPurchaseIntentionAsTo.setMnemonicCode(result.getDrugGoodsTo().getMnemonicCode());
                    //生产厂家
                    drugPurchaseIntentionAsTo.setManufactureFirmValue(result.getDrugGoodsTo().getManufactureFirmValue());
                }
                drugPurchaseIntentionAsToList.add(drugPurchaseIntentionAsTo);
            }
        }
        return drugPurchaseIntentionAsToList;
    }

    /**
     * 新增采购计划申请信息
     */
    @Override
    public void newPurchasePlanApplyList(List<DrugPurchaseIntentionAsNto> drugPurchaseIntentionAsNtoList) {
        List<DrugPurchaseIntentionNto> drugPurchaseIntentionNtoList = new ArrayList<>();
        CurrentOrgInfo loginInfo = HIPCurrentOrgUtils.getCurrentOrgInfo();
        BusinessAssert.notNull(loginInfo, DrugWmsBusinessErrorEnum.BUS_DRUG_WMS_0006, "登录信息");
        BusinessAssert.notNull(loginInfo.getWorkGroupCode(), DrugWmsBusinessErrorEnum.BUS_DRUG_WMS_0006, "登录科室");
        for (DrugPurchaseIntentionAsNto drugPurchaseIntentionAsNto : drugPurchaseIntentionAsNtoList) {
            DrugPurchaseIntentionNto drugPurchaseIntentionNto = new DrugPurchaseIntentionNto();
            drugPurchaseIntentionNto.setDrugGoodsCode(drugPurchaseIntentionAsNto.getDrugGoodsCode());
            drugPurchaseIntentionNto.setDrugGoodsName(drugPurchaseIntentionAsNto.getDrugGoodsName());
            drugPurchaseIntentionNto.setCreatedDate(drugPurchaseIntentionAsNto.getCreatedDate());
            drugPurchaseIntentionNto.setCreatedStaff(drugPurchaseIntentionAsNto.getCreatedStaff());
            drugPurchaseIntentionNto.setApplyStorageCode(drugPurchaseIntentionAsNto.getApplyStorageCode());
            drugPurchaseIntentionNto.setApplyStorageName(drugPurchaseIntentionAsNto.getApplyStorageName());
            drugPurchaseIntentionNto.setIntentionNum(BigDecimal.valueOf(drugPurchaseIntentionAsNto.getPlannedQuantity()));
            drugPurchaseIntentionNto.setStorageCode(loginInfo.getWorkGroupCode());
            drugPurchaseIntentionNto.setStorageName(loginInfo.getWorkGroupName());
            drugPurchaseIntentionNto.setId(drugPurchaseIntentionAsNto.getID());
            drugPurchaseIntentionNtoList.add(drugPurchaseIntentionNto);
        }
        drugPurchaseIntentionService.createDrugPurchaseIntention(drugPurchaseIntentionNtoList);
    }

    /**
     * 采购计划申请选择药品信息
     */
    @Override
    public List<DrugPurchaseIntentionAsTo> getPurchaseSelectedDrug(DrugPurchaseIntentionAsQto drugPurchaseIntentionAsQto) {
        //当前登录科室
        CurrentOrgInfo loginInfo = HIPCurrentOrgUtils.getCurrentOrgInfo();
        BusinessAssert.notNull(loginInfo, DrugWmsBusinessErrorEnum.BUS_DRUG_WMS_0006, "登录信息");
        BusinessAssert.notNull(loginInfo.getWorkGroupCode(), DrugWmsBusinessErrorEnum.BUS_DRUG_WMS_0006, "登录科室");
        String storageCode = loginInfo.getWorkGroupCode();
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        //申请开始时间
        LocalDateTime beginDate = null;
        LocalDateTime endDate = null;
        if (StringUtils.isEmpty(drugPurchaseIntentionAsQto.getStatsStartStr())) {
            Assert.notNull(drugPurchaseIntentionAsQto.getStatsStartStr(), "申请开始时间不能为空！");
        } else {
            beginDate = LocalDateTime.parse(drugPurchaseIntentionAsQto.getStatsStartStr(), fmt);
        }
        //申请结束时间
        if (StringUtils.isEmpty(drugPurchaseIntentionAsQto.getStatsEndStr())) {
            Assert.notNull(drugPurchaseIntentionAsQto.getStatsEndStr(), "申请结束时间不能为空！");
        } else {
            endDate = LocalDateTime.parse(drugPurchaseIntentionAsQto.getStatsEndStr(), fmt);
        }
        //差分list
        List<DrugPurchaseIntentionAsTo> drugPurchaseIntentionAsToDifferenceList = new ArrayList<>();
        //统计时间
        List<DrugPharmacyOutDetailTo> drugPharmacyOutDetailAllList = new ArrayList<>();
        List<DrugPharmacyOutTo> drugPharmacyOutsByStorageCodeList = drugPharmacyOutService.getDrugPharmacyOutsByStorageCode(storageCode, beginDate, endDate);
        if (CollectionUtils.isNotEmpty(drugPharmacyOutsByStorageCodeList)) {
            for (DrugPharmacyOutTo drugPharmacyOutsByStorageCode : drugPharmacyOutsByStorageCodeList) {
                List<DrugPharmacyOutDetailTo> drugPharmacyOutDetails = drugPharmacyOutsByStorageCode.getDrugPharmacyOutDetails();
                if (CollectionUtils.isNotEmpty(drugPharmacyOutDetails)) {
                    drugPharmacyOutDetailAllList.addAll(drugPharmacyOutDetails);
                }
            }
        } else {
            return List.of();
        }
        if (CollectionUtils.isNotEmpty(drugPharmacyOutDetailAllList)) {
            Map<String, List<DrugPharmacyOutDetailTo>> collect = drugPharmacyOutDetailAllList.stream().collect(Collectors.groupingBy(DrugPharmacyOutDetailTo::getDrugGoodsCode));
            for (String value : collect.keySet()) {
                List<DrugPharmacyOutDetailTo> drugPharmacyOutDetailTos = collect.get(value);
                DrugPurchaseIntentionAsTo drugPurchaseIntentionAsTo = new DrugPurchaseIntentionAsTo();
                if (CollectionUtils.isNotEmpty(drugPharmacyOutDetailTos)) {
                    //商品编码
                    drugPurchaseIntentionAsTo.setDrugGoodsCode(value);
                    //商品名称
                    List<DrugPharmacyOutDetailTo> collectDrugGoodsName = drugPharmacyOutDetailTos.stream().filter(a -> a.getDrugGoodsTo() != null && StringUtils.isNotEmpty(a.getDrugGoodsTo().getDrugGoodsName())).toList();
                    drugPurchaseIntentionAsTo.setDrugGoodsName(collectDrugGoodsName.get(0).getDrugGoodsTo().getDrugGoodsName());
                    //规格
                    List<DrugPharmacyOutDetailTo> collectDrugSpec = drugPharmacyOutDetailTos.stream().filter(a -> a.getDrugGoodsTo() != null && StringUtils.isNotEmpty(a.getDrugGoodsTo().getDrugSpec())).toList();
                    drugPurchaseIntentionAsTo.setSpecification(collectDrugSpec.get(0).getDrugGoodsTo().getDrugSpec());
                    //生产厂家
                    List<DrugPharmacyOutDetailTo> collectManufactureName = drugPharmacyOutDetailTos.stream().filter(a -> StringUtils.isNotEmpty(a.getManufactureName())).toList();
                    drugPurchaseIntentionAsTo.setManufactureFirmValue(collectManufactureName.get(0).getManufactureName());
                    //单位
                    List<DrugPharmacyOutDetailTo> collectPackageUnit = drugPharmacyOutDetailTos.stream().filter(a -> a.getDrugGoodsTo() != null && StringUtils.isNotEmpty(a.getDrugGoodsTo().getPackageUnit())).toList();
                    drugPurchaseIntentionAsTo.setUnit(collectPackageUnit.get(0).getDrugGoodsTo().getPackageUnit());
                    //剂型
                    List<DrugPharmacyOutDetailTo> collectDrugFormValue = drugPharmacyOutDetailTos.stream().filter(a -> a.getDrugGoodsTo() != null && StringUtils.isNotEmpty(a.getDrugGoodsTo().getDrugFormValue())).toList();
                    drugPurchaseIntentionAsTo.setDrugFormValue(collectDrugFormValue.get(0).getDrugGoodsTo().getDrugFormValue());
                    //毒理属性
                    List<DrugPharmacyOutDetailTo> collectToxiPropertyName = drugPharmacyOutDetailTos.stream().filter(a -> a.getDrugGoodsTo() != null && StringUtils.isNotEmpty(a.getDrugGoodsTo().getToxiPropertyName())).toList();
                    drugPurchaseIntentionAsTo.setToxicologyProperty(collectToxiPropertyName.get(0).getDrugGoodsTo().getToxiPropertyName());
                    //出库量
                    BigDecimal quantity = drugPharmacyOutDetailTos.stream().map(DrugPharmacyOutDetailTo::getQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                    drugPurchaseIntentionAsTo.setQuantity(quantity);
                    drugPurchaseIntentionAsToDifferenceList.add(drugPurchaseIntentionAsTo);
                }
            }
        } else {
            return List.of();
        }
        //毒理属性
        List<DrugPurchaseIntentionAsTo> drugPurchaseIntentionAsToToxiPropertyList = generate(drugPurchaseIntentionAsToDifferenceList);
        if (StringUtils.isNotEmpty(drugPurchaseIntentionAsQto.getToxicologyProperty())) {
            DrugGoodsWestQto drugGoodsWestQto = new DrugGoodsWestQto();
            drugGoodsWestQto.setToxiProperty(drugPurchaseIntentionAsQto.getToxicologyProperty());
            List<DrugGoodsWestTo> drugGoodsWestsList = drugGoodsWestService.getDrugGoodsWests(drugGoodsWestQto);
            if (CollectionUtils.isNotEmpty(drugGoodsWestsList)) {
                if (CollectionUtils.isNotEmpty(drugPurchaseIntentionAsToToxiPropertyList)) {
                    drugPurchaseIntentionAsToToxiPropertyList = drugPurchaseIntentionAsToToxiPropertyList.stream().filter(a -> drugGoodsWestsList.stream().map(DrugGoodsWestTo::getDrugGoodsCode).toList().contains(a.getDrugGoodsCode())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(drugPurchaseIntentionAsToToxiPropertyList)) {
                        for (DrugPurchaseIntentionAsTo drugPurchaseIntentionAsToToxiProperty : drugPurchaseIntentionAsToToxiPropertyList) {
                            String drugGoodsCode = drugPurchaseIntentionAsToToxiProperty.getDrugGoodsCode();
                            List<DrugGoodsWestTo> collectToxiProperty = drugGoodsWestsList.stream().filter(a -> StringUtils.isNotEmpty(a.getDrugGoodsCode()) && a.getDrugGoodsCode().equals(drugGoodsCode) && StringUtils.isNotEmpty(a.getToxiPropertyName())).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(collectToxiProperty)) {
                                drugPurchaseIntentionAsToToxiProperty.setToxicologyProperty(collectToxiProperty.get(0).getToxiPropertyName());
                            }
                        }
                    } else {
                        return List.of();
                    }
                }
            } else {
                return List.of();
            }
        }
        //当前库存
        List<DrugStockTo> drugStockDrugQuantityList = drugStockService.getDrugStockDrugQuantity(storageCode);
        if (CollectionUtils.isNotEmpty(drugStockDrugQuantityList)) {
            drugPurchaseIntentionAsToToxiPropertyList = drugPurchaseIntentionAsToToxiPropertyList.stream().filter(a -> drugStockDrugQuantityList.stream().map(DrugStockTo::getDrugGoodsCode).toList().contains(a.getDrugGoodsCode())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(drugPurchaseIntentionAsToToxiPropertyList)) {
                for (DrugPurchaseIntentionAsTo drugPurchaseIntentionAsToToxiProperty : drugPurchaseIntentionAsToToxiPropertyList) {
                    String drugGoodsCode = drugPurchaseIntentionAsToToxiProperty.getDrugGoodsCode();
                    List<DrugStockTo> collectQuantity = drugStockDrugQuantityList.stream().filter(a -> StringUtils.isNotEmpty(a.getDrugGoodsCode()) && a.getDrugGoodsCode().equals(drugGoodsCode) && a.getQuantity() != null).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(collectQuantity)) {
                        drugPurchaseIntentionAsToToxiProperty.setCurrentInventory(collectQuantity.get(0).getQuantity());
                    }
                }
            }
        }
        //TODO 查询销售数量接口
        //TODO drugPurchaseIntentionAsToToxiProperty.setSale(xxx);
        //上下限
        List<DrugStockPlaceTo> drugStockPlacesByStorageCodeForLowLimitList = drugStockPlaceService.getDrugStockPlacesByStorageCode(storageCode);
        if (CollectionUtils.isNotEmpty(drugStockPlacesByStorageCodeForLowLimitList)) {
            drugPurchaseIntentionAsToToxiPropertyList = drugPurchaseIntentionAsToToxiPropertyList.stream().filter(a -> drugStockDrugQuantityList.stream().map(DrugStockTo::getDrugGoodsCode).toList().contains(a.getDrugGoodsCode())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(drugPurchaseIntentionAsToToxiPropertyList)) {
                for (DrugPurchaseIntentionAsTo drugPurchaseIntentionAsToToxiProperty : drugPurchaseIntentionAsToToxiPropertyList) {
                    String drugGoodsCode = drugPurchaseIntentionAsToToxiProperty.getDrugGoodsCode();
                    //上限
                    List<DrugStockPlaceTo> collectMaxQuantity = drugStockPlacesByStorageCodeForLowLimitList.stream().filter(a -> StringUtils.isNotEmpty(a.getDrugGoodsCode()) && a.getDrugGoodsCode().equals(drugGoodsCode) && a.getMaxQuantity() != null).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(collectMaxQuantity)) {
                        drugPurchaseIntentionAsToToxiProperty.setUpperLimit(collectMaxQuantity.get(0).getMaxQuantity());
                    }
                    //下限
                    List<DrugStockPlaceTo> collectMinQuantity = drugStockPlacesByStorageCodeForLowLimitList.stream().filter(a -> StringUtils.isNotEmpty(a.getDrugGoodsCode()) && a.getDrugGoodsCode().equals(drugGoodsCode) && a.getMinQuantity() != null).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(collectMinQuantity)) {
                        drugPurchaseIntentionAsToToxiProperty.setLowerLimit(collectMinQuantity.get(0).getMinQuantity());
                    }
                }
            }
        }

        if (!drugPurchaseIntentionAsQto.getZeroInventory() && drugPurchaseIntentionAsQto.getDrugMinimumLimit()) {
            //判断药品下限
            drugPurchaseIntentionAsToToxiPropertyList = drugPurchaseIntentionAsToToxiPropertyList.stream().filter(a -> a.getLowerLimit() != null && a.getCurrentInventory() != null && a.getLowerLimit().compareTo(a.getCurrentInventory().intValue()) > 0).toList();
        } else if (drugPurchaseIntentionAsQto.getZeroInventory()) {
            //零库存
            drugPurchaseIntentionAsToToxiPropertyList = drugPurchaseIntentionAsToToxiPropertyList.stream().filter(a -> a.getCurrentInventory().compareTo(BigDecimal.ZERO) == 0).toList();
        }
        if (drugPurchaseIntentionAsQto.getOutboundQuantity()) {
            //有出库数量
            drugPurchaseIntentionAsToToxiPropertyList = drugPurchaseIntentionAsToToxiPropertyList.stream().filter(a -> a.getQuantity() != null && a.getQuantity().compareTo(BigDecimal.ZERO) > 0).toList();
        }
        if (drugPurchaseIntentionAsQto.getSalesQuantities()) {
            //销售数量
            drugPurchaseIntentionAsToToxiPropertyList = drugPurchaseIntentionAsToToxiPropertyList.stream().filter(a -> a.getSale() != null && a.getSale().compareTo(BigDecimal.ZERO.intValue()) > 0).toList();
        }
        return drugPurchaseIntentionAsToToxiPropertyList;
    }

    public List<DrugPurchaseIntentionAsTo> generate(List<DrugPurchaseIntentionAsTo> list) {
        List<DrugPurchaseIntentionAsTo> drugPurchaseIntentionAsToList = new ArrayList<>();
        for (DrugPurchaseIntentionAsTo drugPurchaseIntentionAsTo : list) {
            if (drugPurchaseIntentionAsTo == null) {
                return new ArrayList<>();
            }
            drugPurchaseIntentionAsTo.setId(drugPurchaseIntentionAsTo.getId());
            drugPurchaseIntentionAsTo.setDrugGoodsCode(drugPurchaseIntentionAsTo.getDrugGoodsCode());
            drugPurchaseIntentionAsTo.setDrugGoodsName(drugPurchaseIntentionAsTo.getDrugGoodsName());
            drugPurchaseIntentionAsTo.setSpecification(drugPurchaseIntentionAsTo.getSpecification());
            drugPurchaseIntentionAsTo.setPackageNum(drugPurchaseIntentionAsTo.getPackageNum());
            drugPurchaseIntentionAsTo.setToxicologyProperty(drugPurchaseIntentionAsTo.getToxicologyProperty());
            drugPurchaseIntentionAsTo.setUnit(drugPurchaseIntentionAsTo.getUnit());
            drugPurchaseIntentionAsTo.setInventoryQuantity(drugPurchaseIntentionAsTo.getInventoryQuantity());
            drugPurchaseIntentionAsTo.setManufactureFirmValue(drugPurchaseIntentionAsTo.getManufactureFirmValue());
            drugPurchaseIntentionAsTo.setCurrentInventory(drugPurchaseIntentionAsTo.getCurrentInventory());
            drugPurchaseIntentionAsTo.setUpperLimit(drugPurchaseIntentionAsTo.getUpperLimit());
            drugPurchaseIntentionAsTo.setLowerLimit(drugPurchaseIntentionAsTo.getLowerLimit());
            drugPurchaseIntentionAsTo.setSale(drugPurchaseIntentionAsTo.getSale());
            drugPurchaseIntentionAsTo.setCreatedStaffName(drugPurchaseIntentionAsTo.getCreatedStaffName());
            drugPurchaseIntentionAsTo.setCreatedStaff(drugPurchaseIntentionAsTo.getCreatedStaff());
            drugPurchaseIntentionAsTo.setMnemonicCode(drugPurchaseIntentionAsTo.getMnemonicCode());
            drugPurchaseIntentionAsTo.setDrugFormValue(drugPurchaseIntentionAsTo.getDrugFormValue());
            drugPurchaseIntentionAsToList.add(drugPurchaseIntentionAsTo);
        }
        return drugPurchaseIntentionAsToList;
    }

}
