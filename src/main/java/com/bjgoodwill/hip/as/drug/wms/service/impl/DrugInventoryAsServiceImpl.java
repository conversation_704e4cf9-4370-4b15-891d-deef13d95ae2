package com.bjgoodwill.hip.as.drug.wms.service.impl;

import com.bjgoodwill.hip.as.drug.wms.service.DrugInventoryAsService;
import com.bjgoodwill.hip.as.drug.wms.service.assembler.DrugInventoryAsAssembler;
import com.bjgoodwill.hip.as.drug.wms.to.*;
import com.bjgoodwill.hip.common.exception.BusinessException;
import com.bjgoodwill.hip.ds.drug.inout.inventory.enmus.DrugInventoryStatusEnum;
import com.bjgoodwill.hip.ds.drug.inout.inventory.service.DrugInventoryService;
import com.bjgoodwill.hip.ds.drug.inout.inventory.to.*;
import com.bjgoodwill.hip.security.util.HIPCurrentOrgUtils;
import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/7/24 16:12
 * @PROJECT: 药品盘点
 */
@Service("com.bjgoodwill.hip.as.drug.wms.service.DrugInventoryAsService")
public class DrugInventoryAsServiceImpl implements DrugInventoryAsService {

    @Autowired
    private DrugInventoryService drugInventoryService;

    /**
     * 药品盘点单初始化
     */
    @Override
    public DrugInventoryTo drugInventoryInitialize() {
        DrugInventoryTo newDrugInventoryByStorageCode = drugInventoryService.getNewDrugInventoryByStorageCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
        if (newDrugInventoryByStorageCode != null) {
            return getDrugInventoryTo(newDrugInventoryByStorageCode);
        }
        return new DrugInventoryTo();
    }

    /**
     * 全盘
     */
    @Override
    public DrugInventoryTo updateDrugInventoryAll(String id) {
        drugInventoryService.updateDrugInventoryAll(id);
        return drugInventoryService.updateRefreshStock(id);
    }

    /**
     * 刷新发药数
     */
    @Override
    public DrugInventoryTo updateRefreshStock(String id) {
        DrugInventoryTo drugInventoryTo = drugInventoryService.updateRefreshStock(id);
        if (CollectionUtils.isNotEmpty(drugInventoryTo.getDrugInventoryDetails())) {
            List<DrugInventoryDetailTo> list = drugInventoryTo.getDrugInventoryDetails().stream().filter(a -> a.getPurchaseAmount() != null).toList();
            BigDecimal reduce = list.stream().map(DrugInventoryDetailTo::getPurchaseAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            drugInventoryTo.setPurchaseAmount(reduce);
        }
        return drugInventoryTo;
    }

    /**
     * 新建盘点单
     */
    @Override
    public DrugInventoryAsTo createDrugInventory(DrugInventoryAsNto drugInventoryAsNto) {
        DrugInventoryNto drugInventoryNto = DrugInventoryAsAssembler.toTo(drugInventoryAsNto);
        DrugInventoryTo drugInventory = drugInventoryService.createDrugInventory(drugInventoryNto);
        return DrugInventoryAsAssembler.toTo(drugInventory);
    }

    /**
     * 查询历史盘点单集合
     */
    @Override
    public List<DrugInventoryTo> getHistoryInventories() {
        DrugInventoryQto drugInventoryQto = new DrugInventoryQto();
        drugInventoryQto.setStorageCode(HIPCurrentOrgUtils.getCurrentOrgInfo().getWorkGroupCode());
        drugInventoryQto.setStatusCode(DrugInventoryStatusEnum.结存.getCode());
        return drugInventoryService.getDrugInventories(drugInventoryQto);
    }

    /**
     * 查询历史盘点单明细数据
     */
    @Override
    public DrugInventoryTo getDrugInventoryById(String id) {
        DrugInventoryTo drugInventoryById = drugInventoryService.getDrugInventoryById(id);
        return getDrugInventoryTo(drugInventoryById);
    }

    @NotNull
    private DrugInventoryTo getDrugInventoryTo(DrugInventoryTo drugInventoryById) {
        if (CollectionUtils.isNotEmpty(drugInventoryById.getDrugInventoryDetails())) {
            List<DrugInventoryDetailTo> list = drugInventoryById.getDrugInventoryDetails().stream().filter(a -> a.getPurchaseAmount() != null).toList();
            BigDecimal reduce = list.stream().map(DrugInventoryDetailTo::getPurchaseAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            drugInventoryById.setPurchaseAmount(reduce);
        }
        return drugInventoryById;
    }

    /**
     * 修改实际盘点数
     */
    @Override
    public DrugInventoryExtendAsTo updateDrugInventoryDetail(DrugInventoryDetailAsEto drugInventoryDetailAsEto) {
        DrugInventoryExtendAsTo drugInventoryExtendAsTo = new DrugInventoryExtendAsTo();
        Assert.notNull(drugInventoryDetailAsEto.getProfitOrLossPrice(), "盈亏金额不能为空！");
        Assert.notNull(drugInventoryDetailAsEto.getVersion(), "版本号不能为空！");
        Assert.notNull(drugInventoryDetailAsEto.getPurchaseAmount(), "盈亏总金额不能为空！");
        if (drugInventoryDetailAsEto.getInventoryNumMax() == null && drugInventoryDetailAsEto.getInventoryNumMin() == null) {
            throw new BusinessException("盘点数不能为空!");
        }
        DrugInventoryDetailEto drugInventoryDetailEto = new DrugInventoryDetailEto();
        //版本
        drugInventoryDetailEto.setVersion(drugInventoryDetailAsEto.getVersion());
        //盘点数大包装
        if (drugInventoryDetailAsEto.getInventoryNumMax() != null) {
            drugInventoryDetailEto.setInventoryNumMax(drugInventoryDetailAsEto.getInventoryNumMax());
        } else {
            drugInventoryDetailEto.setInventoryNumMax(BigDecimal.valueOf(0));
        }
        //盘点数小包装
        if (drugInventoryDetailAsEto.getInventoryNumMin() != null) {
            drugInventoryDetailEto.setInventoryNumMin(drugInventoryDetailAsEto.getInventoryNumMin());
        } else {
            drugInventoryDetailEto.setInventoryNumMin(BigDecimal.valueOf(0));
        }
        //修改盘点数调用领域接口
        DrugInventoryDetailTo drugInventoryDetailTo = drugInventoryService.updateDrugInventoryDetail(drugInventoryDetailAsEto.getId(), drugInventoryDetailEto);
        //根据唯一标识返回药品盘点明细领域接口
        DrugInventoryDetailTo drugInventoryDetailById = drugInventoryService.getDrugInventoryDetailById(drugInventoryDetailAsEto.getId());
        drugInventoryExtendAsTo.setDrugInventoryDetailTo(drugInventoryDetailById);
        //盈亏总金额计算
        drugInventoryExtendAsTo.setPurchaseAmount(drugInventoryDetailAsEto.getPurchaseAmount().subtract(drugInventoryDetailAsEto.getProfitOrLossPrice()).add(drugInventoryDetailTo.getPurchaseAmount()));
        return drugInventoryExtendAsTo;
    }

    /**
     * 结存
     */
    @Override
    public void drugInventoryConfirm(DrugInventoryAsEto drugInventoryAsEto) {
        Assert.notNull(drugInventoryAsEto.getVersion(), "版本号不能为空！");
        DrugInventoryEto drugInventoryEto = new DrugInventoryEto();
        drugInventoryEto.setVersion(drugInventoryAsEto.getVersion());
        drugInventoryService.updateDrugInventoryConfirm(drugInventoryAsEto.getId(), drugInventoryEto);
    }

}
