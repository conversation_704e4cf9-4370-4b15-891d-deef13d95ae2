package com.bjgoodwill.hip.as.drug.opd.controller;

import com.bjgoodwill.hip.as.drug.opd.enums.DrugOpdBusinessErrorEnum;
import com.bjgoodwill.hip.business.util.drug.enums.DrugPresTypeEnum;
import com.bjgoodwill.hip.common.bean.EnumTo;
import com.bjgoodwill.hip.common.util.BusinessAssert;
import com.bjgoodwill.hip.ds.drug.opd.apply.service.DrugOpdApplyService;
import com.bjgoodwill.hip.ds.drug.opd.window.enmus.DrugOpdPreModelEnum;
import com.bjgoodwill.hip.ds.drug.opd.window.enmus.DrugOpdPrintModelEnum;
import com.bjgoodwill.hip.ds.drug.opd.window.service.DrugOpdPrePrintService;
import com.bjgoodwill.hip.ds.drug.opd.window.service.DrugOpdWindowDispensingService;
import com.bjgoodwill.hip.ds.drug.opd.window.service.DrugOpdWindowMatchingService;
import com.bjgoodwill.hip.ds.drug.opd.window.service.DrugOpdWindowService;
import com.bjgoodwill.hip.ds.drug.opd.window.to.*;
import com.bjgoodwill.hip.ds.org.api.service.StaffService;
import com.bjgoodwill.hip.ds.org.api.service.WorkGroupService;
import com.bjgoodwill.hip.ds.org.api.to.StaffTo;
import com.bjgoodwill.hip.ds.org.api.type.WorkGroupType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 门诊配发药窗口维护Controller
 *
 * <AUTHOR> 2025.3.22
 */
@RestController("com.bjgoodwill.hip.as.drug.opd.controller.DrugOpdWindowController")
//@SaCheckPermission("drugOpd:window")
@RequestMapping("/drug/opd/window")
@Tag(name = "门诊配发药窗口维护应用服务", description = "门诊配发药窗口维护应用服务")
public class DrugOpdWindowController {

    @Autowired
    private DrugOpdWindowService drugOpdWindowService;

    @Autowired
    private DrugOpdWindowDispensingService dispensingService;

    @Autowired
    private DrugOpdWindowMatchingService matchingService;

    @Autowired
    private DrugOpdPrePrintService drugOpdPrePrintService;

    @Autowired
    private StaffService staffService;

    @Autowired
    private WorkGroupService workGroupService;

    @Autowired
    private DrugOpdApplyService drugOpdApplyService;

    @Operation(summary = "根据登录工作组查询发药窗口。")
    @GetMapping("/drugOpdWindow/dispensing")
    @ApiResponse(description = "返回发药窗口集合", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = DrugOpdWindowDispensingTo.class))))
    public List<DrugOpdWindowDispensingTo> getDrugOpdWindowDispensing(@RequestParam(name = "workGroupCode") String workGroupCode) {
        DrugOpdWindowDispensingQto qto = new DrugOpdWindowDispensingQto();
        qto.setStorageCode(workGroupCode);
        qto.setSortBy("createdDate");
        qto.setSortOrder("asc");
        return dispensingService.getDrugOpdWindowDispensings(qto);
    }

    @Operation(summary = "根据登录工作组查询配药窗口。")
    @GetMapping("/drugOpdWindow/matching")
    @ApiResponse(description = "返回发药窗口集合", content = @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = DrugOpdWindowMatchingTo.class))))
    public List<DrugOpdWindowMatchingTo> getDrugOpdWindowMatching(@RequestParam(name = "workGroupCode") String workGroupCode) {
        DrugOpdWindowMatchingQto qto = new DrugOpdWindowMatchingQto();
        qto.setStorageCode(workGroupCode);
        qto.setSortBy("createdDate");
        qto.setSortOrder("asc");
        return matchingService.getDrugOpdWindowMatchings(qto);
    }

    @Operation(summary = "获取处方类型", description = "获取处方类型")
    @ApiResponse(description = "返回处方类型", content = @Content(mediaType = "application/json", schema = @Schema(implementation = List.class)))
    @GetMapping(value = "/drugPresType")
    public List<EnumTo<String>> getDrugPresType() {
        return DrugPresTypeEnum.getList();
    }

    @Operation(summary = "获取药房人员", description = "获取药房人员")
    @ApiResponse(description = "返回药房人员", content = @Content(mediaType = "application/json", schema = @Schema(implementation = List.class)))
    @GetMapping(value = "/pharmacyStaff")
    public List<StaffTo> getPharmacyStaff() {
        return staffService.getStaffLocalByPharmacy();
    }

    @Operation(summary = "根据唯一标识启用窗口。", description = "根据唯一标识启用窗口")
    @PutMapping("/{id}/enable")
    public void enableDrugOpdWindow(@PathVariable("id") String id) {
        drugOpdWindowService.enableDrugOpdWindow(id);
    }

    @Operation(summary = "根据唯一标识关闭窗口。", description = "根据唯一标识关闭窗口")
    @PutMapping("/{id}/disable")
    public void disableDrugOpdWindow(@PathVariable("id") String id) {
        drugOpdWindowService.disableDrugOpdWindow(id);
    }

    @Operation(summary = "根据唯一标识关闭窗口，并分配未发药处方到其他窗口。", description = "根据唯一标识关闭窗口，并分配未发药处方到其他窗口")
    @PutMapping("/{id}/disableAndPres")
    public void disableDrugOpdWindowAndPres(@PathVariable("id") String id) {
        drugOpdWindowService.disableWindowAndSharePres(id);
    }

    @Operation(summary = "获取窗口下未发药处方数", description = "获取窗口下未发药处方数")
    @ApiResponse(description = "返回未发药处方数", content = @Content(mediaType = "application/json", schema = @Schema(implementation = Integer.class)))
    @GetMapping(value = "/{winId}/getPresNumByWindow")
    public int getPresNumByWindow(@PathVariable("winId") String winId) {
        return drugOpdApplyService.getPresNumByWindow(winId);
    }


    @Operation(summary = "创建门诊发药窗口。")
    @ApiResponse(description = "返回门诊发药窗口", content = @Content(mediaType = "application/json", schema = @Schema(implementation = DrugOpdWindowDispensingTo.class)))
    @PostMapping("/dispensing/create")
    public DrugOpdWindowDispensingTo createDrugOpdWindowDispensing(@RequestBody @Valid DrugOpdWindowDispensingNto drugOpdWindowDispensingNto) {
        return dispensingService.createDrugOpdWindowDispensing(drugOpdWindowDispensingNto);
    }

    @Operation(summary = "根据唯一标识修改门诊发药窗口。")
    @PutMapping("/dispensing/update/{id}")
    public void updateDrugOpdWindowDispensing(@PathVariable("id") String id, @RequestBody @Valid DrugOpdWindowDispensingEto drugOpdWindowDispensingEto) {
        dispensingService.updateDrugOpdWindowDispensing(id, drugOpdWindowDispensingEto);
    }

    @Operation(summary = "创建门诊配药窗口。")
    @ApiResponse(description = "返回门诊配药窗口", content = @Content(mediaType = "application/json", schema = @Schema(implementation = DrugOpdWindowMatchingTo.class)))
    @PostMapping("/matching/create")
    public DrugOpdWindowMatchingTo createDrugOpdWindowMatching(@RequestBody @Valid DrugOpdWindowMatchingNto drugOpdWindowMatchingNto) {
        return matchingService.createDrugOpdWindowMatching(drugOpdWindowMatchingNto);
    }

    @Operation(summary = "根据唯一标识修改门诊配药窗口。")
    @PutMapping("/matching/update/{id}")
    public void updateDrugOpdWindowMatching(@PathVariable("id") String id, @RequestBody @Valid DrugOpdWindowMatchingEto drugOpdWindowMatchingEto) {
        matchingService.updateDrugOpdWindowMatching(id, drugOpdWindowMatchingEto);
    }


    @Operation(summary = "根据唯一标识返回门诊处方分配及打印模式。")
    @ApiResponse(description = "返回门诊处方分配及打印模式", content = @Content(mediaType = "application/json", schema = @Schema(implementation = DrugOpdPrePrintTo.class)))
    @GetMapping("/xId/{storageCode}")
    public DrugOpdPrePrintTo getDrugOpdPrePrintById(@PathVariable("storageCode") String storageCode) {
        return drugOpdPrePrintService.getDrugOpdPrePrintById(storageCode);
    }

    @Operation(summary = "保存门诊处方分配及打印模式。")
    @ApiResponse(description = "返回门诊处方分配及打印模式", content = @Content(mediaType = "application/json", schema = @Schema(implementation = DrugOpdPrePrintTo.class)))
    @PostMapping("/prePrint/save")
    public DrugOpdPrePrintTo saveDrugOpdPrePrint(@RequestBody @Valid DrugOpdPrePrintNto drugOpdPrePrintNto) {
        String workGroupType = workGroupService.getWorkGroupType(drugOpdPrePrintNto.getStorageCode());
        BusinessAssert.isTrue(WorkGroupType.WORK_GROUP_PHARMACY.equals(workGroupType), DrugOpdBusinessErrorEnum.BUS_DRUG_OPD_9999, "登录科室非药房，请切换登录科室");
        return drugOpdPrePrintService.saveDrugOpdPrePrint(drugOpdPrePrintNto);
    }

    @Operation(summary = "获取处方分配窗口模式", description = "获取处方分配窗口模式")
    @ApiResponse(description = "返回处方分配窗口模式", content = @Content(mediaType = "application/json", schema = @Schema(implementation = List.class)))
    @GetMapping(value = "/drugPreModel")
    public List<EnumTo<String>> getDrugPreModel() {
        return DrugOpdPreModelEnum.getList();
    }

    @Operation(summary = "获取处方打印模式", description = "获取处方打印模式")
    @ApiResponse(description = "返回处方打印模式", content = @Content(mediaType = "application/json", schema = @Schema(implementation = List.class)))
    @GetMapping(value = "/drugPrePrint")
    public List<EnumTo<String>> getDrugPrePrint() {
        return DrugOpdPrintModelEnum.getList();
    }


}
