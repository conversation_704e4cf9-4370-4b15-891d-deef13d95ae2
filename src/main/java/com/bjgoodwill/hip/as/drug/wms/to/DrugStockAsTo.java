package com.bjgoodwill.hip.as.drug.wms.to;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema(description = "药品库存")
public class DrugStockAsTo implements Serializable {

    @Serial
    private static final long serialVersionUID = -6752838903189372743L;

    @Schema(description = "药品明细ID")
    private String drugGoodsDetailId;

    @Schema(description = "库房编码")
    private String storageCode;

    @Schema(description = "库房名称")
    private String storageName;

    @Schema(description = "药品编码")
    private String drugGoodsCode;

    @Schema(description = "药品名称")
    private String drugGoodsName;

    @Schema(description = "规格")
    private String drugSpec;

    @Schema(description = "货位号")
    private String storagePlaceName;

    @Schema(description = "剂型")
    private String drugForm;

    @Schema(description = "库存数量")
    private BigDecimal quantity;

    @Schema(description = "库存数量(显示)")
    private String quantityStr;

    @Schema(description = "大包装数量")
    private BigDecimal quantityMax;

    @Schema(description = "小包装数量")
    private BigDecimal quantityMin;

    @Schema(description = "最小数量(显示)")
    private String quantityMinStr;

    @Schema(description = "零售金额")
    private String salePrice;

    @Schema(description = "零售总额")
    private String saleTotalPrice;

    @Schema(description = "零售总额")
    private BigDecimal saleTotalPriceNum;

    @Schema(description = "购入金额")
    private String purchaseAmount;

    @Schema(description = "购入总额")
    private String purchaseTotalAmount;

    @Schema(description = "购入总额")
    private BigDecimal purchaseTotalAmountNum;

    @Schema(description = "入库次")
    private String batchNumNo;

    @Schema(description = "批号")
    private String batchNo;

    @Schema(description = "有效期")
    private LocalDate effectiveDate;

    @Schema(description = "供应商")
    private String supplier;

    @Schema(description = "供应商名称")
    private String supplierName;

    @Schema(description = "出入库单号")
    private String billNO;

    @Schema(description = "入出库类型")
    private String inOutType;

    @Schema(description = "出入库科室")
    private String inOutDepartment;

    @Schema(description = "出入库数量")
    private String inOutQuantity;

    @Schema(description = "出入库时间")
    private LocalDateTime billDate;

    @Schema(description = "是否可供(显示)")
    private String availability;

    @Schema(description = "可供")
    private Boolean missingLabel;

    @Schema(description = "预扣数量(显示)")
    private String withhold;

    @Schema(description = "可用库存数量(显示)")
    private String availableQuantity;

    @Schema(description = "包装数")
    private Integer packageNum;

    @Schema(description = "拆包")
    private String unpacking;

    @Schema(description = "拆包名称")
    private String unpackingName;

    @Schema(description = "生产厂家")
    private String manufactureFirm;

    @Schema(description = "生产厂家名称")
    private String manufactureFirmValue;

    @Schema(description = "药品追溯码")
    private String drugTraceCode;

    @Schema(description = "最后使用时间")
    private LocalDateTime lastUseDate;

    @Schema(description = "最后使用时间(显示)")
    private String lastUseDateFormat;

    @Schema(description = "药品类型")
    private String drugType;

    @Schema(description = "行背景颜色")
    private String color;

    @Schema(description = "文字颜色")
    private String textColor;

    public String getStorageCode() {
        return storageCode;
    }

    public void setStorageCode(String storageCode) {
        this.storageCode = storageCode;
    }

    public String getStorageName() {
        return storageName;
    }

    public void setStorageName(String storageName) {
        this.storageName = storageName;
    }

    public String getDrugGoodsCode() {
        return drugGoodsCode;
    }

    public void setDrugGoodsCode(String drugGoodsCode) {
        this.drugGoodsCode = drugGoodsCode;
    }

    public String getDrugGoodsName() {
        return drugGoodsName;
    }

    public void setDrugGoodsName(String drugGoodsName) {
        this.drugGoodsName = drugGoodsName;
    }

    public String getDrugSpec() {
        return drugSpec;
    }

    public void setDrugSpec(String drugSpec) {
        this.drugSpec = drugSpec;
    }

    public String getStoragePlaceName() {
        return storagePlaceName;
    }

    public void setStoragePlaceName(String storagePlaceName) {
        this.storagePlaceName = storagePlaceName;
    }

    public String getDrugForm() {
        return drugForm;
    }

    public void setDrugForm(String drugForm) {
        this.drugForm = drugForm;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public BigDecimal getQuantityMin() {
        return quantityMin;
    }

    public void setQuantityMin(BigDecimal quantityMin) {
        this.quantityMin = quantityMin;
    }

    public String getSalePrice() {
        return salePrice;
    }

    public void setSalePrice(String salePrice) {
        this.salePrice = salePrice;
    }

    public String getSaleTotalPrice() {
        return saleTotalPrice;
    }

    public void setSaleTotalPrice(String saleTotalPrice) {
        this.saleTotalPrice = saleTotalPrice;
    }

    public String getPurchaseAmount() {
        return purchaseAmount;
    }

    public void setPurchaseAmount(String purchaseAmount) {
        this.purchaseAmount = purchaseAmount;
    }

    public String getPurchaseTotalAmount() {
        return purchaseTotalAmount;
    }

    public void setPurchaseTotalAmount(String purchaseTotalAmount) {
        this.purchaseTotalAmount = purchaseTotalAmount;
    }

    public String getBatchNumNo() {
        return batchNumNo;
    }

    public void setBatchNumNo(String batchNumNo) {
        this.batchNumNo = batchNumNo;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public LocalDate getEffectiveDate() {
        return effectiveDate;
    }

    public void setEffectiveDate(LocalDate effectiveDate) {
        this.effectiveDate = effectiveDate;
    }

    public String getSupplier() {
        return supplier;
    }

    public void setSupplier(String supplier) {
        this.supplier = supplier;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getBillNO() {
        return billNO;
    }

    public void setBillNO(String billNO) {
        this.billNO = billNO;
    }

    public String getInOutType() {
        return inOutType;
    }

    public void setInOutType(String inOutType) {
        this.inOutType = inOutType;
    }

    public String getInOutDepartment() {
        return inOutDepartment;
    }

    public void setInOutDepartment(String inOutDepartment) {
        this.inOutDepartment = inOutDepartment;
    }

    public String getInOutQuantity() {
        return inOutQuantity;
    }

    public void setInOutQuantity(String inOutQuantity) {
        this.inOutQuantity = inOutQuantity;
    }

    public LocalDateTime getBillDate() {
        return billDate;
    }

    public void setBillDate(LocalDateTime billDate) {
        this.billDate = billDate;
    }

    public String getAvailability() {
        return availability;
    }

    public void setAvailability(String availability) {
        this.availability = availability;
    }

    public Integer getPackageNum() {
        return packageNum;
    }

    public void setPackageNum(Integer packageNum) {
        this.packageNum = packageNum;
    }

    public String getUnpacking() {
        return unpacking;
    }

    public void setUnpacking(String unpacking) {
        this.unpacking = unpacking;
    }

    public String getManufactureFirmValue() {
        return manufactureFirmValue;
    }

    public void setManufactureFirmValue(String manufactureFirmValue) {
        this.manufactureFirmValue = manufactureFirmValue;
    }

    public String getManufactureFirm() {
        return manufactureFirm;
    }

    public void setManufactureFirm(String manufactureFirm) {
        this.manufactureFirm = manufactureFirm;
    }

    public LocalDateTime getLastUseDate() {
        return lastUseDate;
    }

    public void setLastUseDate(LocalDateTime lastUseDate) {
        this.lastUseDate = lastUseDate;
    }

    public BigDecimal getQuantityMax() {
        return quantityMax;
    }

    public void setQuantityMax(BigDecimal quantityMax) {
        this.quantityMax = quantityMax;
    }

    public String getQuantityStr() {
        return quantityStr;
    }

    public void setQuantityStr(String quantityStr) {
        this.quantityStr = quantityStr;
    }

    public String getDrugGoodsDetailId() {
        return drugGoodsDetailId;
    }

    public void setDrugGoodsDetailId(String drugGoodsDetailId) {
        this.drugGoodsDetailId = drugGoodsDetailId;
    }

    public String getQuantityMinStr() {
        return quantityMinStr;
    }

    public void setQuantityMinStr(String quantityMinStr) {
        this.quantityMinStr = quantityMinStr;
    }

    public String getWithhold() {
        return withhold;
    }

    public void setWithhold(String withhold) {
        this.withhold = withhold;
    }

    public String getAvailableQuantity() {
        return availableQuantity;
    }

    public void setAvailableQuantity(String availableQuantity) {
        this.availableQuantity = availableQuantity;
    }

    public Boolean getMissingLabel() {
        return missingLabel;
    }

    public void setMissingLabel(Boolean missingLabel) {
        this.missingLabel = missingLabel;
    }

    public String getDrugTraceCode() {
        return drugTraceCode;
    }

    public void setDrugTraceCode(String drugTraceCode) {
        this.drugTraceCode = drugTraceCode;
    }

    public String getDrugType() {
        return drugType;
    }

    public void setDrugType(String drugType) {
        this.drugType = drugType;
    }

    public String getUnpackingName() {
        return unpackingName;
    }

    public void setUnpackingName(String unpackingName) {
        this.unpackingName = unpackingName;
    }

    public BigDecimal getSaleTotalPriceNum() {
        return saleTotalPriceNum;
    }

    public void setSaleTotalPriceNum(BigDecimal saleTotalPriceNum) {
        this.saleTotalPriceNum = saleTotalPriceNum;
    }

    public BigDecimal getPurchaseTotalAmountNum() {
        return purchaseTotalAmountNum;
    }

    public void setPurchaseTotalAmountNum(BigDecimal purchaseTotalAmountNum) {
        this.purchaseTotalAmountNum = purchaseTotalAmountNum;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public String getTextColor() {
        return textColor;
    }

    public void setTextColor(String textColor) {
        this.textColor = textColor;
    }

    public String getLastUseDateFormat() {
        return lastUseDateFormat;
    }

    public void setLastUseDateFormat(String lastUseDateFormat) {
        this.lastUseDateFormat = lastUseDateFormat;
    }
}
