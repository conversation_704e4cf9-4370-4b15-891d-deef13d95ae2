package com.bjgoodwill.hip.as.to.template;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * File: EconTemplateAsNto
 * Author: zhangyunchuan
 * Date: 2025/2/27
 * Description:
 */
@Schema(description = "经济组套模板")
public class EconTemplateAsNto {

    @Schema(description = "唯一标识")
    private String id;

    @Schema(description = "组套名称")
    private String name;

    @Schema(description = "组套描述")
    private String description;

    @Schema(description = "绑费标识")
    private Boolean bindingFlag;

    @Schema(description = "所属科室编码")
    private String deptCode;

    @Schema(description = "所属科室名称")
    private String deptName;

    @Schema(description = "院区编码")
    private String hospitalAreaCode;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Boolean getBindingFlag() {
        return bindingFlag;
    }

    public void setBindingFlag(Boolean bindingFlag) {
        this.bindingFlag = bindingFlag;
    }

    public String getDeptCode() {
        return deptCode;
    }

    public void setDeptCode(String deptCode) {
        this.deptCode = deptCode;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getHospitalAreaCode() {
        return hospitalAreaCode;
    }

    public void setHospitalAreaCode(String hospitalAreaCode) {
        this.hospitalAreaCode = hospitalAreaCode;
    }
}
