package com.bjgoodwill.hip.as.to;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.time.LocalTime;

public class WorkGroupDto implements Serializable {
    public WorkGroupDto(){

    }
    @Schema(
            name = "uniqueId",
            description = "唯一编码"
    )
    private String uniqueId;
    @Schema(
            name = "id",
            description = "工作组编码"
    )
    private String id;
    @Schema(
            name = "name",
            description = "工作组名称"
    )
    private String name;
    @Schema(
            name = "hospitalAreaCode",
            description = "院区编码"
    )
    private String hospitalAreaCode;
    @Schema(
            name = "hospitalAreaName",
            description = "院区名称"
    )
    private String hospitalAreaName;
    @Schema(
            name = "deptCode",
            description = "科室编码"
    )
    private String deptCode;
    @Schema(
            name = "deptName",
            description = "科室名称"
    )
    private String deptName;
    @Schema(
            name = "place",
            description = "办公地点"
    )
    private String place;
    @Schema(
            name = "tel",
            description = "电话"
    )
    private String tel;
    @Schema(
            name = "introduction",
            description = "介绍说明"
    )
    private String introduction;
    @Schema(
            name = "serviceStartTime",
            description = "服务开始时间"
    )
    private LocalTime serviceStartTime;
    @Schema(
            name = "serviceEndTime",
            description = "服务结束时间"
    )
    private LocalTime serviceEndTime;
    @Schema(
            name = "enable",
            description = "是否可用"
    )
    private Boolean enable;
    @Schema(
            name = "type",
            description = "工作组类型"
    )
    private String type;
    @Schema(
            name = "typeName",
            description = "工作组类型名称"
    )
    private String typeName;
    @Schema(
            name = "dictDeptCode",
            description = "CT08.00.002科室代码"
    )
    private String dictDeptCode;
    @Schema(
            name = "inputPy",
            description = "拼音码"
    )
    private String inputPy;
    @Schema(
            name = "wbCode",
            description = "五笔码"
    )
    private String wbCode;

    public String getUniqueId() {
        return uniqueId;
    }

    public void setUniqueId(String uniqueId) {
        this.uniqueId = uniqueId;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getHospitalAreaCode() {
        return hospitalAreaCode;
    }

    public void setHospitalAreaCode(String hospitalAreaCode) {
        this.hospitalAreaCode = hospitalAreaCode;
    }

    public String getHospitalAreaName() {
        return hospitalAreaName;
    }

    public void setHospitalAreaName(String hospitalAreaName) {
        this.hospitalAreaName = hospitalAreaName;
    }

    public String getDeptCode() {
        return deptCode;
    }

    public void setDeptCode(String deptCode) {
        this.deptCode = deptCode;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getPlace() {
        return place;
    }

    public void setPlace(String place) {
        this.place = place;
    }

    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    public String getIntroduction() {
        return introduction;
    }

    public void setIntroduction(String introduction) {
        this.introduction = introduction;
    }

    public LocalTime getServiceStartTime() {
        return serviceStartTime;
    }

    public void setServiceStartTime(LocalTime serviceStartTime) {
        this.serviceStartTime = serviceStartTime;
    }

    public LocalTime getServiceEndTime() {
        return serviceEndTime;
    }

    public void setServiceEndTime(LocalTime serviceEndTime) {
        this.serviceEndTime = serviceEndTime;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public String getDictDeptCode() {
        return dictDeptCode;
    }

    public void setDictDeptCode(String dictDeptCode) {
        this.dictDeptCode = dictDeptCode;
    }

    public String getInputPy() {
        return inputPy;
    }

    public void setInputPy(String inputPy) {
        this.inputPy = inputPy;
    }

    public String getWbCode() {
        return wbCode;
    }

    public void setWbCode(String wbCode) {
        this.wbCode = wbCode;
    }
}
