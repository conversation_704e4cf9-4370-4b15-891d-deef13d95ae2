package com.bjgoodwill.hip.as.to;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;

@Schema(description = "汉字拼音应用层to")
public class CharacterAsTo implements Serializable {

    @Schema(description = "拼音码")
    private String inputPy;

    public String getInputPy() {
        return inputPy;
    }

    public void setInputPy(String inputPy) {
        this.inputPy = inputPy;
    }

    public CharacterAsTo() {

    }

    public CharacterAsTo(String inputPy) {
        this.inputPy = inputPy;
    }
}
