package com.bjgoodwill.hip.as.enums;

import com.bjgoodwill.hip.common.exception.BusinessErrorEnum;

/**
 * @Author: zhangyunchuan
 * @Date: 2024/12/2 17:08
 * @PROJECT: hip-ac
 */
public enum CommonBusinessErrorEnum implements BusinessErrorEnum {

    BUS_COMMON_0001("[%s]不可为空！"),
    BUS_COMMON_0002("[%s]获取失败！"),
    ;

    private final String message;

    CommonBusinessErrorEnum(String message) {
        this.message = message;
    }

    @Override
    public String getCode() {
        return this.name();
    }

    @Override
    public String getMessage() {
        return message;
    }

}
