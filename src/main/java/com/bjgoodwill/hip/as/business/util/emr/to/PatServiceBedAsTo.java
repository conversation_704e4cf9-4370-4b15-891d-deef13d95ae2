package com.bjgoodwill.hip.as.business.util.emr.to;


import com.bjgoodwill.hip.as.business.util.emr.enums.BedStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

@Schema(description = "床位服务信息")
public class PatServiceBedAsTo implements Serializable {

	@Serial
    private static final long serialVersionUID = -8005756639371747604L;

    @Schema(description = "标识")
    private String id;
    @Schema(description = "科室编码")
    private String deptCode;
    @Schema(description = "科室名称")
    private String deptName;
    @Schema(description = "护理组编码")
    private String deptNurseCode;
    @Schema(description = "护理组名称")
    private String deptNurseName;
    @Schema(description = "房间号")
    private String roomNo;
    @Schema(description = "床号")
    private String bedNo;
    @Schema(description = "床位名称")
    private String bedName;
    @Schema(description = "序号")
    private Short sortNo;
    @Schema(description = "固定项目")
    private String econIpdFixedId;
    @Schema(description = "固定项目名称")
    private String econIpdFixedName;
    @Schema(description = "责任医生编码")
    private String masterDocCode;
    @Schema(description = "责任医生名称")
    private String masterDocName;
    @Schema(description = "责任护士编码")
    private String masterNurseCode;
    @Schema(description = "责任护士名称")
    private String masterNurseName;
    @Schema(description = "床位状态（AVAILABLE空闲，OCCUPIED占用，RESERVED被包床）")
    private BedStatusEnum bedStatus;
    @Schema(description = "床位状态名称")
    private String bedStatusStr;
    @Schema(description = "已启用")
    private boolean enabled;
	@Schema(description = "床位类型")
    private String bedType;
    @Schema(description = "床位类型名称")
    private String bedTypeName;
    @Schema(description = "床位限制")
    private String bedLimit;
    @Schema(description = "床位限制名称")
    private String bedLimitName;
    @Schema(description = "占用患者流水号")
    private String visitCode;
    @Schema(description = "版本")
    private Integer version;
    @Schema(description = "医院编码")
    private String hospitalCode;
    @Schema(description = "创建的人员")
    private String createdStaff;
    @Schema(description = "创建的人员姓名")
    private String createdStaffName;
    @Schema(description = "创建的时间")
    private LocalDateTime createdDate;
    @Schema(description = "最后修改的人员")
    private String updatedStaff;
    @Schema(description = "最后修改的人员姓名")
    private String updatedStaffName;
    @Schema(description = "最后修改的时间")
    private LocalDateTime updatedDate;
    private Integer count;

    @Schema(description = "类型名称：全部，新入院，空床")
    private String typeName;

    private PatIpdInpatientAsTo inpatient;

    public String getId() {
    	return id;
    }

    public void setId(String id) {
    	this.id = id;
    }

    public String getDeptCode() {
    	return deptCode;
    }

    public void setDeptCode(String deptCode) {
    	this.deptCode = deptCode;
    }

    public String getDeptName() {
    	return deptName;
    }

    public void setDeptName(String deptName) {
    	this.deptName = deptName;
    }

    public String getDeptNurseCode() {
    	return deptNurseCode;
    }

    public void setDeptNurseCode(String deptNurseCode) {
    	this.deptNurseCode = deptNurseCode;
    }

    public String getDeptNurseName() {
    	return deptNurseName;
    }

    public void setDeptNurseName(String deptNurseName) {
    	this.deptNurseName = deptNurseName;
    }

    public String getRoomNo() {
    	return roomNo;
    }

    public void setRoomNo(String roomNo) {
    	this.roomNo = roomNo;
    }

    public String getBedNo() {
    	return bedNo;
    }

    public void setBedNo(String bedNo) {
    	this.bedNo = bedNo;
    }

    public String getBedName() {
    	return bedName;
    }

    public void setBedName(String bedName) {
    	this.bedName = bedName;
    }

    public Short getSortNo() {
    	return sortNo;
    }

    public void setSortNo(Short sortNo) {
    	this.sortNo = sortNo;
    }

    public String getEconIpdFixedId() {
    	return econIpdFixedId;
    }

    public void setEconIpdFixedId(String econIpdFixedId) {
    	this.econIpdFixedId = econIpdFixedId;
    }

    public String getEconIpdFixedName() {
    	return econIpdFixedName;
    }

    public void setEconIpdFixedName(String econIpdFixedName) {
    	this.econIpdFixedName = econIpdFixedName;
    }

    public String getMasterDocCode() {
    	return masterDocCode;
    }

    public void setMasterDocCode(String masterDocCode) {
    	this.masterDocCode = masterDocCode;
    }

    public String getMasterDocName() {
    	return masterDocName;
    }

    public void setMasterDocName(String masterDocName) {
    	this.masterDocName = masterDocName;
    }

    public String getMasterNurseCode() {
    	return masterNurseCode;
    }

    public void setMasterNurseCode(String masterNurseCode) {
    	this.masterNurseCode = masterNurseCode;
    }

    public String getMasterNurseName() {
    	return masterNurseName;
    }

    public void setMasterNurseName(String masterNurseName) {
    	this.masterNurseName = masterNurseName;
    }

    public BedStatusEnum getBedStatus() {
    	return bedStatus;
    }

    public void setBedStatus(BedStatusEnum bedStatus) {
    	this.bedStatus = bedStatus;
    }

    public boolean isEnabled() {
    	return enabled;
    }

    public void setEnabled(boolean enabled) {
    	this.enabled = enabled;
    }

	public String getBedType() {
    	return bedType;
    }

    public void setBedType(String bedType) {
    	this.bedType = bedType;
    }

    public String getBedTypeName() {
    	return bedTypeName;
    }

    public void setBedTypeName(String bedTypeName) {
    	this.bedTypeName = bedTypeName;
    }

    public String getBedLimit() {
    	return bedLimit;
    }

    public void setBedLimit(String bedLimit) {
    	this.bedLimit = bedLimit;
    }

    public String getBedLimitName() {
    	return bedLimitName;
    }

    public void setBedLimitName(String bedLimitName) {
    	this.bedLimitName = bedLimitName;
    }

    public String getVisitCode() {
    	return visitCode;
    }

    public void setVisitCode(String visitCode) {
    	this.visitCode = visitCode;
    }

    public Integer getVersion() {
    	return version;
    }

    public void setVersion(Integer version) {
    	this.version = version;
    }

    public String getHospitalCode() {
    	return hospitalCode;
    }

    public void setHospitalCode(String hospitalCode) {
    	this.hospitalCode = hospitalCode;
    }

    public String getCreatedStaff() {
    	return createdStaff;
    }

    public void setCreatedStaff(String createdStaff) {
    	this.createdStaff = createdStaff;
    }

    public String getCreatedStaffName() {
    	return createdStaffName;
    }

    public void setCreatedStaffName(String createdStaffName) {
    	this.createdStaffName = createdStaffName;
    }

    public LocalDateTime getCreatedDate() {
    	return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
    	this.createdDate = createdDate;
    }

    public String getUpdatedStaff() {
    	return updatedStaff;
    }

    public void setUpdatedStaff(String updatedStaff) {
    	this.updatedStaff = updatedStaff;
    }

    public String getUpdatedStaffName() {
    	return updatedStaffName;
    }

    public void setUpdatedStaffName(String updatedStaffName) {
    	this.updatedStaffName = updatedStaffName;
    }

    public LocalDateTime getUpdatedDate() {
    	return updatedDate;
    }

    public void setUpdatedDate(LocalDateTime updatedDate) {
    	this.updatedDate = updatedDate;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public PatIpdInpatientAsTo getInpatient() {
        return inpatient;
    }

    public void setInpatient(PatIpdInpatientAsTo inpatient) {
        this.inpatient = inpatient;
    }

    public String getBedStatusStr() {
        return bedStatusStr;
    }

    public void setBedStatusStr(String bedStatusStr) {
        this.bedStatusStr = bedStatusStr;
    }

    @Override
	public int hashCode() {
		return Objects.hash(id);
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		PatServiceBedAsTo other = (PatServiceBedAsTo) obj;
		return Objects.equals(id, other.id);
	}
}