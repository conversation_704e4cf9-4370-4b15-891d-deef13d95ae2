package com.bjgoodwill.hip.enums;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @description :
 * @createDate : 2025/3/13 15:34
 */
public enum OwnDrugEnum {

    OWNDRUGTYPE("31", "自带药");

    private String code;

    private String name;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    private OwnDrugEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static OwnDrugEnum getValue(String code) {
        for (OwnDrugEnum ownDrugEnum : OwnDrugEnum.values()) {
            if (ownDrugEnum.getCode().equals(code)) {
                return ownDrugEnum;
            }
        }
        return null;
    }

}
