package com.bjgoodwill.hip.to.econ;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: zhangyunchuan
 * @Date: 2024/8/9 14:36
 * @PROJECT: hip-econ-ac
 */
@Schema(description = "支付信息")
public class EconSettlementTo implements Serializable {

    @Schema(description = "患者主索引")
    private String patMiCode;

    @Schema(description = "流水号")
    private String visitCode;

    @Schema(description = "支付方式")
    private String payWay;

    @Schema(description = "金额")
    private BigDecimal amount;

    @Schema(description = "交易类型,1收费、2退费")
    private String businessType;

    @Schema(description = "收款员id")
    private String payeeCode;

    @Schema(description = "收款员姓名")
    private String payeeName;

    @Schema(description = "第三方订单号")
    private String posCode;

    @Schema(description = "订单流水号")
    private String posId;

    @Schema(description = "医疗机构编码")
    private String hospitalCode;

    public String getVisitCode() {
        return visitCode;
    }

    public void setVisitCode(String visitCode) {
        this.visitCode = visitCode;
    }

    public String getPayWay() {
        return payWay;
    }

    public void setPayWay(String payWay) {
        this.payWay = payWay;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getPayeeCode() {
        return payeeCode;
    }

    public void setPayeeCode(String payeeCode) {
        this.payeeCode = payeeCode;
    }

    public String getPayeeName() {
        return payeeName;
    }

    public void setPayeeName(String payeeName) {
        this.payeeName = payeeName;
    }

    public String getPosCode() {
        return posCode;
    }

    public void setPosCode(String posCode) {
        this.posCode = posCode;
    }

    public String getPosId() {
        return posId;
    }

    public void setPosId(String posId) {
        this.posId = posId;
    }

    public String getHospitalCode() {
        return hospitalCode;
    }

    public void setHospitalCode(String hospitalCode) {
        this.hospitalCode = hospitalCode;
    }

    public String getPatMiCode() {
        return patMiCode;
    }

    public void setPatMiCode(String patMiCode) {
        this.patMiCode = patMiCode;
    }
}
