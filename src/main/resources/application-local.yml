server:
  port: 8185
spring:
  cloud:
    nacos:
      config:
        enabled: false
  data:
    redis:
      # Redis数据库索引（默认为0）
      database: 1
      # Redis服务器地址
      host: *************
      # Redis服务器连接端口
      port: 6381
      # Redis服务器连接密码（默认为空）
      password: '^5kTgji@xxre]{s'
      # 连接超时时间
      timeout: 10s
      lettuce:
        pool:
          # 连接池最大连接数
          max-active: 200
          # 连接池最大阻塞等待时间（使用负值表示没有限制）
          max-wait: -1ms
          # 连接池中的最大空闲连接
          max-idle: 10
          # 连接池中的最小空闲连接
          min-idle: 0
  rabbitmq:
    host: *************
    username: hip
    password: '[r4~b]snjHgrPpM'
    virtual-host: hip
    port: 5672
mqtt:
  broker-url: tcp://*************:11883  # MQTT broker地址
  client-id: ${spring.application.name}-${random.uuid}  # 客户端ID
  username: guest  # MQTT broker用户名
  password: Okk1p,'uCHKjr5~  # MQTT broker密码
  clean-session: false  # 清除会话
  default-topic: topic/default  # 默认主题
  keep-alive: 60  # 保持连接时间
  connection-timeout: 30  # 连接超时时间
# Sa-Token 配置
sa-token:
  # token 最低活跃频率（单位：秒），如果 token 超过此时间没有访问系统就会被冻结，默认-1 代表不限制，永不冻结
  active-timeout: -1
  # 是否允许同一账号多地同时登录 （为 true 时允许一起登录, 为 false 时新登录挤掉旧登录）
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个 token （为 true 时所有登录共用一个 token, 为 false 时每次登录新建一个 token）
  is-share: true
  # 是否输出操作日志
  is-log: true
  # 配置 Sa-Token 单独使用的 Redis 连接
  alone-redis:
    # Redis数据库索引（默认为0）
    database: 3
    # Redis服务器地址
    host: *************
    # Redis服务器连接端口
    port: 6381
    # Redis服务器连接密码（默认为空）
    password: '^5kTgji@xxre]{s'
    # 连接超时时间
    timeout: 10s
seata:
  enabled: false # 是否开启 spring-boot 自动装配，true、false,(SSBS)专有配置，默认 true
snail-job:
  server:
    host: **************
    port: 30788
  namespace: jJRWmH4gXS05eDh6kGpUqhzBe7wZbc4K
  group: as_schedule
  token: SJ_Wyz3dmsdbDOkDujOTSSoBjGQP1BMsVnj
hip:
  report:
    url: http://**************:8183
    gateway-url: http://**************:30800
  emr:
    emr-base-url: http://hip-emr-interface-ac:8080
  domains:
    #hip-admin-dc
    admin-dc-url: http://hip-admin-dc.hip-dev.**************.nip.io:30660/
    security:
      name: hip-security-ds
      url: ${hip.domains.admin-dc-url}
    term:
      name: hip-term-ds
      url: ${hip.domains.admin-dc-url}
    param:
      name: hip-param-ds
      url: ${hip.domains.admin-dc-url}
    notice:
      name: hip-notice-ds
      url: ${hip.domains.admin-dc-url}
    fs:
      name: hip-fs-ds
      url: ${hip.domains.admin-dc-url}
    audit:
      name: hip-audit-ds
      url: ${hip.domains.admin-dc-url}
    generation:
      name: hip-code-generation-ds
      url: ${hip.domains.admin-dc-url}
    monitor-message:
      name: hip-monitor-message-ds
      url: ${hip.domains.admin-dc-url}
    print:
      name: hip-print-config-ds
      url: ${hip.domains.admin-dc-url}
    report-manager:
      name: hip-report-manager-ds
      url: ${hip.domains.admin-dc-url}
    #hip-base-dc
    base-dc-url: http://hip-base-dc.hip-dev.**************.nip.io:30660/
    org:
      name: hip-org-ds
      url: ${hip.domains.base-dc-url}
    pat-index:
      name: hip-base-pat-index-ds
      url: ${hip.domains.base-dc-url}
    drug-goods:
      name: hip-base-drug-goods-ds
      url: ${hip.domains.base-dc-url}
    material-goods:
      name: hip-base-material-ds
      url: ${hip.domains.base-dc-url}
    econ-price:
      name: hip-base-econ-price-ds
      url: ${hip.domains.base-dc-url}
    base_cis_diagnose:
      name: hip-base-cis-diagnose-ds
      url: ${hip.domains.base-dc-url}
    base_cis_dict:
      name: hip-base-cis-dict-ds
      url: ${hip.domains.base-dc-url}
    base_cis_medicineitem:
      name: hip-base-cis-medicineitem-ds
      url: ${hip.domains.base-dc-url}
    econ-fixed:
      name: hip-base-econ-fixed-ds
      url: ${hip.domains.base-dc-url}
    econ-statistic:
      name: hip-base-econ-statistic-ds
      url: ${hip.domains.base-dc-url}
    econ-invoice:
      name: hip-econ-invoice-ds
      url: ${hip.domains.base-dc-url}
    base-code:
      name: hip-base-code-ds
      url: ${hip.domains.base-dc-url}
    #hip-cis-base-dc
    cis-base-url: http://hip-cis-base-dc.hip-dev.**************.nip.io:30660/
    cis-cdr:
      url: ${hip.domains.cis-base-url}
      name: hip-cis-cdr-ds
    mtcpoe:
      url: ${hip.domains.cis-base-url}
      name: hip-cis-mt-cpoe-ds
    cis-cds:
      url: ${hip.domains.cis-base-url}
      name: hip-cis-cds-ds
    rule:
      url: ${hip.domains.cis-base-url}
      name: hip-cis-rule-ds
    cis-apply:
      url: ${hip.domains.cis-base-url}
      name: hip-cis-apply-ds
    #hip-econ-pay-dc
    econ-pay-url: http://hip-econ-pay-dc.hip-dev.**************.nip.io:30660/
    econ-pay:
      url: ${hip.domains.econ-pay-url}
      name: hip-econ-pay-ds
    #hip-fm-dc
    fm-dc-url: http://hip-fm-dc.hip-dev.**************.nip.io:30660/
    econ-finance:
      url: ${hip.domains.fm-dc-url}
      name: hip-econ-finance-ds
    drug-balance:
      url: ${hip.domains.fm-dc-url}
      name: hip-drug-balance-ds
    drug-price:
      url: ${hip.domains.fm-dc-url}
      name: hip-drug-price-ds
    #hip-drug-manage-dc
    drug-manage-dc-url: http://hip-drug-manage-dc.hip-dev.**************.nip.io:30660/
    drug-stock:
      url: ${hip.domains.drug-manage-dc-url}
      name: hip-drug-stock-ds
    drug-inout:
      url: ${hip.domains.drug-manage-dc-url}
      name: hip-drug-inout-ds
    drug-pms:
      url: ${hip.domains.drug-manage-dc-url}
      name: hip-drug-pms-ds
    #hip-mate-manage-dc 地址
    mate-manage-dc-url: http://hip-mate-manage-dc.hip-dev.**************.nip.io:30660/
    mate-base:
      url: ${hip.domains.mate-manage-dc-url}
      name: hip-mate-manage-ds
    # hip-mi-dc 地址
    mi-dc-url: http://hip-mi-dc.hip-dev.**************.nip.io:30660/
    mi_opd:
      url: ${hip.domains.mi-dc-url}
      name: hip-mi-opd-ds
    mi-ipd:
      url: ${hip.domains.mi-dc-url}
      name: hip-mi-ipd-ds
    mi_payment:
      url: ${hip.domains.mi-dc-url}
      name: hip-mi-payment-ds
    mi-base:
      url: ${hip.domains.mi-dc-url}
      name: hip-base-mi-ds
    mi-interfaces:
      url: ${hip.domains.mi-dc-url}
      name: hip-mi-interface-ds
    mi_audit:
      url: ${hip.domains.mi-dc-url}
      name: hip-mi-audit-ds
    mi_businessdict:
      url: ${hip.domains.mi-dc-url}
      name: hip-mi-dict-ds
    #hip-opd-dc
    opd-dc-url: http://hip-opd-dc.hip-dev.**************.nip.io:30660/
    opdcpoe:
      name: hip-cis-opd-cpoe-ds
      url: ${hip.domains.opd-dc-url}
    schedule:
      name: hip-pat-schedule-ds
      url: ${hip.domains.opd-dc-url}
    regist:
      name: hip-pat-regist-ds
      url: ${hip.domains.opd-dc-url}
    econ-opd-bill:
      name: hip-econ-opd-bill-ds
      url: ${hip.domains.opd-dc-url}
    econ-opd-acct:
      name: hip-econ-pat-acct-ds
      url: ${hip.domains.opd-dc-url}
    econ-pe:
      name: hip-econ-pe-ds
      url: ${hip.domains.opd-dc-url}
    drug-opd:
      name: hip-drug-opd-ds
      url: ${hip.domains.opd-dc-url}
    #hip-ipd-dc
    ipd-dc-url: http://hip-ipd-dc.hip-dev.**************.nip.io:30660/
    ipdcpoe:
      name: hip-cis-ipd-cpoe-ds
      url: ${hip.domains.ipd-dc-url}
    cis_nurse:
      name: hip-cis-ipd-nurse-ds
      url: ${hip.domains.ipd-dc-url}
    pat-apply:
      name: hip-pat-apply-ds
      url: ${hip.domains.ipd-dc-url}
    pat-in-hospital:
      name: hip-pat-in-hospital-ds
      url: ${hip.domains.ipd-dc-url}
    econ-ipd-bill:
      name: hip-econ-ipd-bill-ds
      url: ${hip.domains.ipd-dc-url}
    drug-ipd:
      name: hip-drug-ipd-ds
      url: ${hip.domains.ipd-dc-url}
    #hip-war-mate-dc
    war-mate-dc-url: http://hip-war-mate-dc.hip-dev.**************.nip.io:30660/
    war-mate:
      name: hip-war-mate-ds
      url: ${hip.domains.war-mate-dc-url}